import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm, useFieldArray, Control } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { contractCalculator } from "@/lib/contractCalculations";
import {
  ArrowRight,
  Save,
  Plus,
  Trash2,
  FileText,
  User,
  Building2,
  DollarSign,
  Calendar,
  Settings,
  AlertTriangle,
  Calculator,
  Eye,
  Target,
  CreditCard,
  Info
} from "lucide-react";
import labels from "../i18n";

// Enhanced contract schema
const contractSchema = z.object({
  // Basic Information
  contractNumber: z.string().min(1, 'رقم العقد مطلوب'),
  contractInternalId: z.string().optional(),
  contractDescription: z.string().optional(),
  contractSubject: z.string().min(1, 'موضوع العقد مطلوب'),
  clientId: z.string().min(1, 'العميل مطلوب'),
  contractType: z.string().min(1, 'نوع العقد مطلوب'),
  contractStatus: z.string().default('نشط'),
  
  // Dates
  contractDate: z.string().optional(),
  contractSigningDate: z.string().min(1, 'تاريخ توقيع العقد مطلوب'),
  financialActivationDate: z.string().optional(),
  startDate: z.string().min(1, 'تاريخ البدء مطلوب'),
  actualStartDate: z.string().optional(),
  endDate: z.string().optional(),
  actualEndDate: z.string().optional(),
  contractDurationYears: z.number().min(1, 'مدة العقد مطلوبة'),
  
  // Asset and Parties
  assetOwner: z.string().optional(),
  financialGuarantorId: z.string().optional(),
  parentContractId: z.string().optional(),
  
  // Multi-product settings
  numberOfProducts: z.number().min(1).default(1),
  hasUnifiedActivationDate: z.boolean().default(true),
  
  // Financial
  totalContractValue: z.number().min(0, 'قيمة العقد مطلوبة'),
  monthlyAmount: z.number().min(0, 'المبلغ الشهري مطلوب'),
  paymentFrequency: z.string().default('شهري'),
  irregularPaymentMonths: z.number().optional(),

  // Final Insurance
  finalInsuranceRate: z.number().default(0),

  // Advance Payment
  advancePaymentMonths: z.number().default(0),
  advancePaymentAmount: z.number().default(0),

  // Check Management
  checkStatus: z.string().default('لم يتقدم بالشيكات'),
  
  // Annual Increases
  annualIncreaseType: z.string().default('لا يوجد'),
  annualIncreaseValue: z.number().default(0),
  annualIncreaseStartYear: z.number().default(2),
  
  // Penalties and Fees
  lateFeeType: z.string().default('نسبة مئوية'),
  lateFeeValue: z.number().default(0),
  gracePeriodDays: z.number().default(0),
  bouncedCheckFeeType: z.string().default('مبلغ ثابت'),
  bouncedCheckFeeValue: z.number().default(0),
  additionalFees: z.string().optional(),
  
  // Notes
  importantNotes: z.string().optional(),
  notes: z.string().optional(),

  // Reference Data Fields (dynamic based on reference lists)
  referenceData: z.record(z.string()).optional(),
  
  // Products (for multi-product contracts)
  products: z.array(z.object({
    productLabel: z.string().min(1, 'تسمية المنتج مطلوبة'),
    area: z.number().min(0.01, 'المساحة يجب أن تكون أكبر من صفر'),
    meterPrice: z.number().min(0.01, 'سعر المتر يجب أن يكون أكبر من صفر'),
    activationDate: z.string().optional(),
    endDate: z.string().optional(),
    billingType: z.enum(['شهري', 'ربع سنوي', 'نصف سنوي', 'سنوي', 'غير منتظم']).default('شهري'),
    irregularBillingMonths: z.number().optional(),
    taxInfo: z.boolean().default(false),
    taxRate: z.number().default(0),
  })).min(1, 'يجب إضافة منتج واحد على الأقل').default([]),
  
  // Partners
  partners: z.array(z.object({
    partnerId: z.string().min(1, 'الشريك مطلوب'),
    partnerType: z.string().default('شريك'),
    partnershipPercentage: z.number().min(0).max(100).default(0),
  })).default([]),
});

type ContractFormData = z.infer<typeof contractSchema>;

// Type helper for form control
const getFormControl = (control: Control<ContractFormData>) => control as any;

const ContractsNew = () => {
  const [lang] = useState<"ar" | "en">((localStorage.getItem("lang") as "ar" | "en") || "ar");
  const [currentStep, setCurrentStep] = useState(1);
  const [showPreview, setShowPreview] = useState(false);
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const t = labels[lang];

  // Fetch clients for selection
  const { data: clients = [] } = useQuery({
    queryKey: ["/api/clients"],
    queryFn: () => apiRequest("/api/clients"),
  });

  // Fetch contracts for parent contract selection
  const { data: contracts = [] } = useQuery({
    queryKey: ["/api/contracts"],
    queryFn: () => apiRequest("/api/contracts"),
  });

  // Fetch reference lists for contracts module
  const { data: referenceLists = [] } = useQuery({
    queryKey: ["/api/reference-lists"],
    queryFn: () => apiRequest("/api/reference-lists"),
  });

  // Filter reference lists for contracts module
  const contractReferenceLists = referenceLists.filter((list: any) => {
    let modules = [];
    if (typeof list.usageModules === 'string') {
      try {
        modules = JSON.parse(list.usageModules);
      } catch (e) {
        modules = [];
      }
    } else if (Array.isArray(list.usageModules)) {
      modules = list.usageModules;
    }
    return modules.includes('contracts') && list.isActive !== false;
  });

  const form = useForm<ContractFormData>({
    resolver: zodResolver(contractSchema),
    mode: "onChange",
    defaultValues: {
      contractNumber: "",
      contractInternalId: undefined,
      contractDescription: undefined,
      contractSubject: "",
      clientId: "",
      contractType: "",
      contractStatus: "نشط",
      contractDate: undefined,
      contractSigningDate: "",
      financialActivationDate: undefined,
      startDate: "",
      actualStartDate: undefined,
      endDate: undefined,
      actualEndDate: undefined,
      contractDurationYears: 1,
      assetOwner: undefined,
      financialGuarantorId: undefined,
      parentContractId: undefined,
      numberOfProducts: 1,
      hasUnifiedActivationDate: true,
      totalContractValue: 0,
      monthlyAmount: 0,
      paymentFrequency: "شهري",
      irregularPaymentMonths: undefined,
      finalInsuranceRate: 0,
      advancePaymentMonths: 0,
      advancePaymentAmount: 0,
      checkStatus: "لم يتقدم بالشيكات",
      annualIncreaseType: "لا يوجد",
      annualIncreaseValue: 0,
      annualIncreaseStartYear: 2,
      lateFeeType: "نسبة مئوية",
      lateFeeValue: 0,
      gracePeriodDays: 0,
      bouncedCheckFeeType: "مبلغ ثابت",
      bouncedCheckFeeValue: 0,
      additionalFees: undefined,
      importantNotes: undefined,
      notes: undefined,
      products: [{
        productLabel: 'منتج افتراضي',
        area: 0,
        meterPrice: 0,
        activationDate: "",
        endDate: "",
        billingType: "شهري",
        irregularBillingMonths: 1,
        taxInfo: false,
        taxRate: 0,
      }],
      partners: [],
      referenceData: {},
    },
  });

  // Field arrays for dynamic products and partners
  const { fields: productFields, append: appendProduct, remove: removeProduct } = useFieldArray({
    control: form.control,
    name: "products",
  });

  const { fields: partnerFields, append: appendPartner, remove: removePartner } = useFieldArray({
    control: form.control,
    name: "partners",
  });

  // Watch for changes to calculate values
  const watchedValues = form.watch();
  const numberOfProducts = form.watch("numberOfProducts");
  const hasUnifiedActivationDate = form.watch("hasUnifiedActivationDate");
  const paymentFrequency = form.watch("paymentFrequency");
  const contractDurationYears = form.watch("contractDurationYears");
  const annualIncreaseType = form.watch("annualIncreaseType");
  const annualIncreaseValue = form.watch("annualIncreaseValue");
  const annualIncreaseStartYear = form.watch("annualIncreaseStartYear");
  const finalInsuranceRate = form.watch("finalInsuranceRate");

  // Auto-generate internal ID
  React.useEffect(() => {
    if (!form.getValues("contractInternalId")) {
      const timestamp = Date.now();
      form.setValue("contractInternalId", `INT-${timestamp}`);
    }
  }, [form]);

  // Auto-calculate end date based on start date and duration
  React.useEffect(() => {
    const startDate = form.getValues("startDate");
    const duration = form.getValues("contractDurationYears");

    if (startDate && duration) {
      const start = new Date(startDate);
      const end = new Date(start);
      end.setFullYear(end.getFullYear() + duration);
      form.setValue("endDate", end.toISOString().split('T')[0]);
    }
  }, [watchedValues.startDate, watchedValues.contractDurationYears, form]);

  // Auto-update start date and contract status based on dates
  React.useEffect(() => {
    const contractSigningDate = form.getValues("contractSigningDate");
    const financialActivationDate = form.getValues("financialActivationDate");
    const endDate = form.getValues("endDate");
    const currentDate = new Date();

    // Set start date based on financial activation date or signing date
    if (financialActivationDate) {
      form.setValue("startDate", financialActivationDate);
    } else if (contractSigningDate) {
      form.setValue("startDate", contractSigningDate);
    }

    // Update contract status
    let status = "غير مفعل"; // Default status

    if (contractSigningDate && !financialActivationDate) {
      // Contract signed but not financially activated
      status = "غير مفعل";
    } else if (financialActivationDate) {
      // Contract has financial activation date
      if (endDate && new Date(endDate) < currentDate) {
        // Contract has ended
        status = "منتهي";
      } else {
        // Contract is active
        status = "نشط";
      }
    }

    form.setValue("contractStatus", status);
  }, [
    watchedValues.contractSigningDate,
    watchedValues.financialActivationDate,
    watchedValues.endDate,
    form
  ]);

  // Sync products array with numberOfProducts
  React.useEffect(() => {
    const currentProducts = productFields.length;

    if (numberOfProducts > currentProducts) {
      // Add products
      for (let i = currentProducts; i < numberOfProducts; i++) {
        appendProduct({
          productLabel: `منتج ${i + 1}`,
          area: 0,
          meterPrice: 0,
          activationDate: "",
          endDate: "",
          billingType: "شهري",
          irregularBillingMonths: 1,
          taxInfo: false,
          taxRate: 0,
        });
      }
    } else if (numberOfProducts < currentProducts) {
      // Remove products
      for (let i = currentProducts - 1; i >= numberOfProducts; i--) {
        removeProduct(i);
      }
    }
  }, [numberOfProducts, productFields.length, appendProduct, removeProduct]);

  // Initialize with one product if none exist
  React.useEffect(() => {
    if (productFields.length === 0 && numberOfProducts >= 1) {
      appendProduct({
        productLabel: 'منتج افتراضي',
        area: 0,
        meterPrice: 0,
        activationDate: "",
        endDate: "",
        billingType: "شهري",
        irregularBillingMonths: 1,
        taxInfo: false,
        taxRate: 0,
      });
    }
  }, [productFields.length, numberOfProducts, appendProduct]);

  // Auto-calculate financial values based on products and settings
  React.useEffect(() => {
    const products = form.getValues("products");

    if (products.length > 0 && contractDurationYears > 0) {
      // Check if all products have area and meter price
      const hasCompleteProducts = products.every(p => p.area > 0 && p.meterPrice > 0);

      if (hasCompleteProducts) {
        const calculation = contractCalculator.calculateTotalContractValue(
          products,
          contractDurationYears,
          annualIncreaseType,
          annualIncreaseValue,
          annualIncreaseStartYear,
          finalInsuranceRate
        );

        // Update total contract value
        form.setValue("totalContractValue", calculation.totalValue + calculation.finalInsurance);

        // Calculate monthly amount based on first year and payment frequency
        const firstYearValue = calculation.yearlyValues[0] || 0;
        const monthlyAmount = contractCalculator.calculateMonthlyAmount(
          firstYearValue,
          paymentFrequency,
          form.getValues("irregularPaymentMonths")
        );

        form.setValue("monthlyAmount", monthlyAmount);

        // Calculate advance payment amount
        const advancePaymentMonths = form.getValues("advancePaymentMonths");
        if (advancePaymentMonths > 0) {
          let totalAdvanceAmount = 0;
          products.forEach((product: any) => {
            const yearlyAmount = (product.area || 0) * (product.meterPrice || 0);
            const monthlyAmount = yearlyAmount / 12;
            totalAdvanceAmount += monthlyAmount * advancePaymentMonths;
          });
          form.setValue("advancePaymentAmount", totalAdvanceAmount);
        }
      }
    }
  }, [
    productFields,
    contractDurationYears,
    annualIncreaseType,
    annualIncreaseValue,
    annualIncreaseStartYear,
    finalInsuranceRate,
    paymentFrequency,
    watchedValues.advancePaymentMonths,
    form
  ]);

  const createContractMutation = useMutation({
    mutationFn: (data: ContractFormData) => apiRequest("/api/contracts/enhanced", {
      method: 'POST',
      body: JSON.stringify(data),
    }),
    onSuccess: () => {
      toast({
        title: "نجح الإنشاء",
        description: "تم إنشاء العقد بنجاح!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/contracts"] });
      // Navigate to contracts list or contract details
      window.location.href = "/contracts";
    },
    onError: (error: any) => {
      console.error('Contract creation error:', error);
      let errorMessage = 'حدث خطأ غير متوقع';

      if (error.message) {
        if (error.message.includes('Contract number already exists')) {
          errorMessage = 'رقم العقد موجود مسبقاً، يرجى استخدام رقم آخر';
        } else if (error.message.includes('Required fields')) {
          errorMessage = 'بعض الحقول المطلوبة مفقودة';
        } else if (error.message.includes('validation')) {
          errorMessage = 'خطأ في التحقق من صحة البيانات';
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        title: "فشل في إنشاء العقد",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: ContractFormData) => {
    console.log('Contract data to submit:', data);

    // Validate required fields
    const errors = [];

    if (!data.contractNumber) errors.push('رقم العقد مطلوب');
    if (!data.contractSubject) errors.push('موضوع العقد مطلوب');
    if (!data.clientId) errors.push('العميل مطلوب');
    if (!data.contractType) errors.push('نوع العقد مطلوب');
    if (!data.contractSigningDate) errors.push('تاريخ توقيع العقد مطلوب');
    if (!data.startDate) errors.push('تاريخ البدء مطلوب');
    if (!data.contractDurationYears || data.contractDurationYears < 1) errors.push('مدة العقد مطلوبة');

    // Check if products are provided and valid
    if (!data.products || data.products.length === 0) {
      // Add default product if none provided
      data.products = [{
        productLabel: 'منتج افتراضي',
        area: 1,
        meterPrice: data.totalContractValue || 1000,
        activationDate: data.startDate,
        endDate: data.endDate,
        billingType: 'شهري',
        irregularBillingMonths: 1,
        taxInfo: false,
        taxRate: 0,
      }];
    } else {
      // Validate existing products
      data.products.forEach((product, index) => {
        if (!product.productLabel) errors.push(`تسمية المنتج ${index + 1} مطلوبة`);
        if (!product.area || product.area <= 0) errors.push(`مساحة المنتج ${index + 1} مطلوبة`);
        if (!product.meterPrice || product.meterPrice <= 0) errors.push(`سعر متر المنتج ${index + 1} مطلوب`);
      });
    }

    // Ensure totalContractValue is set
    if (!data.totalContractValue || data.totalContractValue <= 0) {
      if (data.products && data.products.length > 0) {
        // Calculate from products
        const totalValue = data.products.reduce((sum, product) => {
          const effectivePrice = product.taxInfo ?
            product.meterPrice * (1 + product.taxRate / 100) :
            product.meterPrice;
          return sum + (product.area * effectivePrice * data.contractDurationYears);
        }, 0);
        data.totalContractValue = totalValue;
      } else {
        errors.push('قيمة العقد مطلوبة');
      }
    }

    // Ensure monthlyAmount is set
    if (!data.monthlyAmount || data.monthlyAmount <= 0) {
      data.monthlyAmount = data.totalContractValue / (data.contractDurationYears * 12);
    }

    if (errors.length > 0) {
      toast({
        title: "خطأ في البيانات",
        description: errors.join('، '),
        variant: "destructive",
      });
      return;
    }

    createContractMutation.mutate(data);
  };

  const steps = [
    { id: 1, title: "البيانات الأساسية", icon: FileText },
    { id: 2, title: "الأطراف والشركاء", icon: User },
    { id: 3, title: "المنتجات والمساحات", icon: Building2 },
    { id: 4, title: "الشروط المالية", icon: DollarSign },
    { id: 5, title: "المراجعة والحفظ", icon: Save },
  ];

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Generate installment schedule preview
  const generateInstallmentPreview = () => {
    const products = form.getValues("products");
    const startDate = form.getValues("startDate");
    const contractDurationYears = form.getValues("contractDurationYears");
    const paymentFrequency = form.getValues("paymentFrequency");
    const irregularPaymentMonths = form.getValues("irregularPaymentMonths");

    if (!products.length || !startDate || !contractDurationYears) return [];

    const installments = [];
    const start = new Date(startDate);

    // Calculate months between payments
    let monthsBetween = 1;
    switch (paymentFrequency) {
      case 'شهري': monthsBetween = 1; break;
      case 'ربع سنوي': monthsBetween = 3; break;
      case 'نصف سنوي': monthsBetween = 6; break;
      case 'سنوي': monthsBetween = 12; break;
      case 'غير منتظم': monthsBetween = irregularPaymentMonths || 1; break;
    }

    // Calculate total yearly value with taxes
    const yearlyValue = products.reduce((sum, product) => {
      const effectiveMeterPrice = product.taxInfo
        ? product.meterPrice * (1 + product.taxRate / 100)
        : product.meterPrice;
      return sum + (product.area * effectiveMeterPrice);
    }, 0);

    const paymentsPerYear = 12 / monthsBetween;
    const installmentAmount = yearlyValue / paymentsPerYear;
    const totalInstallments = contractDurationYears * paymentsPerYear;

    // Generate installment dates
    let currentDate = new Date(start);
    for (let i = 0; i < Math.min(totalInstallments, 12); i++) { // Show first 12 installments
      installments.push({
        number: i + 1,
        dueDate: new Date(currentDate).toLocaleDateString('ar-EG'),
        amount: installmentAmount,
        year: Math.floor(i / paymentsPerYear) + 1
      });

      currentDate.setMonth(currentDate.getMonth() + monthsBetween);
    }

    return installments;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-reverse space-x-4">
              <Button variant="ghost" size="sm" onClick={() => window.history.back()}>
                <ArrowRight className="h-4 w-4 ml-2" />
                {lang === "ar" ? "العودة للعقود" : "Back to Contracts"}
              </Button>
              <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {lang === "ar" ? "إضافة عقد جديد" : "Add New Contract"}
              </h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Steps */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => {
                const Icon = step.icon;
                const isActive = currentStep === step.id;
                const isCompleted = currentStep > step.id;
                
                return (
                  <div key={step.id} className="flex items-center">
                    <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                      isActive 
                        ? 'border-primary bg-primary text-white' 
                        : isCompleted 
                        ? 'border-green-500 bg-green-500 text-white'
                        : 'border-gray-300 bg-white text-gray-500'
                    }`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div className="mr-3">
                      <p className={`text-sm font-medium ${
                        isActive ? 'text-primary' : isCompleted ? 'text-green-600' : 'text-gray-500'
                      }`}>
                        {step.title}
                      </p>
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`w-16 h-0.5 mx-4 ${
                        isCompleted ? 'bg-green-500' : 'bg-gray-300'
                      }`} />
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    البيانات الأساسية للعقد
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={getFormControl(form.control)}
                      name="contractNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>رقم العقد *</FormLabel>
                          <FormControl>
                            <Input placeholder="أدخل رقم العقد" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="contractInternalId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>المعرف الداخلي</FormLabel>
                          <FormControl>
                            <Input placeholder="يتم إنشاؤه تلقائياً" {...field} disabled />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="contractSubject"
                      render={({ field }) => (
                        <FormItem className="md:col-span-2">
                          <FormLabel>موضوع العقد *</FormLabel>
                          <FormControl>
                            <Input placeholder="أدخل موضوع العقد" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="contractDescription"
                      render={({ field }) => (
                        <FormItem className="md:col-span-2">
                          <FormLabel>وصف العقد</FormLabel>
                          <FormControl>
                            <Textarea placeholder="أدخل وصف تفصيلي للعقد" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="contractType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>نوع العقد *</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="اختر نوع العقد" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="إيجار">إيجار</SelectItem>
                              <SelectItem value="تمديد مساحة">تمديد مساحة</SelectItem>
                              <SelectItem value="اتفاق مشترك">اتفاق مشترك</SelectItem>
                              <SelectItem value="بيع">بيع</SelectItem>
                              <SelectItem value="خدمات">خدمات</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />



                    <FormField
                      control={getFormControl(form.control)}
                      name="contractSigningDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>تاريخ توقيع العقد *</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                          <p className="text-xs text-gray-600">
                            تاريخ توقيع العقد (أساس حساب المدفوعات إذا لم يتم تحديد تاريخ التفعيل المالي)
                          </p>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="financialActivationDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>تاريخ التفعيل المالي</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                          <p className="text-xs text-gray-600">
                            اختياري - إذا تم تحديده سيكون أساس حساب المدفوعات بدلاً من تاريخ التوقيع
                          </p>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="contractDurationYears"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>مدة العقد (بالسنوات) *</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="1"
                              placeholder="1"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="startDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>تاريخ البدء *</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                          <p className="text-xs text-gray-600">
                            تاريخ بداية العقد (يحتسب تلقائياً من تاريخ التوقيع أو التفعيل المالي)
                          </p>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="endDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>تاريخ انتهاء العقد</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} disabled className="bg-gray-100" />
                          </FormControl>
                          <FormMessage />
                          <p className="text-xs text-gray-600">
                            يحتسب تلقائياً بناءً على تاريخ البدء ومدة العقد
                          </p>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="assetOwner"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>الجهة المالكة للأصل</FormLabel>
                          <FormControl>
                            <Input placeholder="اسم الجهة المالكة" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Reference Data Section */}
                  {contractReferenceLists.length > 0 && (
                    <>
                      <Separator />
                      <div>
                        <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                          <Settings className="h-5 w-5" />
                          البيانات المرجعية
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {contractReferenceLists.map((refList: any) => (
                            <FormField
                              key={refList.id}
                              control={getFormControl(form.control)}
                              name={`referenceData.${refList.listName}`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>{refList.listName}</FormLabel>
                                  <Select onValueChange={field.onChange} value={field.value as string}>
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder={`اختر ${refList.listName}`} />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="">-- اختر --</SelectItem>
                                      {refList.listData.map((item: string, index: number) => (
                                        <SelectItem key={index} value={item}>
                                          {item}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                  {refList.description && (
                                    <p className="text-xs text-gray-600">
                                      {refList.description}
                                    </p>
                                  )}
                                </FormItem>
                              )}
                            />
                          ))}
                        </div>
                      </div>
                    </>
                  )}

                  {/* Contract Status Preview */}
                  {(watchedValues.contractSigningDate || watchedValues.financialActivationDate) && (
                    <>
                      <Separator />
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 className="text-lg font-medium mb-3 flex items-center gap-2 text-blue-800">
                          <Info className="h-5 w-5" />
                          معاينة حالة العقد
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-blue-700">حالة العقد الحالية</label>
                            <div className="flex items-center gap-2 mt-1">
                              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                watchedValues.contractStatus === 'نشط' ? 'bg-green-100 text-green-800' :
                                watchedValues.contractStatus === 'غير مفعل' ? 'bg-yellow-100 text-yellow-800' :
                                watchedValues.contractStatus === 'منتهي' ? 'bg-gray-100 text-gray-800' :
                                watchedValues.contractStatus === 'مفسوخ' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-600'
                              }`}>
                                {watchedValues.contractStatus || "غير محدد"}
                              </span>
                            </div>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-blue-700">أساس تحديد الحالة</label>
                            <p className="text-sm text-blue-600 mt-1">
                              {watchedValues.contractStatus === 'غير مفعل' && 'العقد موقع لكن غير مفعل مالياً'}
                              {watchedValues.contractStatus === 'نشط' && 'العقد مفعل مالياً وساري'}
                              {watchedValues.contractStatus === 'منتهي' && 'انتهت مدة العقد'}
                              {watchedValues.contractStatus === 'مفسوخ' && 'تم فسخ العقد'}
                              {!watchedValues.contractStatus && 'يتم تحديد الحالة تلقائياً بناءً على التواريخ'}
                            </p>
                          </div>
                        </div>
                        <div className="mt-3 p-3 bg-blue-100 rounded text-sm text-blue-700">
                          <strong>ملاحظة:</strong> حالة العقد تحدد تلقائياً ولا يمكن تعديلها يدوياً. تعتمد على تاريخ التوقيع وتاريخ التفعيل المالي وتاريخ انتهاء العقد.
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Step 2: Parties and Partners */}
            {currentStep === 2 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    أطراف العقد والشركاء
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={getFormControl(form.control)}
                      name="clientId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>العميل الأساسي *</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="اختر العميل" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {clients.map((client: any) => (
                                <SelectItem key={client.id} value={client.id.toString()}>
                                  {client.clientName} - {client.clientType}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="financialGuarantorId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>الضامن المالي</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="اختر الضامن المالي (اختياري)" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="none">بدون ضامن</SelectItem>
                              {clients.map((client: any) => (
                                <SelectItem key={client.id} value={client.id.toString()}>
                                  {client.clientName} - {client.clientType}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="parentContractId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>العقد الأم (للتمديدات)</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="اختر العقد الأم (اختياري)" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="none">بدون عقد أم</SelectItem>
                              {contracts.map((contract: any) => (
                                <SelectItem key={contract.id} value={contract.id.toString()}>
                                  {contract.contractNumber} - {contract.clientName}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />

                  {/* Partners Section */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium">الشركاء والتحالفات</h3>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => appendPartner({
                          partnerId: "",
                          partnerType: "شريك",
                          partnershipPercentage: 0,
                        })}
                        className="gap-2"
                      >
                        <Plus className="h-4 w-4" />
                        إضافة شريك
                      </Button>
                    </div>

                    {partnerFields.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        لا توجد شركاء مضافين
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {partnerFields.map((field, index) => (
                          <Card key={field.id} className="p-4">
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                              <FormField
                                control={getFormControl(form.control)}
                                name={`partners.${index}.partnerId`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>الشريك</FormLabel>
                                    <Select onValueChange={field.onChange} value={field.value}>
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="اختر الشريك" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {clients.map((client: any) => (
                                          <SelectItem key={client.id} value={client.id.toString()}>
                                            {client.clientName}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={getFormControl(form.control)}
                                name={`partners.${index}.partnerType`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>نوع الشراكة</FormLabel>
                                    <Select onValueChange={field.onChange} value={field.value}>
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="نوع الشراكة" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        <SelectItem value="شريك">شريك</SelectItem>
                                        <SelectItem value="تحالف">تحالف</SelectItem>
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={getFormControl(form.control)}
                                name={`partners.${index}.partnershipPercentage`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>نسبة الشراكة (%)</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        min="0"
                                        max="100"
                                        placeholder="0"
                                        {...field}
                                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => removePartner(index)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </Card>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 3: Products and Areas */}
            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    المنتجات والمساحات
                  </CardTitle>
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-sm text-blue-700">
                      <strong>مهم:</strong> يجب إدخال بيانات المنتجات (المساحة وسعر المتر) لحساب قيمة العقد تلقائياً.
                      إذا كان العقد لا يحتوي على منتجات محددة، يمكنك استخدام المنتج الافتراضي وإدخال القيمة الإجمالية.
                    </p>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={getFormControl(form.control)}
                      name="numberOfProducts"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>عدد المنتجات *</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="1"
                              placeholder="1"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="hasUnifiedActivationDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              تفعيل موحد
                            </FormLabel>
                            <p className="text-sm text-muted-foreground">
                              جميع المنتجات لها نفس تاريخ التفعيل
                            </p>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />

                  {/* Products List */}
                  <div>
                    <h3 className="text-lg font-medium mb-4">تفاصيل المنتجات</h3>

                    {productFields.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        سيتم إنشاء المنتجات تلقائياً بناءً على العدد المحدد
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {productFields.map((field, index) => (
                          <Card key={field.id} className="p-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                              <FormField
                                control={getFormControl(form.control)}
                                name={`products.${index}.productLabel`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>تسمية المنتج</FormLabel>
                                    <FormControl>
                                      <Input placeholder="منتج 1" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={getFormControl(form.control)}
                                name={`products.${index}.area`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>المساحة (م²)</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        placeholder="0"
                                        {...field}
                                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={getFormControl(form.control)}
                                name={`products.${index}.meterPrice`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>سعر المتر</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        placeholder="0"
                                        {...field}
                                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={getFormControl(form.control)}
                                name={`products.${index}.billingType`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>نوع الفوترة</FormLabel>
                                    <Select onValueChange={field.onChange} value={field.value}>
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="نوع الفوترة" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        <SelectItem value="شهري">شهري</SelectItem>
                                        <SelectItem value="ربع سنوي">ربع سنوي</SelectItem>
                                        <SelectItem value="نصف سنوي">نصف سنوي</SelectItem>
                                        <SelectItem value="سنوي">سنوي</SelectItem>
                                        <SelectItem value="غير منتظم">غير منتظم</SelectItem>
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              {form.watch(`products.${index}.billingType`) === 'غير منتظم' && (
                                <FormField
                                  control={getFormControl(form.control)}
                                  name={`products.${index}.irregularBillingMonths`}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>الفاصل الزمني (بالأشهر)</FormLabel>
                                      <FormControl>
                                        <Input
                                          type="number"
                                          min="1"
                                          max="12"
                                          placeholder="عدد الأشهر بين الفواتير"
                                          {...field}
                                          onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                      <p className="text-xs text-gray-600">
                                        عدد الأشهر بين كل فاتورة والأخرى لهذا المنتج
                                      </p>
                                    </FormItem>
                                  )}
                                />
                              )}

                              <div className="space-y-2">
                                <FormField
                                  control={getFormControl(form.control)}
                                  name={`products.${index}.taxInfo`}
                                  render={({ field }) => (
                                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                      <FormControl>
                                        <Checkbox
                                          checked={field.value}
                                          onCheckedChange={field.onChange}
                                        />
                                      </FormControl>
                                      <FormLabel className="text-sm">
                                        خاضع للضريبة
                                      </FormLabel>
                                    </FormItem>
                                  )}
                                />

                                {form.watch(`products.${index}.taxInfo`) && (
                                  <FormField
                                    control={getFormControl(form.control)}
                                    name={`products.${index}.taxRate`}
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormControl>
                                          <Input
                                            type="number"
                                            min="0"
                                            max="100"
                                            step="0.01"
                                            placeholder="نسبة الضريبة %"
                                            {...field}
                                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                          />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />
                                )}
                              </div>
                            </div>

                            {!hasUnifiedActivationDate && (
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 pt-4 border-t">
                                <FormField
                                  control={getFormControl(form.control)}
                                  name={`products.${index}.activationDate`}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>تاريخ التفعيل</FormLabel>
                                      <FormControl>
                                        <Input type="date" {...field} />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={getFormControl(form.control)}
                                  name={`products.${index}.endDate`}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>تاريخ الانتهاء</FormLabel>
                                      <FormControl>
                                        <Input type="date" {...field} />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                            )}
                          </Card>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 4: Financial Terms */}
            {currentStep === 4 && (
              <div className="space-y-6">
                {/* Basic Financial Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5" />
                      المعلومات المالية الأساسية
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={getFormControl(form.control)}
                        name="totalContractValue"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>إجمالي قيمة العقد * (محسوب تلقائياً)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                disabled
                                className="bg-gray-100"
                              />
                            </FormControl>
                            <FormMessage />
                            <p className="text-xs text-gray-600">
                              يتم احتساب القيمة تلقائياً بناءً على المساحة وسعر المتر والزيادات السنوية والتأمين النهائي
                            </p>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={getFormControl(form.control)}
                        name="monthlyAmount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>المبلغ الشهري * (محسوب تلقائياً)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                disabled
                                className="bg-gray-100"
                              />
                            </FormControl>
                            <FormMessage />
                            <p className="text-xs text-gray-600">
                              المعادل الشهري للسنة الأولى (للمقارنة)
                            </p>
                          </FormItem>
                        )}
                      />



                    </div>
                  </CardContent>
                </Card>

                {/* Annual Increases */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Calculator className="h-5 w-5" />
                      الزيادات السنوية
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={getFormControl(form.control)}
                        name="annualIncreaseType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>نوع الزيادة السنوية</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="نوع الزيادة" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="لا يوجد">لا يوجد</SelectItem>
                                <SelectItem value="مبلغ ثابت">مبلغ ثابت</SelectItem>
                                <SelectItem value="نسبة مئوية">نسبة مئوية</SelectItem>
                                <SelectItem value="نسبة مركبة">نسبة مركبة</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {form.watch("annualIncreaseType") !== "لا يوجد" && (
                        <>
                          <FormField
                            control={getFormControl(form.control)}
                            name="annualIncreaseValue"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  {form.watch("annualIncreaseType") === "مبلغ ثابت" ? "مبلغ الزيادة" : "نسبة الزيادة (%)"}
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    placeholder="0"
                                    {...field}
                                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={getFormControl(form.control)}
                            name="annualIncreaseStartYear"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>تبدأ من السنة</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="1"
                                    placeholder="2"
                                    {...field}
                                    onChange={(e) => field.onChange(parseInt(e.target.value) || 2)}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Penalties and Fees */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5" />
                      الغرامات والرسوم
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={getFormControl(form.control)}
                        name="lateFeeType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>نوع غرامة التأخير</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="نوع الغرامة" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="مبلغ ثابت">مبلغ ثابت</SelectItem>
                                <SelectItem value="نسبة مئوية">نسبة مئوية</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={getFormControl(form.control)}
                        name="lateFeeValue"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {form.watch("lateFeeType") === "مبلغ ثابت" ? "مبلغ الغرامة" : "نسبة الغرامة (%)"}
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={getFormControl(form.control)}
                        name="gracePeriodDays"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>فترة السماح (أيام)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={getFormControl(form.control)}
                        name="bouncedCheckFeeType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>نوع غرامة الشيك المرتجع</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="نوع الغرامة" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="مبلغ ثابت">مبلغ ثابت</SelectItem>
                                <SelectItem value="نسبة مئوية">نسبة مئوية</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={getFormControl(form.control)}
                        name="bouncedCheckFeeValue"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {form.watch("bouncedCheckFeeType") === "مبلغ ثابت" ? "مبلغ الغرامة" : "نسبة الغرامة (%)"}
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={getFormControl(form.control)}
                      name="additionalFees"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>رسوم إضافية</FormLabel>
                          <FormControl>
                            <Textarea placeholder="أدخل تفاصيل أي رسوم إضافية" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>

              {/* Final Insurance */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    التأمين النهائي
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <FormField
                    control={getFormControl(form.control)}
                    name="finalInsuranceRate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>نسبة التأمين النهائي (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            step="0.01"
                            placeholder="0"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                        <p className="text-xs text-gray-600">
                          يحتسب التأمين النهائي كالتالي: (إجمالي أول 3 سنوات + قيمة آخر سنة - السنة الثالثة) × النسبة
                        </p>
                      </FormItem>
                    )}
                  />

                  {/* Display calculated final insurance */}
                  {watchedValues.products?.length > 0 && contractDurationYears > 0 && (
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-800 mb-2">احتساب التأمين النهائي</h4>
                      <div className="text-sm text-blue-700 space-y-1">
                        {(() => {
                          const products = watchedValues.products || [];
                          const hasCompleteProducts = products.every((p: any) => p.area > 0 && p.meterPrice > 0);

                          if (hasCompleteProducts) {
                            const calculation = contractCalculator.calculateTotalContractValue(
                              products,
                              contractDurationYears,
                              annualIncreaseType,
                              annualIncreaseValue,
                              annualIncreaseStartYear,
                              finalInsuranceRate
                            );

                            const firstThreeYears = calculation.yearlyValues.slice(0, 3).reduce((sum, value) => sum + value, 0);
                            const lastYearValue = calculation.yearlyValues[calculation.yearlyValues.length - 1] || 0;
                            const thirdYearValue = calculation.yearlyValues[2] || 0;
                            const finalInsuranceBase = firstThreeYears + lastYearValue - thirdYearValue;

                            return (
                              <>
                                <p>إجمالي أول 3 سنوات: {formatCurrency(firstThreeYears)}</p>
                                <p>قيمة آخر سنة: {formatCurrency(lastYearValue)}</p>
                                <p>السنة الثالثة: {formatCurrency(thirdYearValue)}</p>
                                <p>أساس التأمين: {formatCurrency(finalInsuranceBase)}</p>
                                <p className="font-medium">التأمين النهائي: {formatCurrency(calculation.finalInsurance)}</p>
                              </>
                            );
                          }
                          return <p>أدخل بيانات المنتجات لحساب التأمين النهائي</p>;
                        })()}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Advance Payment */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    الدفعة المقدمة
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={getFormControl(form.control)}
                      name="advancePaymentMonths"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>فترة الدفعة المقدمة (بالأشهر)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="24"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                          <p className="text-xs text-gray-600">
                            عدد الأشهر من قيمة الأقساط التي دفعها العميل مقدماً
                          </p>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="advancePaymentAmount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>مبلغ الدفعة المقدمة (محسوب تلقائياً)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              disabled
                              className="bg-gray-100"
                            />
                          </FormControl>
                          <FormMessage />
                          <p className="text-xs text-gray-600">
                            يحتسب تلقائياً بناءً على فترة الدفعة المقدمة وآلية السداد للمنتجات
                          </p>
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Display advance payment calculation */}
                  {watchedValues.advancePaymentMonths > 0 && watchedValues.products?.length > 0 && (
                    <div className="p-4 bg-green-50 rounded-lg">
                      <h4 className="font-medium text-green-800 mb-2">تفاصيل الدفعة المقدمة</h4>
                      <div className="text-sm text-green-700 space-y-1">
                        {(() => {
                          const products = watchedValues.products || [];
                          const advanceMonths = watchedValues.advancePaymentMonths || 0;

                          if (products.length > 0 && advanceMonths > 0) {
                            let totalAdvanceAmount = 0;
                            const productDetails = products.map((product: any) => {
                              const yearlyAmount = (product.area || 0) * (product.meterPrice || 0);
                              const monthlyAmount = yearlyAmount / 12;
                              const productAdvance = monthlyAmount * advanceMonths;
                              totalAdvanceAmount += productAdvance;

                              return {
                                label: product.productLabel || `منتج ${products.indexOf(product) + 1}`,
                                monthlyAmount,
                                productAdvance,
                                billingType: product.billingType || 'شهري'
                              };
                            });

                            return (
                              <>
                                <p className="font-medium mb-2">تفاصيل الدفعة المقدمة لكل منتج:</p>
                                {productDetails.map((detail, index) => (
                                  <div key={index} className="border-l-2 border-green-300 pl-3 mb-2">
                                    <p className="font-medium">{detail.label}</p>
                                    <p>المبلغ الشهري: {formatCurrency(detail.monthlyAmount)}</p>
                                    <p>آلية السداد: {detail.billingType}</p>
                                    <p>الدفعة المقدمة ({advanceMonths} شهر): {formatCurrency(detail.productAdvance)}</p>
                                  </div>
                                ))}
                                <p className="font-bold text-lg mt-3 pt-3 border-t border-green-300">
                                  إجمالي الدفعة المقدمة: {formatCurrency(totalAdvanceAmount)}
                                </p>
                              </>
                            );
                          }
                          return <p>أدخل بيانات المنتجات وفترة الدفعة المقدمة لحساب المبلغ</p>;
                        })()}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Check Management */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    إدارة الشيكات
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <FormField
                    control={getFormControl(form.control)}
                    name="checkStatus"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>حالة الشيكات</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="اختر حالة الشيكات" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="لم يتقدم بالشيكات">لم يتقدم بالشيكات حتى تاريخه</SelectItem>
                            <SelectItem value="معفي من الشيكات">معفي من تقديم شيكات</SelectItem>
                            <SelectItem value="سيتم إضافة الشيكات">سيتم إضافة شيكات على الاستحقاقات</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                        <p className="text-xs text-gray-600">
                          {field.value === "لم يتقدم بالشيكات" && "سيظهر تنبيه في نظام التنبيهات"}
                          {field.value === "معفي من الشيكات" && "لن يطلب من العميل تقديم شيكات"}
                          {field.value === "سيتم إضافة الشيكات" && "ستتم إضافة الشيكات تلقائياً لشاشة الشيكات"}
                        </p>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Notes */}
                <Card>
                  <CardHeader>
                    <CardTitle>الملاحظات</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <FormField
                      control={getFormControl(form.control)}
                      name="importantNotes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ملاحظات هامة</FormLabel>
                          <FormControl>
                            <Textarea placeholder="أدخل أي ملاحظات هامة حول العقد" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={getFormControl(form.control)}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ملاحظات عامة</FormLabel>
                          <FormControl>
                            <Textarea placeholder="أدخل أي ملاحظات إضافية" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Step 5: Review and Save */}
            {currentStep === 5 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    مراجعة بيانات العقد
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Basic Information Summary */}
                    <div className="space-y-4">
                      <h3 className="font-medium text-lg border-b pb-2">البيانات الأساسية</h3>
                      <div className="space-y-2 text-sm">
                        <div><strong>رقم العقد:</strong> {watchedValues.contractNumber}</div>
                        <div><strong>موضوع العقد:</strong> {watchedValues.contractSubject}</div>
                        <div><strong>نوع العقد:</strong> {watchedValues.contractType}</div>
                        <div><strong>حالة العقد:</strong> {watchedValues.contractStatus}</div>
                        <div><strong>تاريخ البدء:</strong> {watchedValues.startDate}</div>
                        <div><strong>مدة العقد:</strong> {watchedValues.contractDurationYears} سنة</div>
                        <div><strong>تاريخ الانتهاء:</strong> {watchedValues.endDate}</div>
                      </div>
                    </div>

                    {/* Financial Summary */}
                    <div className="space-y-4">
                      <h3 className="font-medium text-lg border-b pb-2">المعلومات المالية</h3>
                      <div className="space-y-2 text-sm">
                        <div><strong>إجمالي قيمة العقد:</strong> {formatCurrency(watchedValues.totalContractValue)}</div>
                        <div><strong>المبلغ الشهري:</strong> {formatCurrency(watchedValues.monthlyAmount)}</div>
                        <div><strong>نوع الزيادة السنوية:</strong> {watchedValues.annualIncreaseType}</div>
                        {watchedValues.annualIncreaseType !== "لا يوجد" && (
                          <div><strong>قيمة الزيادة:</strong> {watchedValues.annualIncreaseValue}</div>
                        )}
                      </div>
                    </div>

                    {/* Products Summary */}
                    {watchedValues.numberOfProducts > 0 && (
                      <div className="space-y-4 md:col-span-2">
                        <h3 className="font-medium text-lg border-b pb-2">المنتجات ({watchedValues.numberOfProducts})</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {watchedValues.products.map((product, index) => (
                            <div key={index} className="p-3 border rounded-lg space-y-1 text-sm">
                              <div><strong>{product.productLabel}</strong></div>
                              <div>المساحة: {product.area} م²</div>
                              <div>سعر المتر: {formatCurrency(product.meterPrice)}</div>
                              <div>نوع الفوترة: {product.billingType}</div>
                              {product.taxInfo && <div>ضريبة: {product.taxRate}%</div>}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Partners Summary */}
                    {watchedValues.partners.length > 0 && (
                      <div className="space-y-4 md:col-span-2">
                        <h3 className="font-medium text-lg border-b pb-2">الشركاء ({watchedValues.partners.length})</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {watchedValues.partners.map((partner, index) => {
                            const partnerClient = clients.find((c: any) => c.id.toString() === partner.partnerId);
                            return (
                              <div key={index} className="p-3 border rounded-lg space-y-1 text-sm">
                                <div><strong>{partnerClient?.clientName || 'غير محدد'}</strong></div>
                                <div>نوع الشراكة: {partner.partnerType}</div>
                                <div>نسبة الشراكة: {partner.partnershipPercentage}%</div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}

                    {/* Installment Schedule Preview */}
                    {watchedValues.startDate && watchedValues.contractDurationYears > 0 && watchedValues.products?.length > 0 && (
                      <div className="space-y-4 md:col-span-2">
                        <h3 className="font-medium text-lg border-b pb-2">جدول الاستحقاقات (معاينة أول 12 قسط)</h3>
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm border-collapse border border-gray-300">
                            <thead>
                              <tr className="bg-gray-50">
                                <th className="border border-gray-300 px-3 py-2 text-right">رقم القسط</th>
                                <th className="border border-gray-300 px-3 py-2 text-right">تاريخ الاستحقاق</th>
                                <th className="border border-gray-300 px-3 py-2 text-right">المبلغ</th>
                                <th className="border border-gray-300 px-3 py-2 text-right">السنة</th>
                              </tr>
                            </thead>
                            <tbody>
                              {generateInstallmentPreview().map((installment, index) => (
                                <tr key={index} className="hover:bg-gray-50">
                                  <td className="border border-gray-300 px-3 py-2">{installment.number}</td>
                                  <td className="border border-gray-300 px-3 py-2">{installment.dueDate}</td>
                                  <td className="border border-gray-300 px-3 py-2">{formatCurrency(installment.amount)}</td>
                                  <td className="border border-gray-300 px-3 py-2">السنة {installment.year}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                          {generateInstallmentPreview().length === 12 && (
                            <p className="text-xs text-gray-600 mt-2">
                              * يتم عرض أول 12 قسط فقط. سيتم إنشاء جدول الاستحقاقات الكامل عند حفظ العقد.
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Important Notes */}
                  {(watchedValues.importantNotes || watchedValues.notes) && (
                    <div className="space-y-4">
                      <h3 className="font-medium text-lg border-b pb-2">الملاحظات</h3>
                      {watchedValues.importantNotes && (
                        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <strong>ملاحظات هامة:</strong>
                          <p className="mt-1 text-sm">{watchedValues.importantNotes}</p>
                        </div>
                      )}
                      {watchedValues.notes && (
                        <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                          <strong>ملاحظات عامة:</strong>
                          <p className="mt-1 text-sm">{watchedValues.notes}</p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Complete Payment Schedule */}
            {currentStep === 4 && watchedValues.products?.length > 0 && watchedValues.startDate && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    جدول المدفوعات الكامل
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {(() => {
                    const products = watchedValues.products || [];
                    const contractSigningDate = watchedValues.contractSigningDate;
                    const financialActivationDate = watchedValues.financialActivationDate;
                    const contractDurationYears = watchedValues.contractDurationYears;
                    const annualIncreaseType = watchedValues.annualIncreaseType;
                    const annualIncreaseValue = watchedValues.annualIncreaseValue;
                    const annualIncreaseStartYear = watchedValues.annualIncreaseStartYear;
                    const advancePaymentMonths = watchedValues.advancePaymentMonths || 0;

                    // Use financial activation date if available, otherwise use signing date
                    const paymentStartDate = financialActivationDate || contractSigningDate;

                    if (!products.length || !paymentStartDate || !contractDurationYears) {
                      return <p className="text-gray-500">أدخل بيانات المنتجات وتاريخ توقيع العقد لعرض جدول المدفوعات</p>;
                    }

                    const hasCompleteProducts = products.every((p: any) => p.area > 0 && p.meterPrice > 0);
                    if (!hasCompleteProducts) {
                      return <p className="text-gray-500">أكمل بيانات المنتجات (المساحة وسعر المتر) لعرض جدول المدفوعات</p>;
                    }

                    // Calculate yearly values with increases
                    const calculation = contractCalculator.calculateTotalContractValue(
                      products,
                      contractDurationYears,
                      annualIncreaseType,
                      annualIncreaseValue,
                      annualIncreaseStartYear,
                      0 // Don't include insurance in payment schedule
                    );

                    const paymentSchedule: any[] = [];
                    let totalContractAmount = 0;
                    let advancePaymentUsed = 0;

                    for (let year = 0; year < contractDurationYears; year++) {
                      const yearValue = calculation.yearlyValues[year] || 0;
                      totalContractAmount += yearValue;

                      // Calculate payments for each product in this year
                      products.forEach((product: any, productIndex: number) => {
                        const productYearValue = (product.area * product.meterPrice) *
                          (year === 0 ? 1 :
                           annualIncreaseType === 'نسبة مئوية' ? (1 + (annualIncreaseValue / 100) * (year >= annualIncreaseStartYear - 1 ? year - annualIncreaseStartYear + 2 : 0)) :
                           annualIncreaseType === 'نسبة مركبة' ? Math.pow(1 + annualIncreaseValue / 100, year >= annualIncreaseStartYear - 1 ? year - annualIncreaseStartYear + 2 : 0) :
                           annualIncreaseType === 'مبلغ ثابت' ? (1 + (annualIncreaseValue * (year >= annualIncreaseStartYear - 1 ? year - annualIncreaseStartYear + 2 : 0)) / (product.area * product.meterPrice)) : 1);

                        // Apply tax if applicable
                        const effectiveValue = product.taxInfo ?
                          productYearValue * (1 + product.taxRate / 100) : productYearValue;

                        // Calculate payment frequency for this product
                        let monthsBetween = 1;
                        switch (product.billingType) {
                          case 'شهري': monthsBetween = 1; break;
                          case 'ربع سنوي': monthsBetween = 3; break;
                          case 'نصف سنوي': monthsBetween = 6; break;
                          case 'سنوي': monthsBetween = 12; break;
                          case 'غير منتظم': monthsBetween = product.irregularBillingMonths || 1; break;
                        }

                        const paymentsPerYear = 12 / monthsBetween;
                        const paymentAmount = effectiveValue / paymentsPerYear;

                        // Generate payments for this product in this year
                        for (let payment = 0; payment < paymentsPerYear; payment++) {
                          const paymentDate = new Date(paymentStartDate);
                          paymentDate.setFullYear(paymentDate.getFullYear() + year);
                          paymentDate.setMonth(paymentDate.getMonth() + (payment * monthsBetween));

                          const periodEndDate = new Date(paymentDate);
                          periodEndDate.setMonth(periodEndDate.getMonth() + monthsBetween);

                          // Check if this payment is covered by advance payment
                          const totalMonthsFromStart = (year * 12) + (payment * monthsBetween);
                          const isCoveredByAdvance = totalMonthsFromStart < advancePaymentMonths;

                          if (isCoveredByAdvance) {
                            advancePaymentUsed += paymentAmount;
                          }

                          paymentSchedule.push({
                            year: year + 1,
                            productLabel: product.productLabel || `منتج ${productIndex + 1}`,
                            paymentNumber: payment + 1,
                            startDate: paymentDate.toLocaleDateString('ar-EG'),
                            endDate: new Date(periodEndDate.getTime() - 24 * 60 * 60 * 1000).toLocaleDateString('ar-EG'),
                            amount: paymentAmount,
                            billingType: product.billingType,
                            isCoveredByAdvance,
                            totalMonthsFromStart
                          });
                        }
                      });
                    }

                    // Sort by date
                    paymentSchedule.sort((a, b) => a.totalMonthsFromStart - b.totalMonthsFromStart);

                    return (
                      <div className="space-y-6">
                        {/* Summary */}
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-blue-50 rounded-lg">
                          <div className="text-center">
                            <p className="text-sm text-blue-600">إجمالي قيمة العقد</p>
                            <p className="text-lg font-bold text-blue-800">{formatCurrency(totalContractAmount)}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-blue-600">عدد الدفعات</p>
                            <p className="text-lg font-bold text-blue-800">{paymentSchedule.length}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-blue-600">الدفعة المقدمة</p>
                            <p className="text-lg font-bold text-blue-800">{formatCurrency(advancePaymentUsed)}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-blue-600">المتبقي للسداد</p>
                            <p className="text-lg font-bold text-blue-800">{formatCurrency(totalContractAmount - advancePaymentUsed)}</p>
                          </div>
                        </div>

                        {/* Payment Schedule Table */}
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm border-collapse border border-gray-300">
                            <thead>
                              <tr className="bg-gray-50">
                                <th className="border border-gray-300 px-3 py-2 text-right">السنة</th>
                                <th className="border border-gray-300 px-3 py-2 text-right">المنتج</th>
                                <th className="border border-gray-300 px-3 py-2 text-right">رقم الدفعة</th>
                                <th className="border border-gray-300 px-3 py-2 text-right">تاريخ البداية</th>
                                <th className="border border-gray-300 px-3 py-2 text-right">تاريخ النهاية</th>
                                <th className="border border-gray-300 px-3 py-2 text-right">المبلغ</th>
                                <th className="border border-gray-300 px-3 py-2 text-right">نوع الفوترة</th>
                                <th className="border border-gray-300 px-3 py-2 text-right">الحالة</th>
                              </tr>
                            </thead>
                            <tbody>
                              {paymentSchedule.map((payment, index) => (
                                <tr key={index} className={`hover:bg-gray-50 ${payment.isCoveredByAdvance ? 'bg-green-50' : ''}`}>
                                  <td className="border border-gray-300 px-3 py-2">السنة {payment.year}</td>
                                  <td className="border border-gray-300 px-3 py-2">{payment.productLabel}</td>
                                  <td className="border border-gray-300 px-3 py-2">{payment.paymentNumber}</td>
                                  <td className="border border-gray-300 px-3 py-2">{payment.startDate}</td>
                                  <td className="border border-gray-300 px-3 py-2">{payment.endDate}</td>
                                  <td className="border border-gray-300 px-3 py-2 font-medium">{formatCurrency(payment.amount)}</td>
                                  <td className="border border-gray-300 px-3 py-2">{payment.billingType}</td>
                                  <td className="border border-gray-300 px-3 py-2">
                                    {payment.isCoveredByAdvance ? (
                                      <Badge variant="secondary" className="bg-green-100 text-green-800">مدفوع مقدماً</Badge>
                                    ) : (
                                      <Badge variant="outline">مستحق</Badge>
                                    )}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>

                        {/* Payment calculation notes */}
                        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                          <h4 className="font-medium text-blue-800 mb-2">ملاحظات حول حساب المدفوعات</h4>
                          <div className="text-sm text-blue-700 space-y-1">
                            <p>
                              <strong>أساس حساب المدفوعات:</strong> {' '}
                              {financialActivationDate ?
                                `تاريخ التفعيل المالي (${new Date(financialActivationDate).toLocaleDateString('ar-EG')})` :
                                `تاريخ توقيع العقد (${new Date(contractSigningDate).toLocaleDateString('ar-EG')})`
                              }
                            </p>
                            <p>
                              <strong>حالة العقد:</strong> {watchedValues.contractStatus}
                            </p>
                            {!financialActivationDate && (
                              <p className="text-amber-700 bg-amber-50 p-2 rounded">
                                ⚠️ العقد غير مفعل مالياً - يمكن إضافة تاريخ التفعيل المالي لاحقاً لتحديث جدول المدفوعات
                              </p>
                            )}
                          </div>
                        </div>

                        {advancePaymentMonths > 0 && (
                          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                            <h4 className="font-medium text-green-800 mb-2">ملاحظة حول الدفعة المقدمة</h4>
                            <p className="text-sm text-green-700">
                              تم تطبيق الدفعة المقدمة لمدة {advancePaymentMonths} شهر على أول الدفعات المستحقة.
                              المبلغ المدفوع مقدماً: {formatCurrency(advancePaymentUsed)}
                            </p>
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </CardContent>
              </Card>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1}
              >
                السابق
              </Button>
              
              <div className="flex gap-2">
                {currentStep < steps.length && (
                  <Button
                    type="button"
                    onClick={nextStep}
                  >
                    التالي
                  </Button>
                )}
                
                {currentStep === steps.length && (
                  <Button
                    type="submit"
                    disabled={createContractMutation.isPending}
                    className="gap-2"
                  >
                    <Save className="h-4 w-4" />
                    {createContractMutation.isPending ? "جاري الحفظ..." : "حفظ العقد"}
                  </Button>
                )}
              </div>
            </div>
          </form>
        </Form>
      </main>
    </div>
  );
};

export default ContractsNew;
