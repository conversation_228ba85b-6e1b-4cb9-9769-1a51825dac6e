import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useRoute } from "wouter";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Input } from "../components/ui/input";
import { Textarea } from "../components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "../components/ui/form";
import { useToast } from "../hooks/use-toast";
import { apiRequest } from "../lib/queryClient";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ader2 } from "lucide-react";
import ClientCard from "../components/ClientCard";
import labels from "../i18n";

const clientSchema = z.object({
  clientId: z.string().min(1, 'رقم العميل مطلوب'),
  clientType: z.enum(['أفراد', 'شركات'], { required_error: 'نوع العميل مطلوب' }),
  clientName: z.string().min(1, 'اسم العميل مطلوب'),
  clientAddress: z.string().optional(),
  clientPhoneWhatsapp: z.string().min(1, 'رقم الهاتف/واتساب مطلوب'),
  clientPhone2: z.string().optional(),
  clientPhone3: z.string().optional(),
  clientEmail: z.string().email('بريد إلكتروني غير صحيح').optional().or(z.literal('')),
  clientNotes: z.string().optional(),
  clientFinancialGuarantee: z.string().optional(),
  clientID_Image: z.string().optional(),
  clientFinancial_Category: z.string().optional(),
  clientLegal_Rep: z.string().optional(),
  clientPartner: z.string().optional(),
  clientReg_Number: z.string().optional(),
  clientTaxReg_Number: z.string().optional(),
  clientLegal_Status: z.string().optional(),
  clientRemarks: z.string().optional(),
});

type ClientFormData = z.infer<typeof clientSchema>;

const ClientsEdit = () => {
  const [lang] = useState<"ar" | "en">((localStorage.getItem("lang") as "ar" | "en") || "ar");
  const [showPreview, setShowPreview] = useState(false);
  const [match, params] = useRoute("/clients/edit/:id");
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const t = labels[lang];

  const clientId = params?.id;

  // Fetch client data
  const { data: client, isLoading: isLoadingClient } = useQuery({
    queryKey: ["/api/clients", clientId],
    queryFn: () => apiRequest(`/api/clients/${clientId}`),
    enabled: !!clientId,
  });

  // Fetch contracts for preview
  const { data: contracts = [] } = useQuery({
    queryKey: ["/api/contracts", clientId],
    queryFn: () => apiRequest(`/api/contracts?clientId=${clientId}`),
    enabled: !!clientId,
  });

  const form = useForm<ClientFormData>({
    resolver: zodResolver(clientSchema),
    defaultValues: {
      clientId: "",
      clientType: "أفراد",
      clientName: "",
      clientAddress: "",
      clientPhoneWhatsapp: "",
      clientPhone2: "",
      clientPhone3: "",
      clientEmail: "",
      clientNotes: "",
      clientFinancialGuarantee: "",
      clientID_Image: "",
      clientFinancial_Category: "",
      clientLegal_Rep: "",
      clientPartner: "",
      clientReg_Number: "",
      clientTaxReg_Number: "",
      clientLegal_Status: "",
      clientRemarks: "",
    },
  });

  // Update form when client data is loaded
  useEffect(() => {
    if (client) {
      form.reset({
        clientId: client.clientId || "",
        clientType: client.clientType || "أفراد",
        clientName: client.clientName || "",
        clientAddress: client.clientAddress || "",
        clientPhoneWhatsapp: client.clientPhoneWhatsapp || "",
        clientPhone2: client.clientPhone2 || "",
        clientPhone3: client.clientPhone3 || "",
        clientEmail: client.clientEmail || "",
        clientNotes: client.clientNotes || "",
        clientFinancialGuarantee: client.clientFinancialGuarantee || "",
        clientID_Image: client.clientID_Image || "",
        clientFinancial_Category: client.clientFinancial_Category || "",
        clientLegal_Rep: client.clientLegal_Rep || "",
        clientPartner: client.clientPartner || "",
        clientReg_Number: client.clientReg_Number || "",
        clientTaxReg_Number: client.clientTaxReg_Number || "",
        clientLegal_Status: client.clientLegal_Status || "",
        clientRemarks: client.clientRemarks || "",
      });
    }
  }, [client, form]);

  const updateClientMutation = useMutation({
    mutationFn: (data: ClientFormData) => apiRequest(`/api/clients/${clientId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),
    onSuccess: () => {
      toast({
        title: "نجح التحديث",
        description: "تم تحديث بيانات العميل بنجاح!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/clients"] });
      queryClient.invalidateQueries({ queryKey: ["/api/clients", clientId] });
    },
    onError: (error: any) => {
      toast({
        title: "خطأ في التحديث",
        description: `خطأ في تحديث البيانات: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: ClientFormData) => {
    updateClientMutation.mutate(data);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        form.setValue("clientID_Image", result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  if (!match) {
    return <div>صفحة غير موجودة</div>;
  }

  if (isLoadingClient) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="mt-4 text-gray-600">جاري تحميل بيانات العميل...</p>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">العميل غير موجود</p>
          <Button onClick={() => window.history.back()}>العودة</Button>
        </div>
      </div>
    );
  }

  if (showPreview) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-reverse space-x-4">
                <Button variant="ghost" size="sm" onClick={() => setShowPreview(false)}>
                  <ArrowRight className="h-4 w-4 ml-2" />
                  {lang === "ar" ? "العودة للتعديل" : "Back to Edit"}
                </Button>
                <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  {lang === "ar" ? "معاينة كارت العميل" : "Client Card Preview"}
                </h1>
              </div>
            </div>
          </div>
        </header>

        <main className="py-8">
          <ClientCard
            client={form.getValues()}
            contracts={contracts}
            showPrintButton={true}
            showEditButton={false}
            onPrint={handlePrint}
          />
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-reverse space-x-4">
              <Button variant="ghost" size="sm" onClick={() => window.history.back()}>
                <ArrowRight className="h-4 w-4 ml-2" />
                {lang === "ar" ? "العودة للعملاء" : "Back to Clients"}
              </Button>
              <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {lang === "ar" ? "تعديل بيانات العميل" : "Edit Client"}
              </h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>{lang === "ar" ? "البيانات الأساسية" : "Basic Information"}</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="clientId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>رقم العميل *</FormLabel>
                      <FormControl>
                        <Input placeholder="أدخل رقم العميل" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>نوع العميل *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر نوع العميل" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="أفراد">أفراد</SelectItem>
                          <SelectItem value="شركات">شركات</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientName"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>اسم العميل *</FormLabel>
                      <FormControl>
                        <Input placeholder="أدخل اسم العميل" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientAddress"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>العنوان</FormLabel>
                      <FormControl>
                        <Textarea placeholder="أدخل عنوان العميل" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientPhoneWhatsapp"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>رقم الهاتف/واتساب *</FormLabel>
                      <FormControl>
                        <Input placeholder="أدخل رقم الهاتف" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientPhone2"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>رقم هاتف إضافي</FormLabel>
                      <FormControl>
                        <Input placeholder="رقم هاتف إضافي" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientPhone3"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>رقم هاتف ثالث</FormLabel>
                      <FormControl>
                        <Input placeholder="رقم هاتف ثالث" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>البريد الإلكتروني</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="أدخل البريد الإلكتروني" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientNotes"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>ملاحظات</FormLabel>
                      <FormControl>
                        <Textarea placeholder="أدخل أي ملاحظات" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Financial & Legal Information */}
            <Card>
              <CardHeader>
                <CardTitle>المعلومات المالية والقانونية</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="clientFinancial_Category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>التصنيف المالي</FormLabel>
                      <FormControl>
                        <Input placeholder="مثال: فئة أ، فئة ب" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientFinancialGuarantee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>الضامن المالي</FormLabel>
                      <FormControl>
                        <Input placeholder="اسم الضامن المالي" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientLegal_Rep"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>الممثل القانوني</FormLabel>
                      <FormControl>
                        <Input placeholder="اسم الممثل القانوني" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientPartner"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>الشريك المرتبط</FormLabel>
                      <FormControl>
                        <Input placeholder="اسم الشريك" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientReg_Number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>رقم التسجيل</FormLabel>
                      <FormControl>
                        <Input placeholder="رقم التسجيل التجاري" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientTaxReg_Number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>رقم التسجيل الضريبي</FormLabel>
                      <FormControl>
                        <Input placeholder="رقم التسجيل الضريبي" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientLegal_Status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>الحالة القانونية</FormLabel>
                      <FormControl>
                        <Input placeholder="مثال: مؤسسة فردية، شركة محدودة" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientID_Image"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>صورة الهوية/السجل التجاري</FormLabel>
                      <FormControl>
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                        />
                      </FormControl>
                      <FormMessage />
                      {field.value && (
                        <div className="mt-2">
                          <img
                            src={field.value}
                            alt="معاينة الصورة"
                            className="w-20 h-20 object-cover rounded border"
                          />
                        </div>
                      )}
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientRemarks"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>ملاحظات إضافية</FormLabel>
                      <FormControl>
                        <Textarea placeholder="أي ملاحظات إضافية" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex gap-4 justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowPreview(true)}
                className="gap-2"
              >
                <Eye className="h-4 w-4" />
                معاينة كارت العميل
              </Button>
              
              <Button
                type="submit"
                disabled={updateClientMutation.isPending}
                className="gap-2"
              >
                {updateClientMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                حفظ التعديلات
              </Button>
            </div>
          </form>
        </Form>
      </main>
    </div>
  );
};

export default ClientsEdit;
