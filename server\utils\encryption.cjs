// ===== ENCRYPTION UTILITIES =====
// Author: Augment Code
// Description: Utility functions for encryption, hashing, and security

const crypto = require('crypto');

// Default encryption settings
const ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32;
const IV_LENGTH = 16;
const TAG_LENGTH = 16;

/**
 * Generate a random encryption key
 * @returns {string} Base64 encoded encryption key
 */
const generateKey = () => {
  return crypto.randomBytes(KEY_LENGTH).toString('base64');
};

/**
 * Generate a random salt
 * @param {number} length - Salt length in bytes
 * @returns {string} Base64 encoded salt
 */
const generateSalt = (length = 16) => {
  return crypto.randomBytes(length).toString('base64');
};

/**
 * Hash password with salt
 * @param {string} password - Password to hash
 * @param {string} salt - Salt for hashing (optional, will generate if not provided)
 * @returns {Object} Object containing hash and salt
 */
const hashPassword = (password, salt = null) => {
  if (!password) {
    throw new Error('Password is required');
  }

  const saltToUse = salt || generateSalt();
  const hash = crypto.pbkdf2Sync(password, saltToUse, 10000, 64, 'sha512').toString('base64');
  
  return {
    hash,
    salt: saltToUse
  };
};

/**
 * Verify password against hash
 * @param {string} password - Password to verify
 * @param {string} hash - Stored hash
 * @param {string} salt - Stored salt
 * @returns {boolean} True if password matches
 */
const verifyPassword = (password, hash, salt) => {
  if (!password || !hash || !salt) {
    return false;
  }

  try {
    const { hash: computedHash } = hashPassword(password, salt);
    return computedHash === hash;
  } catch (error) {
    return false;
  }
};

/**
 * Encrypt sensitive data
 * @param {string} text - Text to encrypt
 * @param {string} key - Encryption key (base64)
 * @returns {string} Encrypted data (base64)
 */
const encrypt = (text, key) => {
  if (!text || !key) {
    throw new Error('Text and key are required');
  }

  try {
    const keyBuffer = Buffer.from(key, 'base64');
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipher(ALGORITHM, keyBuffer, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    
    const tag = cipher.getAuthTag();
    
    // Combine IV, tag, and encrypted data
    const combined = Buffer.concat([iv, tag, Buffer.from(encrypted, 'base64')]);
    
    return combined.toString('base64');
  } catch (error) {
    throw new Error('Encryption failed: ' + error.message);
  }
};

/**
 * Decrypt sensitive data
 * @param {string} encryptedData - Encrypted data (base64)
 * @param {string} key - Encryption key (base64)
 * @returns {string} Decrypted text
 */
const decrypt = (encryptedData, key) => {
  if (!encryptedData || !key) {
    throw new Error('Encrypted data and key are required');
  }

  try {
    const keyBuffer = Buffer.from(key, 'base64');
    const combined = Buffer.from(encryptedData, 'base64');
    
    // Extract IV, tag, and encrypted data
    const iv = combined.slice(0, IV_LENGTH);
    const tag = combined.slice(IV_LENGTH, IV_LENGTH + TAG_LENGTH);
    const encrypted = combined.slice(IV_LENGTH + TAG_LENGTH);
    
    const decipher = crypto.createDecipher(ALGORITHM, keyBuffer, iv);
    decipher.setAuthTag(tag);
    
    let decrypted = decipher.update(encrypted, null, 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    throw new Error('Decryption failed: ' + error.message);
  }
};

/**
 * Generate secure random token
 * @param {number} length - Token length in bytes
 * @returns {string} Random token (hex)
 */
const generateToken = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * Generate UUID v4
 * @returns {string} UUID string
 */
const generateUUID = () => {
  return crypto.randomUUID();
};

/**
 * Hash data with SHA-256
 * @param {string} data - Data to hash
 * @returns {string} SHA-256 hash (hex)
 */
const sha256Hash = (data) => {
  if (!data) return '';
  
  return crypto.createHash('sha256').update(data).digest('hex');
};

/**
 * Generate HMAC signature
 * @param {string} data - Data to sign
 * @param {string} secret - Secret key
 * @returns {string} HMAC signature (hex)
 */
const generateHMAC = (data, secret) => {
  if (!data || !secret) {
    throw new Error('Data and secret are required');
  }
  
  return crypto.createHmac('sha256', secret).update(data).digest('hex');
};

/**
 * Verify HMAC signature
 * @param {string} data - Original data
 * @param {string} signature - HMAC signature to verify
 * @param {string} secret - Secret key
 * @returns {boolean} True if signature is valid
 */
const verifyHMAC = (data, signature, secret) => {
  if (!data || !signature || !secret) {
    return false;
  }
  
  try {
    const expectedSignature = generateHMAC(data, secret);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    return false;
  }
};

/**
 * Mask sensitive data for logging
 * @param {string} data - Data to mask
 * @param {number} visibleChars - Number of characters to show at start and end
 * @returns {string} Masked data
 */
const maskSensitiveData = (data, visibleChars = 2) => {
  if (!data || data.length <= visibleChars * 2) {
    return '*'.repeat(data?.length || 0);
  }
  
  const start = data.substring(0, visibleChars);
  const end = data.substring(data.length - visibleChars);
  const middle = '*'.repeat(data.length - visibleChars * 2);
  
  return start + middle + end;
};

/**
 * Generate secure session ID
 * @returns {string} Session ID
 */
const generateSessionId = () => {
  const timestamp = Date.now().toString(36);
  const randomPart = generateToken(16);
  return `${timestamp}-${randomPart}`;
};

module.exports = {
  generateKey,
  generateSalt,
  hashPassword,
  verifyPassword,
  encrypt,
  decrypt,
  generateToken,
  generateUUID,
  sha256Hash,
  generateHMAC,
  verifyHMAC,
  maskSensitiveData,
  generateSessionId
};
