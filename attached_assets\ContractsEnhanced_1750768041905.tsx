import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON> } from "wouter";
import { Button } from "../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Input } from "../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select";
import { Badge } from "../components/ui/badge";
import { apiRequest } from "../lib/queryClient";
import { cn } from "../lib/utils";
import {
  FileText,
  Plus,
  Search,
  Calendar,
  DollarSign,
  User,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  Star,
  Flag,
  Eye,
  Edit,
  Filter
} from "lucide-react";
import labels from "../i18n";

interface Contract {
  id: number;
  contractNumber: string;
  contractInternalId: string;
  contractDescription: string;
  contractSubject: string;
  clientId: number;
  clientName: string;
  clientType: string;
  contractType: string;
  contractStatus: string;
  contractDate: string;
  startDate: string;
  endDate: string;
  contractDurationYears: number;
  totalContractValue: number;
  monthlyAmount: number;
  paymentFrequency: string;
  numberOfProducts: number;
  hasUnifiedActivationDate: boolean;
  consecutiveMissedPayments: number;
  totalMissedPayments: number;
  autoTerminationSuggested: boolean;
  systemFlags: string;
  createdAt: string;
  updatedAt: string;
}

const ContractsEnhanced = () => {
  const [lang] = useState<"ar" | "en">((localStorage.getItem("lang") as "ar" | "en") || "ar");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [showFilters, setShowFilters] = useState(false);
  const t = labels[lang];

  // Fetch contracts
  const { data: contracts = [], isLoading, error } = useQuery({
    queryKey: ["/api/contracts"],
    queryFn: () => apiRequest("/api/contracts"),
  });

  // Filter contracts
  const filteredContracts = contracts.filter((contract: Contract) => {
    const matchesSearch = 
      contract.contractNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contract.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contract.contractDescription?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contract.contractSubject?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || contract.contractStatus === statusFilter;
    const matchesType = typeFilter === "all" || contract.contractType === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'نشط':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'منتهي':
        return <Clock className="h-4 w-4 text-gray-600" />;
      case 'معلق':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'ملغي':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'نشط':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'منتهي':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'معلق':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'ملغي':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getContractFlags = (contract: Contract) => {
    const flags = [];
    
    if (contract.consecutiveMissedPayments > 0) {
      flags.push(
        <Badge key="late" variant="destructive" className="gap-1">
          <Flag className="h-3 w-3" />
          متأخر ({contract.consecutiveMissedPayments})
        </Badge>
      );
    }
    
    if (contract.autoTerminationSuggested) {
      flags.push(
        <Badge key="termination" variant="destructive" className="gap-1">
          <AlertTriangle className="h-3 w-3" />
          اقتراح فسخ
        </Badge>
      );
    }
    
    if (contract.totalMissedPayments === 0 && contract.contractStatus === 'نشط') {
      flags.push(
        <Badge key="good" variant="secondary" className="gap-1 bg-yellow-100 text-yellow-800">
          <Star className="h-3 w-3" />
          عميل مميز
        </Badge>
      );
    }
    
    return flags;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="mt-4 text-gray-600">جاري تحميل العقود...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">خطأ في تحميل البيانات:</p>
          <p className="text-gray-600 mb-4">{String(error)}</p>
          <Button onClick={() => window.location.reload()}>إعادة المحاولة</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-reverse space-x-4">
              <FileText className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {lang === "ar" ? "إدارة العقود" : "Contracts Management"}
              </h1>
            </div>
            <Link href="/contracts/new">
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                {lang === "ar" ? "إضافة عقد جديد" : "Add New Contract"}
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                البحث والفلترة
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="gap-2"
              >
                <Filter className="h-4 w-4" />
                {showFilters ? "إخفاء الفلاتر" : "إظهار الفلاتر"}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Input
                  placeholder="البحث برقم العقد، اسم العميل، أو الوصف..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              
              {showFilters && (
                <>
                  <div>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="فلترة بالحالة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">جميع الحالات</SelectItem>
                        <SelectItem value="نشط">نشط</SelectItem>
                        <SelectItem value="منتهي">منتهي</SelectItem>
                        <SelectItem value="معلق">معلق</SelectItem>
                        <SelectItem value="ملغي">ملغي</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Select value={typeFilter} onValueChange={setTypeFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="فلترة بالنوع" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">جميع الأنواع</SelectItem>
                        <SelectItem value="إيجار">إيجار</SelectItem>
                        <SelectItem value="تمديد مساحة">تمديد مساحة</SelectItem>
                        <SelectItem value="اتفاق مشترك">اتفاق مشترك</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">إجمالي العقود</p>
                  <p className="text-2xl font-bold text-gray-900">{contracts.length}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">العقود النشطة</p>
                  <p className="text-2xl font-bold text-green-600">
                    {contracts.filter((c: Contract) => c.contractStatus === 'نشط').length}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">العقود المتأخرة</p>
                  <p className="text-2xl font-bold text-red-600">
                    {contracts.filter((c: Contract) => c.consecutiveMissedPayments > 0).length}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600">إجمالي القيمة</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {formatCurrency(contracts.reduce((sum: number, c: Contract) => sum + c.totalContractValue, 0))}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contracts Grid */}
        {filteredContracts.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عقود</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || statusFilter !== "all" || typeFilter !== "all"
                  ? "لا توجد عقود تطابق معايير البحث"
                  : "لم يتم إنشاء أي عقود بعد"}
              </p>
              <Link href="/contracts/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة عقد جديد
                </Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredContracts.map((contract: Contract) => (
              <Card key={contract.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold text-lg">{contract.contractNumber}</h3>
                        <Badge className={cn("text-xs", getStatusColor(contract.contractStatus))}>
                          {getStatusIcon(contract.contractStatus)}
                          <span className="mr-1">{contract.contractStatus}</span>
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-1">{contract.contractSubject || contract.contractDescription}</p>
                      <p className="text-sm font-medium text-blue-600">{contract.contractType}</p>
                    </div>
                  </div>
                  
                  {/* Contract Flags */}
                  <div className="flex flex-wrap gap-1 mt-2">
                    {getContractFlags(contract)}
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">{contract.clientName}</span>
                      <Badge variant="outline" className="text-xs">{contract.clientType}</Badge>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span>{formatDate(contract.startDate)} - {formatDate(contract.endDate)}</span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm">
                      <DollarSign className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">{formatCurrency(contract.totalContractValue)}</span>
                      <span className="text-gray-500">({formatCurrency(contract.monthlyAmount)}/{contract.paymentFrequency})</span>
                    </div>
                    
                    {contract.numberOfProducts > 1 && (
                      <div className="flex items-center gap-2 text-sm">
                        <FileText className="h-4 w-4 text-gray-500" />
                        <span>{contract.numberOfProducts} منتجات</span>
                        {contract.hasUnifiedActivationDate && (
                          <Badge variant="outline" className="text-xs">تفعيل موحد</Badge>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex gap-2 pt-4 mt-4 border-t">
                    <Link href={`/contracts/${contract.id}`} className="flex-1">
                      <Button variant="outline" size="sm" className="w-full gap-2">
                        <Eye className="h-4 w-4" />
                        عرض التفاصيل
                      </Button>
                    </Link>
                    <Link href={`/contracts/${contract.id}/edit`}>
                      <Button variant="outline" size="sm" className="gap-2">
                        <Edit className="h-4 w-4" />
                        تعديل
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </main>
    </div>
  );
};

export default ContractsEnhanced;
