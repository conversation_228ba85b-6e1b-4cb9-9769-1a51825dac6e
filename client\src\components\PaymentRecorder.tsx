import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "./ui/form";
import { Badge } from "./ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useDateFormat } from "@/hooks/use-date-format";
import { useCurrency } from "@/hooks/use-currency";
import CustomDatePicker from "@/components/ui/custom-date-picker";
import {
  CreditCard,
  Save,
  X,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Calculator,
  Calendar,
  DollarSign,
  CheckCircle
} from "lucide-react";

interface Installment {
  installmentId: number;
  installmentNumber: number;
  installmentDate: string;
  paymentDueDate: string;
  installmentAmount: number;
  paidAmount: number;
  remainingAmount: number;
  isPaid: boolean;
  lateDays: number;
  penaltyAmount: number;
}

interface PaymentRecorderProps {
  installment: Installment;
  contractId: number;
  isOpen: boolean;
  onClose: () => void;
  onPaymentRecorded?: (payment: any) => void;
}

const paymentSchema = z.object({
  paidAmount: z.number().min(0, 'المبلغ يجب أن يكون أكبر من صفر'),
  paymentDate: z.string().min(1, 'تاريخ الدفع مطلوب'),
  paymentMethod: z.string().min(1, 'طريقة الدفع مطلوبة'),
  checkNumber: z.string().optional(),
  bankName: z.string().optional(),
  notes: z.string().optional(),
  includePenalty: z.boolean().default(false),
});

type PaymentFormData = z.infer<typeof paymentSchema>;

const PaymentRecorder: React.FC<PaymentRecorderProps> = ({
  installment,
  contractId,
  isOpen,
  onClose,
  onPaymentRecorded
}) => {
  const [showPenaltyCalculation, setShowPenaltyCalculation] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { formatDate } = useDateFormat();

  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      paidAmount: installment.remainingAmount,
      paymentDate: new Date().toISOString().split('T')[0],
      paymentMethod: 'تحويل بنكي',
      checkNumber: '',
      bankName: '',
      notes: '',
      includePenalty: false,
    },
  });

  const onSubmit = async (data: PaymentFormData) => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const paymentData = {
        ...data,
        installmentId: installment.installmentId,
        contractId,
        lateDays: calculateLateDays(data.paymentDate),
        penaltyAmount: data.includePenalty ? calculatePenalty(data.paymentDate) : 0,
      };

      toast({
        title: "تم تسجيل الدفعة",
        description: "تم تسجيل الدفعة بنجاح!",
        variant: "success"
      });

      onPaymentRecorded?.(paymentData);
      onClose();
    } catch (error) {
      toast({
        title: "خطأ في تسجيل الدفعة",
        description: "حدث خطأ أثناء تسجيل الدفعة",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // تم استبدال formatDate بـ useDateFormat hook

  const calculateLateDays = (paymentDate: string) => {
    const dueDate = new Date(installment.paymentDueDate);
    const payDate = new Date(paymentDate);
    return Math.max(0, Math.ceil((payDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)));
  };

  const calculatePenalty = (paymentDate: string) => {
    const lateDays = calculateLateDays(paymentDate);
    return calculateLatePenalty(installment.remainingAmount, lateDays);
  };

  const watchedPaymentDate = form.watch('paymentDate');
  const watchedIncludePenalty = form.watch('includePenalty');
  const lateDays = watchedPaymentDate ? calculateLateDays(watchedPaymentDate) : 0;
  const penaltyAmount = lateDays > 0 ? calculatePenalty(watchedPaymentDate) : 0;
  const totalAmount = form.watch('paidAmount') + (watchedIncludePenalty ? penaltyAmount : 0);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              تسجيل دفعة - القسط رقم {installment.installmentNumber}
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Installment Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              تفاصيل القسط
            </h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">رقم القسط:</span>
                <p className="font-medium">{installment.installmentNumber}</p>
              </div>
              <div>
                <span className="text-gray-600">تاريخ الاستحقاق:</span>
                <p className="font-medium">{formatDate(installment.paymentDueDate)}</p>
              </div>
              <div>
                <span className="text-gray-600">مبلغ القسط:</span>
                <p className="font-medium">{formatCurrency(installment.installmentAmount)}</p>
              </div>
              <div>
                <span className="text-gray-600">المبلغ المتبقي:</span>
                <p className="font-medium text-orange-600">{formatCurrency(installment.remainingAmount)}</p>
              </div>
              {installment.paidAmount > 0 && (
                <div>
                  <span className="text-gray-600">المبلغ المدفوع:</span>
                  <p className="font-medium text-green-600">{formatCurrency(installment.paidAmount)}</p>
                </div>
              )}
            </div>
          </div>

          {/* Late Payment Warning */}
          {lateDays > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <span className="font-medium text-yellow-800">تحذير: دفعة متأخرة</span>
              </div>
              <p className="text-sm text-yellow-700">
                هذه الدفعة متأخرة بـ {lateDays} يوم. قد تطبق غرامات تأخير.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => setShowPenaltyCalculation(!showPenaltyCalculation)}
              >
                <Calculator className="h-4 w-4 mr-2" />
                {showPenaltyCalculation ? 'إخفاء' : 'عرض'} حساب الغرامة
              </Button>
            </div>
          )}

          {/* Penalty Calculation */}
          {showPenaltyCalculation && lateDays > 0 && (
            <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
              <h4 className="font-medium text-red-800 mb-2">حساب غرامة التأخير</h4>
              <div className="text-sm text-red-700 space-y-1">
                <p>أيام التأخير: {lateDays} يوم</p>
                <p>مبلغ القسط: {formatCurrency(installment.remainingAmount)}</p>
                <p className="font-medium">الغرامة المحتسبة: {formatCurrency(penaltyAmount)}</p>
              </div>
            </div>
          )}

          {/* Payment Form */}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="paidAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>المبلغ المدفوع</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="paymentDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>تاريخ الدفع</FormLabel>
                      <FormControl>
                        <CustomDatePicker
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="paymentMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>طريقة الدفع</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر طريقة الدفع" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="نقدي">نقدي</SelectItem>
                        <SelectItem value="تحويل بنكي">تحويل بنكي</SelectItem>
                        <SelectItem value="شيك">شيك</SelectItem>
                        <SelectItem value="بطاقة ائتمان">بطاقة ائتمان</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {form.watch('paymentMethod') === 'شيك' && (
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="checkNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>رقم الشيك</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="bankName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>اسم البنك</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {lateDays > 0 && (
                <FormField
                  control={form.control}
                  name="includePenalty"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <input
                          type="checkbox"
                          checked={field.value}
                          onChange={field.onChange}
                          className="h-4 w-4"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          تضمين غرامة التأخير ({formatCurrency(penaltyAmount)})
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ملاحظات</FormLabel>
                    <FormControl>
                      <Textarea {...field} rows={3} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Payment Summary */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2 flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  ملخص الدفعة
                </h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <div className="flex justify-between">
                    <span>المبلغ الأساسي:</span>
                    <span>{formatCurrency(form.watch('paidAmount') || 0)}</span>
                  </div>
                  {watchedIncludePenalty && penaltyAmount > 0 && (
                    <div className="flex justify-between">
                      <span>غرامة التأخير:</span>
                      <span>{formatCurrency(penaltyAmount)}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-medium border-t pt-1">
                    <span>إجمالي المبلغ:</span>
                    <span>{formatCurrency(totalAmount)}</span>
                  </div>
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <Button type="submit" disabled={isSubmitting} className="flex-1">
                  {isSubmitting ? (
                    <>جاري التسجيل...</>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      تسجيل الدفعة
                    </>
                  )}
                </Button>
                <Button type="button" variant="outline" onClick={onClose}>
                  إلغاء
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentRecorder;
