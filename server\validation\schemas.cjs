// ===== VALIDATION SCHEMAS =====
// Author: Augment Code
// Description: Zod validation schemas for all API endpoints

const { z } = require('zod');

// ===== COMMON SCHEMAS =====

const phoneSchema = z.string()
  .regex(/^[\+]?[0-9\-\(\)\s]+$/, 'رقم هاتف غير صحيح')
  .min(8, 'رقم الهاتف قصير جداً')
  .max(20, 'رقم الهاتف طويل جداً');

const emailSchema = z.string()
  .email('بريد إلكتروني غير صحيح')
  .optional()
  .or(z.literal(''));

const dateSchema = z.string()
  .regex(/^\d{4}-\d{2}-\d{2}$/, 'تاريخ غير صحيح (YYYY-MM-DD)')
  .refine(date => !isNaN(Date.parse(date)), 'تاريخ غير صحيح');

const currencySchema = z.number()
  .positive('المبلغ يجب أن يكون موجباً')
  .max(*********.99, 'المبلغ كبير جداً');

// ===== CLIENT SCHEMAS =====

const createClientSchema = z.object({
  clientId: z.string()
    .min(1, 'رقم العميل مطلوب')
    .max(50, 'رقم العميل طويل جداً'),
  
  clientType: z.string()
    .min(1, 'نوع العميل مطلوب')
    .refine((val) => ['individual', 'company', 'أفراد', 'شركات'].includes(val), {
      message: 'نوع العميل يجب أن يكون فرد أو شركة'
    }),
  
  clientName: z.string()
    .min(2, 'اسم العميل قصير جداً')
    .max(200, 'اسم العميل طويل جداً'),
  
  // Database fields (actual schema)
  address: z.string()
    .max(500, 'العنوان طويل جداً')
    .optional()
    .or(z.literal('')),

  phone: phoneSchema.optional().or(z.literal('')),
  email: emailSchema,

  // Frontend compatibility fields
  clientAddress: z.string()
    .max(500, 'العنوان طويل جداً')
    .optional()
    .or(z.literal('')),

  clientPhoneWhatsapp: phoneSchema,
  clientPhone2: phoneSchema.optional().or(z.literal('')),
  clientPhone3: phoneSchema.optional().or(z.literal('')),
  clientEmail: emailSchema,
  
  clientNotes: z.string()
    .max(1000, 'الملاحظات طويلة جداً')
    .optional()
    .or(z.literal('')),
  
  clientFinancialGuarantee: z.string()
    .max(200, 'الضمان المالي طويل جداً')
    .optional()
    .or(z.literal('')),
  
  clientFinancial_Category: z.string()
    .max(100, 'الفئة المالية طويلة جداً')
    .optional()
    .or(z.literal('')),

  clientLegal_Rep: z.string()
    .max(200, 'الممثل القانوني طويل جداً')
    .optional()
    .or(z.literal('')),

  clientPartner: z.string()
    .max(200, 'الشريك طويل جداً')
    .optional()
    .or(z.literal('')),

  clientReg_Number: z.string()
    .max(50, 'رقم التسجيل طويل جداً')
    .optional()
    .or(z.literal('')),

  clientTaxReg_Number: z.string()
    .max(50, 'رقم التسجيل الضريبي طويل جداً')
    .optional()
    .or(z.literal('')),

  clientLegal_Status: z.string()
    .max(100, 'الوضع القانوني طويل جداً')
    .optional()
    .or(z.literal('')),

  clientRemarks: z.string()
    .max(1000, 'الملاحظات طويلة جداً')
    .optional()
    .or(z.literal('')),

  clientID_Image: z.string()
    .optional()
    .or(z.literal('')),

  clientDocuments: z.string()
    .optional()
    .or(z.literal('')),
  
  isActive: z.boolean().optional().default(true)
});

const updateClientSchema = createClientSchema.partial();

// ===== CONTRACT SCHEMAS =====

const createContractSchema = z.object({
  contractNumber: z.string()
    .min(1, 'رقم العقد مطلوب')
    .max(50, 'رقم العقد طويل جداً'),
  
  contractInternalId: z.string()
    .max(50, 'الرقم الداخلي طويل جداً')
    .optional(),
  
  contractDescription: z.string()
    .max(500, 'وصف العقد طويل جداً')
    .optional(),
  
  contractSubject: z.string()
    .max(200, 'موضوع العقد طويل جداً')
    .optional(),
  
  clientId: z.number()
    .int('رقم العميل يجب أن يكون رقماً صحيحاً')
    .positive('رقم العميل غير صحيح'),
  
  contractType: z.string()
    .min(1, 'نوع العقد مطلوب')
    .max(100, 'نوع العقد طويل جداً'),
  
  contractStatus: z.string()
    .max(50, 'حالة العقد طويلة جداً')
    .optional()
    .default('نشط'),
  
  contractDate: dateSchema.optional(),
  startDate: dateSchema,
  actualStartDate: dateSchema.optional(),
  endDate: dateSchema.optional(),
  actualEndDate: dateSchema.optional(),
  
  contractDurationYears: z.number()
    .int('مدة العقد يجب أن تكون رقماً صحيحاً')
    .positive('مدة العقد يجب أن تكون موجبة')
    .max(100, 'مدة العقد طويلة جداً'),
  
  assetOwner: z.string()
    .max(200, 'مالك الأصل طويل جداً')
    .optional(),
  
  responsibleDepartment: z.string()
    .max(200, 'القسم المسؤول طويل جداً')
    .optional(),
  
  region: z.string()
    .max(100, 'المنطقة طويلة جداً')
    .optional(),
  
  financialGuarantorId: z.number()
    .int()
    .positive()
    .optional(),
  
  parentContractId: z.number()
    .int()
    .positive()
    .optional(),
  
  numberOfProducts: z.number()
    .int()
    .positive()
    .optional()
    .default(1),
  
  hasUnifiedActivationDate: z.boolean()
    .optional()
    .default(true),
  
  totalContractValue: currencySchema,
  monthlyAmount: currencySchema,
  
  paymentDay: z.number()
    .int('يوم الدفع يجب أن يكون رقماً صحيحاً')
    .min(1, 'يوم الدفع يجب أن يكون بين 1 و 31')
    .max(31, 'يوم الدفع يجب أن يكون بين 1 و 31'),
  
  isActive: z.boolean().optional().default(true)
});

const updateContractSchema = createContractSchema.partial();

// ===== PAYMENT SCHEMAS =====

const createPaymentSchema = z.object({
  contractId: z.number()
    .int('رقم العقد يجب أن يكون رقماً صحيحاً')
    .positive('رقم العقد غير صحيح'),
  
  paymentType: z.enum(['installment', 'penalty', 'adjustment'], {
    errorMap: () => ({ message: 'نوع الدفع غير صحيح' })
  }),
  
  amount: currencySchema,
  
  dueDate: dateSchema,
  paidDate: dateSchema.optional(),
  
  status: z.enum(['pending', 'paid', 'overdue', 'cancelled'], {
    errorMap: () => ({ message: 'حالة الدفع غير صحيحة' })
  }).optional().default('pending'),
  
  paymentMethod: z.string()
    .max(50, 'طريقة الدفع طويلة جداً')
    .optional(),
  
  notes: z.string()
    .max(500, 'الملاحظات طويلة جداً')
    .optional(),
  
  installmentNumber: z.number()
    .int()
    .positive()
    .optional()
});

const updatePaymentSchema = createPaymentSchema.partial();

// ===== REFERENCE DATA SCHEMAS =====

const createReferenceDataSchema = z.object({
  category: z.string()
    .min(1, 'الفئة مطلوبة')
    .max(100, 'الفئة طويلة جداً'),
  
  value: z.string()
    .min(1, 'القيمة مطلوبة')
    .max(200, 'القيمة طويلة جداً'),
  
  label: z.string()
    .min(1, 'التسمية مطلوبة')
    .max(200, 'التسمية طويلة جداً'),
  
  description: z.string()
    .max(500, 'الوصف طويل جداً')
    .optional(),
  
  sortOrder: z.number()
    .int()
    .optional()
    .default(0),
  
  isActive: z.boolean()
    .optional()
    .default(true)
});

const updateReferenceDataSchema = createReferenceDataSchema.partial();

module.exports = {
  // Client schemas
  createClientSchema,
  updateClientSchema,
  
  // Contract schemas
  createContractSchema,
  updateContractSchema,
  
  // Payment schemas
  createPaymentSchema,
  updatePaymentSchema,
  
  // Reference data schemas
  createReferenceDataSchema,
  updateReferenceDataSchema,
  
  // Common schemas
  phoneSchema,
  emailSchema,
  dateSchema,
  currencySchema
};
