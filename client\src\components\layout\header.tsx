import { Bell, Globe, Moon, Sun, Menu } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useTheme } from "@/components/theme-provider";
import { useLanguage } from "@/hooks/use-language";
import { cn } from "@/lib/utils";
import labels from "@/lib/i18n";

interface HeaderProps {
  title: string;
  subtitle?: string;
}

export function Header({ title, subtitle }: HeaderProps) {
  const { toggleTheme, theme } = useTheme();
  const { language, toggleLanguage, isRTL } = useLanguage();
  const t = labels[language];

  return (
    <header className="bg-card dark:bg-card shadow-sm border-b border-border dark:border-border px-4 py-3 relative z-40 sticky top-0">
      <div className="flex items-center justify-between">
        <div className={cn(
          "flex items-center gap-4",
          isRTL ? "flex-row-reverse" : "flex-row"
        )}>
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden p-2"
          >
            <Menu className="h-5 w-5" />
          </Button>
          <div className={cn(isRTL ? "text-right" : "text-left")}>
            <h2 className="text-lg font-semibold text-card-foreground dark:text-card-foreground">
              {title}
            </h2>
            {subtitle && (
              <p className="text-xs text-muted-foreground dark:text-muted-foreground">
                {subtitle}
              </p>
            )}
          </div>
        </div>

        <div className={cn(
          "flex items-center gap-4",
          isRTL ? "flex-row-reverse" : "flex-row"
        )}>
          {/* Language Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleLanguage}
            className="flex items-center gap-2"
          >
            <Globe className="h-4 w-4" />
            <span className="text-xs">{language === 'ar' ? 'EN' : 'ع'}</span>
          </Button>

          {/* Dark Mode Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
          >
            {theme === 'dark' ? (
              <Sun className="h-4 w-4" />
            ) : (
              <Moon className="h-4 w-4" />
            )}
          </Button>

          {/* Notifications */}
          <Button
            variant="ghost"
            size="sm"
            className="relative"
          >
            <Bell className="h-4 w-4" />
            <span className={cn(
              "absolute -top-1 w-5 h-5 bg-destructive text-destructive-foreground text-xs rounded-full flex items-center justify-center",
              isRTL ? "-left-1" : "-right-1"
            )}>
              3
            </span>
          </Button>

          {/* User Menu */}
          <div className={cn(
            "flex items-center gap-3",
            isRTL ? "flex-row-reverse" : "flex-row"
          )}>
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              <span className="text-primary-foreground text-sm font-medium">
                {isRTL ? "كق" : "KA"}
              </span>
            </div>
            <div className={cn(
              "hidden md:block",
              isRTL ? "text-right" : "text-left"
            )}>
              <p className="text-xs font-medium text-card-foreground dark:text-card-foreground">
                {isRTL ? "الكبير قوي" : "El Kabeer Awy"}
              </p>
              <p className="text-[10px] text-muted-foreground dark:text-muted-foreground">
                {isRTL ? "مدير النظام" : "System Admin"}
              </p>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
