import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  UserCog, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Shield,
  User,
  Mail,
  Phone,
  Calendar,
  Lock,
  Eye,
  EyeOff
} from "lucide-react";
import { useLanguage } from "@/hooks/use-language";
import labels from "@/lib/i18n";
import { formatDate } from "@/lib/formatters";

interface UserData {
  id: number;
  username: string;
  email: string;
  fullName: string;
  role: string;
  phone?: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
}

export default function Users() {
  const { language } = useLanguage();
  const t = labels[language];
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [editingUser, setEditingUser] = useState<UserData | null>(null);

  // Mock users data - in real app this would come from API
  const mockUsers: UserData[] = [
    {
      id: 1,
      username: "admin",
      email: "<EMAIL>",
      fullName: "محمد أحمد الإدارة",
      role: "مدير النظام",
      phone: "01234567890",
      isActive: true,
      lastLogin: "2024-06-24T10:30:00",
      createdAt: "2024-01-01T00:00:00"
    },
    {
      id: 2,
      username: "manager",
      email: "<EMAIL>", 
      fullName: "فاطمة حسن المدير",
      role: "مدير",
      phone: "01234567891",
      isActive: true,
      lastLogin: "2024-06-23T15:45:00",
      createdAt: "2024-02-15T00:00:00"
    },
    {
      id: 3,
      username: "employee",
      email: "<EMAIL>",
      fullName: "أحمد علي الموظف",
      role: "موظف",
      phone: "01234567892",
      isActive: true,
      lastLogin: "2024-06-24T09:15:00",
      createdAt: "2024-03-01T00:00:00"
    },
    {
      id: 4,
      username: "viewer",
      email: "<EMAIL>",
      fullName: "سارة محمد المشاهد",
      role: "مشاهد",
      isActive: false,
      lastLogin: "2024-06-20T14:20:00",
      createdAt: "2024-04-10T00:00:00"
    }
  ];

  const filteredUsers = mockUsers.filter(user => {
    if (searchQuery && !user.fullName.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !user.email.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    if (roleFilter && user.role !== roleFilter) return false;
    if (statusFilter === 'active' && !user.isActive) return false;
    if (statusFilter === 'inactive' && user.isActive) return false;
    return true;
  });

  const roles = [
    { value: "مدير النظام", label: "مدير النظام", permissions: "جميع الصلاحيات" },
    { value: "مدير", label: "مدير", permissions: "إدارة العقود والعملاء" },
    { value: "موظف", label: "موظف", permissions: "عرض وتعديل البيانات" },
    { value: "مشاهد", label: "مشاهد", permissions: "عرض البيانات فقط" }
  ];

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "مدير النظام": return "default";
      case "مدير": return "secondary";
      case "موظف": return "outline";
      case "مشاهد": return "destructive";
      default: return "outline";
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').slice(0, 2);
  };

  const handleAddUser = () => {
    setEditingUser(null);
    setShowAddModal(true);
  };

  const handleEditUser = (user: UserData) => {
    setEditingUser(user);
    setShowAddModal(true);
  };

  const handleDeleteUser = (userId: number) => {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      // TODO: Implement delete user API call
      console.log('Delete user:', userId);
    }
  };

  const userStats = [
    {
      title: "إجمالي المستخدمين",
      value: mockUsers.length,
      icon: User,
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900/30"
    },
    {
      title: "المستخدمين النشطين",
      value: mockUsers.filter(u => u.isActive).length,
      icon: Shield,
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-100 dark:bg-green-900/30"
    },
    {
      title: "المدراء",
      value: mockUsers.filter(u => u.role === "مدير" || u.role === "مدير النظام").length,
      icon: UserCog,
      color: "text-purple-600 dark:text-purple-400",
      bgColor: "bg-purple-100 dark:bg-purple-900/30"
    },
    {
      title: "آخر تسجيل دخول",
      value: "اليوم",
      icon: Calendar,
      color: "text-orange-600 dark:text-orange-400",
      bgColor: "bg-orange-100 dark:bg-orange-900/30"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl flex items-center gap-2">
                <UserCog className="h-6 w-6" />
                {t.users}
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                إدارة المستخدمين والصلاحيات
              </p>
            </div>
            <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
              <DialogTrigger asChild>
                <Button onClick={handleAddUser} className="gap-2">
                  <Plus className="h-4 w-4" />
                  إضافة مستخدم جديد
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-lg">
                <DialogHeader>
                  <DialogTitle>
                    {editingUser ? 'تعديل مستخدم' : 'إضافة مستخدم جديد'}
                  </DialogTitle>
                </DialogHeader>
                <form className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="fullName">الاسم الكامل *</Label>
                    <Input
                      id="fullName"
                      placeholder="الاسم الكامل للمستخدم"
                      defaultValue={editingUser?.fullName}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="username">اسم المستخدم *</Label>
                    <Input
                      id="username"
                      placeholder="اسم المستخدم"
                      defaultValue={editingUser?.username}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="email">البريد الإلكتروني *</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      defaultValue={editingUser?.email}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="phone">رقم الهاتف</Label>
                    <Input
                      id="phone"
                      placeholder="01234567890"
                      defaultValue={editingUser?.phone}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="role">الدور والصلاحيات *</Label>
                    <Select defaultValue={editingUser?.role}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الدور" />
                      </SelectTrigger>
                      <SelectContent>
                        {roles.map((role) => (
                          <SelectItem key={role.value} value={role.value}>
                            <div className="flex flex-col">
                              <span>{role.label}</span>
                              <span className="text-xs text-muted-foreground">{role.permissions}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {!editingUser && (
                    <div className="space-y-2">
                      <Label htmlFor="password">كلمة المرور *</Label>
                      <div className="relative">
                        <Input
                          id="password"
                          type={showPassword ? "text" : "password"}
                          placeholder="كلمة المرور"
                          required
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute left-2 top-1/2 transform -translate-y-1/2"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex justify-end gap-4 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowAddModal(false)}
                    >
                      {t.cancel}
                    </Button>
                    <Button type="submit">
                      {editingUser ? 'تحديث المستخدم' : 'إضافة المستخدم'}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
      </Card>

      {/* User Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {userStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">{stat.title}</p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                  <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6 bg-muted/30">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">{t.search}</label>
              <div className="relative">
                <Input
                  placeholder="اسم المستخدم أو الإيميل..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pr-10"
                />
                <Search className="h-4 w-4 absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">الدور</label>
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="جميع الأدوار" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأدوار</SelectItem>
                  {roles.map((role) => (
                    <SelectItem key={role.value} value={role.value}>
                      {role.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">الحالة</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="جميع الحالات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="active">نشط</SelectItem>
                  <SelectItem value="inactive">غير نشط</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-end">
              <Button 
                variant="outline" 
                className="w-full gap-2"
                onClick={() => {
                  setSearchQuery('');
                  setRoleFilter('');
                  setStatusFilter('');
                }}
              >
                مسح الفلاتر
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            قائمة المستخدمين
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredUsers.length > 0 ? (
              filteredUsers.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center gap-4">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src="" alt={user.fullName} />
                      <AvatarFallback className="bg-primary/10 text-primary">
                        {getInitials(user.fullName)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{user.fullName}</p>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {user.username}
                        </span>
                        <span className="flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          {user.email}
                        </span>
                        {user.phone && (
                          <span className="flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {user.phone}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant={getRoleBadgeVariant(user.role)}>
                          {user.role}
                        </Badge>
                        <Badge variant={user.isActive ? "default" : "secondary"}>
                          {user.isActive ? "نشط" : "غير نشط"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right space-y-2">
                    <div className="text-sm text-muted-foreground">
                      {user.lastLogin && (
                        <p>آخر دخول: {formatDate(user.lastLogin)}</p>
                      )}
                      <p>انضم: {formatDate(user.createdAt)}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditUser(user)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteUser(user.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <UserCog className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium text-muted-foreground">
                  {searchQuery || roleFilter || statusFilter 
                    ? "لا توجد نتائج تطابق البحث" 
                    : t.noData}
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  {!searchQuery && !roleFilter && !statusFilter && "ابدأ بإضافة مستخدمين جدد للنظام"}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
