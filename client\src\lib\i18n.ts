export interface Labels {
  // Navigation
  dashboard: string;
  clients: string;
  contracts: string;
  payments: string;
  treasuryPayments: string;
  bankPayments: string;
  chequesManagement: string;
  dues: string;
  reports: string;
  financialReports: string;
  collections: string;
  alerts: string;
  auditTrail: string;

  referenceData: string;
  adminDatabase: string;
  settings: string;
  users: string;

  // Common
  name: string;
  email: string;
  phone: string;
  address: string;
  notes: string;
  save: string;
  cancel: string;
  delete: string;
  edit: string;
  view: string;
  print: string;
  search: string;
  add: string;
  actions: string;
  status: string;
  date: string;
  amount: string;
  total: string;
  loading: string;
  noData: string;
  error: string;
  success: string;

  // Client related
  clientName: string;
  clientType: string;
  clientId: string;
  addClient: string;
  editClient: string;
  clientManagement: string;
  clientTypes: {
    individuals: string;
    companies: string;
  };
  financialCategory: string;
  financialGuarantee: string;

  // Contract related
  contractNumber: string;
  contractType: string;
  contractStatus: string;
  startDate: string;
  endDate: string;
  contractValue: string;
  monthlyAmount: string;
  addContract: string;
  editContract: string;
  contractManagement: string;

  // Payment related
  paymentDate: string;
  paymentMethod: string;
  paymentStatus: string;
  dueDate: string;
  paidAmount: string;
  outstandingAmount: string;
  overduePayments: string;

  // Dashboard
  welcomeMessage: string;
  totalClients: string;
  activeContracts: string;
  monthlyRevenue: string;
  recentContracts: string;
  recentPayments: string;
  dashboardAlerts: string;

  // Settings
  companyName: string;
  language: string;
  currency: string;

  // Settings Page
  settingsTitle: string;
  editSettings: string;
  saveSettings: string;
  cancelEdit: string;

  // Company Tab
  companyInfo: string;
  companyInfoDesc: string;
  programName: string;
  companyRegNo: string;
  taxId: string;
  companyPhone: string;
  companyEmail: string;
  companyWebsite: string;
  companyAddress: string;
  aboutCompany: string;
  companyLogo: string;

  // Localization Tab
  localizationSettings: string;
  localizationDesc: string;
  primaryLanguage: string;
  country: string;
  currencySymbol: string;
  dateFormat: string;
  timeFormat: string;
  timeZone: string;
  fiscalYearStart: string;
  workDays: string;

  // Numbering Tab
  numberingSettings: string;
  numberingDesc: string;
  contractNumberFormat: string;
  clientNumberFormat: string;
  paymentNumberFormat: string;
  invoiceNumberFormat: string;
  receiptNumberFormat: string;
  thousandsSeparator: string;

  // Notifications Tab
  notificationSettings: string;
  notificationDesc: string;
  notificationEmail: string;
  notificationFrequency: string;
  emailNotifications: string;
  smsNotifications: string;
  browserNotifications: string;

  // Theme Tab
  themeSettings: string;
  themeDesc: string;
  generalTheme: string;
  fontSize: string;
  fontFamily: string;
  primaryColor: string;
  collapseSidebar: string;
  showAnimations: string;

  // Business Tab
  businessSettings: string;
  businessDesc: string;
  defaultContractDuration: string;
  defaultPaymentTerms: string;
  defaultTaxRate: string;
  backupFrequency: string;
  multiCurrencySupport: string;
  welcomeMessageSetting: string;

  // Security Tab
  securitySettings: string;
  securityDesc: string;
  sessionTimeout: string;
  maxLoginAttempts: string;
  passwordPolicy: string;
  twoFactorAuth: string;
  auditLog: string;

  // System Tab
  systemSettings: string;
  systemDesc: string;
  cacheTimeout: string;
  maxFileSize: string;
  enableCaching: string;
  enableCompression: string;
  debugMode: string;
  systemTools: string;
  exportData: string;
  importData: string;

  // Subtitles
  clientsSubtitle: string;
  contractsSubtitle: string;
  paymentsSubtitle: string;
  reportsSubtitle: string;
  receivablesSubtitle: string;

  settingsSubtitle: string;
  usersSubtitle: string;
  alertsSubtitle: string;
  auditTrailSubtitle: string;
  chequesManagementSubtitle: string;
  duesSubtitle: string;
  financialReportsSubtitle: string;
  systemSettingsSubtitle: string;
}

const arabicLabels: Labels = {
  // Navigation
  dashboard: "الصفحة الرئيسية",
  clients: "العملاء",
  contracts: "العقود",
  payments: "المدفوعات",
  treasuryPayments: "مدفوعات الخزينة",
  bankPayments: "المدفوعات البنكية",
  chequesManagement: "إدارة الشيكات",
  dues: "الاستحقاقات",
  reports: "التقارير",
  financialReports: "التقارير المالية",
  collections: "التحصيلات",
  alerts: "التنبيهات",
  auditTrail: "سجل المراجعة",

  referenceData: "البيانات المرجعية",
  adminDatabase: "إدارة قاعدة البيانات",
  settings: "إعدادات النظام الشاملة",
  users: "إدارة المستخدمين",

  // Common
  name: "الاسم",
  email: "البريد الإلكتروني",
  phone: "رقم الهاتف",
  address: "العنوان",
  notes: "ملاحظات",
  save: "حفظ",
  cancel: "إلغاء",
  delete: "حذف",

  view: "كشف حساب",
  print: "طباعة",
  search: "بحث",
  add: "إضافة",
  actions: "الإجراءات",
  status: "الحالة",
  date: "التاريخ",
  amount: "المبلغ",
  total: "المجموع",
  loading: "جاري التحميل...",
  noData: "لا توجد بيانات",
  error: "خطأ",
  success: "نجح",

  // Client related
  clientName: "اسم العميل",
  clientType: "نوع العميل",
  clientId: "رقم العميل",
  addClient: "إضافة عميل جديد",
  editClient: "تعديل بيانات العميل",
  clientManagement: "إدارة العملاء",
  clientTypes: {
    individuals: "أفراد",
    companies: "شركات",
  },
  financialCategory: "التصنيف المالي",
  financialGuarantee: "الضامن المالي",

  // Contract related
  contractNumber: "رقم العقد",
  contractType: "نوع العقد",
  contractStatus: "حالة العقد",
  startDate: "تاريخ البداية",
  endDate: "تاريخ الانتهاء",
  contractValue: "قيمة العقد",
  monthlyAmount: "القسط الشهري",
  addContract: "إضافة عقد جديد",

  contractManagement: "إدارة العقود",

  // Payment related
  paymentDate: "تاريخ الدفع",
  paymentMethod: "طريقة الدفع",
  paymentStatus: "حالة الدفع",
  dueDate: "تاريخ الاستحقاق",
  paidAmount: "المبلغ المدفوع",
  outstandingAmount: "المبلغ المستحق",
  overduePayments: "المدفوعات المتأخرة",

  // Dashboard
  welcomeMessage: "مرحباً بك في نظام إدارة العقود",
  totalClients: "إجمالي العملاء",
  activeContracts: "العقود النشطة",
  monthlyRevenue: "الإيرادات الشهرية",
  recentContracts: "العقود الحديثة",
  recentPayments: "المدفوعات الحديثة",
  dashboardAlerts: "التنبيهات",

  // Settings
  companyName: "اسم الشركة",
  language: "اللغة",
  currency: "العملة",

  // Settings Page
  settingsTitle: "إعدادات النظام الشاملة",
  editSettings: "تعديل الإعدادات",
  saveSettings: "حفظ الإعدادات",
  cancelEdit: "إلغاء التعديل",

  // Company Tab
  companyInfo: "معلومات الشركة",
  companyInfoDesc: "إعدادات الشركة والمؤسسة",
  programName: "اسم البرنامج",
  companyRegNo: "رقم السجل التجاري",
  taxId: "الرقم الضريبي",
  companyPhone: "هاتف الشركة",
  companyEmail: "بريد الشركة الإلكتروني",
  companyWebsite: "موقع الشركة الإلكتروني",
  companyAddress: "عنوان الشركة",
  aboutCompany: "نبذة عن الشركة",
  companyLogo: "شعار الشركة",

  // Localization Tab
  localizationSettings: "إعدادات المنطقة والتوطين",
  localizationDesc: "إعدادات اللغة والمنطقة",
  primaryLanguage: "اللغة الأساسية",
  country: "البلد",
  currencySymbol: "رمز العملة",
  dateFormat: "تنسيق التاريخ",
  timeFormat: "تنسيق الوقت",
  timeZone: "المنطقة الزمنية",
  fiscalYearStart: "بداية السنة المالية",
  workDays: "أيام العمل",

  // Numbering Tab
  numberingSettings: "تنسيق الأرقام والترقيم التلقائي",
  numberingDesc: "إعدادات ترقيم الوثائق",
  contractNumberFormat: "تنسيق رقم العقد",
  clientNumberFormat: "تنسيق رقم العميل",
  paymentNumberFormat: "تنسيق رقم الدفعة",
  invoiceNumberFormat: "تنسيق رقم الفاتورة",
  receiptNumberFormat: "تنسيق رقم الإيصال",
  thousandsSeparator: "فاصل الآلاف",

  // Notifications Tab
  notificationSettings: "إعدادات الإشعارات والتنبيهات",
  notificationDesc: "إعدادات التنبيهات والإشعارات",
  notificationEmail: "البريد الإلكتروني للإشعارات",
  notificationFrequency: "تكرار الإشعارات",
  emailNotifications: "إشعارات البريد الإلكتروني",
  smsNotifications: "إشعارات الرسائل النصية",
  browserNotifications: "الإشعارات المنبثقة",

  // Theme Tab
  themeSettings: "إعدادات المظهر والواجهة",
  themeDesc: "إعدادات المظهر والألوان",
  generalTheme: "المظهر العام",
  fontSize: "حجم الخط",
  fontFamily: "نوع الخط",
  primaryColor: "اللون الأساسي",
  collapseSidebar: "طي الشريط الجانبي",
  showAnimations: "الحركات والانتقالات",

  // Business Tab
  businessSettings: "إعدادات العمل والأعمال",
  businessDesc: "إعدادات الأعمال والعمليات",
  defaultContractDuration: "مدة العقد الافتراضية (بالأشهر)",
  defaultPaymentTerms: "شروط الدفع الافتراضية",
  defaultTaxRate: "معدل الضريبة الافتراضي (%)",
  backupFrequency: "تكرار النسخ الاحتياطي",
  multiCurrencySupport: "دعم العملات المتعددة",
  welcomeMessageSetting: "رسالة الترحيب",

  // Security Tab
  securitySettings: "إعدادات الأمان والحماية",
  securityDesc: "إعدادات الأمان والحماية",
  sessionTimeout: "انتهاء الجلسة (بالدقائق)",
  maxLoginAttempts: "عدد محاولات تسجيل الدخول",
  passwordPolicy: "سياسة كلمة المرور",
  twoFactorAuth: "المصادقة الثنائية",
  auditLog: "سجل المراجعة",

  // System Tab
  systemSettings: "إعدادات النظام والأداء",
  systemDesc: "إعدادات النظام والأداء",
  cacheTimeout: "مهلة التخزين المؤقت (بالدقائق)",
  maxFileSize: "حد حجم الملف (بالميجابايت)",
  enableCaching: "التخزين المؤقت",
  enableCompression: "ضغط البيانات",
  debugMode: "وضع التطوير",
  systemTools: "أدوات النظام",
  exportData: "تصدير البيانات",
  importData: "استيراد البيانات",

  // Subtitles
  clientsSubtitle: "إدارة وتتبع بيانات العملاء",
  contractsSubtitle: "إدارة وتتبع جميع العقود",
  paymentsSubtitle: "تتبع وإدارة جميع المدفوعات",
  reportsSubtitle: "التقارير المالية والإحصائية",
  receivablesSubtitle: "متابعة الاستحقاقات والذمم",

  settingsSubtitle: "إعدادات النظام والشركة",
  usersSubtitle: "إدارة المستخدمين والصلاحيات",
  alertsSubtitle: "إدارة ومتابعة جميع التنبيهات والإشعارات",
  auditTrailSubtitle: "تتبع جميع العمليات والتغييرات في النظام",
  chequesManagementSubtitle: "إدارة شاملة لجميع الشيكات ومراحل التحصيل",
  duesSubtitle: "متابعة جميع الاستحقاقات والالتزامات المالية",
  financialReportsSubtitle: "تقارير مالية شاملة ومتقدمة مع رسوم بيانية تفاعلية",
  systemSettingsSubtitle: "إدارة وتكوين جميع إعدادات النظام",
};

const englishLabels: Labels = {
  // Navigation
  dashboard: "Dashboard",
  clients: "Clients",
  contracts: "Contracts",
  payments: "Payments",
  treasuryPayments: "Treasury Payments",
  bankPayments: "Bank Payments",
  chequesManagement: "Cheques Management",
  dues: "Receivables",
  reports: "Reports",
  financialReports: "Financial Reports",
  collections: "Collections",
  alerts: "Alerts",
  auditTrail: "Audit Trail",

  referenceData: "Reference Data",
  adminDatabase: "Database Administration",
  settings: "Comprehensive System Settings",
  users: "User Management",

  // Common
  name: "Name",
  email: "Email",
  phone: "Phone",
  address: "Address",
  notes: "Notes",
  save: "Save",
  cancel: "Cancel",
  delete: "Delete",
  edit: "Edit",
  view: "View",
  print: "Print",
  search: "Search",
  add: "Add",
  actions: "Actions",
  status: "Status",
  date: "Date",
  amount: "Amount",
  total: "Total",
  loading: "Loading...",
  noData: "No data available",
  error: "Error",
  success: "Success",

  // Client related
  clientName: "Client Name",
  clientType: "Client Type",
  clientId: "Client ID",
  addClient: "Add New Client",
  editClient: "Edit Client",
  clientManagement: "Client Management",
  clientTypes: {
    individuals: "Individuals",
    companies: "Companies",
  },
  financialCategory: "Financial Category",
  financialGuarantee: "Financial Guarantee",

  // Contract related
  contractNumber: "Contract Number",
  contractType: "Contract Type",
  contractStatus: "Contract Status",
  startDate: "Start Date",
  endDate: "End Date",
  contractValue: "Contract Value",
  monthlyAmount: "Monthly Amount",
  addContract: "Add New Contract",
  editContract: "Edit Contract",
  contractManagement: "Contract Management",

  // Payment related
  paymentDate: "Payment Date",
  paymentMethod: "Payment Method",
  paymentStatus: "Payment Status",
  dueDate: "Due Date",
  paidAmount: "Paid Amount",
  outstandingAmount: "Outstanding Amount",
  overduePayments: "Overdue Payments",

  // Dashboard
  welcomeMessage: "Welcome to Contract Management System",
  totalClients: "Total Clients",
  activeContracts: "Active Contracts",
  monthlyRevenue: "Monthly Revenue",
  recentContracts: "Recent Contracts",
  recentPayments: "Recent Payments",
  dashboardAlerts: "Alerts",

  // Settings
  companyName: "Company Name",
  language: "Language",
  currency: "Currency",

  // Settings Page
  settingsTitle: "Comprehensive System Settings",
  editSettings: "Edit Settings",
  saveSettings: "Save Settings",
  cancelEdit: "Cancel Edit",

  // Company Tab
  companyInfo: "Company Information",
  companyInfoDesc: "Company and organization settings",
  programName: "Program Name",
  companyRegNo: "Commercial Registration Number",
  taxId: "Tax ID",
  companyPhone: "Company Phone",
  companyEmail: "Company Email",
  companyWebsite: "Company Website",
  companyAddress: "Company Address",
  aboutCompany: "About Company",
  companyLogo: "Company Logo",

  // Localization Tab
  localizationSettings: "Localization and Region Settings",
  localizationDesc: "Language and region settings",
  primaryLanguage: "Primary Language",
  country: "Country",
  currencySymbol: "Currency Symbol",
  dateFormat: "Date Format",
  timeFormat: "Time Format",
  timeZone: "Time Zone",
  fiscalYearStart: "Fiscal Year Start",
  workDays: "Work Days",

  // Numbering Tab
  numberingSettings: "Number Formatting and Auto-Numbering",
  numberingDesc: "Document numbering settings",
  contractNumberFormat: "Contract Number Format",
  clientNumberFormat: "Client Number Format",
  paymentNumberFormat: "Payment Number Format",
  invoiceNumberFormat: "Invoice Number Format",
  receiptNumberFormat: "Receipt Number Format",
  thousandsSeparator: "Thousands Separator",

  // Notifications Tab
  notificationSettings: "Notification and Alert Settings",
  notificationDesc: "Alert and notification settings",
  notificationEmail: "Notification Email",
  notificationFrequency: "Notification Frequency",
  emailNotifications: "Email Notifications",
  smsNotifications: "SMS Notifications",
  browserNotifications: "Browser Notifications",

  // Theme Tab
  themeSettings: "Theme and Interface Settings",
  themeDesc: "Appearance and color settings",
  generalTheme: "General Theme",
  fontSize: "Font Size",
  fontFamily: "Font Family",
  primaryColor: "Primary Color",
  collapseSidebar: "Collapse Sidebar",
  showAnimations: "Show Animations",

  // Business Tab
  businessSettings: "Business and Operations Settings",
  businessDesc: "Business and operations settings",
  defaultContractDuration: "Default Contract Duration (months)",
  defaultPaymentTerms: "Default Payment Terms",
  defaultTaxRate: "Default Tax Rate (%)",
  backupFrequency: "Backup Frequency",
  multiCurrencySupport: "Multi-Currency Support",
  welcomeMessageSetting: "Welcome Message",

  // Security Tab
  securitySettings: "Security and Protection Settings",
  securityDesc: "Security and protection settings",
  sessionTimeout: "Session Timeout (minutes)",
  maxLoginAttempts: "Max Login Attempts",
  passwordPolicy: "Password Policy",
  twoFactorAuth: "Two-Factor Authentication",
  auditLog: "Audit Log",

  // System Tab
  systemSettings: "System and Performance Settings",
  systemDesc: "System and performance settings",
  cacheTimeout: "Cache Timeout (minutes)",
  maxFileSize: "Max File Size (MB)",
  enableCaching: "Enable Caching",
  enableCompression: "Enable Compression",
  debugMode: "Debug Mode",
  systemTools: "System Tools",
  exportData: "Export Data",
  importData: "Import Data",

  // Subtitles
  clientsSubtitle: "Manage and track client data",
  contractsSubtitle: "Manage and track all contracts",
  paymentsSubtitle: "Track and manage all payments",
  reportsSubtitle: "Financial and statistical reports",
  receivablesSubtitle: "Monitor receivables and outstanding amounts",

  settingsSubtitle: "System and company settings",
  usersSubtitle: "User management and permissions",
  alertsSubtitle: "Manage and monitor all alerts and notifications",
  auditTrailSubtitle: "Track all operations and system changes",
  chequesManagementSubtitle: "Comprehensive management of all cheques and collection stages",
  duesSubtitle: "Monitor all dues and financial obligations",
  financialReportsSubtitle: "Comprehensive and advanced financial reports with interactive charts",
  systemSettingsSubtitle: "Manage and configure all system settings",
};

export const labels = {
  ar: arabicLabels,
  en: englishLabels,
};

export default labels;
