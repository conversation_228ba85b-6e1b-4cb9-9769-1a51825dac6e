// ===== DATABASE INDEXES MIGRATION =====
// Author: Augment Code
// Description: Create indexes for better query performance

const createIndexes = (db) => {
  console.log('Creating database indexes...');

  // Basic indexes for main tables
  db.run(`CREATE INDEX IF NOT EXISTS idx_clients_client_id ON Clients(clientId)`);
  db.run(`CREATE INDEX IF NOT EXISTS idx_clients_type ON Clients(clientType)`);
  db.run(`CREATE INDEX IF NOT EXISTS idx_clients_active ON Clients(isActive)`);

  db.run(`CREATE INDEX IF NOT EXISTS idx_contracts_id ON Contracts(contractId)`);
  db.run(`CREATE INDEX IF NOT EXISTS idx_contracts_client_id ON Contracts(clientId)`);
  db.run(`CREATE INDEX IF NOT EXISTS idx_contracts_status ON Contracts(status)`);

  db.run(`CREATE INDEX IF NOT EXISTS idx_payments_contract_id ON Payments(contractId)`);

  console.log('Database indexes created successfully');
};

const dropIndexes = (db) => {
  console.log('Dropping database indexes...');

  // Drop all custom indexes
  const indexes = [
    'idx_clients_client_id',
    'idx_clients_type',
    'idx_clients_name',
    'idx_clients_phone',
    'idx_clients_email',
    'idx_clients_active',
    'idx_clients_created',
    'idx_contracts_number',
    'idx_contracts_internal_id',
    'idx_contracts_client_id',
    'idx_contracts_type',
    'idx_contracts_status',
    'idx_contracts_start_date',
    'idx_contracts_end_date',
    'idx_contracts_active',
    'idx_contracts_created',
    'idx_payments_contract_id',
    'idx_payments_type',
    'idx_payments_status',
    'idx_payments_due_date',
    'idx_payments_paid_date',
    'idx_payments_installment',
    'idx_payments_created',
    'idx_reference_category',
    'idx_reference_value',
    'idx_reference_active',
    'idx_reference_sort',
    'idx_contracts_client_status',
    'idx_payments_contract_status',
    'idx_payments_due_status',
    'idx_clients_type_active'
  ];

  indexes.forEach(indexName => {
    db.run(`DROP INDEX IF EXISTS ${indexName}`);
  });

  console.log('Database indexes dropped successfully');
};

module.exports = {
  up: createIndexes,
  down: dropIndexes
};
