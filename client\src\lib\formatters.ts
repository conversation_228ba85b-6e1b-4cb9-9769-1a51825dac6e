// Note: This function is deprecated. Use useCurrency hook instead for consistent currency formatting
export const formatCurrency = (amount: number, currency = "EGP"): string => {
  return new Intl.NumberFormat('ar-EG', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Note: This function is deprecated. Use useDateFormat hook instead for consistent date formatting
export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('ar-EG', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(dateObj);
};

export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('ar-EG', {
    useGrouping: true,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(num);
};

export const formatContractStatus = (status: string) => {
  const statusMap = {
    'نشط': { text: 'نشط', variant: 'default' as const },
    'غير نشط': { text: 'غير نشط', variant: 'outline' as const },
    'منتهي': { text: 'منتهي', variant: 'secondary' as const },
    'ملغي': { text: 'ملغي', variant: 'destructive' as const },
    'معلق': { text: 'معلق', variant: 'outline' as const },
  };

  return statusMap[status as keyof typeof statusMap] || { text: status, variant: 'default' as const };
};

export const formatPaymentStatus = (isPaid: boolean) => {
  return isPaid 
    ? { text: 'مدفوع', variant: 'default' as const, color: 'text-green-600' }
    : { text: 'غير مدفوع', variant: 'destructive' as const, color: 'text-red-600' };
};

export const formatDuration = (years: number): string => {
  if (years === 1) return 'سنة واحدة';
  if (years === 2) return 'سنتان';
  if (years <= 10) return `${years} سنوات`;
  return `${years} سنة`;
};

/**
 * تنسيق المدة بالسنوات والشهور والأيام
 */
export const formatDetailedDuration = (startDate: string, endDate: string): string => {
  if (!startDate || !endDate) return '';

  const start = new Date(startDate);
  const end = new Date(endDate);

  if (end <= start) return 'مدة غير صحيحة';

  // إضافة يوم واحد لتاريخ الانتهاء لحساب المدة الصحيحة
  // لأن العقد من 01/01 إلى 31/12 يجب أن يكون سنة كاملة
  const adjustedEnd = new Date(end);
  adjustedEnd.setDate(adjustedEnd.getDate() + 1);

  let years = adjustedEnd.getFullYear() - start.getFullYear();
  let months = adjustedEnd.getMonth() - start.getMonth();
  let days = adjustedEnd.getDate() - start.getDate();

  // تعديل الحسابات إذا كانت الأيام سالبة
  if (days < 0) {
    months--;
    const lastDayOfPrevMonth = new Date(adjustedEnd.getFullYear(), adjustedEnd.getMonth(), 0).getDate();
    days += lastDayOfPrevMonth;
  }

  // تعديل الحسابات إذا كانت الشهور سالبة
  if (months < 0) {
    years--;
    months += 12;
  }

  // إذا كانت المدة سنة كاملة بالضبط (مثل من 01/01 إلى 31/12)
  if (years >= 1 && months === 0 && days === 0) {
    if (years === 1) return 'سنة واحدة';
    else if (years === 2) return 'سنتان';
    else if (years <= 10) return `${years} سنوات`;
    else return `${years} سنة`;
  }

  const parts = [];

  if (years > 0) {
    if (years === 1) parts.push('سنة واحدة');
    else if (years === 2) parts.push('سنتان');
    else if (years <= 10) parts.push(`${years} سنوات`);
    else parts.push(`${years} سنة`);
  }

  if (months > 0) {
    if (months === 1) parts.push('شهر واحد');
    else if (months === 2) parts.push('شهران');
    else if (months <= 10) parts.push(`${months} أشهر`);
    else parts.push(`${months} شهر`);
  }

  if (days > 0) {
    if (days === 1) parts.push('يوم واحد');
    else if (days === 2) parts.push('يومان');
    else if (days <= 10) parts.push(`${days} أيام`);
    else parts.push(`${days} يوم`);
  }

  if (parts.length === 0) return 'أقل من يوم';

  return parts.join(' و ');
};

/**
 * حساب المدة بالسنوات بدقة أكبر
 */
export const calculateDurationInYears = (startDate: string, endDate: string): number => {
  if (!startDate || !endDate) return 0;

  const start = new Date(startDate);
  const end = new Date(endDate);

  if (end <= start) return 0;

  const yearDiff = end.getFullYear() - start.getFullYear();
  const monthDiff = end.getMonth() - start.getMonth();
  const dayDiff = end.getDate() - start.getDate();

  let duration = yearDiff;

  // إضافة الأشهر والأيام كجزء عشري
  if (monthDiff !== 0 || dayDiff !== 0) {
    const totalDaysInYear = 365;
    const daysFromMonths = monthDiff * 30.44; // متوسط الأيام في الشهر
    const totalExtraDays = daysFromMonths + dayDiff;
    duration += totalExtraDays / totalDaysInYear;
  }

  return Math.round(duration * 100) / 100; // تقريب لأقرب رقمين عشريين
};

/**
 * حساب تاريخ الانتهاء من تاريخ البداية والمدة
 */
export const calculateEndDateFromDuration = (startDate: string, durationYears: number): string => {
  if (!startDate || durationYears <= 0) return '';

  const start = new Date(startDate);
  const end = new Date(start);

  // إضافة السنوات الكاملة
  end.setFullYear(end.getFullYear() + Math.floor(durationYears));

  // إضافة الأشهر والأيام المتبقية
  const remainingFraction = durationYears % 1;
  if (remainingFraction > 0) {
    const remainingDays = Math.round(remainingFraction * 365);
    end.setDate(end.getDate() + remainingDays);
  }

  // طرح يوم واحد للحصول على آخر يوم في فترة العقد
  end.setDate(end.getDate() - 1);

  return end.toISOString().split('T')[0];
};

export const formatPaymentMethod = (method: string): string => {
  const methodMap = {
    'تحويل بنكي': 'تحويل بنكي',
    'شيك': 'شيك',
    'نقدي': 'نقدي',
    'بطاقة ائتمان': 'بطاقة ائتمان',
  };
  
  return methodMap[method as keyof typeof methodMap] || method;
};

export const isContractActive = (contract: { contractStatus: string }): boolean => {
  return contract.contractStatus === 'نشط';
};

export const calculateContractEndDate = (startDate: string, durationYears: number): Date => {
  const start = new Date(startDate);
  const end = new Date(start);
  end.setFullYear(start.getFullYear() + durationYears);
  // طرح يوم واحد للحصول على آخر يوم في فترة العقد
  // مثال: عقد يبدأ 01/01/2025 لمدة سنة ينتهي في 31/12/2025
  end.setDate(end.getDate() - 1);
  return end;
};

export const formatPhoneNumber = (phone: string): string => {
  // Format Egyptian phone numbers
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 11 && cleaned.startsWith('01')) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
  }
  return phone;
};

export const generateClientId = (): string => {
  const year = new Date().getFullYear();
  const random = Math.floor(Math.random() * 999) + 1;
  return `CL-${year}-${random.toString().padStart(3, '0')}`;
};

export const generateContractNumber = (): string => {
  const year = new Date().getFullYear();
  const random = Math.floor(Math.random() * 999) + 1;
  return `CT-${year}-${random.toString().padStart(3, '0')}`;
};
