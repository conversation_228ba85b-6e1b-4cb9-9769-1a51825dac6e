import React, { createContext, useContext, useEffect } from 'react';
import { useSettings } from '@/contexts/settings-context';
import { useDateFormat } from '@/hooks/use-date-format';
import moment from 'moment';

interface DateContextType {
  formatDate: (date: string | Date | null | undefined) => string;
  formatDateTime: (date: string | Date | null | undefined) => string;
  formatDateShort: (date: string | Date | null | undefined) => string;
  formatDateLong: (date: string | Date | null | undefined) => string;
  getDateFormat: () => string;
  getCalendarType: () => string;
  isHijri: () => boolean;
  getCurrentDate: () => string;
  getCurrentDateTime: () => string;
  parseDate: (dateString: string) => Date | null;
  formatDateForInput: (date: Date | null) => string;
}

const DateContext = createContext<DateContextType | undefined>(undefined);

export function DateProvider({ children }: { children: React.ReactNode }) {
  const { settings } = useSettings();
  const {
    formatDate,
    formatDateTime,
    formatDateShort,
    formatDateLong,
    getDateFormat,
    getCalendarType,
    isHijri,
    getCurrentDate,
    getCurrentDateTime,
  } = useDateFormat();

  // Parse date string according to current format
  const parseDate = (dateString: string): Date | null => {
    if (!dateString) return null;

    try {
      // If it's already in ISO format (YYYY-MM-DD), parse it correctly to avoid timezone issues
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        const [year, month, day] = dateString.split('-').map(Number);
        return new Date(year, month - 1, day); // month is 0-based in JavaScript
      }

      // Try to parse using moment with current format
      const currentFormat = getDateFormat();
      const momentDate = moment(dateString, currentFormat, true);
      if (momentDate.isValid()) {
        return momentDate.toDate();
      }

      // Manual parsing for DD/MM/YYYY to avoid timezone issues
      if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateString)) {
        const [day, month, year] = dateString.split('/').map(Number);
        if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900 && year <= 2100) {
          return new Date(year, month - 1, day); // month is 0-based
        }
      }

      // Try other common formats
      const commonFormats = ['YYYY/MM/DD', 'DD-MM-YYYY', 'YYYY-MM-DD', 'MM/DD/YYYY', 'MM-DD-YYYY'];
      for (const format of commonFormats) {
        const testDate = moment(dateString, format, true);
        if (testDate.isValid()) {
          return testDate.toDate();
        }
      }

      // Fallback to native Date parsing
      const fallbackDate = new Date(dateString);
      if (!isNaN(fallbackDate.getTime())) {
        return fallbackDate;
      }

      return null;
    } catch (error) {
      console.warn('Error parsing date:', error);
      return null;
    }
  };

  // Format date for HTML input[type="date"] (always YYYY-MM-DD)
  const formatDateForInput = (date: Date | null): string => {
    if (!date) return '';

    try {
      // Use local date components to avoid timezone issues
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.warn('Error formatting date for input:', error);
      return '';
    }
  };

  const value: DateContextType = {
    formatDate,
    formatDateTime,
    formatDateShort,
    formatDateLong,
    getDateFormat,
    getCalendarType,
    isHijri,
    getCurrentDate,
    getCurrentDateTime,
    parseDate,
    formatDateForInput,
  };

  return (
    <DateContext.Provider value={value}>
      {children}
    </DateContext.Provider>
  );
}

export function useDateContext() {
  const context = useContext(DateContext);
  if (context === undefined) {
    throw new Error('useDateContext must be used within a DateProvider');
  }
  return context;
}

export default DateProvider;
