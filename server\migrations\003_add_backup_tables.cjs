// ===== BACKUP TABLES MIGRATION (SIMPLIFIED) =====
// Author: Augment Code
// Description: Add basic backup functionality

const createBackupTables = (db) => {
  console.log('Creating backup tables...');

  // Backup metadata table
  db.run(`
    CREATE TABLE IF NOT EXISTS BackupMetadata (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      backupName TEXT NOT NULL UNIQUE,
      backupType TEXT NOT NULL CHECK(backupType IN ('FULL', 'INCREMENTAL', 'MANUAL')),
      description TEXT,
      filePath TEXT,
      fileSize INTEGER,
      recordCount INTEGER,
      status TEXT DEFAULT 'PENDING' CHECK(status IN ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED')),
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      completedAt DATETIME,
      errorMessage TEXT
    )
  `);

  // Backup log table for detailed logging
  db.run(`
    CREATE TABLE IF NOT EXISTS BackupLog (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      backupId INTEGER NOT NULL,
      logLevel TEXT NOT NULL CHECK(logLevel IN ('INFO', 'WARNING', 'ERROR')),
      message TEXT NOT NULL,
      details TEXT,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (backupId) REFERENCES BackupMetadata(id)
    )
  `);

  console.log('Backup tables created successfully');
};

const dropBackupTables = (db) => {
  console.log('Dropping backup tables...');

  db.run(`DROP TABLE IF EXISTS BackupLog`);
  db.run(`DROP TABLE IF EXISTS BackupMetadata`);

  console.log('Backup tables dropped successfully');
};

module.exports = {
  up: createBackupTables,
  down: dropBackupTables
};
