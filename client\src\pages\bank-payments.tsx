import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { insertBankPaymentSchema, type InsertBankPayment, type BankPayment } from "@shared/schema";
import { useCurrency } from "@/hooks/use-currency";
import { useDateFormat } from "@/hooks/use-date-format";
import { 
  Plus, 
  Building2, 
  Search, 
  Filter, 
  Edit, 
  Trash2,
  Receipt,
  CreditCard,
  FileText,
  Calendar,
  Landmark
} from "lucide-react";

export default function BankPayments() {
  const { formatCurrency } = useCurrency();
  const { formatDate } = useDateFormat();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingPayment, setEditingPayment] = useState<BankPayment | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [selectedContractId, setSelectedContractId] = useState<number | null>(null);
  const [contractReceivables, setContractReceivables] = useState<any[]>([]);

  const form = useForm<InsertBankPayment>({
    resolver: zodResolver(insertBankPaymentSchema),
    defaultValues: {
      paymentType: 'مرتبطة بعقد',
      isActive: true,
    },
  });

  // Get all bank payments
  const { data: payments, isLoading } = useQuery<BankPayment[]>({
    queryKey: ['/api/bank-payments'],
    queryFn: async () => {
      const response = await fetch('/api/bank-payments');
      if (!response.ok) throw new Error('Failed to fetch bank payments');
      const data = await response.json();

      // تأكد من أن البيانات array
      if (data?.success && Array.isArray(data.data)) {
        return data.data;
      } else if (Array.isArray(data)) {
        return data;
      }
      return [];
    }
  });

  // Get contracts for dropdown
  const { data: contracts } = useQuery({
    queryKey: ['/api/contracts'],
  });

  // Get receivables for dropdown
  const { data: receivables } = useQuery({
    queryKey: ['/api/receivables'],
    select: (data) => data?.receivables || [],
  });

  // Function to fetch contract receivables
  const fetchContractReceivables = async (contractId: number) => {
    try {
      const response = await fetch(`/api/contracts/${contractId}/receivables`);
      const data = await response.json();
      setContractReceivables(data);
    } catch (error) {
      console.error('Error fetching contract receivables:', error);
      setContractReceivables([]);
    }
  };

  // Create payment mutation
  const createPaymentMutation = useMutation({
    mutationFn: (data: InsertBankPayment) =>
      fetch('/api/bank-payments', {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      }).then(res => {
        if (!res.ok) throw new Error("Failed to create bank payment");
        return res.json();
      }),
    onSuccess: () => {
      toast({
        title: "تم تسجيل الدفعة البنكية",
        description: "تم تسجيل الدفعة البنكية بنجاح!",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/bank-payments'] });
      setDialogOpen(false);
      form.reset();
      setEditingPayment(null);
    },
    onError: (error: any) => {
      toast({
        title: "خطأ في تسجيل الدفعة البنكية",
        description: `خطأ: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Update payment mutation
  const updatePaymentMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: InsertBankPayment }) =>
      fetch(`/api/bank-payments/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      }).then(res => {
        if (!res.ok) throw new Error("Failed to update bank payment");
        return res.json();
      }),
    onSuccess: () => {
      toast({
        title: "تم تحديث الدفعة البنكية",
        description: "تم تحديث الدفعة البنكية بنجاح!",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/bank-payments'] });
      setDialogOpen(false);
      form.reset();
      setEditingPayment(null);
    },
    onError: (error: any) => {
      toast({
        title: "خطأ في تحديث الدفعة البنكية",
        description: `خطأ: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Delete payment mutation
  const deletePaymentMutation = useMutation({
    mutationFn: (id: number) =>
      fetch(`/api/bank-payments/${id}`, {
        method: "DELETE",
      }).then(res => {
        if (!res.ok) throw new Error("Failed to delete bank payment");
        return res.json();
      }),
    onSuccess: () => {
      toast({
        title: "تم حذف الدفعة البنكية",
        description: "تم حذف الدفعة البنكية بنجاح!",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/bank-payments'] });
    },
    onError: (error: any) => {
      toast({
        title: "خطأ في حذف الدفعة البنكية",
        description: `خطأ: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: InsertBankPayment) => {
    if (editingPayment) {
      updatePaymentMutation.mutate({ id: editingPayment.id, data });
    } else {
      createPaymentMutation.mutate(data);
    }
  };

  const handleEdit = (payment: BankPayment) => {
    setEditingPayment(payment);
    form.reset(payment);
    setDialogOpen(true);
  };

  const handleDelete = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه الدفعة البنكية؟')) {
      deletePaymentMutation.mutate(id);
    }
  };

  const openNewPaymentDialog = () => {
    setEditingPayment(null);
    form.reset({
      paymentType: 'مرتبطة بعقد',
      isActive: true,
    });
    setDialogOpen(true);
  };

  // Filter payments
  const filteredPayments = payments?.filter(payment => {
    const matchesSearch = searchTerm === "" || 
      payment.bankTransactionNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.clientName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.contractNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.bankName?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterType === "all" || payment.paymentType === filterType;
    
    return matchesSearch && matchesFilter;
  }) || [];

  const contractPayments = filteredPayments.filter(p => p.paymentType === 'مرتبطة بعقد');
  const nonContractPayments = filteredPayments.filter(p => p.paymentType === 'غير مرتبطة بعقد');
  const totalAmount = filteredPayments.reduce((sum, p) => sum + (p.amount || 0), 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl flex items-center gap-2">
                <Landmark className="h-6 w-6 text-green-600" />
                المدفوعات البنكية
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                إدارة المدفوعات والتحويلات البنكية
              </p>
            </div>
            <Button onClick={openNewPaymentDialog} className="gap-2">
              <Plus className="h-4 w-4" />
              تسجيل دفعة بنكية جديدة
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">إجمالي المدفوعات</p>
                <p className="text-2xl font-bold text-green-600">
                  {isLoading ? <Skeleton className="h-8 w-20" /> : filteredPayments.length}
                </p>
              </div>
              <Receipt className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">مرتبطة بعقود</p>
                <p className="text-2xl font-bold text-blue-600">
                  {isLoading ? <Skeleton className="h-8 w-16" /> : contractPayments.length}
                </p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">غير مرتبطة بعقود</p>
                <p className="text-2xl font-bold text-orange-600">
                  {isLoading ? <Skeleton className="h-8 w-16" /> : nonContractPayments.length}
                </p>
              </div>
              <Building2 className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">إجمالي المبالغ</p>
                <p className="text-2xl font-bold text-purple-600">
                  {isLoading ? <Skeleton className="h-8 w-24" /> : formatCurrency(totalAmount)}
                </p>
              </div>
              <CreditCard className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث برقم الحركة البنكية أو اسم العميل أو رقم العقد أو اسم البنك..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-48">
                <Filter className="h-4 w-4 ml-2" />
                <SelectValue placeholder="تصفية النوع" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع المدفوعات</SelectItem>
                <SelectItem value="مرتبطة بعقد">مرتبطة بعقد</SelectItem>
                <SelectItem value="غير مرتبطة بعقد">غير مرتبطة بعقد</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Payments Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Landmark className="h-5 w-5" />
            سجل المدفوعات البنكية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right">رقم الحركة البنكية</TableHead>
                  <TableHead className="text-right">التاريخ</TableHead>
                  <TableHead className="text-right">المبلغ</TableHead>
                  <TableHead className="text-right">البنك</TableHead>
                  <TableHead className="text-right">النوع</TableHead>
                  <TableHead className="text-right">التفاصيل</TableHead>
                  <TableHead className="text-right">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 5 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredPayments.length > 0 ? (
                  filteredPayments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">{payment.bankTransactionNumber}</TableCell>
                      <TableCell>{formatDate(payment.paymentDate)}</TableCell>
                      <TableCell className="font-bold text-green-600">
                        {formatCurrency(payment.amount)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Landmark className="h-4 w-4" />
                          {payment.bankName}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={payment.paymentType === 'مرتبطة بعقد' ? 'default' : 'secondary'}>
                          {payment.paymentType}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {payment.paymentType === 'مرتبطة بعقد' ? (
                          <div className="text-sm">
                            <div className="font-medium">{payment.clientName}</div>
                            <div className="text-muted-foreground">
                              عقد: {payment.contractNumber}
                            </div>
                            {payment.paymentStatus && (
                              <Badge variant="outline" className="mt-1">
                                سداد {payment.paymentStatus}
                              </Badge>
                            )}
                          </div>
                        ) : (
                          <Badge variant="outline">
                            {payment.nonContractType}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(payment)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(payment.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12">
                      <Landmark className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium text-muted-foreground">لا توجد مدفوعات بنكية</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        لم يتم تسجيل أي مدفوعات بنكية حتى الآن
                      </p>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Payment Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingPayment ? 'تعديل الدفعة البنكية' : 'تسجيل دفعة بنكية جديدة'}
            </DialogTitle>
          </DialogHeader>

          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="bankTransactionNumber">رقم الحركة البنكية *</Label>
                <Input
                  id="bankTransactionNumber"
                  {...form.register('bankTransactionNumber')}
                  placeholder="أدخل رقم الحركة البنكية"
                />
                {form.formState.errors.bankTransactionNumber && (
                  <p className="text-sm text-red-600 mt-1">
                    {form.formState.errors.bankTransactionNumber.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="paymentDate">تاريخ الدفع *</Label>
                <Input
                  id="paymentDate"
                  type="date"
                  {...form.register('paymentDate')}
                />
                {form.formState.errors.paymentDate && (
                  <p className="text-sm text-red-600 mt-1">
                    {form.formState.errors.paymentDate.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="amount">المبلغ *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  {...form.register('amount', { valueAsNumber: true })}
                  placeholder="أدخل المبلغ"
                />
                {form.formState.errors.amount && (
                  <p className="text-sm text-red-600 mt-1">
                    {form.formState.errors.amount.message}
                  </p>
                )}
              </div>
            </div>

            {/* Bank and Payment Type */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="bankName">اسم البنك *</Label>
                <Input
                  id="bankName"
                  {...form.register('bankName')}
                  placeholder="أدخل اسم البنك"
                />
                {form.formState.errors.bankName && (
                  <p className="text-sm text-red-600 mt-1">
                    {form.formState.errors.bankName.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="paymentType">نوع المدفوعة *</Label>
                <Select
                  value={form.watch('paymentType')}
                  onValueChange={(value) => form.setValue('paymentType', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر نوع المدفوعة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="مرتبطة بعقد">مرتبطة بعقد</SelectItem>
                    <SelectItem value="غير مرتبطة بعقد">غير مرتبطة بعقد</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Contract-related fields */}
            {form.watch('paymentType') === 'مرتبطة بعقد' && (
              <div className="space-y-4 p-4 border rounded-lg bg-blue-50">
                <h3 className="font-medium text-blue-800">بيانات العقد</h3>

                {/* Contract Selection */}
                <div>
                  <Label htmlFor="contractSelect">اختيار العقد *</Label>
                  <Select
                    value={selectedContractId?.toString() || ''}
                    onValueChange={(value) => {
                      const contractId = parseInt(value);
                      setSelectedContractId(contractId);

                      // Find selected contract and populate fields
                      const selectedContract = contracts?.find(c => c.id === contractId);
                      if (selectedContract) {
                        form.setValue('contractId', contractId);
                        form.setValue('contractNumber', selectedContract.contractNumber);
                        form.setValue('contractSubject', selectedContract.contractSubject);
                        form.setValue('clientName', selectedContract.clientName);

                        // Fetch contract receivables
                        fetchContractReceivables(contractId);
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر العقد" />
                    </SelectTrigger>
                    <SelectContent>
                      {contracts?.map((contract) => (
                        <SelectItem key={contract.id} value={contract.id.toString()}>
                          <div className="flex flex-col">
                            <span className="font-medium">{contract.contractNumber}</span>
                            <span className="text-sm text-muted-foreground">
                              {contract.clientName} - {contract.contractSubject}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Contract Receivables Selection */}
                {contractReceivables.length > 0 && (
                  <div>
                    <Label htmlFor="receivableSelect">اختيار الاستحقاق *</Label>
                    <Select
                      value={form.watch('receivableId')?.toString() || ''}
                      onValueChange={(value) => {
                        const receivableId = parseInt(value);
                        const selectedReceivable = contractReceivables.find(r => r.id === receivableId);
                        if (selectedReceivable) {
                          form.setValue('receivableId', receivableId);
                          form.setValue('receivableDescription', selectedReceivable.description);
                          form.setValue('receivableStartDate', selectedReceivable.dueDate);
                          form.setValue('receivableEndDate', selectedReceivable.dueDate);

                          // Auto-detect payment status based on amount
                          const remainingAmount = selectedReceivable.remainingAmount || selectedReceivable.amount;
                          const paymentAmount = form.watch('amount') || 0;

                          if (paymentAmount >= remainingAmount) {
                            form.setValue('paymentStatus', 'كامل');
                          } else if (paymentAmount > 0) {
                            form.setValue('paymentStatus', 'جزئي');
                          }
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الاستحقاق" />
                      </SelectTrigger>
                      <SelectContent>
                        {contractReceivables.map((receivable) => (
                          <SelectItem key={receivable.id} value={receivable.id.toString()}>
                            <div className="flex flex-col">
                              <div className="flex justify-between items-center">
                                <span className="font-medium">
                                  قسط رقم {receivable.installmentNumber}
                                </span>
                                <Badge
                                  variant={
                                    receivable.status === 'مدفوع' ? 'default' :
                                    receivable.status === 'متأخر' ? 'destructive' :
                                    receivable.status === 'مستحق' ? 'secondary' : 'outline'
                                  }
                                  className="ml-2"
                                >
                                  {receivable.status}
                                </Badge>
                              </div>
                              <div className="text-sm text-muted-foreground">
                                <div>المبلغ: {formatCurrency(receivable.amount)}</div>
                                <div>تاريخ الاستحقاق: {formatDate(receivable.dueDate)}</div>
                                {receivable.remainingAmount > 0 && (
                                  <div className="text-orange-600">
                                    المتبقي: {formatCurrency(receivable.remainingAmount)}
                                  </div>
                                )}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="contractNumber">رقم العقد</Label>
                    <Input
                      id="contractNumber"
                      {...form.register('contractNumber')}
                      placeholder="أدخل رقم العقد"
                      readOnly
                    />
                  </div>

                  <div>
                    <Label htmlFor="clientName">اسم العميل</Label>
                    <Input
                      id="clientName"
                      {...form.register('clientName')}
                      placeholder="أدخل اسم العميل"
                      readOnly
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="contractSubject">موضوع العقد</Label>
                  <Input
                    id="contractSubject"
                    {...form.register('contractSubject')}
                    placeholder="أدخل موضوع العقد"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="receivableStartDate">تاريخ بداية الاستحقاق</Label>
                    <Input
                      id="receivableStartDate"
                      type="date"
                      {...form.register('receivableStartDate')}
                    />
                  </div>

                  <div>
                    <Label htmlFor="receivableEndDate">تاريخ نهاية الاستحقاق</Label>
                    <Input
                      id="receivableEndDate"
                      type="date"
                      {...form.register('receivableEndDate')}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="paymentStatus">حالة السداد</Label>
                  <Select
                    value={form.watch('paymentStatus') || ''}
                    onValueChange={(value) => form.setValue('paymentStatus', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر حالة السداد" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="كامل">سداد كامل</SelectItem>
                      <SelectItem value="جزئي">سداد جزئي</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="receivableDescription">بيان الاستحقاق</Label>
                  <Textarea
                    id="receivableDescription"
                    {...form.register('receivableDescription')}
                    placeholder="أدخل وصف الاستحقاق"
                    rows={2}
                  />
                </div>
              </div>
            )}

            {/* Non-contract fields */}
            {form.watch('paymentType') === 'غير مرتبطة بعقد' && (
              <div className="space-y-4 p-4 border rounded-lg bg-orange-50">
                <h3 className="font-medium text-orange-800">نوع المدفوعة</h3>

                <div>
                  <Label htmlFor="nonContractType">نوع المدفوعة *</Label>
                  <Select
                    value={form.watch('nonContractType') || ''}
                    onValueChange={(value) => form.setValue('nonContractType', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع المدفوعة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="رسوم تفاوض">رسوم تفاوض</SelectItem>
                      <SelectItem value="جدية عرض">جدية عرض</SelectItem>
                      <SelectItem value="تأمين ابتدائي">تأمين ابتدائي</SelectItem>
                      <SelectItem value="رسوم طلب">رسوم طلب</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {/* Notes */}
            <div>
              <Label htmlFor="notes">ملاحظات</Label>
              <Textarea
                id="notes"
                {...form.register('notes')}
                placeholder="أدخل أي ملاحظات إضافية"
                rows={3}
              />
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setDialogOpen(false)}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={createPaymentMutation.isPending || updatePaymentMutation.isPending}
              >
                {editingPayment ? 'تحديث الدفعة البنكية' : 'تسجيل الدفعة البنكية'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
