# نظام رسائل الأخطاء المحسن
## Enhanced Error System Documentation

تم تطوير نظام شامل ومحسن لإدارة وعرض رسائل الأخطاء في البرنامج بحيث تكون أوضح وأكثر فائدة للمستخدم.

## المكونات الرئيسية

### 1. قاموس رسائل الأخطاء (ErrorMessages.cjs)
يحتوي على رسائل أخطاء شاملة ومصنفة حسب النوع:

```javascript
const ErrorMessages = require('./errors/ErrorMessages.cjs');

// استخدام رسالة خطأ محددة
const errorInfo = ErrorMessages.getMessage('VALIDATION', 'CLIENT_ID_EXISTS');
```

**الفئات المتاحة:**
- `VALIDATION`: أخطاء التحقق من البيانات
- `DATABASE`: أخطاء قاعدة البيانات
- `NETWORK`: أخطاء الشبكة والاتصال
- `BUSINESS`: أخطاء منطق العمل
- `FILE`: أخطاء العمليات على الملفات

### 2. مكون عرض الأخطاء المحسن (EnhancedErrorDisplay)
مكون React لعرض الأخطاء بشكل جذاب ومفيد:

```tsx
import { EnhancedErrorDisplay } from '@/components/enhanced-error-display';

<EnhancedErrorDisplay
  error={errorInfo}
  onRetry={() => retryOperation()}
  onAction={(action) => handleAction(action)}
  onDismiss={() => dismissError()}
/>
```

**الميزات:**
- عرض مختلف حسب شدة الخطأ (critical, error, warning, info)
- أزرار إجراءات مقترحة
- إمكانية إعادة المحاولة
- عرض تفاصيل إضافية للمطورين

### 3. معالج الأخطاء المحسن (useEnhancedErrorHandler)
Hook لمعالجة الأخطاء بشكل ذكي:

```tsx
import { useEnhancedErrorHandler } from '@/hooks/use-enhanced-error-handler';

const { handleError, handleValidationError } = useEnhancedErrorHandler();

// معالجة خطأ عام
handleError(error, {
  operation: 'create_client',
  onRetry: () => retryOperation(),
  onAction: (action) => handleAction(action)
});

// معالجة خطأ تحقق من البيانات
handleValidationError('clientId', 'CLIENT_ID_EXISTS');
```

### 4. عرض أخطاء النماذج (FormErrorDisplay)
مكون خاص لعرض أخطاء النماذج:

```tsx
import { FormErrorDisplay, useFormErrors } from '@/components/form-error-display';

const { errors, addError, clearErrors } = useFormErrors();

<FormErrorDisplay
  errors={errors}
  onFieldFocus={(fieldName) => focusField(fieldName)}
  onDismiss={clearErrors}
/>
```

### 5. نظام المساعدة (ErrorHelpSystem)
مكون لعرض نصائح ومساعدة مع الأخطاء:

```tsx
import { ErrorHelpSystem } from '@/components/error-help-system';

<ErrorHelpSystem 
  errorCode="CLIENT_ID_EXISTS"
  operation="create_client"
/>
```

## كيفية الاستخدام

### 1. في الخادم (Server-side)
```javascript
// في معالج API
const ErrorMessages = require('./errors/ErrorMessages.cjs');
const { ValidationError } = require('./errors/CustomErrors.cjs');

// رمي خطأ محدد
if (existingClient) {
  throw new ValidationError(
    ErrorMessages.getMessage('VALIDATION', 'CLIENT_ID_EXISTS').message,
    { clientId: data.clientId }
  );
}
```

### 2. في العميل (Client-side)
```tsx
// في مكون React
import { useEnhancedErrorHandler } from '@/hooks/use-enhanced-error-handler';

const { handleError } = useEnhancedErrorHandler();

const mutation = useMutation({
  mutationFn: createClient,
  onError: (error) => {
    handleError(error, {
      operation: 'create_client',
      onRetry: () => mutation.mutate(data),
      onAction: (action) => {
        switch (action) {
          case 'search_client':
            navigate('/clients');
            break;
          case 'generate_id':
            form.setValue('clientId', generateNewId());
            break;
        }
      }
    });
  }
});
```

### 3. التحقق من البيانات المحسن
```tsx
// في النموذج
const { errors, addError, clearErrors } = useFormErrors();

const validateForm = (data) => {
  clearErrors();
  
  if (!data.clientId) {
    addError('clientId', 'رقم العميل مطلوب', 'أدخل رقم الهوية أو رقم مخصص');
  }
  
  if (!isValidEmail(data.email)) {
    addError('email', 'البريد الإلكتروني غير صحيح', 'مثال: <EMAIL>');
  }
};
```

## أنواع الأخطاء والإجراءات

### أخطاء التحقق من البيانات
- `CLIENT_ID_EXISTS`: رقم العميل موجود → البحث عن العميل أو إنشاء رقم جديد
- `CLIENT_PHONE_INVALID`: رقم هاتف غير صحيح → إرشادات التنسيق الصحيح
- `CLIENT_EMAIL_INVALID`: بريد إلكتروني غير صحيح → أمثلة على التنسيق الصحيح

### أخطاء قاعدة البيانات
- `DATABASE_BUSY`: قاعدة البيانات مشغولة → إعادة المحاولة
- `CONSTRAINT_VIOLATION`: انتهاك قيود البيانات → مراجعة البيانات
- `FOREIGN_KEY_VIOLATION`: مرجع غير صحيح → فحص البيانات المرتبطة

### أخطاء الشبكة
- `CONNECTION_FAILED`: فشل الاتصال → فحص الإنترنت وإعادة المحاولة
- `TIMEOUT`: انتهاء المهلة → إعادة المحاولة
- `SERVER_ERROR`: خطأ في الخادم → الاتصال بالدعم الفني

## الميزات المتقدمة

### 1. التصنيف حسب الشدة
- **Critical**: أخطاء حرجة تتطلب تدخل فوري
- **Error**: أخطاء تمنع إكمال العملية
- **Warning**: تحذيرات لا تمنع المتابعة
- **Info**: معلومات إضافية

### 2. الإجراءات المقترحة
- `search_client`: البحث عن عميل موجود
- `add_client`: إضافة عميل جديد
- `open_settings`: فتح صفحة الإعدادات
- `retry_now`: إعادة المحاولة فوراً
- `setup_reference_data`: إعداد البيانات المرجعية

### 3. المساعدة السياقية
- نصائح مخصصة لكل نوع خطأ
- خطوات الحل المفصلة
- أمثلة عملية
- روابط للمساعدة

## التخصيص والتوسيع

### إضافة رسائل خطأ جديدة
```javascript
// في ErrorMessages.cjs
static CUSTOM_CATEGORY = {
  NEW_ERROR_CODE: {
    message: 'رسالة الخطأ',
    guidance: 'إرشادات الحل',
    severity: 'error',
    action: 'suggested_action'
  }
};
```

### إضافة إجراءات جديدة
```tsx
// في المكون
const handleAction = (action: string) => {
  switch (action) {
    case 'custom_action':
      // تنفيذ الإجراء المخصص
      break;
  }
};
```

## أفضل الممارسات

1. **استخدم رسائل واضحة ومفهومة** باللغة العربية
2. **قدم إرشادات عملية** لحل المشكلة
3. **صنف الأخطاء حسب الشدة** المناسبة
4. **اقترح إجراءات مفيدة** للمستخدم
5. **اختبر رسائل الأخطاء** مع المستخدمين الفعليين

## الصيانة والتطوير

- راجع رسائل الأخطاء دورياً لضمان وضوحها
- أضف رسائل جديدة عند إضافة ميزات جديدة
- اجمع ملاحظات المستخدمين لتحسين الرسائل
- حدث الترجمات عند الحاجة

هذا النظام يوفر تجربة مستخدم محسنة ويساعد في تقليل الأخطاء وتحسين كفاءة استخدام البرنامج.
