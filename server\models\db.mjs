import sqlite3 from 'sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// إنشاء اتصال مباشر بقاعدة البيانات
const dbPath = path.join(__dirname, '../../contract-app.sqlite');
let db = null;
let isConnected = false;

// دالة الاتصال بقاعدة البيانات
async function connect(retries = 3) {
  return new Promise((resolve, reject) => {
    db = new sqlite3.Database(dbPath, sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE, (err) => {
      if (err) {
        console.error('❌ Database connection error:', err.message);
        if (retries > 0) {
          setTimeout(() => {
            connect(retries - 1).then(resolve).catch(reject);
          }, 1000);
        } else {
          reject(err);
        }
      } else {
        isConnected = true;
        console.log('✅ Connected to SQLite database');
        resolve(db);
      }
    });
  });
}

// دالة إغلاق الاتصال
async function close() {
  return new Promise((resolve, reject) => {
    if (db && isConnected) {
      db.close((err) => {
        if (err) {
          console.error('❌ Error closing database:', err.message);
          reject(err);
        } else {
          isConnected = false;
          console.log('✅ Database connection closed');
          resolve();
        }
      });
    } else {
      resolve();
    }
  });
}

// دالة تنفيذ استعلام
function run(sql, params = []) {
  return new Promise((resolve, reject) => {
    if (!db || !isConnected) {
      reject(new Error('Database not connected'));
      return;
    }
    
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ lastID: this.lastID, changes: this.changes });
      }
    });
  });
}

// دالة الحصول على صف واحد
function get(sql, params = []) {
  return new Promise((resolve, reject) => {
    if (!db || !isConnected) {
      reject(new Error('Database not connected'));
      return;
    }
    
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// دالة الحصول على عدة صفوف
function all(sql, params = []) {
  return new Promise((resolve, reject) => {
    if (!db || !isConnected) {
      reject(new Error('Database not connected'));
      return;
    }
    
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// تصدير الوحدة
export default {
  connect,
  close,
  run,
  get,
  all,
  getDb: () => db,
  isConnected: () => isConnected
};
