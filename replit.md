# Contract Management System - Project Documentation

## Overview

This is a comprehensive contract management system built for Arabic-speaking users, featuring a modern React frontend with Express.js backend. The application manages clients, contracts, payments, and provides financial reporting capabilities with full bilingual support (Arabic/English).

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **UI Framework**: Shadcn/ui components with Radix UI primitives
- **Styling**: Tailwind CSS with CSS variables for theming
- **State Management**: TanStack React Query for server state
- **Routing**: Wouter for lightweight client-side routing
- **Forms**: React Hook Form with Zod validation
- **Internationalization**: Custom i18n implementation supporting Arabic (RTL) and English

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ESM modules
- **Database**: PostgreSQL with Drizzle ORM
- **Database Provider**: Neon Database (serverless PostgreSQL)
- **Validation**: Zod schemas shared between frontend and backend
- **Session Management**: Express sessions with PostgreSQL storage

### Database Design
- **ORM**: Drizzle with TypeScript-first approach
- **Schema**: Centralized in `shared/schema.ts` for type safety
- **Main Entities**:
  - Clients (individuals and companies)
  - Contracts with comprehensive metadata
  - Payments with installment tracking
  - Reference data for dropdowns
  - System settings

## Key Components

### Core Features
1. **Client Management**: Complete CRUD operations for individuals and companies
2. **Contract Management**: Contract lifecycle with status tracking
3. **Payment Tracking**: Installment management with overdue notifications
4. **Financial Reporting**: Dashboard with analytics and export capabilities
5. **Receivables Management**: Automated tracking of outstanding payments
6. **Reference Data**: Configurable dropdown values and categories
7. **User Management**: Role-based access control (planned)
8. **Settings**: Company and system configuration

### UI Components
- Responsive design with mobile-first approach
- Dark/light theme support
- RTL layout for Arabic content
- Accessibility-compliant components
- Form validation with real-time feedback
- Loading states and error handling

### Data Validation
- Shared Zod schemas between client and server
- Type-safe API communication
- Input sanitization and validation
- Error handling with user-friendly messages

## Data Flow

### Client-Server Communication
1. Frontend makes API requests through React Query
2. Express routes validate requests using Zod schemas
3. Storage layer interfaces with PostgreSQL via Drizzle
4. Responses include proper error handling and status codes
5. Frontend updates UI reactively based on server state

### State Management
- Server state managed by TanStack React Query
- Local UI state handled by React hooks
- Form state managed by React Hook Form
- Theme and language preferences stored in localStorage

## External Dependencies

### Frontend Dependencies
- **UI Components**: Radix UI primitives, Lucide icons
- **Styling**: Tailwind CSS, class-variance-authority
- **Forms**: React Hook Form, Hookform resolvers
- **Data Fetching**: TanStack React Query
- **Date Handling**: date-fns
- **Build Tools**: Vite, TypeScript, PostCSS

### Backend Dependencies
- **Framework**: Express.js with TypeScript
- **Database**: Drizzle ORM, @neondatabase/serverless
- **Validation**: Zod
- **Session**: connect-pg-simple
- **Development**: tsx for TypeScript execution

## Deployment Strategy

### Development Environment
- **Runtime**: Replit with Node.js 20
- **Database**: PostgreSQL 16 on Replit
- **Development Server**: Vite dev server with HMR
- **API Server**: Express with tsx for TypeScript execution

### Production Build
- **Frontend**: Vite build output to `dist/public`
- **Backend**: esbuild bundles server to `dist/index.js`
- **Database**: Migrations managed by Drizzle Kit
- **Deployment**: Replit autoscale with port 5000

### Environment Configuration
- Database connection via `DATABASE_URL` environment variable
- Development/production modes handled automatically
- Static file serving through Express in production

## Changelog
- June 24, 2025. Initial setup

## User Preferences

Preferred communication style: Simple, everyday language.