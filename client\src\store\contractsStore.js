// ===== CONTRACTS STORE =====
// Author: Augment Code
// Description: Centralized state management for contracts using Zustand

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import apiMiddleware from '../lib/apiMiddleware';

// Initial state
const initialState = {
  // Contracts data
  contracts: [],
  selectedContract: null,
  
  // Search and filters
  searchQuery: '',
  statusFilter: '',
  clientFilter: '',
  contractTypeFilter: '',
  dateRangeFilter: { start: null, end: null },
  
  // Loading states
  isLoading: false,
  isAdding: false,
  isDeleting: false,
  isGeneratingPDF: false,

  // Error states
  error: null,
  addError: null,
  deleteError: null,
  
  // Cache and metadata
  lastFetched: null,
  totalCount: 0,
  
  // UI state
  showContractDetails: false,
  
  // Contract components and calculations
  contractComponents: [],
  contractCalculations: null,
  calculationsLoading: false,
  calculationsError: null
};

// Create the contracts store
export const useContractsStore = create()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Actions
        actions: {
          // Fetch all contracts
          async fetchContracts(forceRefresh = false) {
            const state = get();
            
            if (state.isLoading || (!forceRefresh && state.lastFetched && 
                Date.now() - new Date(state.lastFetched).getTime() < 30000)) {
              return state.contracts;
            }

            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const contracts = await apiMiddleware.get('/api/contracts', {
                loadingMessage: 'جاري تحميل العقود...',
                showErrorNotification: true
              });

              set((state) => {
                state.contracts = contracts || [];
                state.totalCount = contracts?.length || 0;
                state.isLoading = false;
                state.lastFetched = new Date().toISOString();
                state.error = null;
              });

              return contracts;
            } catch (error) {
              console.error('Failed to fetch contracts:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحميل العقود';
              });

              throw error;
            }
          },

          // Search contracts
          async searchContracts(query) {
            set((state) => {
              state.searchQuery = query;
              state.isLoading = true;
              state.error = null;
            });

            try {
              const url = query
                ? `/api/contracts/search?q=${encodeURIComponent(query)}`
                : '/api/contracts';

              const contracts = await apiMiddleware.get(url, {
                loadingMessage: query ? 'جاري البحث...' : 'جاري تحميل العقود...',
                showErrorNotification: true
              });

              set((state) => {
                state.contracts = contracts || [];
                state.totalCount = contracts?.length || 0;
                state.isLoading = false;
                state.error = null;
              });

              return contracts;
            } catch (error) {
              console.error('Failed to search contracts:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في البحث عن العقود';
              });

              throw error;
            }
          },

          // Add new contract
          async addContract(contractData) {
            set((state) => {
              state.isAdding = true;
              state.addError = null;
            });

            try {
              const newContract = await apiMiddleware.post('/api/contracts', contractData, {
                loadingMessage: 'جاري إضافة العقد...',
                successMessage: 'تم إضافة العقد بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.contracts.unshift(newContract);
                state.totalCount += 1;
                state.isAdding = false;
                state.addError = null;
              });

              return { success: true, data: newContract };
            } catch (error) {
              console.error('Failed to add contract:', error);
              
              set((state) => {
                state.isAdding = false;
                state.addError = error.message || 'فشل في إضافة العقد';
              });

              return { success: false, error: error.message };
            }
          },



          // Delete contract
          async deleteContract(contractId) {
            set((state) => {
              state.isDeleting = true;
              state.deleteError = null;
            });

            try {
              await apiMiddleware.delete(`/api/contracts/${contractId}`, {
                loadingMessage: 'جاري حذف العقد...',
                successMessage: 'تم حذف العقد بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.contracts = state.contracts.filter(contract => contract.id !== contractId);
                state.totalCount -= 1;
                
                if (state.selectedContract?.id === contractId) {
                  state.selectedContract = null;
                  state.showContractDetails = false;
                  state.contractComponents = [];
                }
                
                state.isDeleting = false;
                state.deleteError = null;
              });

              return { success: true };
            } catch (error) {
              console.error('Failed to delete contract:', error);
              
              set((state) => {
                state.isDeleting = false;
                state.deleteError = error.message || 'فشل في حذف العقد';
              });

              return { success: false, error: error.message };
            }
          },

          // Generate contract PDF
          async generateContractPDF(contractId) {
            set((state) => {
              state.isGeneratingPDF = true;
            });

            try {
              const pdfData = await apiMiddleware.get(`/api/contracts/${contractId}/pdf`, {
                loadingMessage: 'جاري إنشاء ملف PDF...',
                successMessage: 'تم إنشاء ملف PDF بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.isGeneratingPDF = false;
              });

              return { success: true, data: pdfData };
            } catch (error) {
              console.error('Failed to generate PDF:', error);
              
              set((state) => {
                state.isGeneratingPDF = false;
              });

              return { success: false, error: error.message };
            }
          },

          // Fetch contract components
          async fetchContractComponents(contractId) {
            if (!contractId) return;

            set((state) => {
              state.calculationsLoading = true;
              state.calculationsError = null;
            });

            try {
              const components = await apiMiddleware.get(`/api/contracts/${contractId}/components`, {
                showErrorNotification: false
              });

              set((state) => {
                state.contractComponents = components || [];
                state.calculationsLoading = false;
                state.calculationsError = null;
              });

              return components;
            } catch (error) {
              console.error('Failed to fetch contract components:', error);
              
              set((state) => {
                state.contractComponents = [];
                state.calculationsLoading = false;
                state.calculationsError = error.message;
              });

              return [];
            }
          },

          // Calculate contract totals
          async calculateContractTotals(contractId) {
            if (!contractId) return;

            set((state) => {
              state.calculationsLoading = true;
              state.calculationsError = null;
            });

            try {
              const calculations = await apiMiddleware.get(`/api/contracts/${contractId}/calculations`, {
                showErrorNotification: false
              });

              set((state) => {
                state.contractCalculations = calculations;
                state.calculationsLoading = false;
                state.calculationsError = null;
              });

              return calculations;
            } catch (error) {
              console.error('Failed to calculate contract totals:', error);
              
              set((state) => {
                state.contractCalculations = null;
                state.calculationsLoading = false;
                state.calculationsError = error.message;
              });

              return null;
            }
          },

          // Set selected contract
          setSelectedContract(contract) {
            set((state) => {
              state.selectedContract = contract;
              state.showContractDetails = !!contract;
              
              if (contract) {
                state.contractComponents = [];
                state.contractCalculations = null;
                state.calculationsError = null;
              }
            });

            if (contract?.id) {
              get().actions.fetchContractComponents(contract.id);
              get().actions.calculateContractTotals(contract.id);
            }
          },

          // Set filters
          setSearchQuery(query) {
            set((state) => {
              state.searchQuery = query;
            });
          },

          setStatusFilter(status) {
            set((state) => {
              state.statusFilter = status;
            });
          },

          setClientFilter(clientId) {
            set((state) => {
              state.clientFilter = clientId;
            });
          },

          setContractTypeFilter(type) {
            set((state) => {
              state.contractTypeFilter = type;
            });
          },

          setDateRangeFilter(dateRange) {
            set((state) => {
              state.dateRangeFilter = dateRange;
            });
          },

          // Clear errors
          clearErrors() {
            set((state) => {
              state.error = null;
              state.addError = null;
              state.updateError = null;
              state.deleteError = null;
              state.calculationsError = null;
            });
          },

          // Get filtered contracts
          getFilteredContracts() {
            const state = get();
            const { 
              contracts, 
              searchQuery, 
              statusFilter, 
              clientFilter, 
              contractTypeFilter,
              dateRangeFilter 
            } = state;

            return contracts.filter(contract => {
              // Search filter
              const matchesSearch = !searchQuery ||
                contract.contractName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                contract.contractNumber?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                contract.clientName?.toLowerCase().includes(searchQuery.toLowerCase());

              // Status filter
              const matchesStatus = !statusFilter || statusFilter === "all" ||
                contract.contractStatus === statusFilter;

              // Client filter
              const matchesClient = !clientFilter || clientFilter === "all" ||
                contract.clientId === clientFilter;

              // Contract type filter
              const matchesType = !contractTypeFilter || contractTypeFilter === "all" ||
                contract.contractType === contractTypeFilter;

              // Date range filter
              const matchesDateRange = !dateRangeFilter.start || !dateRangeFilter.end ||
                (new Date(contract.contractStartDate) >= new Date(dateRangeFilter.start) &&
                 new Date(contract.contractStartDate) <= new Date(dateRangeFilter.end));

              return matchesSearch && matchesStatus && matchesClient && matchesType && matchesDateRange;
            });
          },

          // Get contract by ID (from loaded contracts)
          getContractById(contractId) {
            const state = get();
            return state.contracts.find(contract => contract.id === contractId);
          },

          // Fetch contract by ID with all details
          async fetchContractById(contractId, forceRefresh = false) {
            const state = get();

            // Check if contract is already loaded and not forcing refresh
            if (!forceRefresh && state.selectedContract?.id === contractId) {
              return state.selectedContract;
            }

            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              // جلب بيانات العقد الأساسية
              const contract = await apiMiddleware.get(`/api/contracts/${contractId}`, {
                loadingMessage: 'جاري تحميل تفاصيل العقد...',
                showErrorNotification: true
              });

              // جلب التفاصيل المرتبطة بالتوازي
              const [
                products,
                partners,
                attachments,
                installments,
                receivables,
                timeline
              ] = await Promise.all([
                apiMiddleware.get(`/api/contracts/${contractId}/products`).catch(() => []),
                apiMiddleware.get(`/api/contracts/${contractId}/partners`).catch(() => []),
                apiMiddleware.get(`/api/contracts/${contractId}/attachments`).catch(() => []),
                apiMiddleware.get(`/api/contracts/${contractId}/installments`).catch(() => []),
                apiMiddleware.get(`/api/contracts/${contractId}/receivables`).catch(() => []),
                apiMiddleware.get(`/api/contracts/${contractId}/timeline`).catch(() => [])
              ]);

              const fullContract = {
                ...contract,
                products,
                partners,
                attachments,
                installments,
                receivables,
                timeline
              };

              set((state) => {
                state.selectedContract = fullContract;
                state.isLoading = false;
                state.error = null;

                // Update contract in contracts list if it exists
                const contractIndex = state.contracts.findIndex(c => c.id === contractId);
                if (contractIndex !== -1) {
                  state.contracts[contractIndex] = fullContract;
                }
              });

              return fullContract;
            } catch (error) {
              console.error('Failed to fetch contract:', error);

              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحميل تفاصيل العقد';
                state.selectedContract = null;
              });

              throw error;
            }
          },

          // Clear selected contract
          clearSelectedContract() {
            set((state) => {
              state.selectedContract = null;
            });
          },

          // Refresh data
          async refresh() {
            return get().actions.fetchContracts(true);
          }
        }
      })),
      {
        name: 'contracts-store',
        partialize: (state) => ({
          contracts: state.contracts,
          lastFetched: state.lastFetched,
          searchQuery: state.searchQuery,
          statusFilter: state.statusFilter,
          clientFilter: state.clientFilter,
          contractTypeFilter: state.contractTypeFilter
        })
      }
    ),
    {
      name: 'contracts-store'
    }
  )
);

// Selectors
export const useContracts = () => useContractsStore((state) => state.contracts);
export const useContractsLoading = () => useContractsStore((state) => state.isLoading);
export const useContractsError = () => useContractsStore((state) => state.error);
export const useSelectedContract = () => useContractsStore((state) => state.selectedContract);
export const useContractComponents = () => useContractsStore((state) => state.contractComponents);
export const useContractCalculations = () => useContractsStore((state) => state.contractCalculations);
export const useContractsActions = () => useContractsStore((state) => state.actions);

export const useFilteredContracts = () => useContractsStore((state) => 
  state.actions.getFilteredContracts()
);

export const useContractsStatus = () => useContractsStore((state) => ({
  isLoading: state.isLoading,
  isAdding: state.isAdding,
  isUpdating: state.isUpdating,
  isDeleting: state.isDeleting,
  isGeneratingPDF: state.isGeneratingPDF,
  calculationsLoading: state.calculationsLoading,
  error: state.error,
  addError: state.addError,
  updateError: state.updateError,
  deleteError: state.deleteError,
  calculationsError: state.calculationsError,
  totalCount: state.totalCount,
  lastFetched: state.lastFetched
}));

export default useContractsStore;
