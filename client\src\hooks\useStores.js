// ===== CUSTOM HOOKS FOR STORES =====
// Author: Augment Code
// Description: Custom hooks for easy interaction with Zustand stores

import { useCallback, useEffect } from 'react';
import { 
  useSettingsStore, 
  useSettings, 
  useSettingsActions, 
  useSettingsStatus,
  useIsSettingsReady 
} from '../store/settingsStore';
import { 
  useReferenceDataStore, 
  useReferenceActions, 
  useListItems, 
  useListLoading, 
  useListError,
  useIsReferenceInitialized 
} from '../store/referenceDataStore';
import {
  useAppStore,
  useAppActions,
  useIsLoading,
  useNotifications
} from '../store/appStore';
import {
  useClientsStore,
  useClients,
  useClientsLoading,
  useClientsError,
  useSelectedClient,
  useShowClientCard,
  useClientContracts,
  useClientsActions,
  useClientsFilters,
  useFilteredClients,
  useClientsStatus
} from '../store/clientsStore';
import {
  useContractsStore,
  useContracts,
  useContractsLoading,
  useContractsError,
  useSelectedContract,
  useContractComponents,
  useContractCalculations,
  useContractsActions,
  useFilteredContracts,
  useContractsStatus
} from '../store/contractsStore';
import {
  usePaymentsStore,
  usePayments,
  useTreasuryPayments,
  useBankPayments,
  useCashReceipts,
  useChequeReceipts,
  usePaymentsLoading,
  usePaymentsError,
  useSelectedPayment,
  usePaymentStats,
  usePaymentsActions,
  useFilteredPayments,
  usePaymentsStatus
} from '../store/paymentsStore';
import {
  useReceivablesStore,
  useReceivables,
  useOverdueReceivables,
  useUpcomingReceivables,
  usePaidReceivables,
  useReceivablesLoading,
  useReceivablesError,
  useSelectedReceivable,
  useReceivablesStats,
  useReceivablesActions,
  useFilteredReceivables,
  useReceivablesStatus
} from '../store/receivablesStore';
import {
  useChequesStore,
  useCheques,
  useTreasuryCheques,
  useCustodyCheques,
  useCollectionCheques,
  useCollectedCheques,
  useBouncedCheques,
  useChequesLoading,
  useChequesError,
  useSelectedCheque,
  useChequeStats,
  useChequesActions,
  useFilteredCheques,
  useChequesStatus
} from '../store/chequesStore';
import {
  useUsersStore,
  useUsers,
  useCurrentUser,
  useUsersLoading,
  useUsersError,
  useSelectedUser,
  useUserPermissions,
  useAvailableRoles,
  useUsersActions,
  useFilteredUsers,
  useUsersStatus
} from '../store/usersStore';
import {
  useReportsStore,
  useReports,
  useDashboardStats,
  useFinancialReports,
  useContractReports,
  useClientReports,
  usePaymentReports,
  useReportsLoading,
  useReportsError,
  useSelectedReport,
  useReportConfigs,
  useReportsActions,
  useFilteredReports,
  useReportsStatus
} from '../store/reportsStore';

// ===== SETTINGS HOOKS =====

/**
 * Hook for managing settings with automatic initialization
 */
export const useSettingsManager = () => {
  const settings = useSettings();
  const actions = useSettingsActions();
  const status = useSettingsStatus();
  const isReady = useIsSettingsReady();

  // Auto-initialize on mount
  useEffect(() => {
    if (!status.isInitialized && !status.isLoading) {
      actions.initialize();
    }
  }, [status.isInitialized, status.isLoading, actions]);

  const updateSetting = useCallback((key, value) => {
    actions.updateSetting(key, value);
  }, [actions]);

  const saveSettings = useCallback(async () => {
    try {
      await actions.saveSettings();
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const resetSettings = useCallback(() => {
    actions.resetSettings();
  }, [actions]);

  return {
    settings,
    status,
    isReady,
    updateSetting,
    saveSettings,
    resetSettings,
    actions
  };
};

/**
 * Hook for getting a specific setting with default value
 */
export const useSetting = (key, defaultValue = null) => {
  const settings = useSettings();
  return settings[key] ?? defaultValue;
};

/**
 * Hook for managing settings form state
 */
export const useSettingsForm = () => {
  const { settings, updateSetting, saveSettings, status } = useSettingsManager();

  const handleFieldChange = useCallback((field, value) => {
    updateSetting(field, value);
  }, [updateSetting]);

  const handleSubmit = useCallback(async (e) => {
    e?.preventDefault();
    return await saveSettings();
  }, [saveSettings]);

  return {
    settings,
    isLoading: status.isLoading,
    isSaving: status.isSaving,
    hasUnsavedChanges: status.hasUnsavedChanges,
    error: status.error || status.saveError,
    handleFieldChange,
    handleSubmit
  };
};

// ===== REFERENCE DATA HOOKS =====

/**
 * Hook for managing reference data with automatic initialization
 */
export const useReferenceManager = () => {
  const actions = useReferenceActions();
  const isInitialized = useIsReferenceInitialized();

  // Auto-initialize on mount
  useEffect(() => {
    if (!isInitialized) {
      actions.initialize();
    }
  }, [isInitialized, actions]);

  return {
    actions,
    isInitialized
  };
};

/**
 * Hook for managing a specific reference list
 */
export const useReferenceList = (listName) => {
  const items = useListItems(listName);
  const isLoading = useListLoading(listName);
  const error = useListError(listName);
  const actions = useReferenceActions();

  // Auto-fetch list data if not loaded
  useEffect(() => {
    if (!items.length && !isLoading && !error) {
      actions.fetchListData(listName);
    }
  }, [listName, items.length, isLoading, error, actions]);

  const addItem = useCallback(async (item) => {
    try {
      const newItem = await actions.addItem(listName, item);
      return { success: true, data: newItem };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [listName, actions]);

  const updateItem = useCallback(async (itemId, updates) => {
    try {
      const updatedItem = await actions.updateItem(listName, itemId, updates);
      return { success: true, data: updatedItem };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [listName, actions]);

  const deleteItem = useCallback(async (itemId) => {
    try {
      await actions.deleteItem(listName, itemId);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [listName, actions]);

  const refreshList = useCallback(() => {
    return actions.refreshList(listName);
  }, [listName, actions]);

  return {
    items,
    isLoading,
    error,
    addItem,
    updateItem,
    deleteItem,
    refreshList
  };
};

/**
 * Hook for searching in reference lists
 */
export const useReferenceSearch = (listName, searchTerm = '') => {
  const actions = useReferenceActions();
  
  const searchResults = useCallback(() => {
    return actions.searchItems(listName, searchTerm);
  }, [listName, searchTerm, actions]);

  return {
    results: searchResults(),
    search: (term) => actions.searchItems(listName, term)
  };
};

// ===== APP STATE HOOKS =====

/**
 * Hook for managing notifications
 */
export const useNotificationManager = () => {
  const notifications = useNotifications();
  const actions = useAppActions();

  const showSuccess = useCallback((message, options) => {
    return actions.showSuccess(message, options);
  }, [actions]);

  const showError = useCallback((message, options) => {
    return actions.showError(message, options);
  }, [actions]);

  const showWarning = useCallback((message, options) => {
    return actions.showWarning(message, options);
  }, [actions]);

  const showInfo = useCallback((message, options) => {
    return actions.showInfo(message, options);
  }, [actions]);

  const removeNotification = useCallback((id) => {
    actions.removeNotification(id);
  }, [actions]);

  const clearAll = useCallback(() => {
    actions.clearAllNotifications();
  }, [actions]);

  return {
    notifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    removeNotification,
    clearAll
  };
};

/**
 * Hook for managing loading states
 */
export const useLoadingManager = () => {
  const isLoading = useIsLoading();
  const actions = useAppActions();

  const setLoading = useCallback((loading, message = '') => {
    actions.setLoading(loading, message);
  }, [actions]);

  const withLoading = useCallback(async (operation, message = 'جاري التحميل...') => {
    const operationId = `operation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      actions.addLoadingOperation(operationId, message);
      const result = await operation();
      return result;
    } finally {
      actions.removeLoadingOperation(operationId);
    }
  }, [actions]);

  return {
    isLoading,
    setLoading,
    withLoading
  };
};

/**
 * Hook for API calls with automatic loading and error handling
 */
export const useApiCall = () => {
  const { withLoading } = useLoadingManager();
  const { showError, showSuccess } = useNotificationManager();

  const apiCall = useCallback(async (
    operation, 
    {
      loadingMessage = 'جاري التحميل...',
      successMessage = null,
      errorMessage = null,
      showSuccessNotification = false,
      showErrorNotification = true
    } = {}
  ) => {
    try {
      const result = await withLoading(operation, loadingMessage);
      
      if (showSuccessNotification && successMessage) {
        showSuccess(successMessage);
      }
      
      return { success: true, data: result };
    } catch (error) {
      const message = errorMessage || error.message || 'حدث خطأ غير متوقع';
      
      if (showErrorNotification) {
        showError(message);
      }
      
      return { success: false, error: message };
    }
  }, [withLoading, showError, showSuccess]);

  return { apiCall };
};

/**
 * Hook for form submission with loading and error handling
 */
export const useFormSubmit = () => {
  const { apiCall } = useApiCall();

  const submitForm = useCallback(async (
    submitFunction,
    {
      loadingMessage = 'جاري الحفظ...',
      successMessage = 'تم الحفظ بنجاح',
      errorMessage = null
    } = {}
  ) => {
    return apiCall(submitFunction, {
      loadingMessage,
      successMessage,
      errorMessage,
      showSuccessNotification: true,
      showErrorNotification: true
    });
  }, [apiCall]);

  return { submitForm };
};

// ===== CLIENTS HOOKS =====

/**
 * Hook for managing clients with automatic initialization
 */
export const useClientsManager = () => {
  const clients = useClients();
  const actions = useClientsActions();
  const status = useClientsStatus();
  const filteredClients = useFilteredClients();

  // Auto-fetch clients on mount
  useEffect(() => {
    if (clients.length === 0 && !status.isLoading && !status.error) {
      actions.fetchClients();
    }
  }, [clients.length, status.isLoading, status.error, actions]);

  const addClient = useCallback(async (clientData) => {
    try {
      const result = await actions.addClient(clientData);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const updateClient = useCallback(async (clientId, updates) => {
    try {
      const result = await actions.updateClient(clientId, updates);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const deleteClient = useCallback(async (clientId) => {
    try {
      // Check if client can be deleted first
      const canDelete = await actions.checkClientDeletion(clientId);
      if (!canDelete.canDelete) {
        return { success: false, error: canDelete.reason || 'لا يمكن حذف هذا العميل' };
      }

      const result = await actions.deleteClient(clientId);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const searchClients = useCallback((query) => {
    return actions.searchClients(query);
  }, [actions]);

  const refreshClients = useCallback(() => {
    return actions.refresh();
  }, [actions]);

  return {
    clients,
    filteredClients,
    status,
    addClient,
    updateClient,
    deleteClient,
    searchClients,
    refreshClients,
    actions
  };
};

/**
 * Hook for managing client selection and details
 */
export const useClientSelection = () => {
  const selectedClient = useSelectedClient();
  const showClientCard = useShowClientCard();
  const clientContracts = useClientContracts();
  const actions = useClientsActions();
  const status = useClientsStatus();

  const selectClient = useCallback((client) => {
    actions.setSelectedClient(client);
  }, [actions]);

  const clearSelection = useCallback(() => {
    actions.setSelectedClient(null);
  }, [actions]);

  const toggleClientCard = useCallback(() => {
    actions.toggleClientCard();
  }, [actions]);

  return {
    selectedClient,
    showClientCard,
    clientContracts,
    contractsLoading: status.contractsLoading,
    contractsError: status.contractsError,
    selectClient,
    clearSelection,
    toggleClientCard
  };
};

/**
 * Hook for managing client filters and search
 */
export const useClientsFiltersManager = () => {
  const filters = useClientsFilters();
  const actions = useClientsActions();

  const setSearchQuery = useCallback((query) => {
    actions.setSearchQuery(query);
    // Trigger search after a short delay
    const timeoutId = setTimeout(() => {
      actions.searchClients(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [actions]);

  const setClientTypeFilter = useCallback((filter) => {
    actions.setClientTypeFilter(filter);
  }, [actions]);

  const setFinancialCategoryFilter = useCallback((filter) => {
    actions.setFinancialCategoryFilter(filter);
  }, [actions]);

  const clearFilters = useCallback(() => {
    actions.setSearchQuery('');
    actions.setClientTypeFilter('');
    actions.setFinancialCategoryFilter('');
  }, [actions]);

  return {
    ...filters,
    setSearchQuery,
    setClientTypeFilter,
    setFinancialCategoryFilter,
    clearFilters
  };
};

// ===== CONTRACTS HOOKS =====

/**
 * Hook for managing contracts with automatic initialization
 */
export const useContractsManager = () => {
  const contracts = useContracts();
  const actions = useContractsActions();
  const status = useContractsStatus();
  const filteredContracts = useFilteredContracts();

  useEffect(() => {
    if (contracts.length === 0 && !status.isLoading && !status.error) {
      actions.fetchContracts();
    }
  }, [contracts.length, status.isLoading, status.error, actions]);

  const addContract = useCallback(async (contractData) => {
    try {
      const result = await actions.addContract(contractData);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const updateContract = useCallback(async (contractId, updates) => {
    try {
      const result = await actions.updateContract(contractId, updates);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const deleteContract = useCallback(async (contractId) => {
    try {
      const result = await actions.deleteContract(contractId);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const generatePDF = useCallback(async (contractId) => {
    try {
      const result = await actions.generateContractPDF(contractId);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  return {
    contracts,
    filteredContracts,
    status,
    addContract,
    updateContract,
    deleteContract,
    generatePDF,
    actions
  };
};

/**
 * Hook for managing contract selection and details
 */
export const useContractSelection = () => {
  const selectedContract = useSelectedContract();
  const contractComponents = useContractComponents();
  const contractCalculations = useContractCalculations();
  const actions = useContractsActions();
  const status = useContractsStatus();

  const selectContract = useCallback((contract) => {
    actions.setSelectedContract(contract);
  }, [actions]);

  const clearSelection = useCallback(() => {
    actions.setSelectedContract(null);
  }, [actions]);

  return {
    selectedContract,
    contractComponents,
    contractCalculations,
    calculationsLoading: status.calculationsLoading,
    calculationsError: status.calculationsError,
    selectContract,
    clearSelection
  };
};

// ===== PAYMENTS HOOKS =====

/**
 * Hook for managing payments with automatic initialization
 */
export const usePaymentsManager = () => {
  const payments = usePayments();
  const actions = usePaymentsActions();
  const status = usePaymentsStatus();
  const filteredPayments = useFilteredPayments();

  useEffect(() => {
    if (payments.length === 0 && !status.isLoading && !status.error) {
      actions.fetchPayments();
    }
  }, [payments.length, status.isLoading, status.error, actions]);

  const addPayment = useCallback(async (paymentData) => {
    try {
      const result = await actions.addPayment(paymentData);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const updatePayment = useCallback(async (paymentId, updates) => {
    try {
      const result = await actions.updatePayment(paymentId, updates);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const deletePayment = useCallback(async (paymentId) => {
    try {
      const result = await actions.deletePayment(paymentId);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const printReceipt = useCallback(async (paymentId) => {
    try {
      const result = await actions.printPaymentReceipt(paymentId);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  return {
    payments,
    filteredPayments,
    status,
    addPayment,
    updatePayment,
    deletePayment,
    printReceipt,
    actions
  };
};

/**
 * Hook for managing payment types
 */
export const usePaymentTypes = () => {
  const treasuryPayments = useTreasuryPayments();
  const bankPayments = useBankPayments();
  const cashReceipts = useCashReceipts();
  const chequeReceipts = useChequeReceipts();
  const actions = usePaymentsActions();

  const fetchByType = useCallback(async (paymentType) => {
    try {
      const result = await actions.fetchPaymentsByType(paymentType);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  return {
    treasuryPayments,
    bankPayments,
    cashReceipts,
    chequeReceipts,
    fetchByType
  };
};

// ===== RECEIVABLES HOOKS =====

/**
 * Hook for managing receivables with automatic initialization
 */
export const useReceivablesManager = () => {
  const receivables = useReceivables();
  const actions = useReceivablesActions();
  const status = useReceivablesStatus();
  const filteredReceivables = useFilteredReceivables();

  useEffect(() => {
    if (receivables.length === 0 && !status.isLoading && !status.error) {
      actions.fetchReceivables();
    }
  }, [receivables.length, status.isLoading, status.error, actions]);

  const updateStatus = useCallback(async (receivableId, status, paymentData) => {
    try {
      const result = await actions.updateReceivableStatus(receivableId, status, paymentData);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const markAsPaid = useCallback(async (receivableId, paymentData) => {
    try {
      const result = await actions.markReceivableAsPaid(receivableId, paymentData);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const generateInvoice = useCallback(async (receivableId) => {
    try {
      const result = await actions.generateInvoice(receivableId);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  return {
    receivables,
    filteredReceivables,
    status,
    updateStatus,
    markAsPaid,
    generateInvoice,
    actions
  };
};

/**
 * Hook for receivables by status
 */
export const useReceivablesByStatus = () => {
  const overdueReceivables = useOverdueReceivables();
  const upcomingReceivables = useUpcomingReceivables();
  const paidReceivables = usePaidReceivables();
  const actions = useReceivablesActions();

  const fetchByStatus = useCallback(async (status) => {
    try {
      const result = await actions.fetchReceivablesByStatus(status);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  return {
    overdueReceivables,
    upcomingReceivables,
    paidReceivables,
    fetchByStatus
  };
};

// ===== CHEQUES HOOKS =====

/**
 * Hook for managing cheques with automatic initialization
 */
export const useChequesManager = () => {
  const cheques = useCheques();
  const actions = useChequesActions();
  const status = useChequesStatus();
  const filteredCheques = useFilteredCheques();

  useEffect(() => {
    if (cheques.length === 0 && !status.isLoading && !status.error) {
      actions.fetchCheques();
    }
  }, [cheques.length, status.isLoading, status.error, actions]);

  const addCheque = useCallback(async (chequeData) => {
    try {
      const result = await actions.addCheque(chequeData);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const updateCheque = useCallback(async (chequeId, updates) => {
    try {
      const result = await actions.updateCheque(chequeId, updates);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const updateStatus = useCallback(async (chequeId, newStatus, statusData) => {
    try {
      const result = await actions.updateChequeStatus(chequeId, newStatus, statusData);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const deleteCheque = useCallback(async (chequeId) => {
    try {
      const result = await actions.deleteCheque(chequeId);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  return {
    cheques,
    filteredCheques,
    status,
    addCheque,
    updateCheque,
    updateStatus,
    deleteCheque,
    actions
  };
};

/**
 * Hook for cheques by status
 */
export const useChequesByStatus = () => {
  const treasuryCheques = useTreasuryCheques();
  const custodyCheques = useCustodyCheques();
  const collectionCheques = useCollectionCheques();
  const collectedCheques = useCollectedCheques();
  const bouncedCheques = useBouncedCheques();
  const actions = useChequesActions();

  const fetchByStatus = useCallback(async (status) => {
    try {
      const result = await actions.fetchChequesByStatus(status);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  return {
    treasuryCheques,
    custodyCheques,
    collectionCheques,
    collectedCheques,
    bouncedCheques,
    fetchByStatus
  };
};

// ===== USERS HOOKS =====

/**
 * Hook for managing users with automatic initialization
 */
export const useUsersManager = () => {
  const users = useUsers();
  const actions = useUsersActions();
  const status = useUsersStatus();
  const filteredUsers = useFilteredUsers();

  useEffect(() => {
    if (users.length === 0 && !status.isLoading && !status.error) {
      actions.fetchUsers();
    }
  }, [users.length, status.isLoading, status.error, actions]);

  const addUser = useCallback(async (userData) => {
    try {
      const result = await actions.addUser(userData);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const updateUser = useCallback(async (userId, updates) => {
    try {
      const result = await actions.updateUser(userId, updates);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const deleteUser = useCallback(async (userId) => {
    try {
      const result = await actions.deleteUser(userId);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const changePassword = useCallback(async (userId, passwordData) => {
    try {
      const result = await actions.changeUserPassword(userId, passwordData);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const updatePermissions = useCallback(async (userId, permissions) => {
    try {
      const result = await actions.updateUserPermissions(userId, permissions);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  return {
    users,
    filteredUsers,
    status,
    addUser,
    updateUser,
    deleteUser,
    changePassword,
    updatePermissions,
    actions
  };
};

/**
 * Hook for current user and authentication
 */
export const useCurrentUserManager = () => {
  const currentUser = useCurrentUser();
  const userPermissions = useUserPermissions();
  const availableRoles = useAvailableRoles();
  const actions = useUsersActions();
  const status = useUsersStatus();

  useEffect(() => {
    if (!currentUser) {
      actions.fetchCurrentUser();
    }
  }, [currentUser, actions]);

  useEffect(() => {
    if (availableRoles.length === 0 && !status.rolesLoading) {
      actions.fetchAvailableRoles();
    }
  }, [availableRoles.length, status.rolesLoading, actions]);

  const hasPermission = useCallback((permission) => {
    return actions.hasPermission(permission);
  }, [actions]);

  return {
    currentUser,
    userPermissions,
    availableRoles,
    hasPermission,
    permissionsLoading: status.permissionsLoading,
    rolesLoading: status.rolesLoading
  };
};

// ===== REPORTS HOOKS =====

/**
 * Hook for managing reports and dashboard
 */
export const useReportsManager = () => {
  const reports = useReports();
  const dashboardStats = useDashboardStats();
  const actions = useReportsActions();
  const status = useReportsStatus();
  const filteredReports = useFilteredReports();

  useEffect(() => {
    if (!dashboardStats && !status.isDashboardLoading && !status.dashboardError) {
      actions.fetchDashboardStats();
    }
  }, [dashboardStats, status.isDashboardLoading, status.dashboardError, actions]);

  const generateReport = useCallback(async (reportConfig) => {
    try {
      const result = await actions.generateReport(reportConfig);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const exportReport = useCallback(async (reportId, format) => {
    try {
      const result = await actions.exportReport(reportId, format);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const fetchFinancialSummary = useCallback(async (dateRange) => {
    try {
      const result = await actions.fetchFinancialSummary(dateRange);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const fetchContractsAnalysis = useCallback(async (filters) => {
    try {
      const result = await actions.fetchContractsAnalysis(filters);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  return {
    reports,
    dashboardStats,
    filteredReports,
    status,
    generateReport,
    exportReport,
    fetchFinancialSummary,
    fetchContractsAnalysis,
    actions
  };
};

/**
 * Hook for specific report types
 */
export const useReportTypes = () => {
  const financialReports = useFinancialReports();
  const contractReports = useContractReports();
  const clientReports = useClientReports();
  const paymentReports = usePaymentReports();
  const reportConfigs = useReportConfigs();
  const actions = useReportsActions();

  const fetchByType = useCallback(async (reportType, filters) => {
    try {
      const result = await actions.fetchReportsByType(reportType, filters);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  const saveConfig = useCallback(async (config) => {
    try {
      const result = await actions.saveReportConfig(config);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  return {
    financialReports,
    contractReports,
    clientReports,
    paymentReports,
    reportConfigs,
    fetchByType,
    saveConfig
  };
};

// ===== COMBINED HOOKS =====

/**
 * Hook that initializes all stores
 */
export const useStoreInitializer = () => {
  const appActions = useAppActions();
  const settingsActions = useSettingsActions();
  const referenceActions = useReferenceActions();

  useEffect(() => {
    const initializeStores = async () => {
      try {
        // Initialize app store
        appActions.initialize();

        // Initialize settings store
        await settingsActions.initialize();

        // Initialize reference data store
        await referenceActions.initialize();

        console.log('✅ All stores initialized successfully');
      } catch (error) {
        console.error('❌ Failed to initialize stores:', error);
        appActions.setGlobalError(error);
      }
    };

    initializeStores();
  }, [appActions, settingsActions, referenceActions]);
};

/**
 * Hook for getting application status
 */
export const useAppStatus = () => {
  const settingsStatus = useSettingsStatus();
  const isReferenceInitialized = useIsReferenceInitialized();
  const isLoading = useIsLoading();

  const isAppReady = settingsStatus.isInitialized && isReferenceInitialized && !isLoading;
  const isInitializing = settingsStatus.isLoading || !isReferenceInitialized;

  return {
    isAppReady,
    isInitializing,
    settingsReady: settingsStatus.isInitialized,
    referenceDataReady: isReferenceInitialized
  };
};
