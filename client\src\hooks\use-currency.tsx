import { useSettings } from "@/contexts/settings-context";

export function useCurrency() {
  const { settings } = useSettings();

  const formatCurrency = (amount: number): string => {
    const decimals = parseInt(settings.decimalPlaces || '2');
    const separator = settings.numberSeparator || ',';
    const symbol = settings.currencySymbol || 'ج.م';
    const currency = settings.currency || 'EGP';

    // Use Intl.NumberFormat for proper currency formatting
    try {
      // Determine locale based on currency
      let locale = 'ar-EG'; // Default to Arabic Egypt
      
      if (currency === 'EGP' || currency === 'جنيه مصري') {
        locale = 'ar-EG';
      } else if (currency === 'SAR' || currency === 'ريال سعودي') {
        locale = 'ar-SA';
      } else if (currency === 'AED' || currency === 'درهم إماراتي') {
        locale = 'ar-AE';
      } else if (currency === 'KWD' || currency === 'دينار كويتي') {
        locale = 'ar-KW';
      } else if (currency === 'QAR' || currency === 'ريال قطري') {
        locale = 'ar-QA';
      } else if (currency === 'BHD' || currency === 'دينار بحريني') {
        locale = 'ar-BH';
      } else if (currency === 'OMR' || currency === 'ريال عماني') {
        locale = 'ar-OM';
      } else if (currency === 'JOD' || currency === 'دينار أردني') {
        locale = 'ar-JO';
      } else if (currency === 'LBP' || currency === 'ليرة لبنانية') {
        locale = 'ar-LB';
      } else if (currency === 'USD' || currency === 'دولار أمريكي') {
        locale = 'en-US';
      } else if (currency === 'EUR' || currency === 'يورو') {
        locale = 'en-EU';
      }

      // Get currency code for Intl.NumberFormat
      let currencyCode = currency;
      if (currency === 'ريال سعودي') currencyCode = 'SAR';
      else if (currency === 'جنيه مصري') currencyCode = 'EGP';
      else if (currency === 'درهم إماراتي') currencyCode = 'AED';
      else if (currency === 'دينار كويتي') currencyCode = 'KWD';
      else if (currency === 'ريال قطري') currencyCode = 'QAR';
      else if (currency === 'دينار بحريني') currencyCode = 'BHD';
      else if (currency === 'ريال عماني') currencyCode = 'OMR';
      else if (currency === 'دينار أردني') currencyCode = 'JOD';
      else if (currency === 'ليرة لبنانية') currencyCode = 'LBP';
      else if (currency === 'دولار أمريكي') currencyCode = 'USD';
      else if (currency === 'يورو') currencyCode = 'EUR';

      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currencyCode,
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals,
      }).format(amount);
    } catch (error) {
      // Fallback to manual formatting if Intl.NumberFormat fails
      const formatted = amount.toFixed(decimals);
      const parts = formatted.split('.');
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator);
      
      return `${parts.join('.')} ${symbol}`;
    }
  };

  const getCurrencySymbol = (): string => {
    return settings.currencySymbol || 'ج.م';
  };

  const getCurrencyCode = (): string => {
    const currency = settings.currency || 'EGP';
    
    // Convert Arabic currency names to codes
    if (currency === 'ريال سعودي') return 'SAR';
    if (currency === 'جنيه مصري') return 'EGP';
    if (currency === 'درهم إماراتي') return 'AED';
    if (currency === 'دينار كويتي') return 'KWD';
    if (currency === 'ريال قطري') return 'QAR';
    if (currency === 'دينار بحريني') return 'BHD';
    if (currency === 'ريال عماني') return 'OMR';
    if (currency === 'دينار أردني') return 'JOD';
    if (currency === 'ليرة لبنانية') return 'LBP';
    if (currency === 'دولار أمريكي') return 'USD';
    if (currency === 'يورو') return 'EUR';
    
    return currency;
  };

  const getDecimalPlaces = (): number => {
    return parseInt(settings.decimalPlaces || '2');
  };

  const getNumberSeparator = (): string => {
    return settings.numberSeparator || ',';
  };

  return {
    formatCurrency,
    getCurrencySymbol,
    getCurrencyCode,
    getDecimalPlaces,
    getNumberSeparator,
    settings
  };
}
