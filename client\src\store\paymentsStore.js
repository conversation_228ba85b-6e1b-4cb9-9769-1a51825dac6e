// ===== PAYMENTS STORE =====
// Author: Augment Code
// Description: Centralized state management for payments using Zustand

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import apiMiddleware from '../lib/apiMiddleware';

// Initial state
const initialState = {
  // Payments data
  payments: [],
  treasuryPayments: [],
  bankPayments: [],
  cashReceipts: [],
  chequeReceipts: [],
  selectedPayment: null,
  
  // Search and filters
  searchQuery: '',
  paymentTypeFilter: '',
  statusFilter: '',
  clientFilter: '',
  contractFilter: '',
  dateRangeFilter: { start: null, end: null },
  amountRangeFilter: { min: null, max: null },
  
  // Loading states
  isLoading: false,
  isAdding: false,
  isUpdating: false,
  isDeleting: false,
  isPrintingReceipt: false,
  
  // Error states
  error: null,
  addError: null,
  updateError: null,
  deleteError: null,
  
  // Cache and metadata
  lastFetched: null,
  totalCount: 0,
  
  // UI state
  showPaymentDetails: false,
  
  // Payment statistics
  paymentStats: null,
  statsLoading: false,
  statsError: null
};

// Create the payments store
export const usePaymentsStore = create()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Actions
        actions: {
          // Fetch all payments
          async fetchPayments(forceRefresh = false) {
            const state = get();
            
            if (state.isLoading || (!forceRefresh && state.lastFetched && 
                Date.now() - new Date(state.lastFetched).getTime() < 30000)) {
              return state.payments;
            }

            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const payments = await apiMiddleware.get('/api/payments', {
                loadingMessage: 'جاري تحميل المدفوعات...',
                showErrorNotification: true
              });

              set((state) => {
                state.payments = payments || [];
                state.totalCount = payments?.length || 0;
                state.isLoading = false;
                state.lastFetched = new Date().toISOString();
                state.error = null;
              });

              return payments;
            } catch (error) {
              console.error('Failed to fetch payments:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحميل المدفوعات';
              });

              throw error;
            }
          },

          // Fetch payments by type
          async fetchPaymentsByType(paymentType) {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const payments = await apiMiddleware.get(`/api/payments/${paymentType}`, {
                loadingMessage: `جاري تحميل ${paymentType}...`,
                showErrorNotification: true
              });

              set((state) => {
                switch (paymentType) {
                  case 'treasury':
                    state.treasuryPayments = payments || [];
                    break;
                  case 'bank':
                    state.bankPayments = payments || [];
                    break;
                  case 'cash-receipts':
                    state.cashReceipts = payments || [];
                    break;
                  case 'cheque-receipts':
                    state.chequeReceipts = payments || [];
                    break;
                }
                state.isLoading = false;
                state.error = null;
              });

              return payments;
            } catch (error) {
              console.error(`Failed to fetch ${paymentType}:`, error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || `فشل في تحميل ${paymentType}`;
              });

              throw error;
            }
          },

          // Search payments
          async searchPayments(query) {
            set((state) => {
              state.searchQuery = query;
              state.isLoading = true;
              state.error = null;
            });

            try {
              const url = query 
                ? `/api/payments/search?q=${encodeURIComponent(query)}`
                : '/api/payments';

              const payments = await apiMiddleware.get(url, {
                loadingMessage: query ? 'جاري البحث...' : 'جاري تحميل المدفوعات...',
                showErrorNotification: true
              });

              set((state) => {
                state.payments = payments || [];
                state.totalCount = payments?.length || 0;
                state.isLoading = false;
                state.error = null;
              });

              return payments;
            } catch (error) {
              console.error('Failed to search payments:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في البحث عن المدفوعات';
              });

              throw error;
            }
          },

          // Add new payment
          async addPayment(paymentData) {
            set((state) => {
              state.isAdding = true;
              state.addError = null;
            });

            try {
              const newPayment = await apiMiddleware.post('/api/payments', paymentData, {
                loadingMessage: 'جاري إضافة المدفوعة...',
                successMessage: 'تم إضافة المدفوعة بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.payments.unshift(newPayment);
                state.totalCount += 1;
                
                // Add to specific type array
                switch (newPayment.paymentType) {
                  case 'treasury':
                    state.treasuryPayments.unshift(newPayment);
                    break;
                  case 'bank':
                    state.bankPayments.unshift(newPayment);
                    break;
                  case 'cash-receipt':
                    state.cashReceipts.unshift(newPayment);
                    break;
                  case 'cheque-receipt':
                    state.chequeReceipts.unshift(newPayment);
                    break;
                }
                
                state.isAdding = false;
                state.addError = null;
              });

              return { success: true, data: newPayment };
            } catch (error) {
              console.error('Failed to add payment:', error);
              
              set((state) => {
                state.isAdding = false;
                state.addError = error.message || 'فشل في إضافة المدفوعة';
              });

              return { success: false, error: error.message };
            }
          },

          // Update payment
          async updatePayment(paymentId, updates) {
            set((state) => {
              state.isUpdating = true;
              state.updateError = null;
            });

            try {
              const updatedPayment = await apiMiddleware.put(`/api/payments/${paymentId}`, updates, {
                loadingMessage: 'جاري تحديث المدفوعة...',
                successMessage: 'تم تحديث المدفوعة بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                // Update in main payments array
                const index = state.payments.findIndex(payment => payment.id === paymentId);
                if (index !== -1) {
                  state.payments[index] = updatedPayment;
                }
                
                // Update in specific type arrays
                const updateInArray = (array) => {
                  const idx = array.findIndex(payment => payment.id === paymentId);
                  if (idx !== -1) {
                    array[idx] = updatedPayment;
                  }
                };
                
                updateInArray(state.treasuryPayments);
                updateInArray(state.bankPayments);
                updateInArray(state.cashReceipts);
                updateInArray(state.chequeReceipts);
                
                if (state.selectedPayment?.id === paymentId) {
                  state.selectedPayment = updatedPayment;
                }
                
                state.isUpdating = false;
                state.updateError = null;
              });

              return { success: true, data: updatedPayment };
            } catch (error) {
              console.error('Failed to update payment:', error);
              
              set((state) => {
                state.isUpdating = false;
                state.updateError = error.message || 'فشل في تحديث المدفوعة';
              });

              return { success: false, error: error.message };
            }
          },

          // Delete payment
          async deletePayment(paymentId) {
            set((state) => {
              state.isDeleting = true;
              state.deleteError = null;
            });

            try {
              await apiMiddleware.delete(`/api/payments/${paymentId}`, {
                loadingMessage: 'جاري حذف المدفوعة...',
                successMessage: 'تم حذف المدفوعة بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                // Remove from main payments array
                state.payments = state.payments.filter(payment => payment.id !== paymentId);
                state.totalCount -= 1;
                
                // Remove from specific type arrays
                state.treasuryPayments = state.treasuryPayments.filter(payment => payment.id !== paymentId);
                state.bankPayments = state.bankPayments.filter(payment => payment.id !== paymentId);
                state.cashReceipts = state.cashReceipts.filter(payment => payment.id !== paymentId);
                state.chequeReceipts = state.chequeReceipts.filter(payment => payment.id !== paymentId);
                
                if (state.selectedPayment?.id === paymentId) {
                  state.selectedPayment = null;
                  state.showPaymentDetails = false;
                }
                
                state.isDeleting = false;
                state.deleteError = null;
              });

              return { success: true };
            } catch (error) {
              console.error('Failed to delete payment:', error);
              
              set((state) => {
                state.isDeleting = false;
                state.deleteError = error.message || 'فشل في حذف المدفوعة';
              });

              return { success: false, error: error.message };
            }
          },

          // Print payment receipt
          async printPaymentReceipt(paymentId) {
            set((state) => {
              state.isPrintingReceipt = true;
            });

            try {
              const receiptData = await apiMiddleware.get(`/api/payments/${paymentId}/receipt`, {
                loadingMessage: 'جاري إنشاء الإيصال...',
                successMessage: 'تم إنشاء الإيصال بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.isPrintingReceipt = false;
              });

              return { success: true, data: receiptData };
            } catch (error) {
              console.error('Failed to print receipt:', error);
              
              set((state) => {
                state.isPrintingReceipt = false;
              });

              return { success: false, error: error.message };
            }
          },

          // Fetch payment statistics
          async fetchPaymentStats() {
            set((state) => {
              state.statsLoading = true;
              state.statsError = null;
            });

            try {
              const stats = await apiMiddleware.get('/api/payments/statistics', {
                showErrorNotification: false
              });

              set((state) => {
                state.paymentStats = stats;
                state.statsLoading = false;
                state.statsError = null;
              });

              return stats;
            } catch (error) {
              console.error('Failed to fetch payment stats:', error);
              
              set((state) => {
                state.paymentStats = null;
                state.statsLoading = false;
                state.statsError = error.message;
              });

              return null;
            }
          },

          // Set selected payment
          setSelectedPayment(payment) {
            set((state) => {
              state.selectedPayment = payment;
              state.showPaymentDetails = !!payment;
            });
          },

          // Set filters
          setSearchQuery(query) {
            set((state) => {
              state.searchQuery = query;
            });
          },

          setPaymentTypeFilter(type) {
            set((state) => {
              state.paymentTypeFilter = type;
            });
          },

          setStatusFilter(status) {
            set((state) => {
              state.statusFilter = status;
            });
          },

          setClientFilter(clientId) {
            set((state) => {
              state.clientFilter = clientId;
            });
          },

          setContractFilter(contractId) {
            set((state) => {
              state.contractFilter = contractId;
            });
          },

          setDateRangeFilter(dateRange) {
            set((state) => {
              state.dateRangeFilter = dateRange;
            });
          },

          setAmountRangeFilter(amountRange) {
            set((state) => {
              state.amountRangeFilter = amountRange;
            });
          },

          // Clear errors
          clearErrors() {
            set((state) => {
              state.error = null;
              state.addError = null;
              state.updateError = null;
              state.deleteError = null;
              state.statsError = null;
            });
          },

          // Get filtered payments
          getFilteredPayments() {
            const state = get();
            const { 
              payments, 
              searchQuery, 
              paymentTypeFilter, 
              statusFilter, 
              clientFilter, 
              contractFilter,
              dateRangeFilter,
              amountRangeFilter 
            } = state;

            return payments.filter(payment => {
              // Search filter
              const matchesSearch = !searchQuery ||
                payment.receiptNumber?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                payment.clientName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                payment.contractNumber?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                payment.paymentMethod?.toLowerCase().includes(searchQuery.toLowerCase());

              // Payment type filter
              const matchesType = !paymentTypeFilter || paymentTypeFilter === "all" ||
                payment.paymentType === paymentTypeFilter;

              // Status filter
              const matchesStatus = !statusFilter || statusFilter === "all" ||
                payment.paymentStatus === statusFilter;

              // Client filter
              const matchesClient = !clientFilter || clientFilter === "all" ||
                payment.clientId === clientFilter;

              // Contract filter
              const matchesContract = !contractFilter || contractFilter === "all" ||
                payment.contractId === contractFilter;

              // Date range filter
              const matchesDateRange = !dateRangeFilter.start || !dateRangeFilter.end ||
                (new Date(payment.paymentDate) >= new Date(dateRangeFilter.start) &&
                 new Date(payment.paymentDate) <= new Date(dateRangeFilter.end));

              // Amount range filter
              const matchesAmountRange = (!amountRangeFilter.min || payment.amount >= amountRangeFilter.min) &&
                (!amountRangeFilter.max || payment.amount <= amountRangeFilter.max);

              return matchesSearch && matchesType && matchesStatus && matchesClient && 
                     matchesContract && matchesDateRange && matchesAmountRange;
            });
          },

          // Get payment by ID
          getPaymentById(paymentId) {
            const state = get();
            return state.payments.find(payment => payment.id === paymentId);
          },

          // Refresh data
          async refresh() {
            return get().actions.fetchPayments(true);
          }
        }
      })),
      {
        name: 'payments-store',
        partialize: (state) => ({
          payments: state.payments,
          lastFetched: state.lastFetched,
          searchQuery: state.searchQuery,
          paymentTypeFilter: state.paymentTypeFilter,
          statusFilter: state.statusFilter,
          clientFilter: state.clientFilter,
          contractFilter: state.contractFilter
        })
      }
    ),
    {
      name: 'payments-store'
    }
  )
);

// Selectors
export const usePayments = () => usePaymentsStore((state) => state.payments);
export const useTreasuryPayments = () => usePaymentsStore((state) => state.treasuryPayments);
export const useBankPayments = () => usePaymentsStore((state) => state.bankPayments);
export const useCashReceipts = () => usePaymentsStore((state) => state.cashReceipts);
export const useChequeReceipts = () => usePaymentsStore((state) => state.chequeReceipts);
export const usePaymentsLoading = () => usePaymentsStore((state) => state.isLoading);
export const usePaymentsError = () => usePaymentsStore((state) => state.error);
export const useSelectedPayment = () => usePaymentsStore((state) => state.selectedPayment);
export const usePaymentStats = () => usePaymentsStore((state) => state.paymentStats);
export const usePaymentsActions = () => usePaymentsStore((state) => state.actions);

export const useFilteredPayments = () => usePaymentsStore((state) => 
  state.actions.getFilteredPayments()
);

export const usePaymentsStatus = () => usePaymentsStore((state) => ({
  isLoading: state.isLoading,
  isAdding: state.isAdding,
  isUpdating: state.isUpdating,
  isDeleting: state.isDeleting,
  isPrintingReceipt: state.isPrintingReceipt,
  statsLoading: state.statsLoading,
  error: state.error,
  addError: state.addError,
  updateError: state.updateError,
  deleteError: state.deleteError,
  statsError: state.statsError,
  totalCount: state.totalCount,
  lastFetched: state.lastFetched
}));

export default usePaymentsStore;
