const database = require('../models/db.cjs');

class ReceivablesService {

  // جلب جميع الاستحقاقات مع بيانات العقود والعملاء
  getAllReceivables() {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      const sql = `
        SELECT
          r.*,
          c.contractNumber,
          c.contractSubject,
          cl.clientName,
          cl.clientType
        FROM ContractReceivables r
        LEFT JOIN Contracts c ON r.contractId = c.id
        LEFT JOIN Clients cl ON r.clientId = cl.id
        WHERE r.isActive = 1
        ORDER BY r.dueDate ASC
      `;

      db.all(sql, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  }

  // جلب استحقاقات عقد معين
  getReceivablesByContract(contractId) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT
          r.*,
          c.contractNumber,
          c.contractSubject,
          cl.clientName
        FROM ContractReceivables r
        LEFT JOIN Contracts c ON r.contractId = c.id
        LEFT JOIN Clients cl ON r.clientId = cl.id
        WHERE r.contractId = ? AND r.isActive = 1
        ORDER BY r.dueDate ASC
      `;

      this.db.all(sql, [contractId], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  }

  // جلب استحقاق بواسطة ID
  getReceivableById(id) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT
          r.*,
          c.contractNumber,
          c.contractSubject,
          cl.clientName,
          cl.clientType
        FROM ContractReceivables r
        LEFT JOIN Contracts c ON r.contractId = c.id
        LEFT JOIN Clients cl ON r.clientId = cl.id
        WHERE r.id = ? AND r.isActive = 1
      `;

      this.db.get(sql, [id], (err, row) => {
        if (err) return reject(err);
        resolve(row);
      });
    });
  }

  // إنشاء استحقاق جديد
  createReceivable(receivableData) {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO ContractReceivables (
          contractId, clientId, receivableNumber, invoiceCode, dueDate, amount,
          description, status, paymentFrequency, installmentNumber, totalInstallments, isActive
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
      `;

      const params = [
        receivableData.contractId,
        receivableData.clientId,
        receivableData.receivableNumber,
        receivableData.invoiceCode,
        receivableData.dueDate,
        receivableData.amount,
        receivableData.description,
        receivableData.status || 'لم يحن موعده',
        receivableData.paymentFrequency,
        receivableData.installmentNumber,
        receivableData.totalInstallments
      ];

      this.db.run(sql, params, function(err) {
        if (err) return reject(err);
        resolve({ id: this.lastID, ...receivableData });
      });
    });
  }

  // توليد الاستحقاقات لعقد
  generateReceivables(contractId) {
    return new Promise((resolve, reject) => {
      // جلب بيانات العقد
      this.db.get(`
        SELECT * FROM Contracts
        WHERE id = ? AND isActive = 1
      `, [contractId], (err, contract) => {
        if (err) return reject(err);
        if (!contract) return reject(new Error('العقد غير موجود'));

        // التحقق من وجود استحقاقات سابقة
        this.db.get(`
          SELECT COUNT(*) as count
          FROM ContractReceivables
          WHERE contractId = ? AND isActive = 1
        `, [contractId], (err, result) => {
          if (err) return reject(err);
          if (result.count > 0) {
            return reject(new Error('الاستحقاقات موجودة مسبقاً لهذا العقد'));
          }

          // جلب منتجات العقد
          this.db.all(`
            SELECT * FROM ContractProducts
            WHERE contractId = ? AND isActive = 1
          `, [contractId], (err, products) => {
            if (err) return reject(err);

            if (products && products.length > 0) {
              this.generateEnhancedReceivables(contract, products, resolve, reject);
            } else {
              this.generateSimpleReceivables(contract, resolve, reject);
            }
          });
        });
      });
    });
  }

  // توليد استحقاقات بسيطة (بدون منتجات)
  generateSimpleReceivables(contract, resolve, reject) {
    const {
      id: contractId,
      clientId,
      contractNumber,
      startDate,
      totalContractValue,
      paymentFrequency,
      contractDurationYears,
      contractStatus = 'نشط'
    } = contract;

    // تحديد عدد الأقساط والفترة
    let installmentCount = 0;
    let monthsInterval = 1;
    let installmentAmount = 0;

    switch (paymentFrequency) {
      case 'شهري':
        monthsInterval = 1;
        installmentCount = contractDurationYears * 12;
        break;
      case 'ربع سنوي':
        monthsInterval = 3;
        installmentCount = contractDurationYears * 4;
        break;
      case 'نصف سنوي':
        monthsInterval = 6;
        installmentCount = contractDurationYears * 2;
        break;
      case 'سنوي':
        monthsInterval = 12;
        installmentCount = contractDurationYears;
        break;
      default:
        monthsInterval = 1;
        installmentCount = contractDurationYears * 12;
        break;
    }

    installmentAmount = contractStatus === 'نشط' ? (totalContractValue / installmentCount) : 0;

    // توليد الاستحقاقات
    const receivables = [];
    const contractStartDate = new Date(startDate);

    for (let i = 0; i < installmentCount; i++) {
      const dueDate = new Date(contractStartDate);
      dueDate.setMonth(dueDate.getMonth() + (i * monthsInterval));

      const receivableNumber = `${contractNumber}-${String(i + 1).padStart(3, '0')}`;
      const invoiceCode = `${contractNumber}-INV-${String(i + 1).padStart(3, '0')}`;

      let description = `قسط ${paymentFrequency} رقم ${i + 1} من ${installmentCount}`;
      if (contractStatus === 'غير نشط') {
        description += ' (عقد غير نشط - بدون إيرادات)';
      }

      receivables.push({
        contractId,
        clientId,
        receivableNumber,
        invoiceCode,
        dueDate: dueDate.toISOString().split('T')[0],
        amount: installmentAmount,
        description: description,
        status: 'لم يحن موعده',
        paymentFrequency,
        installmentNumber: i + 1,
        totalInstallments: installmentCount
      });
    }

    // إدراج الاستحقاقات في قاعدة البيانات
    this.insertReceivables(receivables, resolve, reject);
  }

  // إدراج الاستحقاقات في قاعدة البيانات
  insertReceivables(receivables, resolve, reject) {
    const insertSQL = `
      INSERT INTO ContractReceivables (
        contractId, clientId, receivableNumber, invoiceCode, dueDate, amount, description,
        status, paymentFrequency, installmentNumber, totalInstallments, isActive
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
    `;

    let insertedCount = 0;
    let errors = [];

    receivables.forEach((receivable) => {
      const values = [
        receivable.contractId,
        receivable.clientId,
        receivable.receivableNumber,
        receivable.invoiceCode,
        receivable.dueDate,
        receivable.amount,
        receivable.description,
        receivable.status,
        receivable.paymentFrequency,
        receivable.installmentNumber,
        receivable.totalInstallments
      ];

      this.db.run(insertSQL, values, function(err) {
        if (err) {
          console.error(`Error inserting receivable:`, err);
          errors.push(err);
        } else {
          insertedCount++;
        }

        // التحقق من انتهاء جميع العمليات
        if (insertedCount + errors.length === receivables.length) {
          if (errors.length > 0) {
            reject(errors[0]);
          } else {
            console.log(`✅ Generated ${insertedCount} receivables`);
            resolve({ count: insertedCount, receivables });
          }
        }
      });
    });
  }
}

module.exports = new ReceivablesService();
