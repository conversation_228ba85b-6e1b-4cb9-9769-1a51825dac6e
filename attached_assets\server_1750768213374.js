console.log('🚀 Starting Contract Management Server...');

const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

console.log('📦 Modules loaded successfully');

const app = express();
app.use(cors());
app.use(express.json({ limit: '10mb' })); // زيادة حد حجم JSON لرفع الصور
app.use(express.urlencoded({ limit: '10mb', extended: true }));

console.log('⚙️ Express middleware configured');

// Log all requests
app.use((req, res, next) => {
  console.log(`🌐 ${req.method} ${req.url} - ${new Date().toISOString()}`);
  next();
});

// Create SQLite database
const dbPath = path.join(__dirname, 'contract-app.sqlite');
const db = new sqlite3.Database(dbPath);

console.log('Starting server with SQLite...');

// Helper function to ensure table exists
function ensureTableExists(tableName, createTableSQL, callback) {
  db.get(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`, [tableName], (err, row) => {
    if (err) {
      console.error(`Error checking table ${tableName}:`, err);
      if (callback) callback(err);
      return;
    }

    if (!row) {
      console.log(`Creating table ${tableName}...`);
      db.run(createTableSQL, (err) => {
        if (err) {
          console.error(`Error creating table ${tableName}:`, err);
        } else {
          console.log(`Table ${tableName} created successfully`);
        }
        if (callback) callback(err);
      });
    } else {
      console.log(`Table ${tableName} already exists`);
      if (callback) callback(null);
    }
  });
}

// Create tables
db.serialize(() => {
  // Settings table
  db.run(`
    CREATE TABLE IF NOT EXISTS Settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      companyName TEXT,
      programName TEXT,
      companyRegNo TEXT,
      taxId TEXT,
      about TEXT,
      companyLogo TEXT,
      language TEXT DEFAULT 'ar',
      currency TEXT,
      currencySymbol TEXT,
      country TEXT,
      dateFormat TEXT,
      decimalPlaces TEXT,
      numberingFormat TEXT,
      notificationEmail TEXT,
      regions TEXT,
      owners TEXT,
      governorates TEXT,
      workDays TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Add new columns if they don't exist (for existing databases)
  db.run("ALTER TABLE Settings ADD COLUMN language TEXT DEFAULT 'ar'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN currencySymbol TEXT", () => {});

  // Reference items table
  db.run(`
    CREATE TABLE IF NOT EXISTS ReferenceItems (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      item_values TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Reference lists table (new structure)
  db.run(`
    CREATE TABLE IF NOT EXISTS ReferenceLists (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      listName TEXT NOT NULL,
      listData TEXT NOT NULL,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Clients table (updated with new fields)
  db.run(`
    CREATE TABLE IF NOT EXISTS Clients (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      clientId TEXT UNIQUE NOT NULL,
      clientType TEXT NOT NULL CHECK (clientType IN ('أفراد', 'شركات')),
      clientName TEXT NOT NULL,
      clientAddress TEXT,
      clientPhoneWhatsapp TEXT NOT NULL,
      clientPhone2 TEXT,
      clientPhone3 TEXT,
      clientEmail TEXT,
      clientNotes TEXT,
      clientFinancialGuarantee TEXT,
      clientID_Image TEXT,
      clientFinancial_Category TEXT,
      clientLegal_Rep TEXT,
      clientPartner TEXT,
      clientReg_Number TEXT,
      clientTaxReg_Number TEXT,
      clientLegal_Status TEXT,
      clientRemarks TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Add new columns to existing Clients table if they don't exist
  db.run("ALTER TABLE Clients ADD COLUMN clientFinancialGuarantee TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientID_Image TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientFinancial_Category TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientLegal_Rep TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientPartner TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientReg_Number TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientTaxReg_Number TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientLegal_Status TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientRemarks TEXT", () => {});

  // Contracts table (Enhanced for advanced contract management)
  db.run(`
    CREATE TABLE IF NOT EXISTS Contracts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractNumber TEXT UNIQUE NOT NULL,
      contractInternalId TEXT UNIQUE,
      contractDescription TEXT,
      contractSubject TEXT,
      clientId INTEGER NOT NULL,
      contractType TEXT NOT NULL,
      contractStatus TEXT DEFAULT 'نشط' CHECK (contractStatus IN ('نشط', 'منتهي', 'ملغي', 'معلق')),
      contractDate DATE,
      startDate DATE NOT NULL,
      actualStartDate DATE,
      endDate DATE,
      actualEndDate DATE,
      contractDurationYears INTEGER NOT NULL,
      assetOwner TEXT,
      financialGuarantorId INTEGER,
      parentContractId INTEGER,
      numberOfProducts INTEGER DEFAULT 1,
      hasUnifiedActivationDate INTEGER DEFAULT 1,
      totalContractValue DECIMAL(15,2) NOT NULL,
      monthlyAmount DECIMAL(15,2) NOT NULL,
      paymentDay INTEGER NOT NULL,
      paymentFrequency TEXT DEFAULT 'شهري' CHECK (paymentFrequency IN ('شهري', 'ربع سنوي', 'نصف سنوي', 'سنوي')),
      firstInstallmentDate DATE,
      paymentMethod TEXT DEFAULT 'تحويل بنكي' CHECK (paymentMethod IN ('تحويل بنكي', 'شيك', 'نقدي')),
      annualIncreaseType TEXT DEFAULT 'لا يوجد' CHECK (annualIncreaseType IN ('لا يوجد', 'مبلغ ثابت', 'نسبة مئوية', 'نسبة مركبة')),
      annualIncreaseValue DECIMAL(10,2) DEFAULT 0,
      annualIncreaseStartYear INTEGER DEFAULT 2,
      lateFeeType TEXT DEFAULT 'نسبة مئوية' CHECK (lateFeeType IN ('مبلغ ثابت', 'نسبة مئوية')),
      lateFeeValue DECIMAL(10,2) DEFAULT 0,
      gracePeriodDays INTEGER DEFAULT 0,
      bouncedCheckFeeType TEXT DEFAULT 'مبلغ ثابت' CHECK (bouncedCheckFeeType IN ('مبلغ ثابت', 'نسبة مئوية')),
      bouncedCheckFeeValue DECIMAL(10,2) DEFAULT 0,
      additionalFees TEXT,
      earlyTerminationReason TEXT,
      earlyTerminationDate DATE,
      importantNotes TEXT,
      systemFlags TEXT,
      autoTerminationSuggested INTEGER DEFAULT 0,
      consecutiveMissedPayments INTEGER DEFAULT 0,
      totalMissedPayments INTEGER DEFAULT 0,
      notes TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (clientId) REFERENCES Clients(id),
      FOREIGN KEY (financialGuarantorId) REFERENCES Clients(id),
      FOREIGN KEY (parentContractId) REFERENCES Contracts(id)
    )
  `);

  // Contract Products table (for multi-product contracts)
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractProducts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      productLabel TEXT NOT NULL,
      area DECIMAL(10,2) NOT NULL,
      meterPrice DECIMAL(10,2) NOT NULL,
      activationDate DATE,
      endDate DATE,
      billingType TEXT DEFAULT 'شهري' CHECK (billingType IN ('شهري', 'ربع سنوي', 'نصف سنوي', 'سنوي')),
      taxInfo INTEGER DEFAULT 0,
      taxRate DECIMAL(5,2) DEFAULT 0,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE
    )
  `);

  // Contract Partners table (for multiple partners/alliances)
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractPartners (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      partnerId INTEGER NOT NULL,
      partnerType TEXT DEFAULT 'شريك' CHECK (partnerType IN ('شريك', 'تحالف')),
      partnershipPercentage DECIMAL(5,2) DEFAULT 0,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE,
      FOREIGN KEY (partnerId) REFERENCES Clients(id)
    )
  `);

  // Contract Attachments table
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractAttachments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      fileName TEXT NOT NULL,
      fileType TEXT NOT NULL,
      fileSize INTEGER,
      filePath TEXT NOT NULL,
      uploadedBy TEXT,
      description TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE
    )
  `);

  // Contract Audit Log table
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractAuditLog (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      action TEXT NOT NULL,
      fieldName TEXT,
      oldValue TEXT,
      newValue TEXT,
      userId TEXT,
      userRole TEXT,
      ipAddress TEXT,
      userAgent TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE
    )
  `);

  // Payments table
  db.run(`
    CREATE TABLE IF NOT EXISTS Payments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      paymentNumber INTEGER NOT NULL,
      dueDate DATE NOT NULL,
      amount DECIMAL(15,2) NOT NULL,
      lateFee DECIMAL(10,2) DEFAULT 0,
      bouncedCheckFee DECIMAL(10,2) DEFAULT 0,
      totalAmount DECIMAL(15,2) NOT NULL,
      isPaid INTEGER DEFAULT 0,
      paidDate DATE,
      paymentMethod TEXT CHECK (paymentMethod IN ('نقدي', 'شيك', 'تحويل بنكي', 'بطاقة ائتمان')),
      checkNumber TEXT,
      bankName TEXT,
      notes TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id)
    )
  `);

  // Contract Financial Details table (for advanced calculations)
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractFinancialDetails (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL UNIQUE,
      numberOfProducts INTEGER NOT NULL DEFAULT 1,
      areaPerProduct DECIMAL(10,2) NOT NULL,
      meterPriceFirstYear DECIMAL(10,2) NOT NULL,
      annualIncreaseType TEXT NOT NULL DEFAULT 'None' CHECK (annualIncreaseType IN ('None', 'Fixed_Percentage', 'Compounded_Percentage')),
      annualIncreaseRate DECIMAL(5,4) DEFAULT 0,
      billingPeriodType TEXT NOT NULL DEFAULT 'Monthly' CHECK (billingPeriodType IN ('Monthly', 'Quarterly', 'Semi-Annual', 'Annually')),
      latePenaltyPercentage DECIMAL(5,4) DEFAULT 0,
      gracePeriodDays INTEGER DEFAULT 0,
      finalInsurancePercentage DECIMAL(5,4) DEFAULT 0,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE
    )
  `);

  // Contract Installments table (enhanced for multi-product support)
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractInstallments (
      installmentId INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      productId INTEGER,
      productLabel TEXT,
      installmentNumber INTEGER NOT NULL,
      installmentDate DATE NOT NULL,
      installmentAmount DECIMAL(15,2) NOT NULL,
      baseAmount DECIMAL(15,2) NOT NULL,
      taxAmount DECIMAL(15,2) DEFAULT 0,
      yearOfContract INTEGER NOT NULL,
      paymentDueDate DATE NOT NULL,
      remainingAmount DECIMAL(15,2) NOT NULL,
      isPaid INTEGER DEFAULT 0,
      paidDate DATE,
      paidAmount DECIMAL(15,2) DEFAULT 0,
      penaltyAmount DECIMAL(10,2) DEFAULT 0,
      lateDays INTEGER DEFAULT 0,
      paymentMethod TEXT,
      checkNumber TEXT,
      bankName TEXT,
      notes TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE,
      FOREIGN KEY (productId) REFERENCES ContractProducts(id) ON DELETE SET NULL
    )
  `);

  // Contract Alerts table (for smart notifications)
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractAlerts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      alertType TEXT NOT NULL CHECK (alertType IN ('متأخر', 'مستحق', 'منتهي', 'تحذير', 'اقتراح_فسخ')),
      alertLevel TEXT DEFAULT 'متوسط' CHECK (alertLevel IN ('منخفض', 'متوسط', 'عالي', 'حرج')),
      alertMessage TEXT NOT NULL,
      alertDate DATE NOT NULL,
      isRead INTEGER DEFAULT 0,
      isResolved INTEGER DEFAULT 0,
      resolvedDate DATE,
      resolvedBy TEXT,
      autoGenerated INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE
    )
  `);

  // Contract Timeline table (for visual timeline)
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractTimeline (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      eventType TEXT NOT NULL CHECK (eventType IN ('توقيع', 'تسليم', 'تعديل', 'إنهاء', 'تجديد', 'دفعة')),
      eventDate DATE NOT NULL,
      eventDescription TEXT NOT NULL,
      eventDetails TEXT,
      userId TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE
    )
  `);

  // Revenue Recognition table (for accounting)
  db.run(`
    CREATE TABLE IF NOT EXISTS RevenueRecognition (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      installmentId INTEGER NOT NULL,
      fiscalYear INTEGER NOT NULL,
      fiscalMonth INTEGER NOT NULL,
      amount DECIMAL(15,2) NOT NULL,
      recognizedDate DATE NOT NULL,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE,
      FOREIGN KEY (installmentId) REFERENCES ContractInstallments(installmentId) ON DELETE CASCADE
    )
  `);

  // Add new columns to existing Contracts table if they don't exist
  db.run("ALTER TABLE Contracts ADD COLUMN irregularPaymentMonths INTEGER", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN finalInsuranceRate REAL DEFAULT 0", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN checkStatus TEXT DEFAULT 'لم يتقدم بالشيكات'", () => {});

  // Update paymentFrequency CHECK constraint to include 'غير منتظم'
  // Note: SQLite doesn't support modifying CHECK constraints, so we'll handle this in application logic

  // Add irregularBillingMonths column to ContractProducts table
  db.run("ALTER TABLE ContractProducts ADD COLUMN irregularBillingMonths INTEGER", () => {});

  // Insert sample data if not exists
  db.get("SELECT COUNT(*) as count FROM Settings", (err, row) => {
    if (!err && row.count === 0) {
      db.run(`
        INSERT INTO Settings (
          companyName, programName, companyRegNo, taxId, about,
          currency, country, dateFormat, decimalPlaces, numberingFormat,
          notificationEmail, regions, owners, governorates, workDays
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        'Test Company', 'Contract Management', '', '', '',
        'EGP', 'Egypt', 'dd/MM/yyyy', '2', 'serial-year-unit',
        '', '[]', '[]', '[]', '[]'
      ]);
      console.log('Sample data created successfully');
    }
  });

  console.log('Database and tables created successfully');
});

// Get settings
app.get('/api/settings', (req, res) => {
  console.log('GET /api/settings request');
  
  db.get("SELECT * FROM Settings ORDER BY id DESC LIMIT 1", (err, row) => {
    if (err) {
      console.error('Error getting settings:', err);
      res.status(500).json({ error: err.message });
      return;
    }
    
    if (row) {
      // Convert JSON strings to arrays
      try {
        row.regions = row.regions ? JSON.parse(row.regions) : [];
        row.owners = row.owners ? JSON.parse(row.owners) : [];
        row.governorates = row.governorates ? JSON.parse(row.governorates) : [];
        row.workDays = row.workDays ? JSON.parse(row.workDays) : [];
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
      }
    }
    
    console.log('Settings retrieved successfully');
    res.json(row);
  });
});

// Update settings
app.post('/api/settings', (req, res) => {
  console.log('POST /api/settings request');
  console.log('Received data keys:', Object.keys(req.body));
  
  const {
    companyName, programName, companyRegNo, taxId, about, companyLogo,
    language, currency, currencySymbol, country, dateFormat, decimalPlaces, numberingFormat,
    notificationEmail, regions, owners, governorates, workDays
  } = req.body;

  const regionsStr = JSON.stringify(regions || []);
  const ownersStr = JSON.stringify(owners || []);
  const governoratesStr = JSON.stringify(governorates || []);
  const workDaysStr = JSON.stringify(workDays || []);

  // Check if record exists
  db.get("SELECT COUNT(*) as count FROM Settings", (err, row) => {
    if (err) {
      console.error('Error checking table:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (row.count === 0) {
      // Insert new record
      console.log('Inserting new record');
      db.run(`
        INSERT INTO Settings (
          companyName, programName, companyRegNo, taxId, about, companyLogo,
          language, currency, currencySymbol, country, dateFormat, decimalPlaces, numberingFormat,
          notificationEmail, regions, owners, governorates, workDays
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        companyName, programName, companyRegNo, taxId, about, companyLogo,
        language, currency, currencySymbol, country, dateFormat, decimalPlaces, numberingFormat,
        notificationEmail, regionsStr, ownersStr, governoratesStr, workDaysStr
      ], function(err) {
        if (err) {
          console.error('Insert error:', err);
          res.status(500).json({ error: err.message });
        } else {
          console.log('Settings inserted successfully');
          res.json({ success: true, id: this.lastID });
        }
      });
    } else {
      // Update existing record
      console.log('Updating existing record');
      db.run(`
        UPDATE Settings SET
          companyName=?, programName=?, companyRegNo=?, taxId=?, about=?, companyLogo=?,
          language=?, currency=?, currencySymbol=?, country=?, dateFormat=?, decimalPlaces=?, numberingFormat=?,
          notificationEmail=?, regions=?, owners=?, governorates=?, workDays=?,
          updatedAt=CURRENT_TIMESTAMP
        WHERE id = (SELECT id FROM Settings ORDER BY id DESC LIMIT 1)
      `, [
        companyName, programName, companyRegNo, taxId, about, companyLogo,
        language, currency, currencySymbol, country, dateFormat, decimalPlaces, numberingFormat,
        notificationEmail, regionsStr, ownersStr, governoratesStr, workDaysStr
      ], function(err) {
        if (err) {
          console.error('Update error:', err);
          res.status(500).json({ error: err.message });
        } else {
          console.log('Settings updated successfully');
          res.json({ success: true, changes: this.changes });
        }
      });
    }
  });
});

// Get reference items
app.get('/api/reference-items', (req, res) => {
  console.log('GET /api/reference-items request');
  
  db.all("SELECT * FROM ReferenceItems", (err, rows) => {
    if (err) {
      console.error('Error getting reference items:', err);
      res.status(500).json({ error: err.message });
      return;
    }
    
    const data = rows.map(item => ({
      ...item,
      values: item.item_values ? JSON.parse(item.item_values) : [],
      editing: false
    }));
    
    console.log('Reference items retrieved successfully');
    res.json(data);
  });
});

// Save reference items
app.post('/api/reference-items', (req, res) => {
  console.log('POST /api/reference-items request');
  console.log('Received data name:', req.body.name);
  
  const { id, name, values } = req.body;
  const valuesStr = JSON.stringify(
    typeof values === "string"
      ? values.split(/,|\n/).map(v => v.trim()).filter(Boolean)
      : values || []
  );

  if (id) {
    // Update
    console.log('Updating reference item');
    db.run(
      "UPDATE ReferenceItems SET name=?, item_values=?, updatedAt=CURRENT_TIMESTAMP WHERE id=?",
      [name, valuesStr, id],
      function(err) {
        if (err) {
          console.error('Update reference item error:', err);
          res.status(500).json({ error: err.message });
        } else {
          console.log('Reference item updated successfully');
          res.json({ success: true, changes: this.changes });
        }
      }
    );
  } else {
    // Insert new
    console.log('Inserting new reference item');
    db.run(
      "INSERT INTO ReferenceItems (name, item_values) VALUES (?, ?)",
      [name, valuesStr],
      function(err) {
        if (err) {
          console.error('Insert reference item error:', err);
          res.status(500).json({ error: err.message });
        } else {
          console.log('Reference item inserted successfully');
          res.json({ success: true, id: this.lastID });
        }
      }
    );
  }
});

// Reference Lists API endpoints

// Get all reference lists
app.get('/api/reference-lists', (req, res) => {
  console.log('GET /api/reference-lists request');

  db.all("SELECT * FROM ReferenceLists WHERE isActive = 1 ORDER BY listName", (err, rows) => {
    if (err) {
      console.error('Error getting reference lists:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    const data = rows.map(item => ({
      ...item,
      listData: item.listData ? JSON.parse(item.listData) : [],
      isActive: Boolean(item.isActive)
    }));

    console.log('Reference lists retrieved successfully:', data.length, 'items');
    res.json(data);
  });
});

// Create new reference list
app.post('/api/reference-lists', (req, res) => {
  console.log('POST /api/reference-lists request');
  console.log('Received data:', req.body);

  const { listName, listData, isActive = true } = req.body;

  if (!listName || !listData || !Array.isArray(listData)) {
    return res.status(400).json({ error: 'listName and listData (array) are required' });
  }

  const listDataStr = JSON.stringify(listData);

  db.run(
    "INSERT INTO ReferenceLists (listName, listData, isActive) VALUES (?, ?, ?)",
    [listName, listDataStr, isActive ? 1 : 0],
    function(err) {
      if (err) {
        console.error('Insert reference list error:', err);
        res.status(500).json({ error: err.message });
      } else {
        console.log('Reference list inserted successfully, ID:', this.lastID);
        res.json({ success: true, id: this.lastID });
      }
    }
  );
});

// Update reference list
app.put('/api/reference-lists/:id', (req, res) => {
  console.log('PUT /api/reference-lists/:id request');
  console.log('ID:', req.params.id);
  console.log('Received data:', req.body);

  const { id } = req.params;
  const { listName, listData, isActive = true } = req.body;

  if (!listName || !listData || !Array.isArray(listData)) {
    return res.status(400).json({ error: 'listName and listData (array) are required' });
  }

  const listDataStr = JSON.stringify(listData);

  db.run(
    "UPDATE ReferenceLists SET listName=?, listData=?, isActive=?, updatedAt=CURRENT_TIMESTAMP WHERE id=?",
    [listName, listDataStr, isActive ? 1 : 0, id],
    function(err) {
      if (err) {
        console.error('Update reference list error:', err);
        res.status(500).json({ error: err.message });
      } else {
        console.log('Reference list updated successfully, changes:', this.changes);
        res.json({ success: true, changes: this.changes });
      }
    }
  );
});

// Delete reference list
app.delete('/api/reference-lists/:id', (req, res) => {
  console.log('DELETE /api/reference-lists/:id request');
  console.log('ID:', req.params.id);

  const { id } = req.params;

  // Soft delete by setting isActive to 0
  db.run(
    "UPDATE ReferenceLists SET isActive=0, updatedAt=CURRENT_TIMESTAMP WHERE id=?",
    [id],
    function(err) {
      if (err) {
        console.error('Delete reference list error:', err);
        res.status(500).json({ error: err.message });
      } else {
        console.log('Reference list deleted successfully, changes:', this.changes);
        res.json({ success: true, changes: this.changes });
      }
    }
  );
});

// Clients API endpoints

// Get all clients
app.get('/api/clients', (req, res) => {
  console.log('GET /api/clients request');

  db.all("SELECT * FROM Clients WHERE isActive = 1 ORDER BY clientName", (err, rows) => {
    if (err) {
      console.error('Error getting clients:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    const data = rows.map(client => ({
      ...client,
      isActive: Boolean(client.isActive)
    }));

    console.log('Clients retrieved successfully:', data.length, 'items');
    res.json(data);
  });
});

// Get client by ID
app.get('/api/clients/:id', (req, res) => {
  console.log('GET /api/clients/:id request');
  console.log('ID:', req.params.id);

  const { id } = req.params;

  db.get("SELECT * FROM Clients WHERE id = ? AND isActive = 1", [id], (err, row) => {
    if (err) {
      console.error('Error getting client:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (!row) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const data = {
      ...row,
      isActive: Boolean(row.isActive)
    };

    console.log('Client retrieved successfully');
    res.json(data);
  });
});

// Create new client
app.post('/api/clients', (req, res) => {
  console.log('🔥 POST /api/clients request received!');
  console.log('🔥 Request headers:', req.headers);
  console.log('🔥 Received data:', req.body);
  console.log('🔥 Request method:', req.method);
  console.log('🔥 Request URL:', req.url);

  const {
    clientId,
    clientType,
    clientName,
    clientAddress,
    clientPhoneWhatsapp,
    clientPhone2,
    clientPhone3,
    clientEmail,
    clientNotes,
    clientFinancialGuarantee,
    clientID_Image,
    clientFinancial_Category,
    clientLegal_Rep,
    clientPartner,
    clientReg_Number,
    clientTaxReg_Number,
    clientLegal_Status,
    clientRemarks,
    isActive = true
  } = req.body;

  // Validation
  if (!clientId || !clientType || !clientName || !clientPhoneWhatsapp) {
    return res.status(400).json({
      error: 'clientId, clientType, clientName, and clientPhoneWhatsapp are required'
    });
  }

  // Check if clientId already exists
  db.get("SELECT id FROM Clients WHERE clientId = ?", [clientId], (err, existingClient) => {
    if (err) {
      console.error('Error checking existing client:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existingClient) {
      return res.status(400).json({ error: 'Client ID already exists' });
    }

    // Insert new client
    db.run(`
      INSERT INTO Clients (
        clientId, clientType, clientName, clientAddress, clientPhoneWhatsapp,
        clientPhone2, clientPhone3, clientEmail, clientNotes,
        clientFinancialGuarantee, clientID_Image, clientFinancial_Category,
        clientLegal_Rep, clientPartner, clientReg_Number, clientTaxReg_Number,
        clientLegal_Status, clientRemarks, isActive
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      clientId, clientType, clientName, clientAddress, clientPhoneWhatsapp,
      clientPhone2, clientPhone3, clientEmail, clientNotes,
      clientFinancialGuarantee, clientID_Image, clientFinancial_Category,
      clientLegal_Rep, clientPartner, clientReg_Number, clientTaxReg_Number,
      clientLegal_Status, clientRemarks, isActive ? 1 : 0
    ], function(err) {
      if (err) {
        console.error('Insert client error:', err);
        res.status(500).json({ error: err.message });
      } else {
        console.log('Client inserted successfully, ID:', this.lastID);
        res.json({ success: true, id: this.lastID });
      }
    });
  });
});

// Update client
app.put('/api/clients/:id', (req, res) => {
  console.log('PUT /api/clients/:id request');
  console.log('ID:', req.params.id);
  console.log('Received data:', req.body);

  const { id } = req.params;
  const {
    clientId,
    clientType,
    clientName,
    clientAddress,
    clientPhoneWhatsapp,
    clientPhone2,
    clientPhone3,
    clientEmail,
    clientNotes,
    clientFinancialGuarantee,
    clientID_Image,
    clientFinancial_Category,
    clientLegal_Rep,
    clientPartner,
    clientReg_Number,
    clientTaxReg_Number,
    clientLegal_Status,
    clientRemarks,
    isActive = true
  } = req.body;

  // Validation
  if (!clientId || !clientType || !clientName || !clientPhoneWhatsapp) {
    return res.status(400).json({
      error: 'clientId, clientType, clientName, and clientPhoneWhatsapp are required'
    });
  }

  // Check if clientId already exists for another client
  db.get("SELECT id FROM Clients WHERE clientId = ? AND id != ?", [clientId, id], (err, existingClient) => {
    if (err) {
      console.error('Error checking existing client:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existingClient) {
      return res.status(400).json({ error: 'Client ID already exists for another client' });
    }

    // Update client
    db.run(`
      UPDATE Clients SET
        clientId=?, clientType=?, clientName=?, clientAddress=?, clientPhoneWhatsapp=?,
        clientPhone2=?, clientPhone3=?, clientEmail=?, clientNotes=?,
        clientFinancialGuarantee=?, clientID_Image=?, clientFinancial_Category=?,
        clientLegal_Rep=?, clientPartner=?, clientReg_Number=?, clientTaxReg_Number=?,
        clientLegal_Status=?, clientRemarks=?, isActive=?, updatedAt=CURRENT_TIMESTAMP
      WHERE id=?
    `, [
      clientId, clientType, clientName, clientAddress, clientPhoneWhatsapp,
      clientPhone2, clientPhone3, clientEmail, clientNotes,
      clientFinancialGuarantee, clientID_Image, clientFinancial_Category,
      clientLegal_Rep, clientPartner, clientReg_Number, clientTaxReg_Number,
      clientLegal_Status, clientRemarks, isActive ? 1 : 0, id
    ], function(err) {
      if (err) {
        console.error('Update client error:', err);
        res.status(500).json({ error: err.message });
      } else {
        console.log('Client updated successfully, changes:', this.changes);
        res.json({ success: true, changes: this.changes });
      }
    });
  });
});

// Delete client (soft delete)
app.delete('/api/clients/:id', (req, res) => {
  console.log('DELETE /api/clients/:id request');
  console.log('ID:', req.params.id);

  const { id } = req.params;

  db.run(
    "UPDATE Clients SET isActive=0, updatedAt=CURRENT_TIMESTAMP WHERE id=?",
    [id],
    function(err) {
      if (err) {
        console.error('Delete client error:', err);
        res.status(500).json({ error: err.message });
      } else {
        console.log('Client deleted successfully, changes:', this.changes);
        res.json({ success: true, changes: this.changes });
      }
    }
  );
});

// Contracts API endpoints

// Get all contracts
app.get('/api/contracts', (req, res) => {
  console.log('GET /api/contracts request');

  const query = `
    SELECT c.*, cl.clientName, cl.clientType, cl.clientPhoneWhatsapp
    FROM Contracts c
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE c.isActive = 1
    ORDER BY c.createdAt DESC
  `;

  db.all(query, (err, rows) => {
    if (err) {
      console.error('Error getting contracts:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    const data = rows.map(contract => ({
      ...contract,
      isActive: Boolean(contract.isActive),
      totalContractValue: parseFloat(contract.totalContractValue),
      monthlyAmount: parseFloat(contract.monthlyAmount),
      lateFeePercentage: parseFloat(contract.lateFeePercentage),
      bouncedCheckFee: parseFloat(contract.bouncedCheckFee)
    }));

    console.log('Contracts retrieved successfully:', data.length, 'items');
    res.json(data);
  });
});

// Get contract by ID
app.get('/api/contracts/:id', (req, res) => {
  console.log('GET /api/contracts/:id request');
  console.log('ID:', req.params.id);

  const { id } = req.params;

  const query = `
    SELECT c.*, cl.clientName, cl.clientType, cl.clientPhoneWhatsapp, cl.clientAddress
    FROM Contracts c
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE c.id = ? AND c.isActive = 1
  `;

  db.get(query, [id], (err, row) => {
    if (err) {
      console.error('Error getting contract:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (!row) {
      return res.status(404).json({ error: 'Contract not found' });
    }

    const data = {
      ...row,
      isActive: Boolean(row.isActive),
      totalContractValue: parseFloat(row.totalContractValue),
      monthlyAmount: parseFloat(row.monthlyAmount),
      lateFeePercentage: parseFloat(row.lateFeePercentage),
      bouncedCheckFee: parseFloat(row.bouncedCheckFee)
    };

    console.log('Contract retrieved successfully');
    res.json(data);
  });
});

// Get payments for a contract
app.get('/api/contracts/:id/payments', (req, res) => {
  console.log('GET /api/contracts/:id/payments request');
  console.log('Contract ID:', req.params.id);

  const { id } = req.params;

  db.all(
    "SELECT * FROM Payments WHERE contractId = ? AND isActive = 1 ORDER BY paymentNumber",
    [id],
    (err, rows) => {
      if (err) {
        console.error('Error getting payments:', err);
        res.status(500).json({ error: err.message });
        return;
      }

      const data = rows.map(payment => ({
        ...payment,
        isPaid: Boolean(payment.isPaid),
        isActive: Boolean(payment.isActive),
        amount: parseFloat(payment.amount),
        lateFee: parseFloat(payment.lateFee),
        bouncedCheckFee: parseFloat(payment.bouncedCheckFee),
        totalAmount: parseFloat(payment.totalAmount)
      }));

      console.log('Payments retrieved successfully:', data.length, 'items');
      res.json(data);
    }
  );
});

// Create new contract
app.post('/api/contracts', (req, res) => {
  console.log('POST /api/contracts request');
  console.log('Received data:', req.body);

  const {
    contractNumber,
    clientId,
    contractType,
    startDate,
    contractDurationYears,
    totalContractValue,
    monthlyAmount,
    paymentDay,
    lateFeePercentage = 0,
    bouncedCheckFee = 0,
    contractStatus = 'نشط',
    notes,
    isActive = true
  } = req.body;

  // Validation
  if (!contractNumber || !clientId || !contractType || !startDate || !contractDurationYears || !totalContractValue || !monthlyAmount || !paymentDay) {
    return res.status(400).json({
      error: 'All required fields must be provided'
    });
  }

  // Check if contract number already exists
  db.get("SELECT id FROM Contracts WHERE contractNumber = ?", [contractNumber], (err, existingContract) => {
    if (err) {
      console.error('Error checking existing contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existingContract) {
      return res.status(400).json({ error: 'Contract number already exists' });
    }

    // Insert new contract
    db.run(`
      INSERT INTO Contracts (
        contractNumber, clientId, contractType, startDate, contractDurationYears,
        totalContractValue, monthlyAmount, paymentDay, lateFeePercentage,
        bouncedCheckFee, contractStatus, notes, isActive
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      contractNumber, clientId, contractType, startDate, contractDurationYears,
      totalContractValue, monthlyAmount, paymentDay, lateFeePercentage,
      bouncedCheckFee, contractStatus, notes, isActive ? 1 : 0
    ], function(err) {
      if (err) {
        console.error('Insert contract error:', err);
        res.status(500).json({ error: err.message });
      } else {
        console.log('Contract inserted successfully, ID:', this.lastID);
        res.json({ success: true, id: this.lastID });
      }
    });
  });
});

// Payments API endpoints

// Update payment (record payment)
app.put('/api/payments/:id', (req, res) => {
  console.log('PUT /api/payments/:id request');
  console.log('ID:', req.params.id);
  console.log('Received data:', req.body);

  const { id } = req.params;
  const {
    isPaid,
    paidDate,
    paymentMethod,
    checkNumber,
    bankName,
    lateFee = 0,
    bouncedCheckFee = 0,
    totalAmount,
    notes
  } = req.body;

  db.run(`
    UPDATE Payments SET
      isPaid=?, paidDate=?, paymentMethod=?, checkNumber=?, bankName=?,
      lateFee=?, bouncedCheckFee=?, totalAmount=?, notes=?, updatedAt=CURRENT_TIMESTAMP
    WHERE id=?
  `, [
    isPaid ? 1 : 0, paidDate, paymentMethod, checkNumber, bankName,
    lateFee, bouncedCheckFee, totalAmount, notes, id
  ], function(err) {
    if (err) {
      console.error('Update payment error:', err);
      res.status(500).json({ error: err.message });
    } else {
      console.log('Payment updated successfully, changes:', this.changes);
      res.json({ success: true, changes: this.changes });
    }
  });
});

// Create payment schedule for a contract
app.post('/api/contracts/:id/generate-payments', (req, res) => {
  console.log('POST /api/contracts/:id/generate-payments request');
  console.log('Contract ID:', req.params.id);

  const { id: contractId } = req.params;

  // First, get contract details
  db.get("SELECT * FROM Contracts WHERE id = ?", [contractId], (err, contract) => {
    if (err) {
      console.error('Error getting contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!contract) {
      return res.status(404).json({ error: 'Contract not found' });
    }

    // Check if payments already exist
    db.get("SELECT COUNT(*) as count FROM Payments WHERE contractId = ?", [contractId], (err, result) => {
      if (err) {
        console.error('Error checking existing payments:', err);
        return res.status(500).json({ error: err.message });
      }

      if (result.count > 0) {
        return res.status(400).json({ error: 'Payment schedule already exists for this contract' });
      }

      // Generate payment schedule
      const startDate = new Date(contract.startDate);
      const totalMonths = contract.contractDurationYears * 12;
      const payments = [];

      for (let i = 0; i < totalMonths; i++) {
        const dueDate = new Date(startDate);
        dueDate.setMonth(dueDate.getMonth() + i);
        dueDate.setDate(contract.paymentDay);

        payments.push([
          contractId,
          i + 1, // paymentNumber
          dueDate.toISOString().split('T')[0], // dueDate
          contract.monthlyAmount, // amount
          0, // lateFee
          0, // bouncedCheckFee
          contract.monthlyAmount // totalAmount
        ]);
      }

      // Insert all payments
      const insertSQL = `
        INSERT INTO Payments (contractId, paymentNumber, dueDate, amount, lateFee, bouncedCheckFee, totalAmount)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      let completed = 0;
      let hasError = false;

      payments.forEach((payment) => {
        db.run(insertSQL, payment, function(err) {
          if (err && !hasError) {
            hasError = true;
            console.error('Error inserting payment:', err);
            return res.status(500).json({ error: err.message });
          }

          completed++;
          if (completed === payments.length && !hasError) {
            console.log('Payment schedule generated successfully:', payments.length, 'payments');
            res.json({ success: true, paymentsCreated: payments.length });
          }
        });
      });
    });
  });
});

// Contract Financial Details API endpoints

// Get financial details for a contract
app.get('/api/contracts/:id/financial-details', (req, res) => {
  console.log('GET /api/contracts/:id/financial-details request');
  console.log('Contract ID:', req.params.id);

  const { id } = req.params;

  db.get("SELECT * FROM ContractFinancialDetails WHERE contractId = ?", [id], (err, row) => {
    if (err) {
      console.error('Error getting financial details:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (!row) {
      return res.status(404).json({ error: 'Financial details not found' });
    }

    const data = {
      ...row,
      areaPerProduct: parseFloat(row.areaPerProduct),
      meterPriceFirstYear: parseFloat(row.meterPriceFirstYear),
      annualIncreaseRate: parseFloat(row.annualIncreaseRate),
      latePenaltyPercentage: parseFloat(row.latePenaltyPercentage),
      finalInsurancePercentage: parseFloat(row.finalInsurancePercentage)
    };

    console.log('Financial details retrieved successfully');
    res.json(data);
  });
});

// Create or update financial details for a contract
app.post('/api/contracts/:id/financial-details', (req, res) => {
  console.log('POST /api/contracts/:id/financial-details request');
  console.log('Contract ID:', req.params.id);
  console.log('Received data:', req.body);

  const { id: contractId } = req.params;
  const {
    numberOfProducts,
    areaPerProduct,
    meterPriceFirstYear,
    annualIncreaseType,
    annualIncreaseRate,
    billingPeriodType,
    latePenaltyPercentage,
    gracePeriodDays,
    finalInsurancePercentage
  } = req.body;

  // Check if financial details already exist
  db.get("SELECT id FROM ContractFinancialDetails WHERE contractId = ?", [contractId], (err, existing) => {
    if (err) {
      console.error('Error checking existing financial details:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existing) {
      // Update existing
      db.run(`
        UPDATE ContractFinancialDetails SET
          numberOfProducts=?, areaPerProduct=?, meterPriceFirstYear=?, annualIncreaseType=?,
          annualIncreaseRate=?, billingPeriodType=?, latePenaltyPercentage=?, gracePeriodDays=?,
          finalInsurancePercentage=?, updatedAt=CURRENT_TIMESTAMP
        WHERE contractId=?
      `, [
        numberOfProducts, areaPerProduct, meterPriceFirstYear, annualIncreaseType,
        annualIncreaseRate, billingPeriodType, latePenaltyPercentage, gracePeriodDays,
        finalInsurancePercentage, contractId
      ], function(err) {
        if (err) {
          console.error('Update financial details error:', err);
          res.status(500).json({ error: err.message });
        } else {
          console.log('Financial details updated successfully');
          res.json({ success: true, id: existing.id });
        }
      });
    } else {
      // Insert new
      db.run(`
        INSERT INTO ContractFinancialDetails (
          contractId, numberOfProducts, areaPerProduct, meterPriceFirstYear, annualIncreaseType,
          annualIncreaseRate, billingPeriodType, latePenaltyPercentage, gracePeriodDays, finalInsurancePercentage
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        contractId, numberOfProducts, areaPerProduct, meterPriceFirstYear, annualIncreaseType,
        annualIncreaseRate, billingPeriodType, latePenaltyPercentage, gracePeriodDays, finalInsurancePercentage
      ], function(err) {
        if (err) {
          console.error('Insert financial details error:', err);
          res.status(500).json({ error: err.message });
        } else {
          console.log('Financial details inserted successfully, ID:', this.lastID);
          res.json({ success: true, id: this.lastID });
        }
      });
    }
  });
});

// Enhanced Contracts API endpoints

// Create new enhanced contract
app.post('/api/contracts/enhanced', (req, res) => {
  console.log('🔥 POST /api/contracts/enhanced request');
  console.log('🔥 Received data:', req.body);

  const {
    contractNumber,
    contractInternalId,
    contractDescription,
    contractSubject,
    clientId,
    contractType,
    contractStatus = 'نشط',
    contractDate,
    contractSigningDate, // Frontend sends this field
    startDate,
    actualStartDate,
    endDate,
    actualEndDate,
    contractDurationYears,
    assetOwner,
    financialGuarantorId,
    parentContractId,
    numberOfProducts = 1,
    hasUnifiedActivationDate = true,
    totalContractValue,
    monthlyAmount,
    paymentFrequency = 'شهري',
    irregularPaymentMonths,
    finalInsuranceRate = 0,
    checkStatus = 'لم يتقدم بالشيكات',
    annualIncreaseType = 'لا يوجد',
    annualIncreaseValue = 0,
    annualIncreaseStartYear = 2,
    lateFeeType = 'نسبة مئوية',
    lateFeeValue = 0,
    gracePeriodDays = 0,
    bouncedCheckFeeType = 'مبلغ ثابت',
    bouncedCheckFeeValue = 0,
    additionalFees,
    importantNotes,
    notes,
    products = [],
    partners = [],
    isActive = true
  } = req.body;

  // Map contractSigningDate to contractDate for database compatibility
  const finalContractDate = contractDate || contractSigningDate;

  // Validation
  if (!contractNumber || !clientId || !contractType || !startDate || !contractDurationYears || !totalContractValue) {
    return res.status(400).json({
      error: 'Required fields: contractNumber, clientId, contractType, startDate, contractDurationYears, totalContractValue'
    });
  }

  // Generate internal ID if not provided
  const internalId = contractInternalId || `INT-${Date.now()}`;

  // Handle optional fields - convert "none" to null and provide defaults
  const processedFinancialGuarantorId = financialGuarantorId === "none" ? null : financialGuarantorId;
  const processedParentContractId = parentContractId === "none" ? null : parentContractId;

  // Calculate end date if not provided
  const calculatedEndDate = endDate || (() => {
    const start = new Date(startDate);
    start.setFullYear(start.getFullYear() + contractDurationYears);
    return start.toISOString().split('T')[0];
  })();

  // Check if contract number already exists
  console.log('🔥 About to check if contract exists...');
  db.get("SELECT id FROM Contracts WHERE contractNumber = ?", [contractNumber], (err, existingContract) => {
    if (err) {
      console.error('🔥 Error checking existing contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existingContract) {
      console.log('🔥 Contract already exists');
      return res.status(400).json({ error: 'Contract number already exists' });
    }

    console.log('🔥 Contract does not exist, proceeding with insert...');

    // Insert new contract
    const insertSQL = `
      INSERT INTO Contracts (
        contractNumber, contractInternalId, contractDescription, contractSubject,
        clientId, contractType, contractStatus, contractDate, startDate, actualStartDate,
        endDate, actualEndDate, contractDurationYears, assetOwner, financialGuarantorId,
        parentContractId, numberOfProducts, hasUnifiedActivationDate, totalContractValue,
        monthlyAmount, paymentDay, paymentFrequency, firstInstallmentDate, paymentMethod, irregularPaymentMonths,
        finalInsuranceRate, checkStatus, annualIncreaseType, annualIncreaseValue, annualIncreaseStartYear, lateFeeType,
        lateFeeValue, gracePeriodDays, bouncedCheckFeeType, bouncedCheckFeeValue,
        additionalFees, earlyTerminationReason, earlyTerminationDate, importantNotes, systemFlags,
        autoTerminationSuggested, consecutiveMissedPayments, totalMissedPayments, notes, isActive
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      contractNumber, internalId, contractDescription, contractSubject,
      clientId, contractType, contractStatus, finalContractDate, startDate, actualStartDate || null,
      calculatedEndDate, actualEndDate || null, contractDurationYears, assetOwner || null, processedFinancialGuarantorId,
      processedParentContractId, numberOfProducts, hasUnifiedActivationDate ? 1 : 0, totalContractValue,
      monthlyAmount, 1, paymentFrequency, startDate, 'تحويل بنكي', irregularPaymentMonths || null, // paymentDay=1, firstInstallmentDate=startDate, paymentMethod='تحويل بنكي'
      finalInsuranceRate || 0, checkStatus, annualIncreaseType, annualIncreaseValue, annualIncreaseStartYear, lateFeeType,
      lateFeeValue, gracePeriodDays, bouncedCheckFeeType, bouncedCheckFeeValue,
      additionalFees || null, null, null, importantNotes || null, null, // earlyTerminationReason, earlyTerminationDate, systemFlags
      0, 0, 0, notes || null, isActive ? 1 : 0 // autoTerminationSuggested, consecutiveMissedPayments, totalMissedPayments
    ];

    console.log('DEBUG: Total values:', values.length);
    console.log('DEBUG: Values:', values);

    db.run(insertSQL, values, function(err) {
      if (err) {
        console.error('Insert contract error:', err);
        return res.status(500).json({ error: err.message });
      }

      const contractId = this.lastID;
      console.log('Contract inserted successfully, ID:', contractId);

      // Insert products if any
      if (products && products.length > 0) {
        const productInserts = products.map(product => [
          contractId,
          product.productLabel,
          product.area,
          product.meterPrice,
          product.activationDate,
          product.endDate,
          product.billingType || 'شهري',
          product.irregularBillingMonths || null,
          product.taxInfo || 0,
          product.taxRate || 0
        ]);

        const productSQL = `
          INSERT INTO ContractProducts (contractId, productLabel, area, meterPrice, activationDate, endDate, billingType, irregularBillingMonths, taxInfo, taxRate)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        productInserts.forEach(product => {
          db.run(productSQL, product, (err) => {
            if (err) console.error('Error inserting product:', err);
          });
        });
      }

      // Insert partners if any
      if (partners && partners.length > 0) {
        const partnerInserts = partners.map(partner => [
          contractId,
          partner.partnerId,
          partner.partnerType || 'شريك',
          partner.partnershipPercentage || 0
        ]);

        const partnerSQL = `
          INSERT INTO ContractPartners (contractId, partnerId, partnerType, partnershipPercentage)
          VALUES (?, ?, ?, ?)
        `;

        partnerInserts.forEach(partner => {
          db.run(partnerSQL, partner, (err) => {
            if (err) console.error('Error inserting partner:', err);
          });
        });
      }

      // Add timeline entry
      db.run(`
        INSERT INTO ContractTimeline (contractId, eventType, eventDate, eventDescription)
        VALUES (?, 'توقيع', ?, 'تم إنشاء العقد')
      `, [contractId, finalContractDate || startDate], (err) => {
        if (err) console.error('Error inserting timeline:', err);
      });

      res.json({ success: true, id: contractId });
    });
  });
});

// Get contract products
app.get('/api/contracts/:id/products', (req, res) => {
  console.log('GET /api/contracts/:id/products request');
  const { id } = req.params;

  db.all("SELECT * FROM ContractProducts WHERE contractId = ? AND isActive = 1", [id], (err, rows) => {
    if (err) {
      console.error('Error getting contract products:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Get contract partners
app.get('/api/contracts/:id/partners', (req, res) => {
  console.log('GET /api/contracts/:id/partners request');
  const { id } = req.params;

  const query = `
    SELECT cp.*, c.clientName, c.clientType
    FROM ContractPartners cp
    LEFT JOIN Clients c ON cp.partnerId = c.id
    WHERE cp.contractId = ? AND cp.isActive = 1
  `;

  db.all(query, [id], (err, rows) => {
    if (err) {
      console.error('Error getting contract partners:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Get contract timeline
app.get('/api/contracts/:id/timeline', (req, res) => {
  console.log('GET /api/contracts/:id/timeline request');
  const { id } = req.params;

  db.all("SELECT * FROM ContractTimeline WHERE contractId = ? AND isActive = 1 ORDER BY eventDate", [id], (err, rows) => {
    if (err) {
      console.error('Error getting contract timeline:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Get contract alerts
app.get('/api/contracts/:id/alerts', (req, res) => {
  console.log('GET /api/contracts/:id/alerts request');
  const { id } = req.params;

  db.all("SELECT * FROM ContractAlerts WHERE contractId = ? ORDER BY alertDate DESC", [id], (err, rows) => {
    if (err) {
      console.error('Error getting contract alerts:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Contract Calculations API

// Generate installment schedule for a contract
app.post('/api/contracts/:id/generate-installments', (req, res) => {
  console.log('POST /api/contracts/:id/generate-installments request');
  const { id } = req.params;

  // Get contract details with products
  const contractQuery = `
    SELECT c.*, cl.clientName
    FROM Contracts c
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE c.id = ?
  `;

  db.get(contractQuery, [id], (err, contract) => {
    if (err) {
      console.error('Error getting contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!contract) {
      return res.status(404).json({ error: 'Contract not found' });
    }

    // Get contract products
    db.all("SELECT * FROM ContractProducts WHERE contractId = ? AND isActive = 1", [id], (err, products) => {
      if (err) {
        console.error('Error getting contract products:', err);
        return res.status(500).json({ error: err.message });
      }

      // Calculate installments using the calculation engine
      const contractData = {
        ...contract,
        products: products || [],
        financialTerms: {
          totalContractValue: contract.totalContractValue,
          monthlyAmount: contract.monthlyAmount,
          paymentDay: contract.paymentDay,
          paymentFrequency: contract.paymentFrequency,
          firstInstallmentDate: contract.firstInstallmentDate,
          annualIncreaseType: contract.annualIncreaseType,
          annualIncreaseValue: contract.annualIncreaseValue,
          annualIncreaseStartYear: contract.annualIncreaseStartYear,
          lateFeeType: contract.lateFeeType,
          lateFeeValue: contract.lateFeeValue,
          gracePeriodDays: contract.gracePeriodDays,
          bouncedCheckFeeType: contract.bouncedCheckFeeType,
          bouncedCheckFeeValue: contract.bouncedCheckFeeValue,
        }
      };

      // Here we would use the calculation engine
      // For now, let's create a simple calculation
      const installments = [];
      const startDate = new Date(contract.startDate);
      const endDate = new Date(contract.endDate);
      const monthlyAmount = contract.monthlyAmount;
      const paymentDay = contract.paymentDay;

      let currentDate = new Date(startDate);
      let installmentNumber = 1;

      while (currentDate <= endDate) {
        const dueDate = new Date(currentDate);
        dueDate.setDate(paymentDay);
        if (dueDate < currentDate) {
          dueDate.setMonth(dueDate.getMonth() + 1);
        }

        const yearOfContract = Math.floor((installmentNumber - 1) / 12) + 1;

        // Apply annual increase
        let adjustedAmount = monthlyAmount;
        if (contract.annualIncreaseType === 'مبلغ ثابت' && yearOfContract >= contract.annualIncreaseStartYear) {
          adjustedAmount += contract.annualIncreaseValue * (yearOfContract - contract.annualIncreaseStartYear + 1);
        } else if (contract.annualIncreaseType === 'نسبة مئوية' && yearOfContract >= contract.annualIncreaseStartYear) {
          adjustedAmount += monthlyAmount * (contract.annualIncreaseValue / 100) * (yearOfContract - contract.annualIncreaseStartYear + 1);
        } else if (contract.annualIncreaseType === 'نسبة مركبة' && yearOfContract >= contract.annualIncreaseStartYear) {
          adjustedAmount = monthlyAmount * Math.pow(1 + (contract.annualIncreaseValue / 100), yearOfContract - contract.annualIncreaseStartYear + 1);
        }

        installments.push({
          contractId: id,
          installmentNumber,
          installmentDate: currentDate.toISOString().split('T')[0],
          paymentDueDate: dueDate.toISOString().split('T')[0],
          baseAmount: adjustedAmount,
          taxAmount: 0,
          installmentAmount: adjustedAmount,
          yearOfContract,
          remainingAmount: adjustedAmount,
          isPaid: 0,
          paidAmount: 0,
          penaltyAmount: 0,
          lateDays: 0,
        });

        currentDate.setMonth(currentDate.getMonth() + 1);
        installmentNumber++;
      }

      // Save installments to database
      const insertSQL = `
        INSERT INTO ContractInstallments (
          contractId, installmentNumber, installmentDate, installmentAmount,
          baseAmount, taxAmount, yearOfContract, paymentDueDate, remainingAmount,
          isPaid, paidAmount, penaltyAmount, lateDays
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      // Clear existing installments first
      db.run("DELETE FROM ContractInstallments WHERE contractId = ?", [id], (err) => {
        if (err) {
          console.error('Error clearing existing installments:', err);
          return res.status(500).json({ error: err.message });
        }

        // Insert new installments
        let insertedCount = 0;
        const totalInstallments = installments.length;

        if (totalInstallments === 0) {
          return res.json({ success: true, installments: [], message: 'No installments to generate' });
        }

        installments.forEach((installment) => {
          db.run(insertSQL, [
            installment.contractId,
            installment.installmentNumber,
            installment.installmentDate,
            installment.installmentAmount,
            installment.baseAmount,
            installment.taxAmount,
            installment.yearOfContract,
            installment.paymentDueDate,
            installment.remainingAmount,
            installment.isPaid,
            installment.paidAmount,
            installment.penaltyAmount,
            installment.lateDays
          ], function(err) {
            if (err) {
              console.error('Error inserting installment:', err);
            } else {
              insertedCount++;
              if (insertedCount === totalInstallments) {
                // Add timeline entry
                db.run(`
                  INSERT INTO ContractTimeline (contractId, eventType, eventDate, eventDescription)
                  VALUES (?, 'تعديل', ?, 'تم إنشاء جدول الأقساط')
                `, [id, new Date().toISOString().split('T')[0]], (err) => {
                  if (err) console.error('Error inserting timeline:', err);
                });

                res.json({
                  success: true,
                  installments,
                  message: `تم إنشاء ${totalInstallments} قسط بنجاح`
                });
              }
            }
          });
        });
      });
    });
  });
});

// Get contract installments
app.get('/api/contracts/:id/installments', (req, res) => {
  console.log('GET /api/contracts/:id/installments request');
  const { id } = req.params;

  const query = `
    SELECT * FROM ContractInstallments
    WHERE contractId = ?
    ORDER BY installmentNumber
  `;

  db.all(query, [id], (err, rows) => {
    if (err) {
      console.error('Error getting installments:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Update installment payment
app.put('/api/installments/:id/payment', (req, res) => {
  console.log('PUT /api/installments/:id/payment request');
  const { id } = req.params;
  const { paidAmount, paymentDate, paymentMethod, checkNumber, bankName, notes } = req.body;

  // Get installment details first
  db.get("SELECT * FROM ContractInstallments WHERE installmentId = ?", [id], (err, installment) => {
    if (err) {
      console.error('Error getting installment:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!installment) {
      return res.status(404).json({ error: 'Installment not found' });
    }

    // Calculate late days and penalty
    const dueDate = new Date(installment.paymentDueDate);
    const payDate = new Date(paymentDate);
    const lateDays = Math.max(0, Math.ceil((payDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)));

    // Get contract details for penalty calculation
    db.get("SELECT * FROM Contracts WHERE id = ?", [installment.contractId], (err, contract) => {
      if (err) {
        console.error('Error getting contract:', err);
        return res.status(500).json({ error: err.message });
      }

      let penaltyAmount = 0;
      if (lateDays > contract.gracePeriodDays) {
        const effectiveLateDays = lateDays - contract.gracePeriodDays;
        if (contract.lateFeeType === 'مبلغ ثابت') {
          penaltyAmount = contract.lateFeeValue * effectiveLateDays;
        } else if (contract.lateFeeType === 'نسبة مئوية') {
          penaltyAmount = installment.installmentAmount * (contract.lateFeeValue / 100) * effectiveLateDays;
        }
      }

      const remainingAmount = Math.max(0, installment.installmentAmount - paidAmount);
      const isPaid = paidAmount >= installment.installmentAmount ? 1 : 0;

      // Update installment
      db.run(`
        UPDATE ContractInstallments
        SET paidAmount = ?, remainingAmount = ?, isPaid = ?, paidDate = ?,
            lateDays = ?, penaltyAmount = ?, paymentMethod = ?, checkNumber = ?,
            bankName = ?, notes = ?, updatedAt = CURRENT_TIMESTAMP
        WHERE installmentId = ?
      `, [
        paidAmount, remainingAmount, isPaid, paymentDate,
        lateDays, penaltyAmount, paymentMethod, checkNumber,
        bankName, notes, id
      ], function(err) {
        if (err) {
          console.error('Error updating installment:', err);
          return res.status(500).json({ error: err.message });
        }

        // Add timeline entry
        db.run(`
          INSERT INTO ContractTimeline (contractId, eventType, eventDate, eventDescription)
          VALUES (?, 'دفعة', ?, ?)
        `, [
          installment.contractId,
          paymentDate,
          `تم دفع ${paidAmount} للقسط رقم ${installment.installmentNumber}`
        ], (err) => {
          if (err) console.error('Error inserting timeline:', err);
        });

        res.json({ success: true, message: 'تم تحديث الدفعة بنجاح' });
      });
    });
  });
});

// Calculate contract financial summary
app.get('/api/contracts/:id/financial-summary', (req, res) => {
  console.log('GET /api/contracts/:id/financial-summary request');
  const { id } = req.params;

  const query = `
    SELECT
      COUNT(*) as totalInstallments,
      SUM(installmentAmount) as totalContractValue,
      SUM(baseAmount) as totalBaseValue,
      SUM(taxAmount) as totalTaxValue,
      SUM(paidAmount) as totalPaid,
      SUM(remainingAmount) as totalOutstanding,
      SUM(penaltyAmount) as totalPenalties,
      SUM(CASE WHEN isPaid = 1 THEN 1 ELSE 0 END) as paidInstallments,
      SUM(CASE WHEN isPaid = 0 AND date(paymentDueDate) < date('now') THEN 1 ELSE 0 END) as overdueInstallments
    FROM ContractInstallments
    WHERE contractId = ?
  `;

  db.get(query, [id], (err, summary) => {
    if (err) {
      console.error('Error getting financial summary:', err);
      return res.status(500).json({ error: err.message });
    }

    const paymentProgress = summary.totalContractValue > 0
      ? (summary.totalPaid / summary.totalContractValue * 100)
      : 0;

    res.json({
      ...summary,
      paymentProgress: Math.round(paymentProgress * 100) / 100
    });
  });
});

// Financial Reports API

// Get financial summary
app.get('/api/reports/financial-summary', (req, res) => {
  console.log('GET /api/reports/financial-summary request');
  const { from, to } = req.query;

  let dateFilter = '';
  let params = [];

  if (from && to) {
    dateFilter = 'WHERE c.startDate BETWEEN ? AND ?';
    params = [from, to];
  }

  const query = `
    SELECT
      COUNT(c.id) as totalContracts,
      COUNT(CASE WHEN c.contractStatus = 'نشط' THEN 1 END) as activeContracts,
      COALESCE(SUM(c.totalContractValue), 0) as totalContractValue,
      COALESCE(SUM(ci.paidAmount), 0) as totalCollected,
      COALESCE(SUM(ci.remainingAmount), 0) as totalOutstanding,
      COALESCE(SUM(CASE WHEN ci.isPaid = 0 AND date(ci.paymentDueDate) < date('now') THEN ci.remainingAmount ELSE 0 END), 0) as totalOverdue,
      COALESCE(AVG(c.totalContractValue), 0) as averageContractValue
    FROM Contracts c
    LEFT JOIN ContractInstallments ci ON c.id = ci.contractId
    ${dateFilter}
  `;

  db.get(query, params, (err, summary) => {
    if (err) {
      console.error('Error getting financial summary:', err);
      return res.status(500).json({ error: err.message });
    }

    const collectionRate = summary.totalContractValue > 0
      ? (summary.totalCollected / summary.totalContractValue * 100)
      : 0;

    res.json({
      ...summary,
      collectionRate: Math.round(collectionRate * 100) / 100
    });
  });
});

// Get monthly revenue data
app.get('/api/reports/monthly-revenue', (req, res) => {
  console.log('GET /api/reports/monthly-revenue request');
  const { from, to } = req.query;

  let dateFilter = '';
  let params = [];

  if (from && to) {
    dateFilter = 'WHERE ci.installmentDate BETWEEN ? AND ?';
    params = [from, to];
  }

  const query = `
    SELECT
      strftime('%Y-%m', ci.installmentDate) as month,
      SUM(ci.installmentAmount) as revenue,
      SUM(ci.paidAmount) as collections,
      SUM(ci.remainingAmount) as outstanding
    FROM ContractInstallments ci
    ${dateFilter}
    GROUP BY strftime('%Y-%m', ci.installmentDate)
    ORDER BY month DESC
    LIMIT 12
  `;

  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Error getting monthly revenue:', err);
      return res.status(500).json({ error: err.message });
    }

    // Format month names
    const formattedRows = rows.map(row => ({
      ...row,
      month: new Date(row.month + '-01').toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long'
      })
    }));

    res.json(formattedRows);
  });
});

// Get contract type analysis
app.get('/api/reports/contract-types', (req, res) => {
  console.log('GET /api/reports/contract-types request');
  const { from, to } = req.query;

  let dateFilter = '';
  let params = [];

  if (from && to) {
    dateFilter = 'WHERE c.startDate BETWEEN ? AND ?';
    params = [from, to];
  }

  const query = `
    SELECT
      c.contractType,
      COUNT(c.id) as count,
      SUM(c.totalContractValue) as totalValue,
      AVG(c.totalContractValue) as averageValue,
      CASE
        WHEN SUM(c.totalContractValue) > 0
        THEN (SUM(ci.paidAmount) / SUM(c.totalContractValue) * 100)
        ELSE 0
      END as collectionRate
    FROM Contracts c
    LEFT JOIN ContractInstallments ci ON c.id = ci.contractId
    ${dateFilter}
    GROUP BY c.contractType
    ORDER BY totalValue DESC
  `;

  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Error getting contract type analysis:', err);
      return res.status(500).json({ error: err.message });
    }

    const formattedRows = rows.map(row => ({
      ...row,
      collectionRate: Math.round(row.collectionRate * 100) / 100
    }));

    res.json(formattedRows);
  });
});

// Get client analysis
app.get('/api/reports/client-analysis', (req, res) => {
  console.log('GET /api/reports/client-analysis request');
  const { from, to } = req.query;

  let dateFilter = '';
  let params = [];

  if (from && to) {
    dateFilter = 'WHERE c.startDate BETWEEN ? AND ?';
    params = [from, to];
  }

  const query = `
    SELECT
      cl.id as clientId,
      cl.clientName,
      COUNT(c.id) as contractsCount,
      SUM(c.totalContractValue) as totalValue,
      SUM(ci.paidAmount) as paidAmount,
      SUM(ci.remainingAmount) as outstandingAmount,
      CASE
        WHEN SUM(c.totalContractValue) > 0
        THEN (SUM(ci.paidAmount) / SUM(c.totalContractValue) * 100)
        ELSE 0
      END as paymentScore
    FROM Clients cl
    LEFT JOIN Contracts c ON cl.id = c.clientId
    LEFT JOIN ContractInstallments ci ON c.id = ci.contractId
    ${dateFilter}
    GROUP BY cl.id, cl.clientName
    HAVING contractsCount > 0
    ORDER BY totalValue DESC
    LIMIT 50
  `;

  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Error getting client analysis:', err);
      return res.status(500).json({ error: err.message });
    }

    const formattedRows = rows.map(row => ({
      ...row,
      paymentScore: Math.round(row.paymentScore * 100) / 100
    }));

    res.json(formattedRows);
  });
});

// Get overdue installments report
app.get('/api/reports/overdue-installments', (req, res) => {
  console.log('GET /api/reports/overdue-installments request');

  const query = `
    SELECT
      ci.*,
      c.contractNumber,
      cl.clientName,
      c.contractType,
      (julianday('now') - julianday(ci.paymentDueDate)) as daysOverdue
    FROM ContractInstallments ci
    JOIN Contracts c ON ci.contractId = c.id
    JOIN Clients cl ON c.clientId = cl.id
    WHERE ci.isPaid = 0
      AND date(ci.paymentDueDate) < date('now')
    ORDER BY daysOverdue DESC
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error getting overdue installments:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Get collection efficiency report
app.get('/api/reports/collection-efficiency', (req, res) => {
  console.log('GET /api/reports/collection-efficiency request');

  const query = `
    SELECT
      strftime('%Y-%m', ci.paidDate) as month,
      COUNT(ci.installmentId) as totalPayments,
      SUM(ci.paidAmount) as totalCollected,
      AVG(julianday(ci.paidDate) - julianday(ci.paymentDueDate)) as avgDaysLate,
      COUNT(CASE WHEN ci.paidDate <= ci.paymentDueDate THEN 1 END) as onTimePayments
    FROM ContractInstallments ci
    WHERE ci.isPaid = 1 AND ci.paidDate IS NOT NULL
    GROUP BY strftime('%Y-%m', ci.paidDate)
    ORDER BY month DESC
    LIMIT 12
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error getting collection efficiency:', err);
      return res.status(500).json({ error: err.message });
    }

    const formattedRows = rows.map(row => ({
      ...row,
      month: new Date(row.month + '-01').toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long'
      }),
      onTimeRate: row.totalPayments > 0 ? (row.onTimePayments / row.totalPayments * 100) : 0,
      avgDaysLate: Math.round(row.avgDaysLate * 10) / 10
    }));

    res.json(formattedRows);
  });
});

// Alerts System API

// Get alerts with filtering
app.get('/api/alerts', (req, res) => {
  console.log('GET /api/alerts request');
  const { tab, search, priority, type } = req.query;

  let whereConditions = ['1=1'];
  let params = [];

  // Tab filtering
  if (tab === 'unread') {
    whereConditions.push('isRead = 0');
  } else if (tab === 'action') {
    whereConditions.push('actionRequired = 1');
  } else if (tab === 'archived') {
    whereConditions.push('isArchived = 1');
  } else {
    whereConditions.push('isArchived = 0'); // Default: show non-archived
  }

  // Search filtering
  if (search && search.trim() !== '') {
    whereConditions.push('(alertTitle LIKE ? OR alertMessage LIKE ? OR contractNumber LIKE ? OR clientName LIKE ?)');
    const searchTerm = `%${search}%`;
    params.push(searchTerm, searchTerm, searchTerm, searchTerm);
  }

  // Priority filtering
  if (priority && priority !== 'all') {
    whereConditions.push('alertPriority = ?');
    params.push(priority);
  }

  // Type filtering
  if (type && type !== 'all') {
    whereConditions.push('alertType = ?');
    params.push(type);
  }

  const query = `
    SELECT ca.*, c.contractNumber, cl.clientName
    FROM ContractAlerts ca
    LEFT JOIN Contracts c ON ca.contractId = c.id
    LEFT JOIN Clients cl ON ca.clientId = cl.id
    WHERE ${whereConditions.join(' AND ')}
    ORDER BY ca.alertDate DESC, ca.alertPriority DESC
  `;

  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Error getting alerts:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Get alert statistics
app.get('/api/alerts/stats', (req, res) => {
  console.log('GET /api/alerts/stats request');

  const query = `
    SELECT
      COUNT(*) as totalAlerts,
      SUM(CASE WHEN isRead = 0 THEN 1 ELSE 0 END) as unreadAlerts,
      SUM(CASE WHEN alertPriority IN ('عالي', 'حرج') THEN 1 ELSE 0 END) as highPriorityAlerts,
      SUM(CASE WHEN actionRequired = 1 THEN 1 ELSE 0 END) as actionRequiredAlerts,
      SUM(CASE WHEN alertType = 'payment_overdue' THEN 1 ELSE 0 END) as overduePayments,
      SUM(CASE WHEN alertType = 'contract_expiring' THEN 1 ELSE 0 END) as contractExpirations,
      SUM(CASE WHEN alertType = 'system_alert' THEN 1 ELSE 0 END) as systemAlerts
    FROM ContractAlerts
    WHERE isArchived = 0
  `;

  db.get(query, [], (err, stats) => {
    if (err) {
      console.error('Error getting alert stats:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(stats);
  });
});

// Mark alert as read
app.put('/api/alerts/:id/read', (req, res) => {
  console.log('PUT /api/alerts/:id/read request');
  const { id } = req.params;

  db.run("UPDATE ContractAlerts SET isRead = 1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?", [id], function(err) {
    if (err) {
      console.error('Error marking alert as read:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Alert not found' });
    }

    res.json({ success: true, message: 'Alert marked as read' });
  });
});

// Archive alert
app.put('/api/alerts/:id/archive', (req, res) => {
  console.log('PUT /api/alerts/:id/archive request');
  const { id } = req.params;

  db.run("UPDATE ContractAlerts SET isArchived = 1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?", [id], function(err) {
    if (err) {
      console.error('Error archiving alert:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Alert not found' });
    }

    res.json({ success: true, message: 'Alert archived' });
  });
});

// Delete alert
app.delete('/api/alerts/:id', (req, res) => {
  console.log('DELETE /api/alerts/:id request');
  const { id } = req.params;

  db.run("DELETE FROM ContractAlerts WHERE id = ?", [id], function(err) {
    if (err) {
      console.error('Error deleting alert:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Alert not found' });
    }

    res.json({ success: true, message: 'Alert deleted' });
  });
});

// Create new alert
app.post('/api/alerts', (req, res) => {
  console.log('POST /api/alerts request');
  const {
    alertType,
    alertTitle,
    alertMessage,
    alertPriority = 'متوسط',
    contractId,
    clientId,
    alertDate,
    actionRequired = false,
    actionUrl
  } = req.body;

  if (!alertType || !alertTitle || !alertMessage) {
    return res.status(400).json({ error: 'Alert type, title, and message are required' });
  }

  db.run(`
    INSERT INTO ContractAlerts (
      alertType, alertTitle, alertMessage, alertPriority, contractId, clientId,
      alertDate, actionRequired, actionUrl, isRead, isArchived
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 0)
  `, [
    alertType, alertTitle, alertMessage, alertPriority, contractId, clientId,
    alertDate || new Date().toISOString(), actionRequired ? 1 : 0, actionUrl
  ], function(err) {
    if (err) {
      console.error('Error creating alert:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json({ success: true, id: this.lastID, message: 'Alert created successfully' });
  });
});

// Generate automatic alerts (to be called by a scheduler)
app.post('/api/alerts/generate', (req, res) => {
  console.log('POST /api/alerts/generate request');

  // Check for overdue payments
  const overdueQuery = `
    SELECT ci.*, c.contractNumber, cl.clientName, c.clientId
    FROM ContractInstallments ci
    JOIN Contracts c ON ci.contractId = c.id
    JOIN Clients cl ON c.clientId = cl.id
    WHERE ci.isPaid = 0
      AND date(ci.paymentDueDate) < date('now')
      AND NOT EXISTS (
        SELECT 1 FROM ContractAlerts ca
        WHERE ca.contractId = c.id
          AND ca.alertType = 'payment_overdue'
          AND date(ca.alertDate) = date('now')
      )
  `;

  db.all(overdueQuery, [], (err, overdueInstallments) => {
    if (err) {
      console.error('Error checking overdue payments:', err);
      return res.status(500).json({ error: err.message });
    }

    // Create alerts for overdue payments
    overdueInstallments.forEach(installment => {
      const daysOverdue = Math.ceil((new Date().getTime() - new Date(installment.paymentDueDate).getTime()) / (1000 * 60 * 60 * 24));

      db.run(`
        INSERT INTO ContractAlerts (
          alertType, alertTitle, alertMessage, alertPriority, contractId, clientId,
          alertDate, actionRequired, actionUrl, isRead, isArchived
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, ?, 0, 0)
      `, [
        'payment_overdue',
        `دفعة متأخرة - ${installment.contractNumber}`,
        `القسط رقم ${installment.installmentNumber} متأخر بـ ${daysOverdue} يوم للعميل ${installment.clientName}`,
        daysOverdue > 30 ? 'حرج' : daysOverdue > 15 ? 'عالي' : 'متوسط',
        installment.contractId,
        installment.clientId,
        new Date().toISOString(),
        `/contracts/${installment.contractId}`
      ], (err) => {
        if (err) console.error('Error creating overdue alert:', err);
      });
    });

    // Check for contracts expiring soon
    const expiringQuery = `
      SELECT c.*, cl.clientName
      FROM Contracts c
      JOIN Clients cl ON c.clientId = cl.id
      WHERE c.contractStatus = 'نشط'
        AND date(c.endDate) BETWEEN date('now') AND date('now', '+30 days')
        AND NOT EXISTS (
          SELECT 1 FROM ContractAlerts ca
          WHERE ca.contractId = c.id
            AND ca.alertType = 'contract_expiring'
            AND date(ca.alertDate) = date('now')
        )
    `;

    db.all(expiringQuery, [], (err, expiringContracts) => {
      if (err) {
        console.error('Error checking expiring contracts:', err);
        return res.status(500).json({ error: err.message });
      }

      // Create alerts for expiring contracts
      expiringContracts.forEach(contract => {
        const daysUntilExpiry = Math.ceil((new Date(contract.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));

        db.run(`
          INSERT INTO ContractAlerts (
            alertType, alertTitle, alertMessage, alertPriority, contractId, clientId,
            alertDate, actionRequired, actionUrl, isRead, isArchived
          ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, ?, 0, 0)
        `, [
          'contract_expiring',
          `عقد ينتهي قريباً - ${contract.contractNumber}`,
          `العقد مع ${contract.clientName} سينتهي خلال ${daysUntilExpiry} يوم`,
          daysUntilExpiry <= 7 ? 'حرج' : daysUntilExpiry <= 15 ? 'عالي' : 'متوسط',
          contract.id,
          contract.clientId,
          new Date().toISOString(),
          `/contracts/${contract.id}`
        ], (err) => {
          if (err) console.error('Error creating expiring contract alert:', err);
        });
      });

      res.json({
        success: true,
        message: `Generated ${overdueInstallments.length} overdue alerts and ${expiringContracts.length} expiring contract alerts`
      });
    });
  });
});

// Start server
const PORT = 5001;
app.listen(PORT, () => {
  console.log('Server running on http://localhost:' + PORT);
  console.log('Database: SQLite (contract-app.sqlite)');
  console.log('Ready to save data!');
  console.log('You can now open the app and test data saving');
});

// Close database on app termination
process.on('SIGINT', () => {
  console.log('\nClosing database...');
  db.close((err) => {
    if (err) {
      console.error('Error closing database:', err);
    } else {
      console.log('Database closed successfully');
    }
    process.exit(0);
  });
});
