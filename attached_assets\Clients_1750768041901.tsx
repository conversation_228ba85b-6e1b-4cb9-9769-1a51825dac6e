import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON> } from "wouter";
import { But<PERSON> } from "../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Input } from "../components/ui/input";
import { apiRequest } from "../lib/queryClient";
import { cn } from "../lib/utils";
import ClientCard from "../components/ClientCard";
import {
  Users,
  Plus,
  Search,
  Phone,
  Mail,
  MapPin,
  Edit,
  Trash2,
  Printer,
  ArrowRight
} from "lucide-react";
import labels from "../i18n";

const Clients = () => {
  const [lang] = useState<"ar" | "en">((localStorage.getItem("lang") as "ar" | "en") || "ar");
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [selectedClient, setSelectedClient] = useState<any>(null);
  const [showCard, setShowCard] = useState(false);
  const t = labels[lang];

  // Fetch clients
  const { data: clients = [], isLoading, error } = useQuery({
    queryKey: ["/api/clients"],
    queryFn: () => apiRequest("/api/clients"),
  });



  // Fetch contracts for selected client
  const { data: contracts = [] } = useQuery({
    queryKey: ["/api/contracts", selectedClient?.id],
    queryFn: () => apiRequest(`/api/contracts?clientId=${selectedClient?.id}`),
    enabled: !!selectedClient?.id,
  });

  // Handle client card view
  const handleViewClientCard = (client: any) => {
    setSelectedClient(client);
    setShowCard(true);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleViewContract = (contractId: number) => {
    // Navigate to contract details page
    console.log("View contract:", contractId);
  };

  const handleEditClient = () => {
    // Navigate to edit client page
    window.location.href = `/clients/edit/${selectedClient?.id}`;
  };

  // Filter clients based on search and type
  const filteredClients = clients.filter((client: any) => {
    const matchesSearch =
      client.clientName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.clientId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.clientPhoneWhatsapp?.includes(searchTerm) ||
      client.clientEmail?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.clientFinancial_Category?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = typeFilter === "all" || client.clientType === typeFilter;

    return matchesSearch && matchesType;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="mt-4 text-gray-600">جاري تحميل العملاء...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">خطأ في تحميل البيانات:</p>
          <p className="text-gray-600 mb-4">{error.message}</p>
          <Button onClick={() => window.location.reload()}>إعادة المحاولة</Button>
        </div>
      </div>
    );
  }

  // Show client card if selected
  if (showCard && selectedClient) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-reverse space-x-4">
                <Button variant="ghost" size="sm" onClick={() => setShowCard(false)}>
                  <ArrowRight className="h-4 w-4 ml-2" />
                  {lang === "ar" ? "العودة للعملاء" : "Back to Clients"}
                </Button>
                <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
                <div className="flex items-center space-x-reverse space-x-2">
                  <Printer className="h-5 w-5 text-primary" />
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                    {lang === "ar" ? "كارت العميل - " : "Client Card - "}{selectedClient.clientName}
                  </h1>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="py-8">
          <ClientCard
            client={selectedClient}
            contracts={contracts}
            showPrintButton={true}
            showEditButton={true}
            onPrint={handlePrint}
            onEdit={handleEditClient}
            onViewContract={handleViewContract}
          />
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-reverse space-x-2">
              <Users className="h-5 w-5 text-primary" />
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {t.clients}
              </h1>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                ({filteredClients.length} {lang === "ar" ? "عميل" : "clients"})
              </span>
            </div>
            <Link href="/clients/new">
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                {lang === "ar" ? "إضافة عميل جديد" : "Add New Client"}
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filter */}
        <div className="mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={lang === "ar" ? "البحث في العملاء..." : "Search clients..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600"
              title={lang === "ar" ? "فلترة حسب النوع" : "Filter by type"}
            >
              <option value="all">{lang === "ar" ? "جميع الأنواع" : "All Types"}</option>
              <option value="أفراد">{lang === "ar" ? "أفراد" : "Individuals"}</option>
              <option value="شركات">{lang === "ar" ? "شركات" : "Companies"}</option>
            </select>
          </div>
        </div>

        {/* Clients Grid */}
        {filteredClients.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Users className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {searchTerm
                  ? (lang === "ar" ? "لا توجد نتائج" : "No results found")
                  : (lang === "ar" ? "لا يوجد عملاء" : "No clients yet")
                }
              </h3>
              <p className="text-gray-500 dark:text-gray-400 text-center mb-4">
                {searchTerm
                  ? (lang === "ar" ? "جرب البحث بكلمات مختلفة" : "Try searching with different terms")
                  : (lang === "ar" ? "ابدأ بإضافة عميل جديد" : "Get started by adding a new client")
                }
              </p>
              {!searchTerm && (
                <Link href="/clients/new">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    {lang === "ar" ? "إضافة عميل جديد" : "Add New Client"}
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredClients.map((client: any) => (
              <Card key={client.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg mb-1">{client.clientName}</CardTitle>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {lang === "ar" ? "رقم العميل:" : "Client ID:"} {client.clientId}
                      </p>
                      <div className="flex gap-2 mt-2">
                        <span className={cn(
                          "inline-block px-2 py-1 rounded-full text-xs font-medium",
                          client.clientType === "أفراد"
                            ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                            : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                        )}>
                          {client.clientType}
                        </span>
                        {client.clientFinancial_Category && (
                          <span className="inline-block px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                            {client.clientFinancial_Category}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  {client.clientPhoneWhatsapp && (
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-600 dark:text-gray-300">{client.clientPhoneWhatsapp}</span>
                    </div>
                  )}

                  {client.clientEmail && (
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-600 dark:text-gray-300">{client.clientEmail}</span>
                    </div>
                  )}

                  {client.clientAddress && (
                    <div className="flex items-start gap-2 text-sm">
                      <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                      <span className="text-gray-600 dark:text-gray-300 line-clamp-2">{client.clientAddress}</span>
                    </div>
                  )}

                  {client.clientReg_Number && (
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-gray-500 dark:text-gray-400">
                        {lang === "ar" ? "رقم التسجيل:" : "Reg. No:"}
                      </span>
                      <span className="text-gray-600 dark:text-gray-300">{client.clientReg_Number}</span>
                    </div>
                  )}

                  {client.clientLegal_Rep && (
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-gray-500 dark:text-gray-400">
                        {lang === "ar" ? "الممثل القانوني:" : "Legal Rep:"}
                      </span>
                      <span className="text-gray-600 dark:text-gray-300">{client.clientLegal_Rep}</span>
                    </div>
                  )}

                  <div className="flex gap-2 pt-3">
                    <Button
                      variant="default"
                      size="sm"
                      className="flex-1 gap-2"
                      onClick={() => handleViewClientCard(client)}
                    >
                      <Printer className="h-4 w-4" />
                      {lang === "ar" ? "طباعة كارت العميل" : "Print Client Card"}
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </main>
    </div>
  );
};

export default Clients;