const express = require('express');
const router = express.Router();
const clientsController = require('../controllers/clientsController.cjs');

// Routes for clients management
router.get('/', clientsController.getAllClients);                    // GET /api/clients
router.get('/stats', clientsController.getClientsStats);             // GET /api/clients/stats
router.get('/by-client-id/:clientId', clientsController.getClientByClientId); // GET /api/clients/by-client-id/:clientId
router.get('/search/:query', clientsController.searchClients);       // GET /api/clients/search/:query
router.get('/:id/can-delete', clientsController.canDeleteClient);    // GET /api/clients/:id/can-delete (يجب أن يكون قبل /:id)
router.get('/:id', clientsController.getClientById);                 // GET /api/clients/:id
router.post('/', (req, res, next) => {
  console.log('🛣️ POST /api/clients route reached');
  console.log('📦 Request body:', req.body);
  next();
}, clientsController.createClient);                    // POST /api/clients
router.put('/:id', clientsController.updateClient);                  // PUT /api/clients/:id
router.delete('/:id', clientsController.deleteClient);               // DELETE /api/clients/:id

module.exports = router;
