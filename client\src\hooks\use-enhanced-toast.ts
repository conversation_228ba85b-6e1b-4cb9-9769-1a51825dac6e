import { useToast } from "./use-toast"

interface EnhancedErrorInfo {
  message: string;
  guidance?: string;
  severity?: 'critical' | 'error' | 'warning' | 'info';
  action?: string;
  retryable?: boolean;
  confirmable?: boolean;
  technical?: boolean;
  context?: any;
  operation?: string;
  title?: string;
}

interface ToastAction {
  label: string;
  onClick: () => void;
}

export function useEnhancedToast() {
  const { toast } = useToast()

  const showSuccess = (title: string, description?: string, action?: ToastAction) => {
    toast({
      title,
      description,
      variant: "default",
      duration: 3000,
      action: action ? {
        altText: action.label,
        label: action.label,
        onClick: action.onClick
      } : undefined
    })
  }

  const showError = (title: string, description?: string, action?: ToastAction) => {
    toast({
      title,
      description,
      variant: "destructive",
      duration: 7000,
      action: action ? {
        altText: action.label,
        label: action.label,
        onClick: action.onClick
      } : undefined
    })
  }

  const showWarning = (title: string, description?: string, action?: ToastAction) => {
    toast({
      title,
      description,
      variant: "warning",
      duration: 5000,
      action: action ? {
        altText: action.label,
        label: action.label,
        onClick: action.onClick
      } : undefined
    })
  }

  const showInfo = (title: string, description?: string, action?: ToastAction) => {
    toast({
      title,
      description,
      variant: "info",
      duration: 3000,
      action: action ? {
        altText: action.label,
        label: action.label,
        onClick: action.onClick
      } : undefined
    })
  }

  const showCritical = (title: string, description?: string, action?: ToastAction) => {
    toast({
      title,
      description,
      variant: "destructive",
      duration: 10000, // Longer duration for critical errors
      action: action ? {
        altText: action.label,
        label: action.label,
        onClick: action.onClick
      } : undefined
    })
  }

  // رسائل محددة للعمليات الشائعة
  const showSaveSuccess = (entityName: string = "البيانات") => {
    showSuccess(
      "تم الحفظ بنجاح",
      `تم حفظ ${entityName} بنجاح`
    )
  }

  const showUpdateSuccess = (entityName: string = "البيانات") => {
    showSuccess(
      "تم التحديث بنجاح",
      `تم تحديث ${entityName} بنجاح`
    )
  }

  const showDeleteSuccess = (entityName: string = "العنصر") => {
    showSuccess(
      "تم الحذف بنجاح",
      `تم حذف ${entityName} بنجاح`
    )
  }

  const showDeleteWarning = (entityName: string = "العنصر", details?: string) => {
    showWarning(
      "تم الحذف مع تحذير",
      details || `تم حذف ${entityName} ولكن هناك بيانات مرتبطة`
    )
  }

  const showSaveError = (error?: string) => {
    showError(
      "خطأ في الحفظ",
      error || "حدث خطأ أثناء حفظ البيانات"
    )
  }

  const showUpdateError = (error?: string) => {
    showError(
      "خطأ في التحديث",
      error || "حدث خطأ أثناء تحديث البيانات"
    )
  }

  const showDeleteError = (error?: string) => {
    showError(
      "خطأ في الحذف",
      error || "حدث خطأ أثناء حذف البيانات"
    )
  }

  const showLoadingError = (error?: string) => {
    showError(
      "خطأ في تحميل البيانات",
      error || "حدث خطأ أثناء تحميل البيانات"
    )
  }

  const showValidationError = (error?: string) => {
    showWarning(
      "خطأ في البيانات المدخلة",
      error || "يرجى التحقق من البيانات المدخلة"
    )
  }

  const showNetworkError = () => {
    showError(
      "خطأ في الاتصال",
      "تحقق من اتصال الإنترنت وحاول مرة أخرى"
    )
  }

  const showPermissionError = () => {
    showError(
      "غير مصرح",
      "ليس لديك صلاحية لتنفيذ هذا الإجراء"
    )
  }

  // Enhanced error display function
  const showEnhancedError = (errorInfo: EnhancedErrorInfo, options?: {
    onRetry?: () => void;
    onAction?: (action: string) => void;
  }) => {
    const { onRetry, onAction } = options || {};

    // Determine severity and duration
    const severity = errorInfo.severity || 'error';
    let toastFunction = showError;

    switch (severity) {
      case 'critical':
        toastFunction = showCritical;
        break;
      case 'error':
        toastFunction = showError;
        break;
      case 'warning':
        toastFunction = showWarning;
        break;
      case 'info':
        toastFunction = showInfo;
        break;
    }

    // Prepare action
    let action: ToastAction | undefined;

    if (errorInfo.retryable && onRetry) {
      action = {
        label: 'إعادة المحاولة',
        onClick: onRetry
      };
    } else if (errorInfo.action && onAction) {
      const actionLabels = {
        'search_client': 'البحث عن العميل',
        'add_client': 'إضافة عميل جديد',
        'add_product': 'إضافة منتج',
        'open_settings': 'فتح الإعدادات',
        'setup_reference_data': 'إعداد البيانات المرجعية',
        'review_form': 'مراجعة النموذج',
        'check_references': 'فحص المراجع',
        'complete_form': 'إكمال النموذج',
        'retry_now': 'إعادة المحاولة',
        'retry_later': 'المحاولة لاحقاً'
      };

      action = {
        label: actionLabels[errorInfo.action] || 'إجراء',
        onClick: () => onAction(errorInfo.action!)
      };
    }

    // Show toast with enhanced information
    toastFunction(
      errorInfo.title || errorInfo.message,
      errorInfo.guidance,
      action
    );
  };

  // Enhanced validation error display
  const showValidationErrors = (errors: Array<{field: string, message: string, guidance?: string}>) => {
    if (errors.length === 1) {
      const error = errors[0];
      showWarning(
        `خطأ في ${getFieldDisplayName(error.field)}`,
        error.guidance || error.message
      );
    } else {
      showWarning(
        `${errors.length} أخطاء في النموذج`,
        'يرجى مراجعة الحقول المميزة باللون الأحمر وتصحيح الأخطاء'
      );
    }
  };

  // Helper function to get field display names
  const getFieldDisplayName = (fieldName: string) => {
    const fieldNames = {
      'clientId': 'رقم العميل',
      'clientName': 'اسم العميل',
      'clientPhone': 'رقم الهاتف',
      'clientEmail': 'البريد الإلكتروني',
      'contractNumber': 'رقم العقد',
      'contractStartDate': 'تاريخ بداية العقد',
      'productName': 'اسم المنتج',
      'area': 'المساحة',
      'meterPrice': 'سعر المتر'
    };
    return fieldNames[fieldName] || fieldName;
  };

  return {
    // الدوال الأساسية
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showCritical,

    // رسائل العمليات الشائعة
    showSaveSuccess,
    showUpdateSuccess,
    showDeleteSuccess,
    showDeleteWarning,

    // رسائل الأخطاء الشائعة
    showSaveError,
    showUpdateError,
    showDeleteError,
    showLoadingError,
    showValidationError,
    showNetworkError,
    showPermissionError,

    // Enhanced error functions
    showEnhancedError,
    showValidationErrors,

    // الدالة الأصلية للحالات المخصصة
    toast
  }
}
