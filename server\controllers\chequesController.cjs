const chequesService = require('../services/chequesService.cjs');

exports.getAllCheques = async (req, res) => {
  try {
    const cheques = await chequesService.getAllCheques();
    res.json(cheques);
  } catch (error) {
    res.status(500).json({ error: 'فشل في جلب الشيكات', details: error.message });
  }
};

exports.getChequeById = async (req, res) => {
  try {
    const cheque = await chequesService.getChequeById(req.params.id);
    if (!cheque) return res.status(404).json({ error: 'الشيك غير موجود' });
    res.json(cheque);
  } catch (error) {
    res.status(500).json({ error: 'فشل في جلب الشيك', details: error.message });
  }
};

exports.createCheque = async (req, res) => {
  try {
    const newCheque = await chequesService.createCheque(req.body);
    res.status(201).json(newCheque);
  } catch (error) {
    res.status(400).json({ error: 'فشل في إنشاء الشيك', details: error.message });
  }
};
