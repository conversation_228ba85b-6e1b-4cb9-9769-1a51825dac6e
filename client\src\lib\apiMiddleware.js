// ===== API MIDDLEWARE =====
// Author: Augment Code
// Description: Centralized API middleware for handling requests with loading, error handling, and caching

import { useAppStore } from '../store/appStore';

// API configuration
const API_CONFIG = {
  baseURL: '',
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
  cacheTimeout: 5 * 60 * 1000 // 5 minutes
};

// Request cache
const requestCache = new Map();

// Request interceptors
const requestInterceptors = [];
const responseInterceptors = [];

/**
 * API Middleware class for handling all API requests
 */
class APIMiddleware {
  constructor() {
    this.pendingRequests = new Map();
    this.requestId = 0;
  }

  /**
   * Generate unique request ID
   */
  generateRequestId() {
    return `req_${++this.requestId}_${Date.now()}`;
  }

  /**
   * Create cache key for request
   */
  createCacheKey(url, options = {}) {
    const method = options.method || 'GET';
    const body = options.body ? JSON.stringify(options.body) : '';
    return `${method}:${url}:${body}`;
  }

  /**
   * Check if request is cacheable
   */
  isCacheable(method, options = {}) {
    return method === 'GET' && !options.noCache;
  }

  /**
   * Get cached response
   */
  getCachedResponse(cacheKey) {
    const cached = requestCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < API_CONFIG.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  /**
   * Cache response
   */
  setCachedResponse(cacheKey, data) {
    requestCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Apply request interceptors
   */
  async applyRequestInterceptors(url, options) {
    let modifiedOptions = { ...options };
    
    for (const interceptor of requestInterceptors) {
      try {
        const result = await interceptor(url, modifiedOptions);
        if (result) {
          modifiedOptions = result;
        }
      } catch (error) {
        console.warn('Request interceptor failed:', error);
      }
    }
    
    return modifiedOptions;
  }

  /**
   * Apply response interceptors
   */
  async applyResponseInterceptors(response, url, options) {
    let modifiedResponse = response;
    
    for (const interceptor of responseInterceptors) {
      try {
        const result = await interceptor(modifiedResponse, url, options);
        if (result) {
          modifiedResponse = result;
        }
      } catch (error) {
        console.warn('Response interceptor failed:', error);
      }
    }
    
    return modifiedResponse;
  }

  /**
   * Sleep function for retry delays
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Make HTTP request with retry logic
   */
  async makeRequest(url, options = {}, attempt = 1) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response;
    } catch (error) {
      clearTimeout(timeoutId);

      // Retry logic for network errors
      if (attempt < API_CONFIG.retryAttempts && 
          (error.name === 'TypeError' || error.name === 'AbortError')) {
        
        console.warn(`Request failed (attempt ${attempt}), retrying...`, error);
        await this.sleep(API_CONFIG.retryDelay * attempt);
        return this.makeRequest(url, options, attempt + 1);
      }

      throw error;
    }
  }

  /**
   * Main API request method
   */
  async request(endpoint, options = {}) {
    const requestId = this.generateRequestId();
    const url = endpoint.startsWith('http') ? endpoint : `${API_CONFIG.baseURL}${endpoint}`;
    const method = options.method || 'GET';
    
    // Get app store actions
    const appActions = useAppStore.getState().actions;
    
    try {
      // Check for duplicate requests
      const requestKey = `${method}:${url}`;
      if (this.pendingRequests.has(requestKey)) {
        console.warn('Duplicate request detected, waiting for existing request...');
        return await this.pendingRequests.get(requestKey);
      }

      // Check cache for GET requests
      const cacheKey = this.createCacheKey(url, options);
      if (this.isCacheable(method, options)) {
        const cachedResponse = this.getCachedResponse(cacheKey);
        if (cachedResponse) {
          console.log('Returning cached response for:', url);
          return cachedResponse;
        }
      }

      // Start loading
      appActions.addLoadingOperation(requestId, options.loadingMessage || 'جاري التحميل...');

      // Apply request interceptors
      const processedOptions = await this.applyRequestInterceptors(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      });

      // Create request promise
      const requestPromise = this.executeRequest(url, processedOptions, requestId, cacheKey);
      
      // Store pending request
      this.pendingRequests.set(requestKey, requestPromise);

      // Execute request
      const result = await requestPromise;

      // Remove from pending requests
      this.pendingRequests.delete(requestKey);

      return result;

    } catch (error) {
      // Remove from pending requests
      const requestKey = `${method}:${url}`;
      this.pendingRequests.delete(requestKey);

      // Handle error
      console.error('API request failed:', error);
      
      // Record error in performance metrics
      appActions.incrementErrorCount();

      // Show error notification if enabled
      if (options.showErrorNotification !== false) {
        appActions.showError(
          options.errorMessage || error.message || 'حدث خطأ في الاتصال بالخادم'
        );
      }

      throw error;
    } finally {
      // Stop loading
      appActions.removeLoadingOperation(requestId);
    }
  }

  /**
   * Execute the actual request
   */
  async executeRequest(url, options, requestId, cacheKey) {
    const startTime = Date.now();
    
    try {
      // Make the request
      const response = await this.makeRequest(url, options);
      
      // Record response time
      const responseTime = Date.now() - startTime;
      const appActions = useAppStore.getState().actions;
      appActions.recordApiResponseTime(url, responseTime);

      // Apply response interceptors
      const processedResponse = await this.applyResponseInterceptors(response, url, options);

      // Parse response
      const data = await processedResponse.json();

      // Cache GET requests
      if (this.isCacheable(options.method, options)) {
        this.setCachedResponse(cacheKey, data);
      }

      return data;

    } catch (error) {
      // Enhanced error information
      const enhancedError = new Error(error.message);
      enhancedError.url = url;
      enhancedError.method = options.method;
      enhancedError.requestId = requestId;
      enhancedError.timestamp = new Date().toISOString();
      
      throw enhancedError;
    }
  }

  /**
   * Convenience methods for different HTTP methods
   */
  async get(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: 'GET' });
  }

  async post(endpoint, data, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  async put(endpoint, data, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  async patch(endpoint, data, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'PATCH',
      body: JSON.stringify(data)
    });
  }

  async delete(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * Upload file with progress tracking
   */
  async upload(endpoint, formData, options = {}) {
    const requestId = this.generateRequestId();
    const url = `${API_CONFIG.baseURL}${endpoint}`;
    const appActions = useAppStore.getState().actions;

    try {
      appActions.addLoadingOperation(requestId, options.loadingMessage || 'جاري رفع الملف...');

      const response = await fetch(url, {
        method: 'POST',
        body: formData,
        ...options
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (options.showSuccessNotification !== false) {
        appActions.showSuccess(options.successMessage || 'تم رفع الملف بنجاح');
      }

      return data;

    } catch (error) {
      console.error('File upload failed:', error);
      
      if (options.showErrorNotification !== false) {
        appActions.showError(
          options.errorMessage || error.message || 'فشل في رفع الملف'
        );
      }

      throw error;
    } finally {
      appActions.removeLoadingOperation(requestId);
    }
  }

  /**
   * Clear cache
   */
  clearCache(pattern = null) {
    if (pattern) {
      for (const [key] of requestCache) {
        if (key.includes(pattern)) {
          requestCache.delete(key);
        }
      }
    } else {
      requestCache.clear();
    }
  }

  /**
   * Add request interceptor
   */
  addRequestInterceptor(interceptor) {
    requestInterceptors.push(interceptor);
  }

  /**
   * Add response interceptor
   */
  addResponseInterceptor(interceptor) {
    responseInterceptors.push(interceptor);
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: requestCache.size,
      keys: Array.from(requestCache.keys())
    };
  }
}

// Create singleton instance
const apiMiddleware = new APIMiddleware();

// Add default interceptors
apiMiddleware.addRequestInterceptor((url, options) => {
  // Add timestamp to prevent caching issues
  if (options.method === 'GET' && !options.noTimestamp) {
    const separator = url.includes('?') ? '&' : '?';
    const timestampedUrl = `${url}${separator}_t=${Date.now()}`;
    return { ...options, url: timestampedUrl };
  }
  return options;
});

apiMiddleware.addResponseInterceptor((response, url, options) => {
  // Log successful requests in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`✅ API Success: ${options.method || 'GET'} ${url}`);
  }
  return response;
});

export default apiMiddleware;
