import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import {
  User,
  Mail,
  Phone,
  Shield,
  Key,
  Save,
  X,
  ArrowLeft,
  AlertTriangle,
  Eye,
  EyeOff
} from "lucide-react";

// User schema - will be moved to shared/schema.ts when Users table is created
const userSchema = z.object({
  username: z.string().min(3, "اسم المستخدم يجب أن يكون 3 أحرف على الأقل"),
  email: z.string().email("البريد الإلكتروني غير صحيح"),
  password: z.string().min(6, "كلمة المرور يجب أن تكون 6 أحرف على الأقل"),
  confirmPassword: z.string(),
  firstName: z.string().min(1, "الاسم الأول مطلوب"),
  lastName: z.string().min(1, "الاسم الأخير مطلوب"),
  phoneNumber: z.string().optional(),
  role: z.enum(['admin', 'manager', 'user', 'viewer']).default('user'),
  department: z.string().optional(),
  isActive: z.boolean().default(true),
  canCreateContracts: z.boolean().default(false),
  canEditContracts: z.boolean().default(false),
  canDeleteContracts: z.boolean().default(false),
  canViewReports: z.boolean().default(false),
  canManageUsers: z.boolean().default(false),
  canManageSettings: z.boolean().default(false),
  notes: z.string().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "كلمات المرور غير متطابقة",
  path: ["confirmPassword"],
});

type UserFormData = z.infer<typeof userSchema>;

const UsersNew = () => {
  const [, setLocation] = useLocation();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { toast } = useToast();

  const form = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
      firstName: "",
      lastName: "",
      phoneNumber: "",
      role: "user",
      department: "",
      isActive: true,
      canCreateContracts: false,
      canEditContracts: false,
      canDeleteContracts: false,
      canViewReports: false,
      canManageUsers: false,
      canManageSettings: false,
      notes: "",
    },
  });

  const onSubmit = async (data: UserFormData) => {
    try {
      // TODO: Replace with actual API call when Users table is created
      console.log("User data to be saved:", data);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "تم إنشاء المستخدم بنجاح",
        description: `تم إنشاء المستخدم ${data.firstName} ${data.lastName} بنجاح`,
      });

      // Navigate back to users list (when it exists)
      setLocation("/users");
    } catch (error) {
      toast({
        title: "خطأ في إنشاء المستخدم",
        description: "حدث خطأ أثناء إنشاء المستخدم. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    }
  };

  const roleOptions = [
    { value: "admin", label: "مدير النظام", description: "صلاحيات كاملة" },
    { value: "manager", label: "مدير", description: "صلاحيات إدارية" },
    { value: "user", label: "مستخدم", description: "صلاحيات أساسية" },
    { value: "viewer", label: "مشاهد", description: "صلاحيات عرض فقط" },
  ];

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setLocation("/users")}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            العودة
          </Button>
          <div>
            <h1 className="text-3xl font-bold">إضافة مستخدم جديد</h1>
            <p className="text-muted-foreground">إنشاء حساب مستخدم جديد في النظام</p>
          </div>
        </div>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              المعلومات الأساسية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">الاسم الأول *</Label>
                <Input
                  id="firstName"
                  {...form.register("firstName")}
                  placeholder="أدخل الاسم الأول"
                />
                {form.formState.errors.firstName && (
                  <p className="text-sm text-red-600">{form.formState.errors.firstName.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="lastName">الاسم الأخير *</Label>
                <Input
                  id="lastName"
                  {...form.register("lastName")}
                  placeholder="أدخل الاسم الأخير"
                />
                {form.formState.errors.lastName && (
                  <p className="text-sm text-red-600">{form.formState.errors.lastName.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="email">البريد الإلكتروني *</Label>
                <Input
                  id="email"
                  type="email"
                  {...form.register("email")}
                  placeholder="<EMAIL>"
                />
                {form.formState.errors.email && (
                  <p className="text-sm text-red-600">{form.formState.errors.email.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="phoneNumber">رقم الهاتف</Label>
                <Input
                  id="phoneNumber"
                  {...form.register("phoneNumber")}
                  placeholder="***********"
                />
              </div>

              <div>
                <Label htmlFor="department">القسم</Label>
                <Input
                  id="department"
                  {...form.register("department")}
                  placeholder="أدخل اسم القسم"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              معلومات الحساب
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="username">اسم المستخدم *</Label>
                <Input
                  id="username"
                  {...form.register("username")}
                  placeholder="أدخل اسم المستخدم"
                />
                {form.formState.errors.username && (
                  <p className="text-sm text-red-600">{form.formState.errors.username.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="role">الدور *</Label>
                <Select
                  value={form.watch("role")}
                  onValueChange={(value) => form.setValue("role", value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الدور" />
                  </SelectTrigger>
                  <SelectContent>
                    {roleOptions.map((role) => (
                      <SelectItem key={role.value} value={role.value}>
                        <div>
                          <div className="font-medium">{role.label}</div>
                          <div className="text-sm text-muted-foreground">{role.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="password">كلمة المرور *</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    {...form.register("password")}
                    placeholder="أدخل كلمة المرور"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {form.formState.errors.password && (
                  <p className="text-sm text-red-600">{form.formState.errors.password.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="confirmPassword">تأكيد كلمة المرور *</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    {...form.register("confirmPassword")}
                    placeholder="أعد إدخال كلمة المرور"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {form.formState.errors.confirmPassword && (
                  <p className="text-sm text-red-600">{form.formState.errors.confirmPassword.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              الصلاحيات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="canCreateContracts"
                  checked={form.watch("canCreateContracts")}
                  onCheckedChange={(checked) => form.setValue("canCreateContracts", !!checked)}
                />
                <Label htmlFor="canCreateContracts">إنشاء العقود</Label>
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="canEditContracts"
                  checked={form.watch("canEditContracts")}
                  onCheckedChange={(checked) => form.setValue("canEditContracts", !!checked)}
                />
                <Label htmlFor="canEditContracts">تعديل العقود</Label>
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="canDeleteContracts"
                  checked={form.watch("canDeleteContracts")}
                  onCheckedChange={(checked) => form.setValue("canDeleteContracts", !!checked)}
                />
                <Label htmlFor="canDeleteContracts">حذف العقود</Label>
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="canViewReports"
                  checked={form.watch("canViewReports")}
                  onCheckedChange={(checked) => form.setValue("canViewReports", !!checked)}
                />
                <Label htmlFor="canViewReports">عرض التقارير</Label>
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="canManageUsers"
                  checked={form.watch("canManageUsers")}
                  onCheckedChange={(checked) => form.setValue("canManageUsers", !!checked)}
                />
                <Label htmlFor="canManageUsers">إدارة المستخدمين</Label>
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="canManageSettings"
                  checked={form.watch("canManageSettings")}
                  onCheckedChange={(checked) => form.setValue("canManageSettings", !!checked)}
                />
                <Label htmlFor="canManageSettings">إدارة الإعدادات</Label>
              </div>
            </div>

            <div className="flex items-center space-x-2 space-x-reverse">
              <Checkbox
                id="isActive"
                checked={form.watch("isActive")}
                onCheckedChange={(checked) => form.setValue("isActive", !!checked)}
              />
              <Label htmlFor="isActive">حساب نشط</Label>
            </div>
          </CardContent>
        </Card>

        {/* Notes */}
        <Card>
          <CardHeader>
            <CardTitle>ملاحظات</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              {...form.register("notes")}
              placeholder="أدخل أي ملاحظات إضافية..."
              rows={3}
            />
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex items-center justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => setLocation("/users")}
            className="gap-2"
          >
            <X className="h-4 w-4" />
            إلغاء
          </Button>
          <Button type="submit" className="gap-2">
            <Save className="h-4 w-4" />
            حفظ المستخدم
          </Button>
        </div>
      </form>

      {/* Warning Notice */}
      <div className="text-center text-sm text-muted-foreground mt-8 p-4 bg-yellow-50 rounded-lg">
        <AlertTriangle className="h-5 w-5 mx-auto mb-2 text-yellow-600" />
        <p>هذه الصفحة جاهزة للاستخدام</p>
        <p>سيتم ربطها بقاعدة البيانات عند إنشاء جدول المستخدمين</p>
      </div>
    </div>
  );
};

export default UsersNew;
