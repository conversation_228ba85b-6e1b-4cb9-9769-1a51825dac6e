import React from 'react';
import { useToast, Toast } from '../../hooks/use-toast';
import { X, CheckCircle, AlertCircle } from 'lucide-react';

const ToastContainer = () => {
  const { toasts } = useToast();

  if (toasts.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map((toast) => (
        <ToastItem key={toast.id} toast={toast} />
      ))}
    </div>
  );
};

const ToastItem = ({ toast }: { toast: Toast }) => {
  const { toasts } = useToast();
  const isSuccess = toast.variant === 'default';
  const isError = toast.variant === 'destructive';

  return (
    <div
      className={`
        flex items-center gap-3 p-4 rounded-lg shadow-lg border min-w-[300px] max-w-[400px]
        animate-in slide-in-from-right-full duration-300
        ${isSuccess 
          ? 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200' 
          : isError 
          ? 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200'
          : 'bg-white border-gray-200 text-gray-800 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200'
        }
      `}
    >
      {/* Icon */}
      <div className="flex-shrink-0">
        {isSuccess && <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />}
        {isError && <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        {toast.title && (
          <div className="font-medium text-sm">
            {toast.title}
          </div>
        )}
        {toast.description && (
          <div className="text-sm opacity-90 mt-1">
            {toast.description}
          </div>
        )}
      </div>

      {/* Close button */}
      <button
        type="button"
        title="Close notification"
        onClick={() => {
          // Remove toast manually if needed
          const index = toasts.findIndex((t: Toast) => t.id === toast.id);
          if (index > -1) {
            toasts.splice(index, 1);
          }
        }}
        className="flex-shrink-0 p-1 rounded-md hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  );
};

export default ToastContainer;
