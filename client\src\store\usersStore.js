// ===== USERS STORE =====
// Author: Augment Code
// Description: Centralized state management for users using Zustand

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import apiMiddleware from '../lib/apiMiddleware';

// Initial state
const initialState = {
  // Users data
  users: [],
  currentUser: null,
  selectedUser: null,
  
  // Search and filters
  searchQuery: '',
  roleFilter: '',
  statusFilter: '',
  departmentFilter: '',
  
  // Loading states
  isLoading: false,
  isAdding: false,
  isUpdating: false,
  isDeleting: false,
  isChangingPassword: false,
  isUpdatingPermissions: false,
  
  // Error states
  error: null,
  addError: null,
  updateError: null,
  deleteError: null,
  passwordError: null,
  permissionsError: null,
  
  // Cache and metadata
  lastFetched: null,
  totalCount: 0,
  
  // UI state
  showUserDetails: false,
  
  // User permissions and roles
  userPermissions: [],
  availableRoles: [],
  permissionsLoading: false,
  rolesLoading: false
};

// Create the users store
export const useUsersStore = create()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Actions
        actions: {
          // Fetch all users
          async fetchUsers(forceRefresh = false) {
            const state = get();
            
            if (state.isLoading || (!forceRefresh && state.lastFetched && 
                Date.now() - new Date(state.lastFetched).getTime() < 30000)) {
              return state.users;
            }

            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const users = await apiMiddleware.get('/api/users', {
                loadingMessage: 'جاري تحميل المستخدمين...',
                showErrorNotification: true
              });

              set((state) => {
                state.users = users || [];
                state.totalCount = users?.length || 0;
                state.isLoading = false;
                state.lastFetched = new Date().toISOString();
                state.error = null;
              });

              return users;
            } catch (error) {
              console.error('Failed to fetch users:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحميل المستخدمين';
              });

              throw error;
            }
          },

          // Fetch current user
          async fetchCurrentUser() {
            try {
              const currentUser = await apiMiddleware.get('/api/auth/me', {
                showErrorNotification: false
              });

              set((state) => {
                state.currentUser = currentUser;
              });

              return currentUser;
            } catch (error) {
              console.error('Failed to fetch current user:', error);
              
              set((state) => {
                state.currentUser = null;
              });

              return null;
            }
          },

          // Search users
          async searchUsers(query) {
            set((state) => {
              state.searchQuery = query;
              state.isLoading = true;
              state.error = null;
            });

            try {
              const url = query 
                ? `/api/users/search?q=${encodeURIComponent(query)}`
                : '/api/users';

              const users = await apiMiddleware.get(url, {
                loadingMessage: query ? 'جاري البحث...' : 'جاري تحميل المستخدمين...',
                showErrorNotification: true
              });

              set((state) => {
                state.users = users || [];
                state.totalCount = users?.length || 0;
                state.isLoading = false;
                state.error = null;
              });

              return users;
            } catch (error) {
              console.error('Failed to search users:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في البحث عن المستخدمين';
              });

              throw error;
            }
          },

          // Add new user
          async addUser(userData) {
            set((state) => {
              state.isAdding = true;
              state.addError = null;
            });

            try {
              const newUser = await apiMiddleware.post('/api/users', userData, {
                loadingMessage: 'جاري إضافة المستخدم...',
                successMessage: 'تم إضافة المستخدم بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.users.unshift(newUser);
                state.totalCount += 1;
                state.isAdding = false;
                state.addError = null;
              });

              return { success: true, data: newUser };
            } catch (error) {
              console.error('Failed to add user:', error);
              
              set((state) => {
                state.isAdding = false;
                state.addError = error.message || 'فشل في إضافة المستخدم';
              });

              return { success: false, error: error.message };
            }
          },

          // Update user
          async updateUser(userId, updates) {
            set((state) => {
              state.isUpdating = true;
              state.updateError = null;
            });

            try {
              const updatedUser = await apiMiddleware.put(`/api/users/${userId}`, updates, {
                loadingMessage: 'جاري تحديث المستخدم...',
                successMessage: 'تم تحديث المستخدم بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                const index = state.users.findIndex(user => user.id === userId);
                if (index !== -1) {
                  state.users[index] = updatedUser;
                }
                
                if (state.selectedUser?.id === userId) {
                  state.selectedUser = updatedUser;
                }
                
                if (state.currentUser?.id === userId) {
                  state.currentUser = updatedUser;
                }
                
                state.isUpdating = false;
                state.updateError = null;
              });

              return { success: true, data: updatedUser };
            } catch (error) {
              console.error('Failed to update user:', error);
              
              set((state) => {
                state.isUpdating = false;
                state.updateError = error.message || 'فشل في تحديث المستخدم';
              });

              return { success: false, error: error.message };
            }
          },

          // Delete user
          async deleteUser(userId) {
            set((state) => {
              state.isDeleting = true;
              state.deleteError = null;
            });

            try {
              await apiMiddleware.delete(`/api/users/${userId}`, {
                loadingMessage: 'جاري حذف المستخدم...',
                successMessage: 'تم حذف المستخدم بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.users = state.users.filter(user => user.id !== userId);
                state.totalCount -= 1;
                
                if (state.selectedUser?.id === userId) {
                  state.selectedUser = null;
                  state.showUserDetails = false;
                }
                
                state.isDeleting = false;
                state.deleteError = null;
              });

              return { success: true };
            } catch (error) {
              console.error('Failed to delete user:', error);
              
              set((state) => {
                state.isDeleting = false;
                state.deleteError = error.message || 'فشل في حذف المستخدم';
              });

              return { success: false, error: error.message };
            }
          },

          // Change user password
          async changeUserPassword(userId, passwordData) {
            set((state) => {
              state.isChangingPassword = true;
              state.passwordError = null;
            });

            try {
              await apiMiddleware.put(`/api/users/${userId}/password`, passwordData, {
                loadingMessage: 'جاري تغيير كلمة المرور...',
                successMessage: 'تم تغيير كلمة المرور بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.isChangingPassword = false;
                state.passwordError = null;
              });

              return { success: true };
            } catch (error) {
              console.error('Failed to change password:', error);
              
              set((state) => {
                state.isChangingPassword = false;
                state.passwordError = error.message || 'فشل في تغيير كلمة المرور';
              });

              return { success: false, error: error.message };
            }
          },

          // Update user permissions
          async updateUserPermissions(userId, permissions) {
            set((state) => {
              state.isUpdatingPermissions = true;
              state.permissionsError = null;
            });

            try {
              const updatedUser = await apiMiddleware.put(`/api/users/${userId}/permissions`, {
                permissions
              }, {
                loadingMessage: 'جاري تحديث الصلاحيات...',
                successMessage: 'تم تحديث الصلاحيات بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                const index = state.users.findIndex(user => user.id === userId);
                if (index !== -1) {
                  state.users[index] = updatedUser;
                }
                
                if (state.selectedUser?.id === userId) {
                  state.selectedUser = updatedUser;
                }
                
                state.isUpdatingPermissions = false;
                state.permissionsError = null;
              });

              return { success: true, data: updatedUser };
            } catch (error) {
              console.error('Failed to update permissions:', error);
              
              set((state) => {
                state.isUpdatingPermissions = false;
                state.permissionsError = error.message || 'فشل في تحديث الصلاحيات';
              });

              return { success: false, error: error.message };
            }
          },

          // Fetch user permissions
          async fetchUserPermissions(userId) {
            set((state) => {
              state.permissionsLoading = true;
            });

            try {
              const permissions = await apiMiddleware.get(`/api/users/${userId}/permissions`, {
                showErrorNotification: false
              });

              set((state) => {
                state.userPermissions = permissions || [];
                state.permissionsLoading = false;
              });

              return permissions;
            } catch (error) {
              console.error('Failed to fetch user permissions:', error);
              
              set((state) => {
                state.userPermissions = [];
                state.permissionsLoading = false;
              });

              return [];
            }
          },

          // Fetch available roles
          async fetchAvailableRoles() {
            set((state) => {
              state.rolesLoading = true;
            });

            try {
              const roles = await apiMiddleware.get('/api/roles', {
                showErrorNotification: false
              });

              set((state) => {
                state.availableRoles = roles || [];
                state.rolesLoading = false;
              });

              return roles;
            } catch (error) {
              console.error('Failed to fetch roles:', error);
              
              set((state) => {
                state.availableRoles = [];
                state.rolesLoading = false;
              });

              return [];
            }
          },

          // Set selected user
          setSelectedUser(user) {
            set((state) => {
              state.selectedUser = user;
              state.showUserDetails = !!user;
            });

            if (user?.id) {
              get().actions.fetchUserPermissions(user.id);
            }
          },

          // Set filters
          setSearchQuery(query) {
            set((state) => {
              state.searchQuery = query;
            });
          },

          setRoleFilter(role) {
            set((state) => {
              state.roleFilter = role;
            });
          },

          setStatusFilter(status) {
            set((state) => {
              state.statusFilter = status;
            });
          },

          setDepartmentFilter(department) {
            set((state) => {
              state.departmentFilter = department;
            });
          },

          // Clear errors
          clearErrors() {
            set((state) => {
              state.error = null;
              state.addError = null;
              state.updateError = null;
              state.deleteError = null;
              state.passwordError = null;
              state.permissionsError = null;
            });
          },

          // Get filtered users
          getFilteredUsers() {
            const state = get();
            const { 
              users, 
              searchQuery, 
              roleFilter, 
              statusFilter, 
              departmentFilter 
            } = state;

            return users.filter(user => {
              // Search filter
              const matchesSearch = !searchQuery ||
                user.username?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                user.fullName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                user.email?.toLowerCase().includes(searchQuery.toLowerCase());

              // Role filter
              const matchesRole = !roleFilter || roleFilter === "all" ||
                user.role === roleFilter;

              // Status filter
              const matchesStatus = !statusFilter || statusFilter === "all" ||
                user.status === statusFilter;

              // Department filter
              const matchesDepartment = !departmentFilter || departmentFilter === "all" ||
                user.department === departmentFilter;

              return matchesSearch && matchesRole && matchesStatus && matchesDepartment;
            });
          },

          // Get user by ID
          getUserById(userId) {
            const state = get();
            return state.users.find(user => user.id === userId);
          },

          // Check user permission
          hasPermission(permission) {
            const state = get();
            if (!state.currentUser) return false;
            
            return state.currentUser.permissions?.includes(permission) || 
                   state.currentUser.role === 'admin';
          },

          // Refresh data
          async refresh() {
            return get().actions.fetchUsers(true);
          }
        }
      })),
      {
        name: 'users-store',
        partialize: (state) => ({
          users: state.users,
          currentUser: state.currentUser,
          lastFetched: state.lastFetched,
          searchQuery: state.searchQuery,
          roleFilter: state.roleFilter,
          statusFilter: state.statusFilter,
          departmentFilter: state.departmentFilter
        })
      }
    ),
    {
      name: 'users-store'
    }
  )
);

// Selectors
export const useUsers = () => useUsersStore((state) => state.users);
export const useCurrentUser = () => useUsersStore((state) => state.currentUser);
export const useUsersLoading = () => useUsersStore((state) => state.isLoading);
export const useUsersError = () => useUsersStore((state) => state.error);
export const useSelectedUser = () => useUsersStore((state) => state.selectedUser);
export const useUserPermissions = () => useUsersStore((state) => state.userPermissions);
export const useAvailableRoles = () => useUsersStore((state) => state.availableRoles);
export const useUsersActions = () => useUsersStore((state) => state.actions);

export const useFilteredUsers = () => useUsersStore((state) => 
  state.actions.getFilteredUsers()
);

export const useUsersStatus = () => useUsersStore((state) => ({
  isLoading: state.isLoading,
  isAdding: state.isAdding,
  isUpdating: state.isUpdating,
  isDeleting: state.isDeleting,
  isChangingPassword: state.isChangingPassword,
  isUpdatingPermissions: state.isUpdatingPermissions,
  permissionsLoading: state.permissionsLoading,
  rolesLoading: state.rolesLoading,
  error: state.error,
  addError: state.addError,
  updateError: state.updateError,
  deleteError: state.deleteError,
  passwordError: state.passwordError,
  permissionsError: state.permissionsError,
  totalCount: state.totalCount,
  lastFetched: state.lastFetched
}));

export default useUsersStore;
