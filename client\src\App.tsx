import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/components/theme-provider";
import { LanguageProvider } from "@/hooks/use-language";
import { AppLayout } from "@/components/layout/app-layout";
import { useLanguage } from "@/hooks/use-language";
import { Toaster } from "@/components/ui/toaster";
import { SettingsProvider } from "@/contexts/settings-context";
import { DateProvider } from "@/contexts/date-context";
import labels from "@/lib/i18n";
import { Suspense, lazy, Component, ErrorInfo, ReactNode } from "react";

// Error Boundary Component
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<{ children: ReactNode }, ErrorBoundaryState> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Application Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-background">
          <div className="text-center p-8 max-w-md">
            <div className="text-6xl mb-4">⚠️</div>
            <h1 className="text-2xl font-bold mb-4 text-destructive">خطأ في التطبيق</h1>
            <p className="text-muted-foreground mb-6">
              حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة أو المحاولة لاحقاً.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              إعادة تحميل الصفحة
            </button>
            {this.state.error && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm text-muted-foreground">
                  تفاصيل الخطأ
                </summary>
                <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto">
                  {this.state.error.message}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Loading Component
const LoadingSpinner = () => (
  <div className="min-h-screen flex items-center justify-center bg-background">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
      <p className="text-muted-foreground">جاري تحميل الصفحة...</p>
    </div>
  </div>
);

// Context Provider with Error Boundary
const ContextProvider = ({ children }: { children: ReactNode }) => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ErrorBoundary>
          <ThemeProvider defaultTheme="light" storageKey="contract-management-theme">
            <ErrorBoundary>
              <LanguageProvider>
                <ErrorBoundary>
                  <SettingsProvider>
                    <ErrorBoundary>
                      <DateProvider>
                        <TooltipProvider>
                          {children}
                          <Toaster />
                        </TooltipProvider>
                      </DateProvider>
                    </ErrorBoundary>
                  </SettingsProvider>
                </ErrorBoundary>
              </LanguageProvider>
            </ErrorBoundary>
          </ThemeProvider>
        </ErrorBoundary>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

// Lazy loaded pages for better performance
const Dashboard = lazy(() => import("@/pages/dashboard"));
const Clients = lazy(() => import("@/pages/clients"));
const ClientsNew = lazy(() => import("@/pages/clients-new"));
const ClientsEdit = lazy(() => import("@/pages/clients-edit"));
const Contracts = lazy(() => import("@/pages/contracts"));
const ContractsNewSingle = lazy(() => import("@/pages/contracts-new-single"));
const ContractEdit = lazy(() => import("@/pages/contracts-edit"));
const ContractViewNew = lazy(() => import("@/pages/contract-view-new"));

const ContractVersions = lazy(() => import("@/pages/contract-versions"));

const TreasuryPayments = lazy(() => import("@/pages/treasury-payments"));
const BankPayments = lazy(() => import("@/pages/bank-payments"));
const PaymentsUnified = lazy(() => import("@/pages/payments-unified"));
const Reports = lazy(() => import("@/pages/reports"));
const Receivables = lazy(() => import("@/pages/receivables"));
const UsersNew = lazy(() => import("@/pages/users-new"));
const AlertsCenter = lazy(() => import("@/pages/AlertsCenter"));
const AuditTrail = lazy(() => import("@/pages/AuditTrail"));
const ChequesManagement = lazy(() => import("@/pages/cheques-management"));

// Test pages
const TestClientsStore = lazy(() => import("@/test-clients-store"));
const TestAllStores = lazy(() => import("@/test-all-stores"));

const Settings = lazy(() => import("@/pages/settings"));
const ReferenceData = lazy(() => import("@/pages/reference-data"));
const AdminDatabase = lazy(() => import("@/pages/admin-database"));
const Login = lazy(() => import("@/pages/login"));
const NotFound = lazy(() => import("@/pages/not-found"));


function AppContent() {
  const { language, isRTL } = useLanguage();
  const t = labels[language];

  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Switch>
        <Route path="/">
          <AppLayout title={t.dashboard} subtitle={t.welcomeMessage}>
            <Dashboard />
          </AppLayout>
        </Route>

        <Route path="/clients">
          <AppLayout title={t.clients} subtitle={t.clientsSubtitle}>
            <Clients />
          </AppLayout>
        </Route>

        <Route path="/clients/new">
          <AppLayout title={t.addClient} subtitle={isRTL ? "إضافة عميل جديد للنظام" : "Add a new client to the system"}>
            <ClientsNew />
          </AppLayout>
        </Route>

        <Route path="/clients/edit/:id">
          <AppLayout title={t.editClient} subtitle={isRTL ? "تعديل بيانات العميل" : "Edit client information"}>
            <ClientsEdit />
          </AppLayout>
        </Route>

        <Route path="/contracts">
          <AppLayout title={t.contracts} subtitle={t.contractsSubtitle}>
            <Contracts />
          </AppLayout>
        </Route>

        <Route path="/contracts/new">
          <AppLayout title={isRTL ? "إنشاء عقد جديد" : "Create New Contract"} subtitle={isRTL ? "إنشاء عقد جديد مع جميع البيانات والحسابات المتقدمة" : "Create a new contract with all data and advanced calculations"}>
            <ContractsNewSingle />
          </AppLayout>
        </Route>

        <Route path="/contracts/edit/:id">
          <AppLayout title="تعديل العقد" subtitle="تعديل بيانات العقد والمعلومات المرتبطة به">
            <ContractEdit />
          </AppLayout>
        </Route>

        <Route path="/contracts/view/:id">
          <AppLayout title="كشف حساب عقد" subtitle="كشف حساب مفصل للعقد مع الاستحقاقات وحالات السداد">
            <ContractViewNew />
          </AppLayout>
        </Route>



        <Route path="/contracts/:id/versions">
          <AppLayout title="النسخ السابقة" subtitle="عرض جميع النسخ السابقة للعقد وتاريخ التعديلات">
            <ContractVersions />
          </AppLayout>
        </Route>



        <Route path="/payments">
          <AppLayout title="إدارة المدفوعات" subtitle="نظرة شاملة على جميع المدفوعات من الخزينة والبنك">
            <PaymentsUnified />
          </AppLayout>
        </Route>

        <Route path="/treasury-payments">
          <AppLayout title={t.treasuryPayments} subtitle="إدارة الإيصالات النقدية وإيصالات الشيكات المستلمة بخزينة الشركة">
            <TreasuryPayments />
          </AppLayout>
        </Route>

        <Route path="/bank-payments">
          <AppLayout title={t.bankPayments} subtitle="إدارة المدفوعات والتحويلات البنكية">
            <BankPayments />
          </AppLayout>
        </Route>

        <Route path="/reports">
          <AppLayout title={t.reports} subtitle={t.reportsSubtitle}>
            <Reports />
          </AppLayout>
        </Route>

        <Route path="/receivables">
          <AppLayout title={t.dues} subtitle={t.receivablesSubtitle}>
            <Receivables />
          </AppLayout>
        </Route>

        <Route path="/admin-database">
          <AppLayout title="إدارة قاعدة البيانات" subtitle="أدوات إدارة وصيانة قاعدة البيانات">
            <AdminDatabase />
          </AppLayout>
        </Route>

        <Route path="/settings">
          <AppLayout title="إعدادات النظام الشاملة" subtitle="إدارة جميع إعدادات البرنامج والشركة والنظام">
            <Settings />
          </AppLayout>
        </Route>

        <Route path="/reference-data">
          <AppLayout title="البيانات المرجعية" subtitle="إدارة قوائم البنوك والمحافظات والمناطق والأقسام">
            <ReferenceData />
          </AppLayout>
        </Route>

        <Route path="/users">
          <AppLayout title={t.users} subtitle={t.usersSubtitle}>
            <UsersNew />
          </AppLayout>
        </Route>

        <Route path="/alerts">
          <AppLayout title={t.alerts} subtitle={t.alertsSubtitle}>
            <AlertsCenter />
          </AppLayout>
        </Route>

        <Route path="/audit-trail">
          <AppLayout title={t.auditTrail} subtitle={t.auditTrailSubtitle}>
            <AuditTrail />
          </AppLayout>
        </Route>

        <Route path="/cheques-management">
          <AppLayout title={t.chequesManagement} subtitle={t.chequesManagementSubtitle}>
            <ChequesManagement />
          </AppLayout>
        </Route>
        {/* Test Routes */}
        <Route path="/test-clients">
          <AppLayout title="اختبار نظام العملاء" subtitle="اختبار شامل لـ Clients Store والـ Custom Hooks">
            <TestClientsStore />
          </AppLayout>
        </Route>

        <Route path="/test-all-stores">
          <AppLayout title="اختبار جميع الأنظمة" subtitle="اختبار شامل لجميع Zustand Stores والـ Custom Hooks">
            <TestAllStores />
          </AppLayout>
        </Route>





        <Route path="/login">
          <Login />
        </Route>

        <Route component={NotFound} />
      </Switch>
    </Suspense>
  );
}

function App() {
  return (
    <ContextProvider>
      <AppContent />
    </ContextProvider>
  );
}

export default App;
