import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DollarSign,
  AlertTriangle,
  Clock,
  Phone,
  Mail,
  Search,
  Filter,
  Download,
  Calendar,
  User,
  CheckCircle,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  FileText,
  Receipt,
  X
} from "lucide-react";
import { useLanguage } from "@/hooks/use-language";
import { useSettingsFormatters } from "@/hooks/use-settings";
import labels from "@/lib/i18n";

interface Receivable {
  id: number;
  contractId: number;
  clientId: number;
  receivableNumber: string;
  invoiceCode: string; // كود الفاتورة الفريد
  dueDate: string;
  amount: number;
  description: string;
  status: 'مستحق' | 'مدفوع' | 'متأخر' | 'لم يحن موعده';
  paymentFrequency: string;
  installmentNumber: number;
  totalInstallments: number;
  contractNumber: string;
  contractType: string;
  contractDescription: string;
  clientNumber: string;
  clientName: string;
  clientType: string;
  clientPhoneWhatsapp: string;
  clientEmail: string;
  createdAt: string;
  updatedAt: string;
  isPaid?: boolean;
  paidAmount?: number;
  remainingAmount?: number;
  paidDate?: string;
}

interface ReceivablesStats {
  totalReceivables: number;
  totalAmount: number;
  dueAmount: number;
  overdueAmount: number;
  paidAmount: number;
  futureAmount: number;
  dueCount: number;
  overdueCount: number;
  paidCount: number;
  futureCount: number;
}

export default function Receivables() {
  const { language, isRTL } = useLanguage();
  const { formatCurrency, formatDate } = useSettingsFormatters();
  const t = labels[language];

  const [searchQuery, setSearchQuery] = useState("");
  const [contractSearch, setContractSearch] = useState("");
  const [clientSearch, setClientSearch] = useState("");
  const [yearSearch, setYearSearch] = useState("");
  const [monthSearch, setMonthSearch] = useState("all");
  const [dateFromSearch, setDateFromSearch] = useState("");
  const [dateToSearch, setDateToSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [sortBy, setSortBy] = useState("dueDate");
  const [sortOrder, setSortOrder] = useState("ASC");
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch receivables data (simplified - filtering done locally)
  const { data: receivablesData, isLoading: receivablesLoading, refetch } = useQuery({
    queryKey: ['/api/receivables', {
      status: statusFilter,
      sortBy,
      sortOrder,
      page: currentPage
    }],
    queryFn: async () => {
      const params = new URLSearchParams({
        sortBy,
        sortOrder,
        page: currentPage.toString(),
        limit: '100' // Increased limit for local filtering
      });

      if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter);

      const response = await apiRequest('GET', `/api/receivables?${params}`);
      const data = await response.json();

      // تأكد من أن البيانات في الشكل الصحيح
      if (data?.success && Array.isArray(data.data)) {
        return { receivables: data.data, pagination: data.pagination };
      } else if (Array.isArray(data)) {
        return { receivables: data, pagination: null };
      }

      return { receivables: [], pagination: null };
    },
    refetchInterval: 30000, // Refetch every 30 seconds to update statuses
  });

  // Fetch receivables statistics
  const { data: stats, isLoading: statsLoading } = useQuery<ReceivablesStats>({
    queryKey: ['/api/receivables/stats'],
    queryFn: async () => {
      const response = await fetch('/api/receivables/stats');
      if (!response.ok) throw new Error('Failed to fetch receivables stats');
      return response.json();
    },
  });

  const allReceivables = receivablesData?.receivables || [];
  const pagination = receivablesData?.pagination;

  // Apply local filtering
  const filteredReceivables = allReceivables.filter((receivable: Receivable) => {
    // البحث العام في الوصف
    const matchesGeneralSearch = !searchQuery ||
      receivable.description.toLowerCase().includes(searchQuery.toLowerCase());

    // البحث برقم العقد
    const matchesContract = !contractSearch ||
      receivable.contractNumber?.toLowerCase().includes(contractSearch.toLowerCase()) ||
      receivable.contractId?.toString().includes(contractSearch);

    // البحث باسم العميل
    const matchesClient = !clientSearch ||
      receivable.clientName?.toLowerCase().includes(clientSearch.toLowerCase());

    // البحث بالسنة
    const matchesYear = !yearSearch ||
      new Date(receivable.dueDate).getFullYear().toString() === yearSearch;

    // البحث بالشهر (رقم الشهر من 1-12)
    const matchesMonth = monthSearch === "all" ||
      (new Date(receivable.dueDate).getMonth() + 1).toString() === monthSearch;

    // البحث بالفترة الزمنية
    const dueDate = new Date(receivable.dueDate);
    const fromDate = dateFromSearch ? new Date(dateFromSearch) : null;
    const toDate = dateToSearch ? new Date(dateToSearch) : null;

    const matchesDateRange = (!fromDate || dueDate >= fromDate) &&
                            (!toDate || dueDate <= toDate);

    // البحث بالحالة
    const matchesStatus = statusFilter === "all" || receivable.status === statusFilter;

    return matchesGeneralSearch && matchesContract && matchesClient &&
           matchesYear && matchesMonth && matchesDateRange && matchesStatus;
  });

  const receivables = filteredReceivables;

  // Helper functions
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'ASC' ? 'DESC' : 'ASC');
    } else {
      setSortBy(column);
      setSortOrder('ASC');
    }
    setCurrentPage(1);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'مستحق': { variant: 'default' as const, color: 'bg-orange-500' },
      'متأخر': { variant: 'destructive' as const, color: 'bg-red-500' },
      'مدفوع': { variant: 'secondary' as const, color: 'bg-green-500' },
      'لم يحن موعده': { variant: 'outline' as const, color: 'bg-gray-500' }
    };
    return statusConfig[status] || statusConfig['مستحق'];
  };

  // Function to count overdue receivables for a specific contract
  const getContractOverdueCount = (contractId: number) => {
    if (!receivables) return 0;
    return receivables.filter(r => r.contractId === contractId && r.status === 'متأخر').length;
  };

  const getSortIcon = (column: string) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />;
    return sortOrder === 'ASC' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  const receivableStats = [
    {
      title: "إجمالي الاستحقاقات",
      value: formatCurrency(stats?.totalAmount || 0),
      count: stats?.totalReceivables || 0,
      icon: DollarSign,
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900/30"
    },
    {
      title: "استحقاقات متأخرة",
      value: formatCurrency(stats?.overdueAmount || 0),
      count: stats?.overdueCount || 0,
      icon: AlertTriangle,
      color: "text-red-600 dark:text-red-400",
      bgColor: "bg-red-100 dark:bg-red-900/30"
    },
    {
      title: "استحقاقات مستحقة",
      value: formatCurrency(stats?.dueAmount || 0),
      count: stats?.dueCount || 0,
      icon: Clock,
      color: "text-orange-600 dark:text-orange-400",
      bgColor: "bg-orange-100 dark:bg-orange-900/30"
    },
    {
      title: "مدفوعات مكتملة",
      value: formatCurrency(stats?.paidAmount || 0),
      count: stats?.paidCount || 0,
      icon: CheckCircle,
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-100 dark:bg-green-900/30"
    }
  ];

  // حساب عدد النتائج لكل حالة
  const getStatusCount = (status: string) => {
    if (status === 'all') return allReceivables.length;
    return allReceivables.filter(r => r.status === status).length;
  };

  const contactClient = (receivable: Receivable, method: 'phone' | 'email') => {
    if (method === 'phone' && receivable.clientPhoneWhatsapp) {
      window.open(`tel:${receivable.clientPhoneWhatsapp}`);
    } else if (method === 'email' && receivable.clientEmail) {
      window.open(`mailto:${receivable.clientEmail}`);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl flex items-center gap-2">
                <DollarSign className="h-6 w-6" />
                {t.dues}
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                متابعة الاستحقاقات والذمم المالية
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" className="gap-2">
                <Download className="h-4 w-4" />
                تصدير القائمة
              </Button>
              <Button className="gap-2">
                <Mail className="h-4 w-4" />
                إرسال تذكيرات
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {receivableStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">{stat.title}</p>
                    {statsLoading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      <>
                        <p className="text-2xl font-bold">{stat.value}</p>
                        {stat.count !== null && (
                          <p className="text-xs text-muted-foreground">
                            {stat.count} عنصر
                          </p>
                        )}
                      </>
                    )}
                  </div>
                  <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4 bg-muted/30">
          {/* الصف الأول - حقول البحث الأساسية */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3 mb-3">
            {/* البحث العام */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">البحث العام</label>
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
                <Input
                  placeholder="البحث..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-7 h-8 text-sm"
                />
              </div>
            </div>

            {/* البحث برقم العقد */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">رقم العقد</label>
              <Input
                placeholder="CT-2024-001"
                value={contractSearch}
                onChange={(e) => setContractSearch(e.target.value)}
                className="h-8 text-sm"
              />
            </div>

            {/* البحث باسم العميل */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">اسم العميل</label>
              <Input
                placeholder="اسم العميل"
                value={clientSearch}
                onChange={(e) => setClientSearch(e.target.value)}
                className="h-8 text-sm"
              />
            </div>

            {/* البحث بالسنة */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">السنة</label>
              <Input
                placeholder="2024"
                value={yearSearch}
                onChange={(e) => setYearSearch(e.target.value)}
                className="h-8 text-sm"
                type="number"
                min="2020"
                max="2030"
              />
            </div>

            {/* البحث بالشهر */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">الشهر</label>
              <Select value={monthSearch} onValueChange={setMonthSearch}>
                <SelectTrigger className="h-8 text-sm">
                  <SelectValue placeholder="الشهر" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الشهور</SelectItem>
                  <SelectItem value="1">يناير</SelectItem>
                  <SelectItem value="2">فبراير</SelectItem>
                  <SelectItem value="3">مارس</SelectItem>
                  <SelectItem value="4">أبريل</SelectItem>
                  <SelectItem value="5">مايو</SelectItem>
                  <SelectItem value="6">يونيو</SelectItem>
                  <SelectItem value="7">يوليو</SelectItem>
                  <SelectItem value="8">أغسطس</SelectItem>
                  <SelectItem value="9">سبتمبر</SelectItem>
                  <SelectItem value="10">أكتوبر</SelectItem>
                  <SelectItem value="11">نوفمبر</SelectItem>
                  <SelectItem value="12">ديسمبر</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* البحث بالحالة */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                <div className="flex items-center gap-1">
                  <Filter className="h-3 w-3" />
                  الحالة
                </div>
              </label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="h-8 text-sm">
                  <SelectValue placeholder="الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                        جميع الحالات
                      </div>
                      <span className="text-xs text-muted-foreground">({getStatusCount('all')})</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="مستحق">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                        مستحق
                      </div>
                      <span className="text-xs text-muted-foreground">({getStatusCount('مستحق')})</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="متأخر">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-red-500"></div>
                        متأخر
                      </div>
                      <span className="text-xs text-muted-foreground">({getStatusCount('متأخر')})</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="مدفوع">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        مدفوع
                      </div>
                      <span className="text-xs text-muted-foreground">({getStatusCount('مدفوع')})</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="لم يحن موعده">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-gray-500"></div>
                        لم يحن موعده
                      </div>
                      <span className="text-xs text-muted-foreground">({getStatusCount('لم يحن موعده')})</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* الصف الثاني - فلاتر التاريخ وزر التفريغ */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
            {/* البحث بالفترة - من */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">من تاريخ</label>
              <Input
                type="date"
                value={dateFromSearch}
                onChange={(e) => setDateFromSearch(e.target.value)}
                className="h-8 text-sm"
              />
            </div>

            {/* البحث بالفترة - إلى */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">إلى تاريخ</label>
              <Input
                type="date"
                value={dateToSearch}
                onChange={(e) => setDateToSearch(e.target.value)}
                className="h-8 text-sm"
              />
            </div>

            {/* مساحة فارغة */}
            <div className="hidden md:block"></div>
            <div className="hidden lg:block"></div>
            <div className="hidden lg:block"></div>

            {/* زر تفريغ الفلاتر */}
            <div className="flex items-end">
              {(searchQuery || contractSearch || clientSearch || yearSearch || monthSearch !== "all" || dateFromSearch || dateToSearch || statusFilter !== "all") && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => {
                    setSearchQuery("");
                    setContractSearch("");
                    setClientSearch("");
                    setYearSearch("");
                    setMonthSearch("all");
                    setDateFromSearch("");
                    setDateToSearch("");
                    setStatusFilter("all");
                  }}
                  className="h-8 text-sm w-full"
                >
                  <X className="h-3 w-3 mr-1" />
                  تفريغ
                </Button>
              )}
            </div>
          </div>

          {/* مؤشرات الفلاتر النشطة */}
          {(statusFilter !== "all" || searchQuery || contractSearch || clientSearch || yearSearch || monthSearch !== "all" || dateFromSearch || dateToSearch) && (
            <div className="mt-3 pt-3 border-t">
              <div className="flex flex-wrap gap-2 items-center">
                <span className="text-xs font-medium text-gray-600">الفلاتر النشطة:</span>
                {statusFilter !== "all" && (
                  <Badge variant="secondary" className="gap-1 text-xs">
                    <Filter className="h-3 w-3" />
                    الحالة: {statusFilter}
                  </Badge>
                )}
                {searchQuery && (
                  <Badge variant="secondary" className="gap-1 text-xs">
                    <Search className="h-3 w-3" />
                    البحث: {searchQuery.length > 10 ? searchQuery.substring(0, 10) + '...' : searchQuery}
                  </Badge>
                )}
                {contractSearch && (
                  <Badge variant="secondary" className="gap-1 text-xs">
                    العقد: {contractSearch}
                  </Badge>
                )}
                {clientSearch && (
                  <Badge variant="secondary" className="gap-1 text-xs">
                    العميل: {clientSearch.length > 10 ? clientSearch.substring(0, 10) + '...' : clientSearch}
                  </Badge>
                )}
                {yearSearch && (
                  <Badge variant="secondary" className="gap-1 text-xs">
                    السنة: {yearSearch}
                  </Badge>
                )}
                {monthSearch !== "all" && (
                  <Badge variant="secondary" className="gap-1 text-xs">
                    الشهر: {monthSearch}
                  </Badge>
                )}
                {(dateFromSearch || dateToSearch) && (
                  <Badge variant="secondary" className="gap-1 text-xs">
                    <Calendar className="h-3 w-3" />
                    الفترة: {dateFromSearch || '...'} - {dateToSearch || '...'}
                  </Badge>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Receivables List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              قائمة الاستحقاقات
              {(searchQuery || contractSearch || clientSearch || yearSearch || monthSearch !== "all" || dateFromSearch || dateToSearch || statusFilter !== "all") && (
                <Badge variant="outline" className="text-xs">
                  مفلترة
                </Badge>
              )}
            </div>
            <div className="text-sm font-normal text-gray-600">
              {receivables.length} من أصل {allReceivables.length} نتيجة
              {statusFilter !== "all" && (
                <span className="text-xs text-muted-foreground ml-2">
                  (الحالة: {statusFilter})
                </span>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {receivablesLoading || statsLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <Skeleton className="w-12 h-12 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                      <Skeleton className="h-3 w-20" />
                    </div>
                  </div>
                  <div className="text-right space-y-2">
                    <Skeleton className="h-6 w-24" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
              ))}
            </div>
          ) : receivables.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">
                      <Button variant="ghost" onClick={() => handleSort('receivableNumber')} className="h-auto p-0 font-semibold">
                        رقم الاستحقاق {getSortIcon('receivableNumber')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-right">
                      <Button variant="ghost" onClick={() => handleSort('invoiceCode')} className="h-auto p-0 font-semibold">
                        كود الفاتورة {getSortIcon('invoiceCode')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-right">
                      <Button variant="ghost" onClick={() => handleSort('clientName')} className="h-auto p-0 font-semibold">
                        العميل {getSortIcon('clientName')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-right">
                      <Button variant="ghost" onClick={() => handleSort('contractNumber')} className="h-auto p-0 font-semibold">
                        رقم العقد {getSortIcon('contractNumber')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-right">
                      <Button variant="ghost" onClick={() => handleSort('dueDate')} className="h-auto p-0 font-semibold">
                        تاريخ الاستحقاق {getSortIcon('dueDate')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-right">
                      <Button variant="ghost" onClick={() => handleSort('amount')} className="h-auto p-0 font-semibold">
                        المبلغ المستحق {getSortIcon('amount')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-right">المبلغ المدفوع</TableHead>
                    <TableHead className="text-right">المبلغ المتبقي</TableHead>
                    <TableHead className="text-right">
                      <Button variant="ghost" onClick={() => handleSort('status')} className="h-auto p-0 font-semibold">
                        الحالة {getSortIcon('status')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {receivables.map((receivable) => (
                    <TableRow key={receivable.id} className="hover:bg-muted/50">
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          {receivable.receivableNumber}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {receivable.installmentNumber} من {receivable.totalInstallments}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Receipt className="h-4 w-4 text-blue-600" />
                          <span className="font-mono text-blue-600">{receivable.invoiceCode}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{receivable.clientName}</div>
                        <div className="text-xs text-muted-foreground">
                          {receivable.clientType} - رقم: {receivable.clientNumber}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{receivable.contractNumber}</div>
                        <div className="text-xs text-muted-foreground">{receivable.contractType}</div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          {formatDate(receivable.dueDate)}
                        </div>
                        <div className="text-xs text-muted-foreground">{receivable.paymentFrequency}</div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(receivable.amount)}
                      </TableCell>
                      <TableCell className="font-medium text-green-600">
                        {formatCurrency(receivable.paidAmount || 0)}
                      </TableCell>
                      <TableCell className="font-medium text-orange-600">
                        {formatCurrency(receivable.remainingAmount || receivable.amount)}
                      </TableCell>
                      <TableCell>
                        <div>
                          <Badge variant={getStatusBadge(receivable.status).variant}>
                            {receivable.status}
                          </Badge>
                          {receivable.status === 'متأخر' && (
                            <div className="text-xs text-muted-foreground mt-1">
                              {getContractOverdueCount(receivable.contractId)} متأخر في العقد
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => contactClient(receivable, 'phone')}
                            disabled={!receivable.clientPhoneWhatsapp}
                          >
                            <Phone className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => contactClient(receivable, 'email')}
                            disabled={!receivable.clientEmail}
                          >
                            <Mail className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {pagination && pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    صفحة {pagination.page} من {pagination.totalPages} ({pagination.total} استحقاق)
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={!pagination.hasPrev}
                    >
                      السابق
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={!pagination.hasNext}
                    >
                      التالي
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-12">
              <DollarSign className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <p className="text-lg font-medium text-muted-foreground">
                {(searchQuery || contractSearch || clientSearch || yearSearch || monthSearch !== "all" || dateFromSearch || dateToSearch || (statusFilter && statusFilter !== 'all'))
                  ? "لا توجد نتائج تطابق معايير البحث"
                  : "لا توجد استحقاقات حالياً"}
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                {!(searchQuery || contractSearch || clientSearch || yearSearch || monthSearch !== "all" || dateFromSearch || dateToSearch) && (!statusFilter || statusFilter === 'all')
                  ? "قم بإنشاء عقود لتوليد الاستحقاقات"
                  : "جرب تعديل معايير البحث أو مسح الفلاتر"}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
