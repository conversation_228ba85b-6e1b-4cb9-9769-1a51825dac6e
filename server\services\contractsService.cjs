const database = require('../models/db.cjs');

class ContractsService {

  // جلب جميع العقود مع بيانات العملاء
  getAllContracts() {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      const sql = `
        SELECT
          c.*,
          cl.clientName,
          cl.clientType,
          cl.phone
        FROM Contracts c
        LEFT JOIN Clients cl ON c.clientId = cl.clientId
        WHERE c.isActive = 1
        ORDER BY c.createdAt DESC
      `;

      db.all(sql, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  }

  // جلب عقد بواسطة ID مع بيانات العميل والمنتجات
  getContractById(id) {
    return new Promise((resolve, reject) => {
      const contractSql = `
        SELECT
          c.*,
          cl.clientName,
          cl.clientType,
          cl.phone,
          cl.address
        FROM Contracts c
        LEFT JOIN Clients cl ON c.clientId = cl.clientId
        WHERE c.id = ? AND c.isActive = 1
      `;

      this.db.get(contractSql, [id], (err, contract) => {
        if (err) return reject(err);
        if (!contract) return resolve(null);

        // جلب منتجات العقد
        const productsSql = `
          SELECT * FROM ContractProducts
          WHERE contractId = ? AND isActive = 1
          ORDER BY id
        `;

        this.db.all(productsSql, [id], (err, products) => {
          if (err) return reject(err);

          contract.products = products || [];
          resolve(contract);
        });
      });
    });
  }

  // جلب عقد بواسطة رقم العقد
  getContractByNumber(contractNumber) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT
          c.*,
          cl.clientName,
          cl.clientType
        FROM Contracts c
        LEFT JOIN Clients cl ON c.clientId = cl.id
        WHERE c.contractNumber = ? AND c.isActive = 1
      `;

      this.db.get(sql, [contractNumber], (err, row) => {
        if (err) return reject(err);
        resolve(row);
      });
    });
  }

  // البحث في العقود
  searchContracts(query) {
    return new Promise((resolve, reject) => {
      const searchQuery = `%${query}%`;
      const sql = `
        SELECT
          c.*,
          cl.clientName,
          cl.clientType
        FROM Contracts c
        LEFT JOIN Clients cl ON c.clientId = cl.id
        WHERE (c.contractNumber LIKE ? OR c.contractSubject LIKE ? OR cl.clientName LIKE ?)
        AND c.isActive = 1
        ORDER BY c.createdAt DESC
      `;

      this.db.all(sql, [searchQuery, searchQuery, searchQuery], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  }

  // إنشاء عقد جديد
  createContract(contractData) {
    return new Promise((resolve, reject) => {
      // التحقق من وجود العقد (نشط أو محذوف)
      this.db.get('SELECT id, isActive FROM Contracts WHERE contractId = ?',
        [contractData.contractId], (err, existing) => {
        if (err) return reject(err);

        if (existing) {
          if (existing.isActive === 1) {
            return reject(new Error('رقم العقد موجود مسبقاً'));
          } else {
            // العقد محذوف - استرجاعه وتحديث بياناته
            return this.restoreAndUpdateContract(existing.id, contractData, resolve, reject);
          }
        }

        const sql = `
          INSERT INTO Contracts (
            contractId, clientId, contractType, contractTitle, contractValue,
            currency, startDate, endDate, status, paymentTerms, description,
            attachments, isActive, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `;

        const params = [
          contractData.contractId,
          contractData.clientId,
          contractData.contractType,
          contractData.contractTitle,
          contractData.contractValue || 0,
          contractData.currency || 'SAR',
          contractData.startDate,
          contractData.endDate || null,
          contractData.status || 'active',
          contractData.paymentTerms || null,
          contractData.description || null,
          contractData.attachments || null
        ];

        this.db.run(sql, params, function(err) {
          if (err) return reject(err);

          const contractId = this.lastID;

          // جلب العقد المُنشأ
          this.db.get('SELECT * FROM Contracts WHERE id = ?', [contractId], (err, row) => {
            if (err) return reject(err);
            resolve({
              id: row.id,
              contractId: row.contractId,
              clientId: row.clientId,
              contractType: row.contractType,
              contractTitle: row.contractTitle,
              contractValue: row.contractValue,
              currency: row.currency,
              startDate: row.startDate,
              endDate: row.endDate,
              status: row.status,
              paymentTerms: row.paymentTerms,
              description: row.description,
              attachments: row.attachments
            });
          });
        }.bind(this));
      });
    });
  }

  // استرجاع وتحديث عقد محذوف
  restoreAndUpdateContract(id, contractData, resolve, reject) {
    const sql = `
      UPDATE Contracts SET
        contractType = ?, contractTitle = ?, contractValue = ?, currency = ?,
        startDate = ?, endDate = ?, status = ?, paymentTerms = ?, description = ?,
        attachments = ?, isActive = 1, isDeleted = 0, updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const params = [
      contractData.contractType,
      contractData.contractTitle,
      contractData.contractValue || 0,
      contractData.currency || 'SAR',
      contractData.startDate,
      contractData.endDate || null,
      contractData.status || 'active',
      contractData.paymentTerms || null,
      contractData.description || null,
      contractData.attachments || null,
      id
    ];

    this.db.run(sql, params, function(err) {
      if (err) return reject(err);

      // جلب البيانات المحدثة
      this.db.get('SELECT * FROM Contracts WHERE id = ?', [id], (err, row) => {
        if (err) return reject(err);
        resolve({
          id: row.id,
          contractId: row.contractId,
          clientId: row.clientId,
          contractType: row.contractType,
          contractTitle: row.contractTitle,
          contractValue: row.contractValue,
          currency: row.currency,
          startDate: row.startDate,
          endDate: row.endDate,
          status: row.status,
          paymentTerms: row.paymentTerms,
          description: row.description,
          attachments: row.attachments,
          restored: true
        });
      });
    }.bind(this));
  }
}

module.exports = new ContractsService();
