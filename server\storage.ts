import { 
  Client, 
  InsertClient, 
  Contract, 
  InsertContract, 
  Payment, 
  InsertPayment, 
  ReferenceData, 
  InsertReferenceData,
  Settings,
  InsertSettings,
  DashboardStats
} from "@shared/schema";

export interface IStorage {
  // Client operations
  getClients(): Promise<Client[]>;
  getClient(id: number): Promise<Client | undefined>;
  createClient(client: InsertClient): Promise<Client>;
  updateClient(id: number, client: Partial<InsertClient>): Promise<Client>;
  deleteClient(id: number): Promise<boolean>;
  searchClients(query: string): Promise<Client[]>;

  // Contract operations
  getContracts(): Promise<Contract[]>;
  getContract(id: number): Promise<Contract | undefined>;
  getContractsByClient(clientId: number): Promise<Contract[]>;
  createContract(contract: InsertContract): Promise<Contract>;
  updateContract(id: number, contract: Partial<InsertContract>): Promise<Contract>;
  deleteContract(id: number): Promise<boolean>;

  // Payment operations
  getPayments(): Promise<Payment[]>;
  getPayment(id: number): Promise<Payment | undefined>;
  getPaymentsByContract(contractId: number): Promise<Payment[]>;
  createPayment(payment: InsertPayment): Promise<Payment>;
  updatePayment(id: number, payment: Partial<InsertPayment>): Promise<Payment>;
  deletePayment(id: number): Promise<boolean>;
  getOverduePayments(): Promise<Payment[]>;

  // Reference data operations
  getReferenceData(): Promise<ReferenceData[]>;
  getReferenceDataByCategory(category: string): Promise<ReferenceData[]>;
  createReferenceData(data: InsertReferenceData): Promise<ReferenceData>;
  updateReferenceData(id: number, data: Partial<InsertReferenceData>): Promise<ReferenceData>;
  deleteReferenceData(id: number): Promise<boolean>;

  // Settings operations
  getSettings(): Promise<Settings | undefined>;
  updateSettings(settings: Partial<InsertSettings>): Promise<Settings>;

  // Dashboard operations
  getDashboardStats(): Promise<DashboardStats>;
}

export class MemStorage implements IStorage {
  private clients: Map<number, Client>;
  private contracts: Map<number, Contract>;
  private payments: Map<number, Payment>;
  private referenceData: Map<number, ReferenceData>;
  private settings: Settings | undefined;
  private currentId: number;

  constructor() {
    this.clients = new Map();
    this.contracts = new Map();
    this.payments = new Map();
    this.referenceData = new Map();
    this.currentId = 1;
    this.initializeDefaultData();
  }

  private initializeDefaultData() {
    // Initialize default settings
    this.settings = {
      id: 1,
      companyName: "شركة إدارة العقود",
      programName: "نظام إدارة العقود",
      language: "ar",
      currency: "EGP",
      currencySymbol: "ج.م",
      country: "مصر",
      dateFormat: "DD/MM/YYYY",
      decimalPlaces: "2",
      numberingFormat: "auto",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Initialize reference data
    const referenceCategories = [
      { category: "clientTypes", name: "أفراد", nameEn: "Individuals", value: "أفراد" },
      { category: "clientTypes", name: "شركات", nameEn: "Companies", value: "شركات" },
      { category: "contractTypes", name: "عقد إيجار", nameEn: "Rental Contract", value: "عقد إيجار" },
      { category: "contractTypes", name: "عقد بيع", nameEn: "Sales Contract", value: "عقد بيع" },
      { category: "contractStatus", name: "نشط", nameEn: "Active", value: "نشط" },
      { category: "contractStatus", name: "منتهي", nameEn: "Expired", value: "منتهي" },
      { category: "contractStatus", name: "ملغي", nameEn: "Cancelled", value: "ملغي" },
      { category: "contractStatus", name: "معلق", nameEn: "Suspended", value: "معلق" },
      { category: "paymentMethods", name: "تحويل بنكي", nameEn: "Bank Transfer", value: "تحويل بنكي" },
      { category: "paymentMethods", name: "شيك", nameEn: "Check", value: "شيك" },
      { category: "paymentMethods", name: "نقدي", nameEn: "Cash", value: "نقدي" },
      { category: "financialCategories", name: "ممتاز", nameEn: "Excellent", value: "ممتاز" },
      { category: "financialCategories", name: "جيد", nameEn: "Good", value: "جيد" },
      { category: "financialCategories", name: "متوسط", nameEn: "Average", value: "متوسط" },
      { category: "financialCategories", name: "ضعيف", nameEn: "Poor", value: "ضعيف" },
    ];

    referenceCategories.forEach((item, index) => {
      this.referenceData.set(index + 1, {
        id: index + 1,
        category: item.category,
        name: item.name,
        nameEn: item.nameEn,
        value: item.value,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    });

    this.currentId = referenceCategories.length + 2;
  }

  // Client operations
  async getClients(): Promise<Client[]> {
    return Array.from(this.clients.values()).filter(client => client.isActive);
  }

  async getClient(id: number): Promise<Client | undefined> {
    return this.clients.get(id);
  }

  async createClient(insertClient: InsertClient): Promise<Client> {
    const id = this.currentId++;
    const now = new Date();
    const client: Client = {
      ...insertClient,
      id,
      clientId: insertClient.clientId || `CL-${new Date().getFullYear()}-${id.toString().padStart(3, '0')}`,
      createdAt: now,
      updatedAt: now,
    };
    this.clients.set(id, client);
    return client;
  }

  async updateClient(id: number, updateData: Partial<InsertClient>): Promise<Client> {
    const client = this.clients.get(id);
    if (!client) {
      throw new Error('Client not found');
    }
    const updatedClient = {
      ...client,
      ...updateData,
      updatedAt: new Date(),
    };
    this.clients.set(id, updatedClient);
    return updatedClient;
  }

  async deleteClient(id: number): Promise<boolean> {
    const client = this.clients.get(id);
    if (!client) return false;
    
    const updatedClient = { ...client, isActive: false, updatedAt: new Date() };
    this.clients.set(id, updatedClient);
    return true;
  }

  async searchClients(query: string): Promise<Client[]> {
    const allClients = Array.from(this.clients.values()).filter(client => client.isActive);
    if (!query) return allClients;
    
    const lowerQuery = query.toLowerCase();
    return allClients.filter(client => 
      client.clientName.toLowerCase().includes(lowerQuery) ||
      client.clientPhoneWhatsapp.includes(query) ||
      client.clientId.toLowerCase().includes(lowerQuery)
    );
  }

  // Contract operations
  async getContracts(): Promise<Contract[]> {
    return Array.from(this.contracts.values()).filter(contract => contract.isActive);
  }

  async getContract(id: number): Promise<Contract | undefined> {
    return this.contracts.get(id);
  }

  async getContractsByClient(clientId: number): Promise<Contract[]> {
    return Array.from(this.contracts.values()).filter(
      contract => contract.clientId === clientId && contract.isActive
    );
  }

  async createContract(insertContract: InsertContract): Promise<Contract> {
    const id = this.currentId++;
    const now = new Date();
    
    // Get client details
    const client = this.clients.get(insertContract.clientId);
    if (!client) {
      throw new Error('Client not found');
    }

    const contract: Contract = {
      ...insertContract,
      id,
      contractNumber: insertContract.contractNumber || `CT-${new Date().getFullYear()}-${id.toString().padStart(3, '0')}`,
      clientName: client.clientName,
      clientType: client.clientType,
      clientPhoneWhatsapp: client.clientPhoneWhatsapp,
      clientAddress: client.clientAddress,
      createdAt: now,
      updatedAt: now,
    };
    this.contracts.set(id, contract);
    return contract;
  }

  async updateContract(id: number, updateData: Partial<InsertContract>): Promise<Contract> {
    const contract = this.contracts.get(id);
    if (!contract) {
      throw new Error('Contract not found');
    }
    const updatedContract = {
      ...contract,
      ...updateData,
      updatedAt: new Date(),
    };
    this.contracts.set(id, updatedContract);
    return updatedContract;
  }

  async deleteContract(id: number): Promise<boolean> {
    const contract = this.contracts.get(id);
    if (!contract) return false;
    
    const updatedContract = { ...contract, isActive: false, updatedAt: new Date() };
    this.contracts.set(id, updatedContract);
    return true;
  }

  // Payment operations
  async getPayments(): Promise<Payment[]> {
    return Array.from(this.payments.values()).filter(payment => payment.isActive);
  }

  async getPayment(id: number): Promise<Payment | undefined> {
    return this.payments.get(id);
  }

  async getPaymentsByContract(contractId: number): Promise<Payment[]> {
    return Array.from(this.payments.values()).filter(
      payment => payment.contractId === contractId && payment.isActive
    );
  }

  async createPayment(insertPayment: InsertPayment): Promise<Payment> {
    const id = this.currentId++;
    const now = new Date();
    const payment: Payment = {
      ...insertPayment,
      id,
      createdAt: now,
      updatedAt: now,
    };
    this.payments.set(id, payment);
    return payment;
  }

  async updatePayment(id: number, updateData: Partial<InsertPayment>): Promise<Payment> {
    const payment = this.payments.get(id);
    if (!payment) {
      throw new Error('Payment not found');
    }
    const updatedPayment = {
      ...payment,
      ...updateData,
      updatedAt: new Date(),
    };
    this.payments.set(id, updatedPayment);
    return updatedPayment;
  }

  async deletePayment(id: number): Promise<boolean> {
    const payment = this.payments.get(id);
    if (!payment) return false;
    
    const updatedPayment = { ...payment, isActive: false, updatedAt: new Date() };
    this.payments.set(id, updatedPayment);
    return true;
  }

  async getOverduePayments(): Promise<Payment[]> {
    const today = new Date().toISOString().split('T')[0];
    return Array.from(this.payments.values()).filter(
      payment => payment.isActive && !payment.isPaid && payment.dueDate < today
    );
  }

  // Reference data operations
  async getReferenceData(): Promise<ReferenceData[]> {
    return Array.from(this.referenceData.values()).filter(data => data.isActive);
  }

  async getReferenceDataByCategory(category: string): Promise<ReferenceData[]> {
    return Array.from(this.referenceData.values()).filter(
      data => data.category === category && data.isActive
    );
  }

  async createReferenceData(insertData: InsertReferenceData): Promise<ReferenceData> {
    const id = this.currentId++;
    const now = new Date();
    const data: ReferenceData = {
      ...insertData,
      id,
      createdAt: now,
      updatedAt: now,
    };
    this.referenceData.set(id, data);
    return data;
  }

  async updateReferenceData(id: number, updateData: Partial<InsertReferenceData>): Promise<ReferenceData> {
    const data = this.referenceData.get(id);
    if (!data) {
      throw new Error('Reference data not found');
    }
    const updatedData = {
      ...data,
      ...updateData,
      updatedAt: new Date(),
    };
    this.referenceData.set(id, updatedData);
    return updatedData;
  }

  async deleteReferenceData(id: number): Promise<boolean> {
    const data = this.referenceData.get(id);
    if (!data) return false;
    
    const updatedData = { ...data, isActive: false, updatedAt: new Date() };
    this.referenceData.set(id, updatedData);
    return true;
  }

  // Settings operations
  async getSettings(): Promise<Settings | undefined> {
    return this.settings;
  }

  async updateSettings(updateData: Partial<InsertSettings>): Promise<Settings> {
    if (!this.settings) {
      throw new Error('Settings not initialized');
    }
    this.settings = {
      ...this.settings,
      ...updateData,
      updatedAt: new Date(),
    };
    return this.settings;
  }

  // Dashboard operations
  async getDashboardStats(): Promise<DashboardStats> {
    const clients = await this.getClients();
    const contracts = await this.getContracts();
    const payments = await this.getPayments();
    const overduePayments = await this.getOverduePayments();

    const activeContracts = contracts.filter(contract => contract.contractStatus === 'نشط');
    const outstandingAmount = payments
      .filter(payment => !payment.isPaid)
      .reduce((total, payment) => total + payment.totalAmount, 0);

    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const monthlyRevenue = payments
      .filter(payment => {
        const paymentDate = payment.paidDate ? new Date(payment.paidDate) : null;
        return payment.isPaid && paymentDate && 
               paymentDate.getMonth() === currentMonth && 
               paymentDate.getFullYear() === currentYear;
      })
      .reduce((total, payment) => total + payment.amount, 0);

    const totalRevenue = payments
      .filter(payment => payment.isPaid)
      .reduce((total, payment) => total + payment.amount, 0);

    return {
      totalClients: clients.length,
      activeContracts: activeContracts.length,
      outstandingAmount,
      overduePayments: overduePayments.length,
      monthlyRevenue,
      totalRevenue,
    };
  }
}

export class DatabaseStorage implements IStorage {
  // Client operations
  async getClients(): Promise<Client[]> {
    return await db.select().from(clients).orderBy(desc(clients.createdAt));
  }

  async getClient(id: number): Promise<Client | undefined> {
    const [client] = await db.select().from(clients).where(eq(clients.id, id));
    return client || undefined;
  }

  async createClient(client: InsertClient): Promise<Client> {
    // Generate unique client ID
    const clientId = `C${Date.now()}`;
    const [newClient] = await db
      .insert(clients)
      .values({ ...client, clientId })
      .returning();
    return newClient;
  }

  async updateClient(id: number, client: Partial<InsertClient>): Promise<Client> {
    const [updatedClient] = await db
      .update(clients)
      .set({ ...client, updatedAt: new Date() })
      .where(eq(clients.id, id))
      .returning();
    return updatedClient;
  }

  async deleteClient(id: number): Promise<boolean> {
    const result = await db.delete(clients).where(eq(clients.id, id));
    return result.rowCount > 0;
  }

  async searchClients(query: string): Promise<Client[]> {
    return await db
      .select()
      .from(clients)
      .where(
        or(
          like(clients.clientName, `%${query}%`),
          like(clients.clientPhoneWhatsapp, `%${query}%`)
        )
      )
      .orderBy(desc(clients.createdAt));
  }

  // Contract operations
  async getContracts(): Promise<Contract[]> {
    return await db.select().from(contracts).orderBy(desc(contracts.createdAt));
  }

  async getContract(id: number): Promise<Contract | undefined> {
    const [contract] = await db.select().from(contracts).where(eq(contracts.id, id));
    return contract || undefined;
  }

  async getContractsByClient(clientId: number): Promise<Contract[]> {
    return await db
      .select()
      .from(contracts)
      .where(eq(contracts.clientId, clientId))
      .orderBy(desc(contracts.createdAt));
  }

  async createContract(contract: InsertContract): Promise<Contract> {
    // Generate unique contract number
    const contractNumber = `CNT${Date.now()}`;
    const [newContract] = await db
      .insert(contracts)
      .values({ ...contract, contractNumber })
      .returning();
    return newContract;
  }

  async updateContract(id: number, contract: Partial<InsertContract>): Promise<Contract> {
    const [updatedContract] = await db
      .update(contracts)
      .set({ ...contract, updatedAt: new Date() })
      .where(eq(contracts.id, id))
      .returning();
    return updatedContract;
  }

  async deleteContract(id: number): Promise<boolean> {
    const result = await db.delete(contracts).where(eq(contracts.id, id));
    return result.rowCount > 0;
  }

  // Payment operations
  async getPayments(): Promise<Payment[]> {
    return await db.select().from(payments).orderBy(desc(payments.createdAt));
  }

  async getPayment(id: number): Promise<Payment | undefined> {
    const [payment] = await db.select().from(payments).where(eq(payments.id, id));
    return payment || undefined;
  }

  async getPaymentsByContract(contractId: number): Promise<Payment[]> {
    return await db
      .select()
      .from(payments)
      .where(eq(payments.contractId, contractId))
      .orderBy(desc(payments.createdAt));
  }

  async createPayment(payment: InsertPayment): Promise<Payment> {
    const [newPayment] = await db
      .insert(payments)
      .values(payment)
      .returning();
    return newPayment;
  }

  async updatePayment(id: number, payment: Partial<InsertPayment>): Promise<Payment> {
    const [updatedPayment] = await db
      .update(payments)
      .set({ ...payment, updatedAt: new Date() })
      .where(eq(payments.id, id))
      .returning();
    return updatedPayment;
  }

  async deletePayment(id: number): Promise<boolean> {
    const result = await db.delete(payments).where(eq(payments.id, id));
    return result.rowCount > 0;
  }

  async getOverduePayments(): Promise<Payment[]> {
    return await db
      .select()
      .from(payments)
      .where(
        and(
          eq(payments.paymentStatus, 'مستحق'),
          sql`${payments.dueDate} < CURRENT_DATE`
        )
      )
      .orderBy(desc(payments.dueDate));
  }

  // Reference data operations
  async getReferenceData(): Promise<ReferenceData[]> {
    return await db
      .select()
      .from(referenceData)
      .where(eq(referenceData.isActive, true))
      .orderBy(referenceData.category, referenceData.sortOrder);
  }

  async getReferenceDataByCategory(category: string): Promise<ReferenceData[]> {
    return await db
      .select()
      .from(referenceData)
      .where(
        and(
          eq(referenceData.category, category),
          eq(referenceData.isActive, true)
        )
      )
      .orderBy(referenceData.sortOrder);
  }

  async createReferenceData(data: InsertReferenceData): Promise<ReferenceData> {
    const [newData] = await db
      .insert(referenceData)
      .values(data)
      .returning();
    return newData;
  }

  async updateReferenceData(id: number, data: Partial<InsertReferenceData>): Promise<ReferenceData> {
    const [updatedData] = await db
      .update(referenceData)
      .set({ ...data, updatedAt: new Date() })
      .where(eq(referenceData.id, id))
      .returning();
    return updatedData;
  }

  async deleteReferenceData(id: number): Promise<boolean> {
    const result = await db.delete(referenceData).where(eq(referenceData.id, id));
    return result.rowCount > 0;
  }

  // Settings operations
  async getSettings(): Promise<Settings | undefined> {
    const [setting] = await db.select().from(settings).limit(1);
    return setting || undefined;
  }

  async updateSettings(settingsData: Partial<InsertSettings>): Promise<Settings> {
    // Check if settings exist
    const existingSettings = await this.getSettings();
    
    if (existingSettings) {
      const [updatedSettings] = await db
        .update(settings)
        .set({ ...settingsData, updatedAt: new Date() })
        .where(eq(settings.id, existingSettings.id))
        .returning();
      return updatedSettings;
    } else {
      const [newSettings] = await db
        .insert(settings)
        .values(settingsData)
        .returning();
      return newSettings;
    }
  }

  // Dashboard operations
  async getDashboardStats(): Promise<DashboardStats> {
    const [clientCount] = await db.select({ count: sql<number>`count(*)` }).from(clients);
    const [contractCount] = await db.select({ count: sql<number>`count(*)` }).from(contracts);
    const [activeContracts] = await db
      .select({ count: sql<number>`count(*)` })
      .from(contracts)
      .where(eq(contracts.contractStatus, 'نشط'));
    
    const [totalRevenue] = await db
      .select({ sum: sql<number>`coalesce(sum(cast(total_contract_value as decimal)), 0)` })
      .from(contracts);
    
    const [overduePaymentsCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(payments)
      .where(
        and(
          eq(payments.paymentStatus, 'مستحق'),
          sql`${payments.dueDate} < CURRENT_DATE`
        )
      );

    return {
      totalClients: clientCount.count || 0,
      totalContracts: contractCount.count || 0,
      activeContracts: activeContracts.count || 0,
      totalRevenue: totalRevenue.sum || 0,
      outstandingAmount: 0, // Will be calculated with more complex query
      overduePayments: overduePaymentsCount.count || 0,
      monthlyRevenue: 0, // Will be calculated with more complex query
    };
  }
}

export const storage = new DatabaseStorage();
