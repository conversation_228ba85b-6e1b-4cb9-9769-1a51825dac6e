import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Plus,
  FileSignature,
  Calendar,
  DollarSign,
  User,
  Trash2,
  Eye,
  Edit,
  MoreHorizontal
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useEnhancedToast } from "@/hooks/use-enhanced-toast";
import { formatContractStatus } from "@/lib/formatters";
import { useCurrency } from "@/hooks/use-currency";
import { useDateFormat } from "@/hooks/use-date-format";
import { useLanguage } from "@/hooks/use-language";
import { cn } from "@/lib/utils";
import labels from "@/lib/i18n";
import type { Contract } from "@shared/schema";

export default function Contracts() {
  const { language, isRTL } = useLanguage();
  const t = labels[language];
  const { formatCurrency } = useCurrency();
  const { formatDate } = useDateFormat();
  const [, setLocation] = useLocation();
  const { showDeleteSuccess, showDeleteWarning, showDeleteError } = useEnhancedToast();
  const queryClient = useQueryClient();

  const { data: contracts, isLoading } = useQuery<Contract[]>({
    queryKey: ['/api/contracts'],
    queryFn: async () => {
      const response = await fetch('/api/contracts');
      if (!response.ok) throw new Error('Failed to fetch contracts');
      const data = await response.json();

      // تأكد من أن البيانات array
      if (data?.success && Array.isArray(data.data)) {
        return data.data;
      } else if (Array.isArray(data)) {
        return data;
      }
      return [];
    }
  });

  // Delete contract mutation
  const deleteContractMutation = useMutation({
    mutationFn: async (contractId: number) => {
      const response = await fetch(`/api/contracts/${contractId}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        throw new Error('فشل في حذف العقد');
      }
      return response.json();
    },
    onSuccess: (data: any) => {
      queryClient.invalidateQueries({ queryKey: ['/api/contracts'] });

      // فحص إذا كان هناك تحذير من الاستحقاقات غير المسددة
      if (data?.hasUnpaidReceivables && data?.warning) {
        showDeleteWarning("العقد", data.warning.ar);
      } else {
        showDeleteSuccess("العقد");
      }
    },
    onError: (error: Error) => {
      showDeleteError(error.message);
    },
  });

  const handleAddContract = () => {
    setLocation('/contracts/new');
  };

  const handleViewContract = (contractId: number) => {
    setLocation(`/contracts/view/${contractId}`);
  };



  const handleDeleteContract = (contractId: number) => {
    deleteContractMutation.mutate(contractId);
  };

  return (
    <div className={cn("space-y-6", isRTL ? "text-right" : "text-left")}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className={cn(isRTL ? "text-right" : "text-left")}>
              <CardTitle className="text-xl">{t.contractManagement}</CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                إدارة وتتبع جميع العقود
              </p>
            </div>
            <div className={cn("flex gap-2", isRTL ? "flex-row-reverse" : "flex-row")}>
              <Button className="gap-2" onClick={handleAddContract}>
                <Plus className="h-4 w-4" />
                {t.addContract}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Contracts Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-3">
        {isLoading ? (
          Array.from({ length: 18 }).map((_, i) => (
            <Card key={i} className="p-2">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-3 w-20" />
                  <Skeleton className="h-3 w-3" />
                </div>
                <Skeleton className="h-3 w-12" />
                <div className="grid grid-cols-2 gap-1">
                  <Skeleton className="h-2 w-full" />
                  <Skeleton className="h-2 w-full" />
                  <Skeleton className="h-2 w-full" />
                  <Skeleton className="h-2 w-full" />
                  <Skeleton className="h-2 w-full" />
                  <Skeleton className="h-2 w-full" />
                </div>
              </div>
            </Card>
          ))
        ) : contracts && contracts.length > 0 ? (
          contracts.map((contract) => {
            const statusInfo = formatContractStatus(contract.contractStatus);
            return (
              <Card key={contract.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-1 px-2 pt-2">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-xs font-semibold truncate">
                        {contract.contractNumber}
                      </CardTitle>
                      <div className="flex items-center gap-1 mt-1">
                        <Badge variant={statusInfo.variant} className="text-[10px] px-1 py-0">
                          {statusInfo.text}
                        </Badge>
                        {(contract.versionNumber && contract.versionNumber > 1) && (
                          <Badge variant="outline" className="text-[9px] px-1 py-0">
                            v{contract.versionNumber}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-5 w-5 p-0">
                          <MoreHorizontal className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-36">
                        <DropdownMenuItem
                          onClick={() => handleViewContract(contract.id)}
                          className="gap-2 text-xs"
                        >
                          <Eye className="h-3 w-3" />
                          {t.view}
                        </DropdownMenuItem>

                        <DropdownMenuItem
                          onClick={() => setLocation(`/contracts/edit/${contract.id}`)}
                          className="gap-2 text-xs"
                        >
                          <Edit className="h-3 w-3" />
                          تعديل
                        </DropdownMenuItem>

                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <DropdownMenuItem
                              onSelect={(e) => e.preventDefault()}
                              className="gap-2 text-red-600 focus:text-red-600 text-xs"
                            >
                              <Trash2 className="h-3 w-3" />
                              حذف
                            </DropdownMenuItem>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>تأكيد حذف العقد</AlertDialogTitle>
                              <AlertDialogDescription>
                                هل أنت متأكد من حذف العقد رقم "{contract.contractNumber}"؟
                                هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع البيانات المرتبطة بالعقد.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>إلغاء</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteContract(contract.id)}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                حذف العقد
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="px-2 pb-2">
                  <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-[10px]">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">الاسم:</span>
                      <span className="font-medium truncate ml-1">{contract.clientName}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-muted-foreground">رقم العميل:</span>
                      <span className="font-medium truncate ml-1">{(contract as any).clientNumber}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-muted-foreground">الإدارة:</span>
                      <span className="font-medium truncate ml-1">{contract.contractType}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-muted-foreground">الاعداد/المزايدة:</span>
                      <span className="font-medium">{formatDate(contract.contractDate || contract.createdAt)}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-muted-foreground">التفعيل:</span>
                      <span className="font-medium">{formatDate(contract.startDate)}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-muted-foreground">القيمة:</span>
                      <span className="font-medium">{formatCurrency(contract.totalContractValue)}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-muted-foreground">المدة:</span>
                      <span className="font-medium">{contract.contractDurationYears} سنة</span>
                    </div>

                    <div className="flex justify-between col-span-2">
                      <span className="text-muted-foreground">التأمين النهائي:</span>
                      <span className="font-medium">{formatCurrency(contract.finalInsurance || 0)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        ) : (
          <Card className="col-span-full">
            <CardContent className="p-12 text-center">
              <div className="text-muted-foreground">
                <FileSignature className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">{t.noData}</p>
                <p className="text-sm mt-2">ابدأ بإضافة عقد جديد</p>
              </div>
              <Button className="mt-4 gap-2" onClick={handleAddContract}>
                <Plus className="h-4 w-4" />
                {t.addContract}
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
