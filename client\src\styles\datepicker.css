/* Custom DatePicker Styles */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container {
  width: 100%;
}

.custom-datepicker {
  font-family: inherit;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.react-datepicker__header {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  border-radius: 0.375rem 0.375rem 0 0;
  padding: 8px 0;
  text-align: center;
  position: relative;
}

.react-datepicker__current-month {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
  text-align: center !important;
  width: 100% !important;
  display: block !important;
}

.react-datepicker__day-names {
  margin-bottom: 4px;
}

.react-datepicker__day-name {
  color: #64748b;
  font-weight: 500;
  font-size: 0.875rem;
}

.react-datepicker__day {
  color: #374151;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.react-datepicker__day:hover {
  background-color: #e0e7ff;
  color: #3730a3;
}

.react-datepicker__day--selected {
  background-color: #3b82f6 !important;
  color: white !important;
}

.react-datepicker__day--keyboard-selected {
  background-color: #dbeafe;
  color: #1e40af;
}

.react-datepicker__day--today {
  background-color: #fef3c7;
  color: #92400e;
  font-weight: 600;
}

.react-datepicker__day--outside-month {
  color: #9ca3af;
}

.react-datepicker__navigation {
  top: 12px;
  z-index: 1;
}

.react-datepicker__navigation--previous {
  left: 12px;
}

.react-datepicker__navigation--next {
  right: 12px;
}

/* تحسين موضع اسم الشهر */
.react-datepicker__month-container {
  text-align: center;
}

.react-datepicker__month {
  text-align: center;
}

.react-datepicker__navigation-icon::before {
  border-color: #6b7280;
}

.react-datepicker__navigation:hover .react-datepicker__navigation-icon::before {
  border-color: #374151;
}

.react-datepicker__month-dropdown,
.react-datepicker__year-dropdown {
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.react-datepicker__month-option,
.react-datepicker__year-option {
  padding: 8px 12px;
  transition: background-color 0.2s;
}

.react-datepicker__month-option:hover,
.react-datepicker__year-option:hover {
  background-color: #f1f5f9;
}

.react-datepicker__month-option--selected,
.react-datepicker__year-option--selected {
  background-color: #3b82f6;
  color: white;
}

/* RTL Support */
.react-datepicker[dir="rtl"] {
  direction: rtl;
}

.react-datepicker[dir="rtl"] .react-datepicker__navigation--previous {
  right: 12px;
  left: auto;
}

.react-datepicker[dir="rtl"] .react-datepicker__navigation--next {
  left: 12px;
  right: auto;
}
