// ===== DATE UTILITIES =====
// Author: Augment Code
// Description: Utility functions for date manipulation and formatting

/**
 * Format date to Arabic locale
 * @param {Date|string} date - Date to format
 * @param {string} format - Format type ('short', 'long', 'medium')
 * @returns {string} Formatted date string
 */
const formatDateArabic = (date, format = 'medium') => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';

  const options = {
    short: { year: 'numeric', month: '2-digit', day: '2-digit' },
    medium: { year: 'numeric', month: 'long', day: 'numeric' },
    long: { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      weekday: 'long'
    }
  };

  return dateObj.toLocaleDateString('ar-SA', options[format] || options.medium);
};

/**
 * Format date to Hijri calendar
 * @param {Date|string} date - Date to format
 * @returns {string} Hijri date string
 */
const formatDateHijri = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';

  try {
    return dateObj.toLocaleDateString('ar-SA-u-ca-islamic', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    // Fallback to Gregorian if Hijri is not supported
    return formatDateArabic(date);
  }
};

/**
 * Calculate age from birth date
 * @param {Date|string} birthDate - Birth date
 * @returns {number} Age in years
 */
const calculateAge = (birthDate) => {
  if (!birthDate) return 0;
  
  const birth = new Date(birthDate);
  const today = new Date();
  
  if (isNaN(birth.getTime())) return 0;
  
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return Math.max(0, age);
};

/**
 * Calculate difference between two dates
 * @param {Date|string} startDate - Start date
 * @param {Date|string} endDate - End date
 * @param {string} unit - Unit of measurement ('days', 'months', 'years')
 * @returns {number} Difference in specified unit
 */
const dateDifference = (startDate, endDate, unit = 'days') => {
  if (!startDate || !endDate) return 0;
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0;
  
  const diffTime = Math.abs(end - start);
  
  switch (unit) {
    case 'days':
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    case 'months':
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30.44)); // Average month
    case 'years':
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 365.25)); // Account for leap years
    default:
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
};

/**
 * Add time to date
 * @param {Date|string} date - Base date
 * @param {number} amount - Amount to add
 * @param {string} unit - Unit ('days', 'months', 'years')
 * @returns {Date} New date
 */
const addToDate = (date, amount, unit = 'days') => {
  if (!date) return null;
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return null;
  
  switch (unit) {
    case 'days':
      dateObj.setDate(dateObj.getDate() + amount);
      break;
    case 'months':
      dateObj.setMonth(dateObj.getMonth() + amount);
      break;
    case 'years':
      dateObj.setFullYear(dateObj.getFullYear() + amount);
      break;
  }
  
  return dateObj;
};

/**
 * Check if date is in the past
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is in the past
 */
const isPastDate = (date) => {
  if (!date) return false;
  
  const dateObj = new Date(date);
  const today = new Date();
  
  // Set time to start of day for comparison
  dateObj.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);
  
  return dateObj < today;
};

/**
 * Check if date is today
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is today
 */
const isToday = (date) => {
  if (!date) return false;
  
  const dateObj = new Date(date);
  const today = new Date();
  
  return dateObj.toDateString() === today.toDateString();
};

/**
 * Get start and end of month for a given date
 * @param {Date|string} date - Reference date
 * @returns {Object} Object with startOfMonth and endOfMonth
 */
const getMonthBounds = (date = new Date()) => {
  const dateObj = new Date(date);
  
  const startOfMonth = new Date(dateObj.getFullYear(), dateObj.getMonth(), 1);
  const endOfMonth = new Date(dateObj.getFullYear(), dateObj.getMonth() + 1, 0);
  
  return { startOfMonth, endOfMonth };
};

/**
 * Get fiscal year dates
 * @param {Date|string} date - Reference date
 * @param {string} fiscalYearStart - Fiscal year start (MM/DD format)
 * @returns {Object} Object with fiscalYearStart and fiscalYearEnd
 */
const getFiscalYear = (date = new Date(), fiscalYearStart = '01/01') => {
  const dateObj = new Date(date);
  const [month, day] = fiscalYearStart.split('/').map(Number);
  
  let fiscalStart = new Date(dateObj.getFullYear(), month - 1, day);
  
  // If current date is before fiscal year start, use previous year
  if (dateObj < fiscalStart) {
    fiscalStart = new Date(dateObj.getFullYear() - 1, month - 1, day);
  }
  
  const fiscalEnd = new Date(fiscalStart);
  fiscalEnd.setFullYear(fiscalEnd.getFullYear() + 1);
  fiscalEnd.setDate(fiscalEnd.getDate() - 1);
  
  return {
    fiscalYearStart: fiscalStart,
    fiscalYearEnd: fiscalEnd
  };
};

/**
 * Validate date string
 * @param {string} dateString - Date string to validate
 * @returns {boolean} True if valid date
 */
const isValidDate = (dateString) => {
  if (!dateString) return false;
  
  const date = new Date(dateString);
  return !isNaN(date.getTime());
};

module.exports = {
  formatDateArabic,
  formatDateHijri,
  calculateAge,
  dateDifference,
  addToDate,
  isPastDate,
  isToday,
  getMonthBounds,
  getFiscalYear,
  isValidDate
};
