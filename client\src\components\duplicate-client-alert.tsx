import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  AlertTriangle, 
  Search, 
  RefreshCw, 
  User, 
  Phone, 
  Mail,
  MapPin,
  Calendar
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDateFormat } from '@/hooks/use-date-format';

interface ExistingClient {
  clientId: string;
  clientName: string;
  clientType: string;
  clientPhoneWhatsapp?: string;
  clientEmail?: string;
  clientAddress?: string;
  createdAt?: string;
}

interface DuplicateClientAlertProps {
  duplicateClientId: string;
  existingClient?: ExistingClient;
  onSearchClient: () => void;
  onGenerateNewId: () => void;
  onDismiss?: () => void;
  className?: string;
}

export function DuplicateClientAlert({
  duplicateClientId,
  existingClient,
  onSearchClient,
  onGenerateNewId,
  onDismiss,
  className
}: DuplicateClientAlertProps) {
  
  // استخدام hook التنسيق الموحد للتواريخ
  const { formatDate } = useDateFormat();

  return (
    <Alert variant="destructive" className={cn("border-red-200 bg-red-50 dark:bg-red-950/20", className)}>
      <AlertTriangle className="h-5 w-5 text-red-600" />
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <AlertTitle className="text-lg font-semibold text-red-900 dark:text-red-100">
            العميل مسجل من قبل
          </AlertTitle>
          <Badge variant="destructive" className="text-xs">
            رقم مكرر
          </Badge>
        </div>

        <AlertDescription className="text-red-800 dark:text-red-200">
          رقم العميل <strong>{duplicateClientId}</strong> موجود في النظام مسبقاً.
          {existingClient ? ' إليك بيانات العميل الموجود:' : ' يرجى اختيار رقم عميل مختلف أو البحث عن العميل الموجود.'}
        </AlertDescription>

        {existingClient && (
          <Card className="bg-white/50 dark:bg-gray-800/50 border-red-200">
            <CardContent className="p-4 space-y-3">
              <div className="flex items-center gap-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                <User className="w-4 h-4 text-red-600" />
                بيانات العميل الموجود:
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-600 dark:text-gray-400">الاسم:</span>
                  <span className="text-gray-900 dark:text-gray-100">{existingClient.clientName}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-600 dark:text-gray-400">النوع:</span>
                  <Badge variant="outline" className="text-xs">
                    {existingClient.clientType}
                  </Badge>
                </div>
                
                {existingClient.clientPhoneWhatsapp && (
                  <div className="flex items-center gap-2">
                    <Phone className="w-3 h-3 text-gray-500" />
                    <span className="text-gray-900 dark:text-gray-100">{existingClient.clientPhoneWhatsapp}</span>
                  </div>
                )}
                
                {existingClient.clientEmail && existingClient.clientEmail !== 'لا يوجد' && (
                  <div className="flex items-center gap-2">
                    <Mail className="w-3 h-3 text-gray-500" />
                    <span className="text-gray-900 dark:text-gray-100">{existingClient.clientEmail}</span>
                  </div>
                )}
                
                {existingClient.clientAddress && existingClient.clientAddress !== 'لا يوجد' && (
                  <div className="flex items-center gap-2 md:col-span-2">
                    <MapPin className="w-3 h-3 text-gray-500" />
                    <span className="text-gray-900 dark:text-gray-100">{existingClient.clientAddress}</span>
                  </div>
                )}
                
                {existingClient.createdAt && (
                  <div className="flex items-center gap-2 md:col-span-2">
                    <Calendar className="w-3 h-3 text-gray-500" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">
                      مسجل في: {formatDate(existingClient.createdAt)}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        <div className="flex flex-col sm:flex-row gap-3 pt-2">
          <Button 
            onClick={onSearchClient}
            className="gap-2 bg-red-600 hover:bg-red-700 text-white"
          >
            <Search className="w-4 h-4" />
            {existingClient ? 'عرض بيانات العميل' : 'البحث عن العميل'}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={onGenerateNewId}
            className="gap-2 border-red-300 text-red-700 hover:bg-red-50"
          >
            <RefreshCw className="w-4 h-4" />
            إنشاء رقم عميل جديد
          </Button>
          
          {onDismiss && (
            <Button 
              variant="ghost" 
              onClick={onDismiss}
              className="text-red-600 hover:text-red-800 hover:bg-red-50"
            >
              إغلاق
            </Button>
          )}
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mt-4">
          <div className="flex items-start gap-2">
            <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                نصيحة:
              </p>
              <p className="text-yellow-700 dark:text-yellow-300">
                إذا كان هذا نفس العميل الذي تريد تسجيله، يمكنك استخدام بياناته الموجودة بدلاً من إنشاء عميل جديد.
                وإذا كان عميل مختلف، تأكد من استخدام رقم هوية أو رقم مخصص مختلف.
              </p>
            </div>
          </div>
        </div>
      </div>
    </Alert>
  );
}

// Hook لاستخدام تنبيه العميل المكرر
export function useDuplicateClientAlert() {
  const [duplicateAlert, setDuplicateAlert] = React.useState<{
    show: boolean;
    clientId: string;
    existingClient?: ExistingClient;
  }>({
    show: false,
    clientId: '',
    existingClient: undefined
  });

  const showDuplicateAlert = (clientId: string, existingClient?: ExistingClient) => {
    setDuplicateAlert({
      show: true,
      clientId,
      existingClient
    });
  };

  const hideDuplicateAlert = () => {
    setDuplicateAlert({
      show: false,
      clientId: '',
      existingClient: undefined
    });
  };

  return {
    duplicateAlert,
    showDuplicateAlert,
    hideDuplicateAlert
  };
}

export default DuplicateClientAlert;
