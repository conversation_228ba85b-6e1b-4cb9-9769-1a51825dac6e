// ===== ROUTES INDEX (UPDATED WITH ERROR HANDLING) =====
// Author: Augment Code
// Description: Central router that combines all route modules with error handling

const express = require('express');
const router = express.Router();

// Import error handling
const ErrorHandler = require('../middleware/errorHandler.cjs');

// Import all route modules (no database connection needed - they use the pool directly)
const settingsRoutes = require('./settings');
const referenceDataRoutes = require('./reference-data-updated'); // Using updated version with connection pool
// const clientsRoutes = require('./clients');
// const contractsRoutes = require('./contracts');
// const paymentsRoutes = require('./payments');
// const receivablesRoutes = require('./receivables');
// const chequesRoutes = require('./cheques');
// const dashboardRoutes = require('./dashboard');
// const reportsRoutes = require('./reports');
// const alertsRoutes = require('./alerts');

// Mount routes with their respective prefixes and error handling
router.use('/settings', settingsRoutes);

// Reference data routes - mount at root level since they handle their own prefixes
router.use('/', referenceDataRoutes);

// TODO: Uncomment as other route modules are completed
// router.use('/clients', clientsRoutes);
// router.use('/contracts', contractsRoutes);
// router.use('/new-contracts', contractsRoutes);
// router.use('/treasury-payments', paymentsRoutes);
// router.use('/bank-payments', paymentsRoutes);
// router.use('/cash-receipts', paymentsRoutes);
// router.use('/cheque-receipts', paymentsRoutes);
// router.use('/payments', paymentsRoutes);
// router.use('/installments', paymentsRoutes);
// router.use('/receivables', receivablesRoutes);
// router.use('/cheques', chequesRoutes);
// router.use('/dashboard', dashboardRoutes);
// router.use('/reports', reportsRoutes);
// router.use('/alerts', alertsRoutes);

// Test route with error handling
router.post('/test', ErrorHandler.asyncHandler(async (req, res) => {
  console.log('🧪 TEST endpoint called!');
  console.log('Request body:', req.body);
  
  res.json({ 
    success: true, 
    message: 'Test endpoint working with Connection Pool and Error Handling!', 
    timestamp: new Date().toISOString(),
    requestId: req.id || 'unknown'
  });
}));

module.exports = router;
