import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTit<PERSON> } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { type InsertClient } from "@shared/schema";
import { clientSchema, type ClientFormData } from "@/lib/validation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useEnhancedToast } from "@/hooks/use-enhanced-toast";
import { use<PERSON>anguage } from "@/hooks/use-language";
import { generateClientId } from "@/lib/formatters";
import { cn } from "@/lib/utils";
import { Eye, Save, Building2 } from "lucide-react";
import { ClientCard } from "@/components/client-card";
import { useReferenceDataOptions, REFERENCE_LISTS } from "@/hooks/use-reference-data";
import labels from "@/lib/i18n";

interface AddClientModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AddClientModal({ open, onOpenChange }: AddClientModalProps) {
  const { language, isRTL } = useLanguage();
  const t = labels[language];
  const { showSaveSuccess, showSaveError } = useEnhancedToast();
  const queryClient = useQueryClient();
  const [showPreview, setShowPreview] = useState(false);

  // Get reference data options
  const { options: clientTypeOptions, isLoading: clientTypesLoading } = useReferenceDataOptions("clients", REFERENCE_LISTS.clients.types);
  const { options: clientCategoryOptions, isLoading: clientCategoriesLoading } = useReferenceDataOptions("clients", REFERENCE_LISTS.clients.categories);

  const form = useForm<ClientFormData>({
    resolver: zodResolver(clientSchema),
    defaultValues: {
      clientId: generateClientId(),
      clientType: 'individual',
      clientName: '',
      clientPhoneWhatsapp: '',
      clientPhone2: '',
      clientPhone3: '',
      clientEmail: '',
      clientAddress: '',
      clientNotes: '',
      clientFinancial_Category: '',
      clientLegal_Rep: '',
      clientReg_Number: '',
      clientTaxReg_Number: '',
      clientLegal_Status: '',
      clientRemarks: '',
      clientID_Image: '',
      clientDocuments: '',
      isActive: true,
    },
  });

  const createClientMutation = useMutation({
    mutationFn: async (data: ClientFormData) => {
      const response = await apiRequest('POST', '/api/clients', data);
      return response.json();
    },
    onSuccess: async (result, variables) => {
      // Add the new client to existing cache data immediately
      queryClient.setQueryData(['/api/clients'], (oldData: any) => {
        if (oldData && Array.isArray(oldData)) {
          return [variables, ...oldData];
        }
        return [variables];
      });

      // Also update search queries cache if they exist
      queryClient.setQueryData(['/api/clients', ''], (oldData: any) => {
        if (oldData && Array.isArray(oldData)) {
          return [variables, ...oldData];
        }
        return [variables];
      });

      // Invalidate all client queries to ensure consistency
      await queryClient.invalidateQueries({
        queryKey: ['/api/clients'],
        exact: false
      });

      onOpenChange(false);
      form.reset();
      showSaveSuccess("العميل الجديد");
    },
    onError: (error: any) => {
      showSaveError(error.message || "حدث خطأ أثناء إضافة العميل");
    },
  });

  const onSubmit = (data: InsertClient) => {
    createClientMutation.mutate(data);
  };

  const handlePreview = () => {
    setShowPreview(!showPreview);
  };

  const handlePrint = () => {
    window.print();
  };

  // Get current form values for preview
  const currentFormValues = form.watch();

  if (showPreview) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
          <DialogHeader>
            <div className={cn(
              "flex items-center justify-between",
              isRTL ? "flex-row-reverse" : ""
            )}>
              <DialogTitle>{isRTL ? "معاينة كارت العميل" : "Client Card Preview"}</DialogTitle>
              <Button variant="ghost" size="sm" onClick={() => setShowPreview(false)}>
                <Eye className="h-4 w-4 mr-2" />
                {isRTL ? "العودة للتعديل" : "Back to Edit"}
              </Button>
            </div>
          </DialogHeader>

          <div className="mt-4">
            <ClientCard
              client={currentFormValues as any}
              contracts={[]}
              showPrintButton={true}
              showEditButton={false}
              onPrint={handlePrint}
            />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t.addClient}</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>{isRTL ? "البيانات الأساسية" : "Basic Information"}</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="clientId">{t.clientId}</Label>
                <Input
                  id="clientId"
                  {...form.register('clientId')}
                  placeholder={isRTL ? "سيتم إنشاؤه تلقائياً" : "Auto-generated"}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientType">{t.clientType} *</Label>
                <Select
                  value={form.watch('clientType')}
                  onValueChange={(value) => form.setValue('clientType', value as 'individual' | 'company')}
                  disabled={clientTypesLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={
                      clientTypesLoading
                        ? (isRTL ? "جاري التحميل..." : "Loading...")
                        : (isRTL ? "اختر نوع العميل" : "Select client type")
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    {clientTypeOptions.length > 0 ? (
                      clientTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))
                    ) : (
                      <>
                        <SelectItem value="individual">{t.clientTypes.individuals}</SelectItem>
                        <SelectItem value="company">{t.clientTypes.companies}</SelectItem>
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="clientName">{t.clientName} *</Label>
                <Input
                  id="clientName"
                  {...form.register('clientName')}
                  placeholder={isRTL ? "اسم العميل الكامل" : "Full client name"}
                  required
                />
                {form.formState.errors.clientName && (
                  <p className="text-sm text-destructive">
                    {form.formState.errors.clientName.message}
                  </p>
                )}
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="clientAddress">{t.address}</Label>
                <Textarea
                  id="clientAddress"
                  {...form.register('clientAddress')}
                  placeholder={isRTL ? "عنوان العميل التفصيلي" : "Detailed client address"}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientPhoneWhatsapp">{isRTL ? "رقم الهاتف/واتساب" : "Phone/WhatsApp"} *</Label>
                <Input
                  id="clientPhoneWhatsapp"
                  {...form.register('clientPhoneWhatsapp')}
                  placeholder="01234567890"
                  required
                />
                {form.formState.errors.clientPhoneWhatsapp && (
                  <p className="text-sm text-destructive">
                    {form.formState.errors.clientPhoneWhatsapp.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientPhone2">{isRTL ? "رقم هاتف إضافي" : "Additional Phone"}</Label>
                <Input
                  id="clientPhone2"
                  {...form.register('clientPhone2')}
                  placeholder="01234567890"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientPhone3">{isRTL ? "رقم هاتف ثالث" : "Third Phone"}</Label>
                <Input
                  id="clientPhone3"
                  {...form.register('clientPhone3')}
                  placeholder="01234567890"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientEmail">{t.email}</Label>
                <Input
                  id="clientEmail"
                  type="email"
                  {...form.register('clientEmail')}
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="clientNotes">{t.notes}</Label>
                <Textarea
                  id="clientNotes"
                  {...form.register('clientNotes')}
                  placeholder={isRTL ? "ملاحظات إضافية عن العميل" : "Additional notes about the client"}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Financial & Legal Information */}
          <Card>
            <CardHeader>
              <CardTitle className={cn(
                "flex items-center space-x-2",
                isRTL ? "space-x-reverse" : ""
              )}>
                <Building2 className="h-5 w-5 text-primary" />
                <span>{isRTL ? "المعلومات المالية والقانونية" : "Financial & Legal Information"}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="clientFinancial_Category">{t.financialCategory}</Label>
                <Select
                  value={form.watch('clientFinancial_Category') || ''}
                  onValueChange={(value) => form.setValue('clientFinancial_Category', value)}
                  disabled={clientCategoriesLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={
                      clientCategoriesLoading
                        ? (isRTL ? "جاري التحميل..." : "Loading...")
                        : (isRTL ? "اختر التصنيف" : "Select category")
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    {clientCategoryOptions.length > 0 ? (
                      clientCategoryOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))
                    ) : (
                      <>
                        <SelectItem value="ممتاز">ممتاز</SelectItem>
                        <SelectItem value="جيد">جيد</SelectItem>
                        <SelectItem value="متوسط">متوسط</SelectItem>
                        <SelectItem value="ضعيف">ضعيف</SelectItem>
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientLegal_Rep">{isRTL ? "الممثل القانوني" : "Legal Representative"}</Label>
                <Input
                  id="clientLegal_Rep"
                  {...form.register('clientLegal_Rep')}
                  placeholder={isRTL ? "اسم الممثل القانوني" : "Legal representative name"}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientReg_Number">{isRTL ? "رقم التسجيل" : "Registration Number"}</Label>
                <Input
                  id="clientReg_Number"
                  {...form.register('clientReg_Number')}
                  placeholder={isRTL ? "رقم التسجيل التجاري" : "Commercial registration number"}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientTaxReg_Number">{isRTL ? "رقم التسجيل الضريبي" : "Tax Registration Number"}</Label>
                <Input
                  id="clientTaxReg_Number"
                  {...form.register('clientTaxReg_Number')}
                  placeholder={isRTL ? "رقم التسجيل الضريبي" : "Tax registration number"}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientLegal_Status">{isRTL ? "الحالة القانونية" : "Legal Status"}</Label>
                <Input
                  id="clientLegal_Status"
                  {...form.register('clientLegal_Status')}
                  placeholder={isRTL ? "مثال: مؤسسة فردية، شركة محدودة" : "e.g., Sole Proprietorship, LLC"}
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="clientRemarks">{isRTL ? "ملاحظات إضافية" : "Additional Remarks"}</Label>
                <Textarea
                  id="clientRemarks"
                  {...form.register('clientRemarks')}
                  placeholder={isRTL ? "ملاحظات إضافية مفصلة" : "Detailed additional remarks"}
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className={cn(
            "flex gap-4 pt-4 border-t",
            isRTL ? "justify-start" : "justify-end"
          )}>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              {t.cancel}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handlePreview}
              className="gap-2"
            >
              <Eye className="h-4 w-4" />
              {isRTL ? "معاينة كارت العميل" : "Preview Client Card"}
            </Button>
            <Button
              type="submit"
              disabled={createClientMutation.isPending}
              className="gap-2"
            >
              {createClientMutation.isPending ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              {createClientMutation.isPending ? t.loading : t.save}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
