import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { type Settings, insertSettingsSchema } from "@shared/schema";
import * as z from "zod";
import { cn } from "@/lib/utils";
import {
  Settings as SettingsIcon,
  Building,
  Globe,
  Hash,
  Bell,
  Upload,
  X,
  Save,
  Edit,
  Plus,
  Trash2
} from "lucide-react";
import { useLanguage } from "@/hooks/use-language";
import { useTheme } from "@/components/theme-provider";
import labels from "@/lib/i18n";

// Settings form schema
const settingsSchema = z.object({
  companyName: z.string().min(1, "اسم الشركة مطلوب"),
  programName: z.string().min(1, "اسم البرنامج مطلوب"),
  aboutProgram: z.string().optional(),
  companyLogo: z.string().optional(),
  language: z.enum(["ar", "en"]),
  dateFormat: z.string(),
  currencySymbol: z.string(),
  fontFamily: z.string(),
  workDays: z.string(),
  darkMode: z.boolean(),
});

// Reference Data Tab Component
const ReferenceDataTab = ({ lang }: { lang: "ar" | "en" }) => {
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [listName, setListName] = useState("");
  const [listDataText, setListDataText] = useState("");

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch reference lists
  const { data: referenceLists, isLoading } = useQuery({
    queryKey: ["/api/reference-data"],
    queryFn: () => apiRequest("/api/reference-data"),
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: async (data: { category: string; name: string; nameEn?: string }) => {
      return await apiRequest("/api/reference-data", {
        method: "POST",
        body: JSON.stringify(data),
        headers: { "Content-Type": "application/json" }
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-data"] });
      toast({
        title: lang === "ar" ? "تم إنشاء القائمة بنجاح" : "List created successfully",
      });
      handleCancel();
    },
    onError: (error: Error) => {
      toast({
        title: lang === "ar" ? "خطأ في إنشاء القائمة" : "Error creating list",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: { category: string; name: string; nameEn?: string } }) => {
      return await apiRequest(`/api/reference-data/${id}`, {
        method: "PUT",
        body: JSON.stringify(data),
        headers: { "Content-Type": "application/json" }
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-data"] });
      toast({
        title: lang === "ar" ? "تم تحديث القائمة بنجاح" : "List updated successfully",
      });
      handleCancel();
    },
    onError: (error: Error) => {
      toast({
        title: lang === "ar" ? "خطأ في تحديث القائمة" : "Error updating list",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      return await apiRequest(`/api/reference-data/${id}`, {
        method: "DELETE"
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-data"] });
      toast({
        title: lang === "ar" ? "تم حذف القائمة بنجاح" : "List deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: lang === "ar" ? "خطأ في حذف القائمة" : "Error deleting list",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Handle form submission
  const handleSubmit = () => {
    if (!listName.trim()) {
      toast({
        title: lang === "ar" ? "خطأ" : "Error",
        description: lang === "ar" ? "اسم القائمة مطلوب" : "List name is required",
        variant: "destructive",
      });
      return;
    }

    if (!listDataText.trim()) {
      toast({
        title: lang === "ar" ? "خطأ" : "Error",
        description: lang === "ar" ? "بيانات القائمة مطلوبة" : "List data is required",
        variant: "destructive",
      });
      return;
    }

    // Split data by "|" and filter empty values
    const listData = listDataText.split("|").map(item => item.trim()).filter(item => item !== "");

    if (listData.length === 0) {
      toast({
        title: lang === "ar" ? "خطأ" : "Error",
        description: lang === "ar" ? "يجب إدخال بيانات صحيحة للقائمة" : "Valid list data is required",
        variant: "destructive",
      });
      return;
    }

    const submitData = { category: listName.trim(), name: listData[0], nameEn: listData[1] };

    if (editingId) {
      updateMutation.mutate({ id: editingId, data: submitData });
    } else {
      createMutation.mutate(submitData);
    }
  };

  // Handle edit
  const handleEdit = (list: any) => {
    setEditingId(list.id);
    setIsAdding(true);
    setListName(list.category);
    setListDataText(`${list.name} | ${list.nameEn || ""}`);
  };

  // Handle cancel
  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    setListName("");
    setListDataText("");
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{lang === "ar" ? "البيانات المرجعية" : "Reference Data"}</CardTitle>
          <Button onClick={() => setIsAdding(true)} disabled={isAdding}>
            <Plus className="h-4 w-4 mr-2" />
            {lang === "ar" ? "إضافة قائمة" : "Add List"}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add/Edit Form */}
        {isAdding && (
          <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
            <h3 className="text-lg font-medium mb-4">
              {editingId
                ? (lang === "ar" ? "تعديل القائمة" : "Edit List")
                : (lang === "ar" ? "إضافة قائمة جديدة" : "Add New List")
              }
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  {lang === "ar" ? "اسم القائمة *" : "List Name *"}
                </label>
                <Input
                  value={listName}
                  onChange={(e) => setListName(e.target.value)}
                  placeholder={lang === "ar" ? "أدخل اسم القائمة" : "Enter list name"}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  {lang === "ar" ? "بيانات القائمة *" : "List Data *"}
                </label>
                <Textarea
                  value={listDataText}
                  onChange={(e) => setListDataText(e.target.value)}
                  placeholder={lang === "ar"
                    ? "أدخل البيانات مفصولة بـ | مثال: عنصر1 | عنصر2"
                    : "Enter data separated by | example: item1 | item2"
                  }
                  className="min-h-[100px] resize-vertical"
                  rows={4}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {lang === "ar"
                    ? "استخدم الرمز | للفصل بين العناصر"
                    : "Use | to separate items"
                  }
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={handleSubmit}
                  disabled={createMutation.isPending || updateMutation.isPending}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {editingId
                    ? (lang === "ar" ? "تحديث" : "Update")
                    : (lang === "ar" ? "حفظ" : "Save")
                  }
                </Button>
                <Button variant="outline" onClick={handleCancel}>
                  <X className="h-4 w-4 mr-2" />
                  {lang === "ar" ? "إلغاء" : "Cancel"}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Lists Display */}
        <div className="space-y-4">
          {referenceLists?.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {lang === "ar" ? "لا توجد قوائم مرجعية" : "No reference lists found"}
            </div>
          ) : (
            referenceLists?.map((list: any) => (
              <div key={list.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-lg font-medium">{list.category}</h4>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(list)}
                      disabled={isAdding}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteMutation.mutate(list.id)}
                      disabled={deleteMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm">
                    {list.name}
                  </span>
                  {list.nameEn && (
                    <span className="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-sm">
                      {list.nameEn}
                    </span>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const SystemSettings = () => {
  const { language } = useLanguage();
  const { theme, setTheme } = useTheme();
  const [activeTab, setActiveTab] = useState("general");
  const [companyLogo, setCompanyLogo] = useState("");
  const [isGeneralEditing, setIsGeneralEditing] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();
  const t = labels[language];

  // Forms
  const generalForm = useForm<Partial<Settings>>({
    resolver: zodResolver(settingsSchema.partial()),
    defaultValues: {
      companyName: "",
      programName: "",
      aboutProgram: "",
    },
  });

  // Fetch settings
  const { data: settings, isLoading } = useQuery({
    queryKey: ["/api/settings"],
    queryFn: () => apiRequest("/api/settings"),
  });

  // Update forms when data loads
  useEffect(() => {
    if (settings) {
      generalForm.reset({
        companyName: settings.companyName || "",
        programName: settings.programName || "",
        aboutProgram: settings.aboutProgram || "",
      });
      setCompanyLogo(settings.companyLogo || "");
    }
  }, [settings, generalForm]);

  // Update settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (data: Partial<Settings>) => {
      return await apiRequest("/api/settings", {
        method: "PUT",
        body: JSON.stringify(data),
        headers: { "Content-Type": "application/json" }
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/settings"] });
      toast({
        title: language === "ar" ? "تم تحديث الإعدادات بنجاح" : "Settings updated successfully",
      });
      setIsGeneralEditing(false);
    },
    onError: (error: Error) => {
      toast({
        title: language === "ar" ? "خطأ في تحديث الإعدادات" : "Error updating settings",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleGeneralSubmit = (data: Partial<Settings>) => {
    updateSettingsMutation.mutate({
      ...data,
      companyLogo,
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <SettingsIcon className="h-6 w-6" />
        <h1 className="text-2xl font-bold">{t.settings}</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Building className="h-4 w-4" />
            {language === "ar" ? "عام" : "General"}
          </TabsTrigger>
          <TabsTrigger value="localization" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            {language === "ar" ? "اللغة والعملة" : "Localization"}
          </TabsTrigger>
          <TabsTrigger value="reference" className="flex items-center gap-2">
            <Hash className="h-4 w-4" />
            {language === "ar" ? "البيانات المرجعية" : "Reference Data"}
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            {language === "ar" ? "الإشعارات" : "Notifications"}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>{language === "ar" ? "الإعدادات العامة" : "General Settings"}</CardTitle>
                <Button
                  variant="outline"
                  onClick={() => setIsGeneralEditing(!isGeneralEditing)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  {isGeneralEditing 
                    ? (language === "ar" ? "إلغاء" : "Cancel")
                    : (language === "ar" ? "تعديل" : "Edit")
                  }
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Form {...generalForm}>
                <form onSubmit={generalForm.handleSubmit(handleGeneralSubmit)} className="space-y-4">
                  <FormField
                    control={generalForm.control}
                    name="companyName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{language === "ar" ? "اسم الشركة" : "Company Name"}</FormLabel>
                        <FormControl>
                          <Input 
                            {...field} 
                            disabled={!isGeneralEditing}
                            placeholder={language === "ar" ? "أدخل اسم الشركة" : "Enter company name"}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={generalForm.control}
                    name="programName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{language === "ar" ? "اسم البرنامج" : "Program Name"}</FormLabel>
                        <FormControl>
                          <Input 
                            {...field} 
                            disabled={!isGeneralEditing}
                            placeholder={language === "ar" ? "أدخل اسم البرنامج" : "Enter program name"}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={generalForm.control}
                    name="aboutProgram"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{language === "ar" ? "نبذة عن البرنامج" : "About Program"}</FormLabel>
                        <FormControl>
                          <Textarea 
                            {...field} 
                            disabled={!isGeneralEditing}
                            placeholder={language === "ar" ? "أدخل نبذة عن البرنامج" : "Enter program description"}
                            rows={4}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {isGeneralEditing && (
                    <Button type="submit" disabled={updateSettingsMutation.isPending}>
                      <Save className="h-4 w-4 mr-2" />
                      {language === "ar" ? "حفظ التغييرات" : "Save Changes"}
                    </Button>
                  )}
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="localization">
          <Card>
            <CardHeader>
              <CardTitle>{language === "ar" ? "إعدادات اللغة والعملة" : "Language & Currency Settings"}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500 dark:text-gray-400">
                {language === "ar" ? "قريباً..." : "Coming soon..."}
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reference">
          <ReferenceDataTab lang={language} />
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>{language === "ar" ? "إعدادات الإشعارات" : "Notification Settings"}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500 dark:text-gray-400">
                {language === "ar" ? "قريباً..." : "Coming soon..."}
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SystemSettings;