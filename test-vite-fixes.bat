@echo off
echo ===== اختبار إصلاحات Vite =====
echo.

echo 1. اختبار تشغيل السيرفر (ESM)...
echo Starting server.mjs for 5 seconds...
start "Test Server" cmd /c "timeout /t 5 && node server.mjs"

echo.
echo 2. انتظار 3 ثوان...
timeout /t 3 /nobreak > nul

echo.
echo 3. اختبار Vite dev server...
echo Starting Vite for 10 seconds...
start "Test Vite" cmd /c "timeout /t 10 && npm run dev"

echo.
echo ===== الاختبار مكتمل =====
echo إذا لم تظهر أخطاء، فالإصلاحات تعمل بشكل صحيح
echo.
pause
