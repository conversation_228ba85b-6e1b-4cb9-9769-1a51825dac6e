import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Plus, Edit, Trash2, Save, X, Settings } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { z } from "zod";
import labels from "../i18n";

// Enhanced reference list schema
const referenceListSchema = z.object({
  listName: z.string().min(1, 'اسم القائمة مطلوب'),
  listData: z.array(z.string()).min(1, 'يجب إضافة عنصر واحد على الأقل'),
  usageModules: z.array(z.string()).min(1, 'يجب تحديد موديول واحد على الأقل'),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

type ReferenceList = z.infer<typeof referenceListSchema> & {
  id?: number;
  createdAt?: string;
  updatedAt?: string;
};

type InsertReferenceList = z.infer<typeof referenceListSchema>;

const ReferenceData = () => {
  const [lang] = useState<"ar" | "en">((localStorage.getItem("lang") as "ar" | "en") || "ar");
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [newItems, setNewItems] = useState<string[]>([""]);
  
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Form for adding/editing lists
  const form = useForm({
    resolver: zodResolver(referenceListSchema),
    defaultValues: {
      listName: "",
      listData: [],
      usageModules: [],
      description: "",
      isActive: true,
    },
  });

  // Fetch reference lists
  const { data: referenceLists, isLoading } = useQuery({
    queryKey: ["/api/reference-lists"],
    queryFn: () => apiRequest("/api/reference-lists"),
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: async (data: InsertReferenceList) => {
      return await apiRequest("/api/reference-lists", {
        method: "POST",
        body: JSON.stringify(data),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-lists"] });
      toast({
        title: lang === "ar" ? "تم إنشاء القائمة بنجاح" : "List created successfully",
      });
      setIsAdding(false);
      setNewItems([""]);
      form.reset();
    },
    onError: (error: Error) => {
      toast({
        title: lang === "ar" ? "خطأ في إنشاء القائمة" : "Error creating list",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: InsertReferenceList }) => {
      return await apiRequest(`/api/reference-lists/${id}`, {
        method: "PUT",
        body: JSON.stringify(data),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-lists"] });
      toast({
        title: lang === "ar" ? "تم تحديث القائمة بنجاح" : "List updated successfully",
      });
      setEditingId(null);
      form.reset();
    },
    onError: (error: Error) => {
      toast({
        title: lang === "ar" ? "خطأ في تحديث القائمة" : "Error updating list",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      return await apiRequest(`/api/reference-lists/${id}`, {
        method: "DELETE",
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-lists"] });
      toast({
        title: lang === "ar" ? "تم حذف القائمة بنجاح" : "List deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: lang === "ar" ? "خطأ في حذف القائمة" : "Error deleting list",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Handle form submission
  const onSubmit = (data: any) => {
    const filteredData = newItems.filter(item => item.trim() !== "");
    const usageModules = form.getValues("usageModules") || [];

    // Validate that at least one module is selected
    if (usageModules.length === 0) {
      toast({
        title: lang === "ar" ? "خطأ في البيانات" : "Data Error",
        description: lang === "ar" ? "يجب تحديد موديول واحد على الأقل" : "At least one module must be selected",
        variant: "destructive",
      });
      return;
    }

    const submitData = {
      ...data,
      listData: filteredData,
      usageModules: usageModules,
      description: data.description || ""
    };

    console.log('Submitting data:', submitData);

    if (editingId) {
      updateMutation.mutate({ id: editingId, data: submitData });
    } else {
      createMutation.mutate(submitData);
    }
  };

  // Handle edit
  const handleEdit = (list: ReferenceList) => {
    setEditingId(list.id!);
    setIsAdding(true);
    setNewItems(list.listData.length > 0 ? list.listData : [""]);

    // Parse usageModules if it's a string (from database)
    let parsedModules = [];
    if (typeof list.usageModules === 'string') {
      try {
        parsedModules = JSON.parse(list.usageModules);
      } catch (e) {
        parsedModules = [];
      }
    } else if (Array.isArray(list.usageModules)) {
      parsedModules = list.usageModules;
    }

    form.reset({
      listName: list.listName,
      listData: list.listData,
      usageModules: parsedModules,
      description: list.description || "",
      isActive: list.isActive !== false,
    });
  };

  // Handle cancel
  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    setNewItems([""]);
    form.reset();
  };

  // Add new item input
  const addNewItem = () => {
    setNewItems([...newItems, ""]);
  };

  // Remove item input
  const removeItem = (index: number) => {
    const updated = newItems.filter((_, i) => i !== index);
    setNewItems(updated.length > 0 ? updated : [""]);
  };

  // Update item value
  const updateItem = (index: number, value: string) => {
    const updated = [...newItems];
    updated[index] = value;
    setNewItems(updated);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">
          {lang === "ar" ? "البيانات المرجعية" : "Reference Data"}
        </h2>
        <Button onClick={() => setIsAdding(true)} disabled={isAdding}>
          <Plus className="h-4 w-4 mr-2" />
          {lang === "ar" ? "إضافة قائمة" : "Add List"}
        </Button>
      </div>

      {/* Add/Edit Form */}
      {isAdding && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingId 
                ? (lang === "ar" ? "تعديل القائمة" : "Edit List")
                : (lang === "ar" ? "إضافة قائمة جديدة" : "Add New List")
              }
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="listName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{lang === "ar" ? "اسم القائمة *" : "List Name *"}</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder={lang === "ar" ? "أدخل اسم القائمة" : "Enter list name"}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{lang === "ar" ? "وصف القائمة" : "List Description"}</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder={lang === "ar" ? "وصف مختصر للقائمة" : "Brief description"}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>



                <div>
                  <label className="text-sm font-medium mb-3 block">
                    {lang === "ar" ? "الموديولات المستخدمة *" : "Usage Modules *"}
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 p-4 border rounded-lg">
                    {[
                      { value: "contracts", label: lang === "ar" ? "العقود" : "Contracts" },
                      { value: "clients", label: lang === "ar" ? "العملاء" : "Clients" },
                      { value: "payments", label: lang === "ar" ? "المدفوعات" : "Payments" },
                      { value: "checks", label: lang === "ar" ? "الشيكات" : "Checks" },
                      { value: "receivables", label: lang === "ar" ? "الاستحقاقات" : "Receivables" },
                      { value: "reports", label: lang === "ar" ? "التقارير" : "Reports" },
                    ].map((module) => {
                      const currentModules = form.watch("usageModules") || [];
                      return (
                        <div key={module.value} className="flex items-center space-x-2 space-x-reverse">
                          <Checkbox
                            id={module.value}
                            checked={currentModules.includes(module.value)}
                            onCheckedChange={(checked) => {
                              const currentValues = form.getValues("usageModules") || [];
                              if (checked) {
                                form.setValue("usageModules", [...currentValues, module.value]);
                              } else {
                                form.setValue("usageModules", currentValues.filter((v: string) => v !== module.value));
                              }
                            }}
                          />
                          <label
                            htmlFor={module.value}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {module.label}
                          </label>
                        </div>
                      );
                    })}
                  </div>
                  <p className="text-xs text-gray-600 mt-2">
                    {lang === "ar"
                      ? "حدد الموديولات التي ستستخدم هذه القائمة"
                      : "Select the modules that will use this list"
                    }
                  </p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    {lang === "ar" ? "بيانات القائمة *" : "List Data *"}
                  </label>
                  {newItems.map((item, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        value={item}
                        onChange={(e) => updateItem(index, e.target.value)}
                        placeholder={lang === "ar" ? "أدخل عنصر القائمة" : "Enter list item"}
                      />
                      {newItems.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeItem(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addNewItem}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {lang === "ar" ? "إضافة عنصر" : "Add Item"}
                  </Button>
                </div>

                <div className="flex gap-2">
                  <Button type="submit" disabled={createMutation.isPending || updateMutation.isPending}>
                    <Save className="h-4 w-4 mr-2" />
                    {editingId 
                      ? (lang === "ar" ? "تحديث" : "Update")
                      : (lang === "ar" ? "حفظ" : "Save")
                    }
                  </Button>
                  <Button type="button" variant="outline" onClick={handleCancel}>
                    <X className="h-4 w-4 mr-2" />
                    {lang === "ar" ? "إلغاء" : "Cancel"}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}

      {/* Lists Display */}
      <div className="grid gap-4">
        {referenceLists?.map((list: ReferenceList) => (
          <Card key={list.id}>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg">{list.listName}</CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEdit(list)}
                  disabled={isAdding}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => deleteMutation.mutate(list.id!)}
                  disabled={deleteMutation.isPending}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Description */}
              {list.description && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {list.description}
                </p>
              )}

              {/* Usage Modules */}
              <div>
                <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  {lang === "ar" ? "الموديولات المستخدمة:" : "Usage Modules:"}
                </h4>
                <div className="flex flex-wrap gap-2">
                  {(() => {
                    // Parse usageModules if it's a string
                    let modules = [];
                    if (typeof list.usageModules === 'string') {
                      try {
                        modules = JSON.parse(list.usageModules);
                      } catch (e) {
                        modules = [];
                      }
                    } else if (Array.isArray(list.usageModules)) {
                      modules = list.usageModules;
                    }

                    const moduleLabels: Record<string, string> = {
                      contracts: lang === "ar" ? "العقود" : "Contracts",
                      clients: lang === "ar" ? "العملاء" : "Clients",
                      payments: lang === "ar" ? "المدفوعات" : "Payments",
                      checks: lang === "ar" ? "الشيكات" : "Checks",
                      receivables: lang === "ar" ? "الاستحقاقات" : "Receivables",
                      reports: lang === "ar" ? "التقارير" : "Reports",
                    };

                    return modules.map((module: string, index: number) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs font-medium"
                      >
                        {moduleLabels[module] || module}
                      </span>
                    ));
                  })()}
                </div>
              </div>

              {/* List Data */}
              <div>
                <h4 className="text-sm font-medium mb-2">
                  {lang === "ar" ? "بيانات القائمة:" : "List Data:"}
                </h4>
                <div className="flex flex-wrap gap-2">
                  {list.listData.map((item, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-sm"
                    >
                      {item}
                    </span>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ReferenceData;
