// ===== UNIFIED DATABASE HOOKS =====
// مجموعة موحدة من الـ hooks للتعامل مع قاعدة البيانات
// نظام واحد مستقر بدون تعقيدات

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { db, type ApiResponse, type QueryFilters } from '@/lib/unified-database';
import { useToast } from '@/hooks/use-toast';

// ===== CONTRACTS HOOKS =====

/**
 * جلب جميع العقود
 */
export function useContracts(filters?: QueryFilters) {
  return useQuery({
    queryKey: ['contracts', filters],
    queryFn: () => db.getContracts(filters),
    staleTime: 5 * 60 * 1000, // 5 دقائق
    retry: 2,
  });
}

/**
 * جلب عقد محدد
 */
export function useContract(id: string) {
  return useQuery({
    queryKey: ['contract', id],
    queryFn: () => db.getContract(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // دقيقتان
    retry: 2,
  });
}

/**
 * إنشاء عقد جديد
 */
export function useCreateContract() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: any) => db.createContract(data),
    onSuccess: (result: ApiResponse) => {
      if (result.success) {
        queryClient.invalidateQueries({ queryKey: ['contracts'] });
        toast({
          title: 'تم إنشاء العقد بنجاح',
          description: result.message,
          variant: 'default',
        });
      } else {
        toast({
          title: 'خطأ في إنشاء العقد',
          description: result.error,
          variant: 'destructive',
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: 'خطأ في إنشاء العقد',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}



/**
 * حذف عقد
 */
export function useDeleteContract() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => db.deleteContract(id),
    onSuccess: (result: ApiResponse) => {
      if (result.success) {
        queryClient.invalidateQueries({ queryKey: ['contracts'] });
        toast({
          title: 'تم حذف العقد بنجاح',
          description: result.message,
          variant: 'default',
        });
      } else {
        toast({
          title: 'خطأ في حذف العقد',
          description: result.error,
          variant: 'destructive',
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: 'خطأ في حذف العقد',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
}

// ===== CONTRACT RELATED DATA HOOKS =====

/**
 * جلب استحقاقات العقد
 */
export function useContractReceivables(contractId: string) {
  return useQuery({
    queryKey: ['contract-receivables', contractId],
    queryFn: () => db.getContractReceivables(contractId),
    enabled: !!contractId,
    staleTime: 2 * 60 * 1000,
    retry: 2,
  });
}

/**
 * جلب أقساط العقد
 */
export function useContractInstallments(contractId: string) {
  return useQuery({
    queryKey: ['contract-installments', contractId],
    queryFn: () => db.getContractInstallments(contractId),
    enabled: !!contractId,
    staleTime: 2 * 60 * 1000,
    retry: 2,
  });
}

/**
 * جلب منتجات العقد
 */
export function useContractProducts(contractId: string) {
  return useQuery({
    queryKey: ['contract-products', contractId],
    queryFn: () => db.getContractProducts(contractId),
    enabled: !!contractId,
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
}

/**
 * توليد استحقاقات العقد
 */
export function useGenerateReceivables() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (contractId: string) => db.generateContractReceivables(contractId),
    onSuccess: (result: ApiResponse, contractId) => {
      if (result.success) {
        queryClient.invalidateQueries({ queryKey: ['contract-receivables', contractId] });
        queryClient.invalidateQueries({ queryKey: ['receivables'] });
        toast({
          title: 'تم توليد الاستحقاقات بنجاح',
          description: result.message,
          variant: 'default',
        });
      } else {
        toast({
          title: 'خطأ في توليد الاستحقاقات',
          description: result.error,
          variant: 'destructive',
        });
      }
    },
  });
}

// ===== CLIENTS HOOKS =====

/**
 * جلب جميع العملاء
 */
export function useClients(filters?: QueryFilters) {
  return useQuery({
    queryKey: ['clients', filters],
    queryFn: () => db.getClients(filters),
    staleTime: 10 * 60 * 1000, // 10 دقائق
    retry: 2,
  });
}

/**
 * جلب عميل محدد
 */
export function useClient(id: string) {
  return useQuery({
    queryKey: ['client', id],
    queryFn: () => db.getClient(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
}

/**
 * إنشاء عميل جديد
 */
export function useCreateClient() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: any) => db.createClient(data),
    onSuccess: (result: ApiResponse) => {
      if (result.success) {
        // Invalidate all client queries (including search queries)
        queryClient.invalidateQueries({
          queryKey: ['clients'],
          exact: false // This will invalidate all queries that start with ['clients']
        });
        toast({
          title: 'تم إنشاء العميل بنجاح',
          description: result.message,
          variant: 'default',
        });
      } else {
        toast({
          title: 'خطأ في إنشاء العميل',
          description: result.error,
          variant: 'destructive',
        });
      }
    },
  });
}

// ===== RECEIVABLES HOOKS =====

/**
 * جلب جميع الاستحقاقات
 */
export function useReceivables(filters?: QueryFilters) {
  return useQuery({
    queryKey: ['receivables', filters],
    queryFn: () => db.getReceivables(filters),
    staleTime: 2 * 60 * 1000,
    retry: 2,
    // إزالة التحديث التلقائي لتجنب مشاكل الـ intervals
  });
}

// ===== PAYMENTS HOOKS =====

/**
 * جلب جميع المدفوعات
 */
export function usePayments(filters?: QueryFilters) {
  return useQuery({
    queryKey: ['payments', filters],
    queryFn: () => db.getPayments(filters),
    staleTime: 2 * 60 * 1000,
    retry: 2,
  });
}

/**
 * إنشاء دفعة جديدة
 */
export function useCreatePayment() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: any) => db.createPayment(data),
    onSuccess: (result: ApiResponse) => {
      if (result.success) {
        queryClient.invalidateQueries({ queryKey: ['payments'] });
        queryClient.invalidateQueries({ queryKey: ['receivables'] });
        toast({
          title: 'تم إنشاء الدفعة بنجاح',
          description: result.message,
          variant: 'default',
        });
      } else {
        toast({
          title: 'خطأ في إنشاء الدفعة',
          description: result.error,
          variant: 'destructive',
        });
      }
    },
  });
}

/**
 * جلب إيصالات الخزينة
 */
export function useCashReceipts(filters?: QueryFilters) {
  return useQuery({
    queryKey: ['cash-receipts', filters],
    queryFn: () => db.getCashReceipts(filters),
    staleTime: 2 * 60 * 1000,
    retry: 2,
  });
}

// ===== CHEQUES HOOKS =====

/**
 * جلب جميع الشيكات
 */
export function useCheques(filters?: QueryFilters) {
  return useQuery({
    queryKey: ['cheques', filters],
    queryFn: () => db.getCheques(filters),
    staleTime: 2 * 60 * 1000,
    retry: 2,
  });
}

// ===== REPORTS HOOKS =====

/**
 * جلب التقرير المالي
 */
export function useFinancialReport(filters?: QueryFilters) {
  return useQuery({
    queryKey: ['financial-report', filters],
    queryFn: () => db.getFinancialReport(filters),
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
}

/**
 * جلب إحصائيات لوحة التحكم
 */
export function useDashboardStats(filters?: QueryFilters) {
  return useQuery({
    queryKey: ['dashboard-stats', filters],
    queryFn: () => db.getDashboardStats(filters),
    staleTime: 1 * 60 * 1000, // دقيقة واحدة
    retry: 2,
    // إزالة التحديث التلقائي لتجنب مشاكل الـ intervals
  });
}

// ===== SETTINGS HOOKS =====

/**
 * جلب الإعدادات
 */
export function useSettings() {
  return useQuery({
    queryKey: ['settings'],
    queryFn: () => db.getSettings(),
    staleTime: 30 * 60 * 1000, // 30 دقيقة
    retry: 2,
  });
}

/**
 * حفظ الإعدادات
 */
export function useSaveSettings() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: any) => db.saveSettings(data),
    onSuccess: (result: ApiResponse) => {
      if (result.success) {
        queryClient.invalidateQueries({ queryKey: ['settings'] });
        toast({
          title: 'تم حفظ الإعدادات بنجاح',
          description: result.message,
          variant: 'default',
        });
      } else {
        toast({
          title: 'خطأ في حفظ الإعدادات',
          description: result.error,
          variant: 'destructive',
        });
      }
    },
  });
}

// ===== REFERENCE DATA HOOKS =====

/**
 * جلب البيانات المرجعية
 */
export function useReferenceData(listName?: string) {
  return useQuery({
    queryKey: ['reference-data', listName],
    queryFn: () => db.getReferenceData(listName),
    staleTime: 15 * 60 * 1000, // 15 دقيقة
    retry: 2,
  });
}

// ===== UTILITY HOOKS =====

/**
 * فحص حالة الاتصال
 */
export function useConnectionStatus() {
  return useQuery({
    queryKey: ['connection-status'],
    queryFn: () => db.checkConnection(),
    staleTime: 30 * 1000, // 30 ثانية
    retry: 1,
    // إزالة التحديث التلقائي لتجنب مشاكل الـ intervals
  });
}
