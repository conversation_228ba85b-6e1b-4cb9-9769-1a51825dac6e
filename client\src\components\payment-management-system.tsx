import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { insertPaymentSchema, type InsertPayment, type Payment, type ContractInstallment } from "@shared/schema";
import { EnhancedContractCalculator } from "@/lib/enhanced-contract-calculator";
import { useCurrency } from "@/hooks/use-currency";
import { useDateFormat } from "@/hooks/use-date-format";
import { 
  CreditCard, 
  DollarSign, 
  Plus, 
  Search, 
  Filter, 
  Download, 
  Receipt,
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar
} from "lucide-react";

const paymentFormSchema = insertPaymentSchema.extend({
  installmentIds: z.array(z.number()).optional(),
});

type PaymentFormData = z.infer<typeof paymentFormSchema>;

interface PaymentManagementSystemProps {
  contractId: number;
  installments: ContractInstallment[];
  onPaymentRecorded?: (payment: Payment) => void;
}

export default function PaymentManagementSystem({
  contractId,
  installments,
  onPaymentRecorded
}: PaymentManagementSystemProps) {
  const { toast } = useToast();
  const { formatCurrency } = useCurrency();
  const { formatDate } = useDateFormat();
  const queryClient = useQueryClient();
  const [selectedInstallments, setSelectedInstallments] = useState<number[]>([]);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Fetch payments for this contract
  const { data: payments = [], isLoading: paymentsLoading } = useQuery<Payment[]>({
    queryKey: [`/api/contracts/${contractId}/payments`],
  });

  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentFormSchema),
    defaultValues: {
      contractId,
      paymentNumber: 1,
      dueDate: "",
      amount: 0,
      lateFee: 0,
      bouncedCheckFee: 0,
      totalAmount: 0,
      isPaid: false,
      paidDate: "",
      paymentMethod: "نقدي",
      checkNumber: "",
      bankName: "",
      notes: "",
      isActive: true,
    },
  });

  // Calculate payment summary
  const paymentSummary = React.useMemo(() => {
    const totalDue = installments.reduce((sum, inst) => sum + inst.installmentAmount, 0);
    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
    const totalLateFees = payments.reduce((sum, payment) => sum + (payment.lateFee || 0), 0);
    const totalBouncedFees = payments.reduce((sum, payment) => sum + (payment.bouncedCheckFee || 0), 0);
    const paidCount = payments.filter(p => p.isPaid).length;
    const overdueCount = installments.filter(inst => 
      !inst.isPaid && new Date(inst.paymentDueDate) < new Date()
    ).length;

    return {
      totalDue,
      totalPaid,
      totalRemaining: totalDue - totalPaid,
      totalLateFees,
      totalBouncedFees,
      paidCount,
      overdueCount,
      paymentProgress: totalDue > 0 ? (totalPaid / totalDue) * 100 : 0
    };
  }, [installments, payments]);

  // Create payment mutation
  const createPaymentMutation = useMutation({
    mutationFn: (data: PaymentFormData) =>
      fetch(`/api/contracts/${contractId}/payments`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      }).then(res => {
        if (!res.ok) throw new Error("Failed to create payment");
        return res.json();
      }),
    onSuccess: (newPayment) => {
      toast({
        title: "تم تسجيل الدفعة",
        description: "تم تسجيل الدفعة بنجاح!",
      });
      queryClient.invalidateQueries({ queryKey: [`/api/contracts/${contractId}/payments`] });
      queryClient.invalidateQueries({ queryKey: [`/api/contracts/${contractId}/installments`] });
      setPaymentDialogOpen(false);
      form.reset();
      if (onPaymentRecorded) {
        onPaymentRecorded(newPayment);
      }
    },
    onError: (error: any) => {
      toast({
        title: "خطأ في تسجيل الدفعة",
        description: `خطأ: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: PaymentFormData) => {
    // Calculate total amount including fees
    const totalAmount = data.amount + (data.lateFee || 0) + (data.bouncedCheckFee || 0);
    const paymentData = {
      ...data,
      totalAmount,
      isPaid: true,
      paidDate: new Date().toISOString().split('T')[0],
    };
    
    createPaymentMutation.mutate(paymentData);
  };

  // Auto-calculate total amount when component amounts change
  useEffect(() => {
    const amount = form.watch("amount") || 0;
    const lateFee = form.watch("lateFee") || 0;
    const bouncedCheckFee = form.watch("bouncedCheckFee") || 0;
    const totalAmount = amount + lateFee + bouncedCheckFee;
    form.setValue("totalAmount", totalAmount);
  }, [form.watch("amount"), form.watch("lateFee"), form.watch("bouncedCheckFee")]);

  // Filter installments based on search and status
  const filteredInstallments = installments.filter(installment => {
    const matchesSearch = searchTerm === "" || 
      installment.installmentNumber.toString().includes(searchTerm) ||
      (installment.productLabel && installment.productLabel.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesStatus = statusFilter === "all" ||
      (statusFilter === "paid" && installment.isPaid) ||
      (statusFilter === "unpaid" && !installment.isPaid) ||
      (statusFilter === "overdue" && !installment.isPaid && new Date(installment.paymentDueDate) < new Date());
    
    return matchesSearch && matchesStatus;
  });

  const getInstallmentStatus = (installment: ContractInstallment) => {
    if (installment.isPaid) {
      return { status: 'paid', label: 'مدفوع', color: 'bg-green-100 text-green-800' };
    }
    
    const dueDate = new Date(installment.paymentDueDate);
    const today = new Date();
    
    if (dueDate < today) {
      return { status: 'overdue', label: 'متأخر', color: 'bg-red-100 text-red-800' };
    }
    
    const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    if (daysUntilDue <= 7) {
      return { status: 'due-soon', label: 'مستحق قريباً', color: 'bg-yellow-100 text-yellow-800' };
    }
    
    return { status: 'pending', label: 'معلق', color: 'bg-gray-100 text-gray-800' };
  };

  const handleQuickPayment = (installment: ContractInstallment) => {
    form.setValue("amount", installment.remainingAmount);
    form.setValue("dueDate", installment.paymentDueDate);
    form.setValue("paymentNumber", installment.installmentNumber);
    
    // Calculate late fee if overdue
    const dueDate = new Date(installment.paymentDueDate);
    const today = new Date();
    if (dueDate < today) {
      const daysLate = Math.ceil((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
      const lateFee = contractCalculator.calculateLateFee(
        installment.installmentAmount,
        daysLate,
        0, // grace period
        'نسبة مئوية',
        1 // 1% per day
      );
      form.setValue("lateFee", lateFee);
    }
    
    setPaymentDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Payment Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المستحق</p>
                <p className="text-2xl font-bold text-blue-600">
                  {formatCurrency(paymentSummary.totalDue)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المدفوع</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(paymentSummary.totalPaid)}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المتبقي</p>
                <p className="text-2xl font-bold text-orange-600">
                  {formatCurrency(paymentSummary.totalRemaining)}
                </p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">الغرامات</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatCurrency(paymentSummary.totalLateFees + paymentSummary.totalBouncedFees)}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">تقدم السداد</span>
            <span className="text-sm text-gray-600">
              {paymentSummary.paidCount} من {installments.length} أقساط
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${paymentSummary.paymentProgress}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-600 mt-1">
            <span>{paymentSummary.paymentProgress.toFixed(1)}%</span>
            <span>{paymentSummary.overdueCount} متأخر</span>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="installments" className="space-y-4">
        <TabsList>
          <TabsTrigger value="installments">الأقساط</TabsTrigger>
          <TabsTrigger value="payments">المدفوعات</TabsTrigger>
          <TabsTrigger value="reports">التقارير</TabsTrigger>
        </TabsList>

        {/* Installments Tab */}
        <TabsContent value="installments" className="space-y-4">
          {/* Search and Filter */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الأقساط..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="تصفية الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأقساط</SelectItem>
                  <SelectItem value="paid">مدفوع</SelectItem>
                  <SelectItem value="unpaid">غير مدفوع</SelectItem>
                  <SelectItem value="overdue">متأخر</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Dialog open={paymentDialogOpen} onOpenChange={setPaymentDialogOpen}>
              <DialogTrigger asChild>
                <Button className="gap-2">
                  <Plus className="h-4 w-4" />
                  تسجيل دفعة جديدة
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>تسجيل دفعة جديدة</DialogTitle>
                </DialogHeader>
                
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="paymentNumber">رقم الدفعة</Label>
                      <Input
                        id="paymentNumber"
                        type="number"
                        {...form.register("paymentNumber", { valueAsNumber: true })}
                      />
                    </div>

                    <div>
                      <Label htmlFor="dueDate">تاريخ الاستحقاق</Label>
                      <Input
                        id="dueDate"
                        type="date"
                        {...form.register("dueDate")}
                      />
                    </div>

                    <div>
                      <Label htmlFor="amount">المبلغ الأساسي</Label>
                      <Input
                        id="amount"
                        type="number"
                        step="0.01"
                        {...form.register("amount", { valueAsNumber: true })}
                      />
                    </div>

                    <div>
                      <Label htmlFor="lateFee">غرامة التأخير</Label>
                      <Input
                        id="lateFee"
                        type="number"
                        step="0.01"
                        {...form.register("lateFee", { valueAsNumber: true })}
                      />
                    </div>

                    <div>
                      <Label htmlFor="bouncedCheckFee">غرامة الشيك المرتد</Label>
                      <Input
                        id="bouncedCheckFee"
                        type="number"
                        step="0.01"
                        {...form.register("bouncedCheckFee", { valueAsNumber: true })}
                      />
                    </div>

                    <div>
                      <Label htmlFor="totalAmount">إجمالي المبلغ</Label>
                      <Input
                        id="totalAmount"
                        type="number"
                        step="0.01"
                        {...form.register("totalAmount", { valueAsNumber: true })}
                        readOnly
                        className="bg-gray-50"
                      />
                    </div>

                    <div>
                      <Label htmlFor="paymentMethod">طريقة السداد</Label>
                      <Select
                        value={form.watch("paymentMethod") || ""}
                        onValueChange={(value) => form.setValue("paymentMethod", value as any)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="اختر طريقة السداد" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="نقدي">نقدي</SelectItem>
                          <SelectItem value="شيك">شيك</SelectItem>
                          <SelectItem value="تحويل بنكي">تحويل بنكي</SelectItem>
                          <SelectItem value="بطاقة ائتمان">بطاقة ائتمان</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {form.watch("paymentMethod") === "شيك" && (
                      <>
                        <div>
                          <Label htmlFor="checkNumber">رقم الشيك</Label>
                          <Input
                            id="checkNumber"
                            {...form.register("checkNumber")}
                          />
                        </div>
                        <div>
                          <Label htmlFor="bankName">اسم البنك</Label>
                          <Input
                            id="bankName"
                            {...form.register("bankName")}
                          />
                        </div>
                      </>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="notes">ملاحظات</Label>
                    <Textarea
                      id="notes"
                      {...form.register("notes")}
                      rows={3}
                    />
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setPaymentDialogOpen(false)}
                    >
                      إلغاء
                    </Button>
                    <Button
                      type="submit"
                      disabled={createPaymentMutation.isPending}
                    >
                      {createPaymentMutation.isPending ? "جاري الحفظ..." : "حفظ الدفعة"}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {/* Installments Table */}
          <Card>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>رقم القسط</TableHead>
                      <TableHead>تاريخ الاستحقاق</TableHead>
                      <TableHead>المبلغ</TableHead>
                      <TableHead>المدفوع</TableHead>
                      <TableHead>المتبقي</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredInstallments.map((installment) => {
                      const status = getInstallmentStatus(installment);
                      
                      return (
                        <TableRow key={installment.installmentId}>
                          <TableCell className="font-medium">
                            {installment.installmentNumber}
                          </TableCell>
                          <TableCell>
                            {formatDate(installment.paymentDueDate)}
                          </TableCell>
                          <TableCell>
                            {formatCurrency(installment.installmentAmount)}
                          </TableCell>
                          <TableCell>
                            {formatCurrency(installment.paidAmount)}
                          </TableCell>
                          <TableCell>
                            {formatCurrency(installment.remainingAmount)}
                          </TableCell>
                          <TableCell>
                            <Badge className={status.color}>
                              {status.label}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {!installment.isPaid && (
                              <Button
                                size="sm"
                                onClick={() => handleQuickPayment(installment)}
                                className="gap-1"
                              >
                                <CreditCard className="h-3 w-3" />
                                دفع
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payments Tab */}
        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>سجل المدفوعات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>رقم الدفعة</TableHead>
                      <TableHead>تاريخ الدفع</TableHead>
                      <TableHead>المبلغ</TableHead>
                      <TableHead>طريقة السداد</TableHead>
                      <TableHead>الغرامات</TableHead>
                      <TableHead>الإجمالي</TableHead>
                      <TableHead>الحالة</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {payments.map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell className="font-medium">
                          {payment.paymentNumber}
                        </TableCell>
                        <TableCell>
                          {payment.paidDate ? formatDate(payment.paidDate) : '-'}
                        </TableCell>
                        <TableCell>
                          {formatCurrency(payment.amount)}
                        </TableCell>
                        <TableCell>
                          {payment.paymentMethod || '-'}
                        </TableCell>
                        <TableCell>
                          {formatCurrency((payment.lateFee || 0) + (payment.bouncedCheckFee || 0))}
                        </TableCell>
                        <TableCell>
                          {formatCurrency(payment.totalAmount)}
                        </TableCell>
                        <TableCell>
                          <Badge className={payment.isPaid ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                            {payment.isPaid ? 'مدفوع' : 'معلق'}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Reports Tab */}
        <TabsContent value="reports" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>تقرير المدفوعات الشهرية</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full gap-2">
                  <Download className="h-4 w-4" />
                  تصدير التقرير
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>تقرير الأقساط المتأخرة</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full gap-2">
                  <Receipt className="h-4 w-4" />
                  عرض التقرير
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
