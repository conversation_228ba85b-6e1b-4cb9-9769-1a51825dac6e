import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useLanguage } from "@/hooks/use-language";
import { useCurrency } from "@/hooks/use-currency";
import { useDateFormat } from "@/hooks/use-date-format";
import { FileText, X, Download, Printer, User, Calculator, Calendar, TrendingUp } from "lucide-react";

interface ContractPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  contractData: any;
  calculationResults?: any;
}

export default function ContractPreviewModal({
  isOpen,
  onClose,
  contractData,
  calculationResults
}: ContractPreviewModalProps) {
  const { language, isRTL } = useLanguage();
  const { formatCurrency } = useCurrency();
  const { formatDate } = useDateFormat();

  if (!contractData) return null;

  // Debug: Log contract data to see what we're receiving
  console.log('🔍 Contract Preview - contractData:', contractData);
  console.log('🔍 Contract Preview - calculationResults:', calculationResults);

  // Generate installments from yearly amounts (same as financial summary)
  const generateInstallmentsFromYearlyAmounts = () => {
    if (!calculationResults?.yearlyAmounts) return [];

    const installments = [];
    let installmentNumber = 1;

    calculationResults.yearlyAmounts.forEach((yearData, yearIndex) => {
      yearData.products?.forEach((product) => {
        for (let i = 0; i < product.installmentsPerYear; i++) {
          // Calculate dates for this installment
          const yearStartDate = new Date(contractData.products?.[0]?.activationDate || new Date());
          yearStartDate.setFullYear(yearStartDate.getFullYear() + yearIndex);

          const installmentDate = new Date(yearStartDate);
          if (product.billingType === 'شهري') {
            installmentDate.setMonth(installmentDate.getMonth() + i);
          } else if (product.billingType === 'ربع سنوي') {
            installmentDate.setMonth(installmentDate.getMonth() + (i * 3));
          } else if (product.billingType === 'نصف سنوي') {
            installmentDate.setMonth(installmentDate.getMonth() + (i * 6));
          }

          const periodStart = new Date(installmentDate);
          const periodEnd = new Date(installmentDate);
          if (product.billingType === 'شهري') {
            periodEnd.setMonth(periodEnd.getMonth() + 1);
          } else if (product.billingType === 'ربع سنوي') {
            periodEnd.setMonth(periodEnd.getMonth() + 3);
          } else if (product.billingType === 'نصف سنوي') {
            periodEnd.setMonth(periodEnd.getMonth() + 6);
          } else {
            periodEnd.setFullYear(periodEnd.getFullYear() + 1);
          }
          periodEnd.setDate(periodEnd.getDate() - 1);

          const dueDate = new Date(installmentDate);
          dueDate.setDate(dueDate.getDate() + 30); // 30 days to pay

          installments.push({
            number: installmentNumber++,
            amount: product.installmentAmount,
            periodStart: installmentDate,
            periodEnd: periodEnd,
            dueDate: dueDate,
            year: yearData.year,
            productLabel: product.productLabel,
            billingType: product.billingType
          });
        }
      });
    });

    return installments.slice(0, 8); // Show first 8 installments
  };

  const installments = generateInstallmentsFromYearlyAmounts();

  // Calculate contract dates from products
  const getContractDates = () => {
    if (!contractData.products || contractData.products.length === 0) {
      return { startDate: new Date(), endDate: new Date() };
    }

    const dates = contractData.products
      .filter(p => p.activationDate && p.endDate)
      .map(p => ({
        start: new Date(p.activationDate),
        end: new Date(p.endDate)
      }));

    if (dates.length === 0) {
      return { startDate: new Date(), endDate: new Date() };
    }

    const startDate = new Date(Math.min(...dates.map(d => d.start.getTime())));
    const endDate = new Date(Math.max(...dates.map(d => d.end.getTime())));

    return { startDate, endDate };
  };

  const { startDate: contractStartDate, endDate: contractEndDate } = getContractDates();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {language === 'ar' ? 'معاينة العقد قبل الحفظ' : 'Contract Preview'}
          </DialogTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'تحميل' : 'Download'}
            </Button>
            <Button variant="outline" size="sm">
              <Printer className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'طباعة' : 'Print'}
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-8 p-6 bg-white print:p-4 print:space-y-6" dir="rtl">
          {/* Contract Header */}
          <div className="text-center border-b-2 border-blue-600 pb-6">
            <div className="bg-gradient-to-r from-blue-50 to-green-50 p-6 rounded-lg">
              <h1 className="text-2xl font-bold text-gray-800 mb-2">معاينة العقد</h1>
              <div className="text-lg font-semibold text-blue-600">
                رقم العقد: {contractData.contractNumber}
              </div>
              <div className="text-sm text-gray-600 mt-2">
                تاريخ الإنشاء: {new Date().toLocaleDateString('ar-EG')}
              </div>
            </div>
          </div>

          {/* المقطع الأول: البيانات الأساسية */}
          <Card className="border-2 border-blue-200">
            <CardHeader className="bg-blue-50">
              <CardTitle className="text-center flex items-center justify-center gap-2 text-blue-800">
                <User className="h-6 w-6" />
البيانات الأساسية
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6" dir="rtl">
              {/* بيانات العميل */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2 text-right">
                  بيانات العميل
                </h3>
                {(!contractData.clientId || contractData.clientId === 0) && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                    <div className="flex items-center gap-2 text-yellow-800">
                      <span className="text-sm font-medium">⚠️ لم يتم اختيار عميل للعقد</span>
                    </div>
                  </div>
                )}
                <div className="grid grid-cols-2 gap-4 text-sm" dir="rtl">
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-gray-600 min-w-[100px]">اسم العميل:</span>
                    <span className={`font-semibold ${!contractData.clientName ? 'text-red-500' : ''}`}>
                      {contractData.clientName || 'غير محدد'}
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-gray-600 min-w-[100px]">رقم العميل:</span>
                    <span className="font-semibold">
                      {contractData.clientNumber || 'غير محدد'}
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-gray-600 min-w-[100px]">نوع العميل:</span>
                    <span className="font-semibold">{contractData.clientType || 'أفراد'}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-gray-600 min-w-[100px]">تصنيف العميل:</span>
                    <span className="font-semibold">{contractData.clientClassification || 'عادي'}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-gray-600 min-w-[100px]">عنوان العميل:</span>
                    <span className="font-semibold">{contractData.clientAddress || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-gray-600 min-w-[100px]">هاتف العميل:</span>
                    <span className="font-semibold">{contractData.clientPhoneWhatsapp || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center gap-3 col-span-2">
                    <span className="font-medium text-gray-600 min-w-[100px]">الضامن المالي:</span>
                    <span className="font-semibold">
                      {contractData.financialGuarantorId && contractData.financialGuarantorId > 0
                        ? (contractData.financialGuarantorName || 'يوجد ضامن')
                        : 'لا يوجد ضامن'}
                    </span>
                  </div>
                </div>
              </div>

              {/* بيانات العقد الأساسية */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2 text-right">
                  بيانات العقد الأساسية
                </h3>
                <div className="grid grid-cols-2 gap-4 text-sm" dir="rtl">
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-gray-600 min-w-[120px]">رقم العقد:</span>
                    <span className="font-semibold text-blue-600">{contractData.contractNumber}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-gray-600 min-w-[120px]">موضوع العقد:</span>
                    <span className="font-semibold">{contractData.contractSubject || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-gray-600 min-w-[120px]">مدة العقد:</span>
                    <span className="font-semibold">{calculationResults?.summary?.totalYears || contractData.contractDurationYears || 'غير محدد'} سنة</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-gray-600 min-w-[120px]">حالة العقد:</span>
                    <Badge variant={contractData.contractStatus === 'نشط' ? 'default' : 'secondary'}>
                      {contractData.contractStatus || 'نشط'}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-gray-600 min-w-[120px]">مالك الأصل:</span>
                    <span className="font-semibold">{contractData.assetOwner || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-gray-600 min-w-[120px]">الإدارة المسئولة:</span>
                    <span className="font-semibold">{contractData.responsibleDepartment || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center gap-3 col-span-2">
                    <span className="font-medium text-gray-600 min-w-[120px]">المنطقة:</span>
                    <span className="font-semibold">{contractData.region || 'غير محدد'}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* المقطع الثاني: مكونات العقد */}
          {contractData.products && contractData.products.length > 0 && (
            <Card className="border-2 border-green-200">
              <CardHeader className="bg-green-50">
                <CardTitle className="text-center flex items-center justify-center gap-2 text-green-800">
                  <FileText className="h-6 w-6" />
مكونات العقد
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6" dir="rtl">
                <div className="space-y-4">
                  {contractData.products.map((product: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4 bg-gray-50">
                      <div className="grid grid-cols-2 gap-4 text-sm" dir="rtl">
                        <div className="flex items-center gap-3">
                          <span className="font-medium text-gray-600 min-w-[120px]">اسم المنتج:</span>
                          <span className="font-semibold">{product.productLabel || `منتج ${index + 1}`}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="font-medium text-gray-600 min-w-[120px]">المساحة:</span>
                          <span className="font-semibold">{product.area} متر مربع</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="font-medium text-gray-600 min-w-[120px]">سعر المتر:</span>
                          <span className="font-semibold">{formatCurrency(product.meterPrice)}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="font-medium text-gray-600 min-w-[120px]">نوع الفوترة:</span>
                          <span className="font-semibold">{product.billingType}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="font-medium text-gray-600 min-w-[120px]">تاريخ التفعيل:</span>
                          <span className="font-semibold">{formatDate(product.activationDate)}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="font-medium text-gray-600 min-w-[120px]">تاريخ الانتهاء:</span>
                          <span className="font-semibold">{formatDate(product.endDate)}</span>
                        </div>
                        <div className="flex items-center gap-3 col-span-2">
                          <span className="font-medium text-gray-600 min-w-[120px]">نسبة الزيادة السنوية:</span>
                          <span className="font-semibold text-orange-600">
                            {product.hasAnnualIncrease && product.increaseValue > 0
                              ? `${product.increaseValue}% من السنة الثانية`
                              : 'لا توجد زيادة'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* المقطع الثالث: ملخص الالتزامات المالية */}
          <Card className="border-2 border-purple-200">
            <CardHeader className="bg-purple-50">
              <CardTitle className="text-center flex items-center justify-center gap-2 text-purple-800">
                <Calculator className="h-6 w-6" />
ملخص الالتزامات المالية
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6" dir="rtl">
              <div className="grid grid-cols-2 gap-6">
                {/* الالتزامات الأساسية */}
                <div className="space-y-4">
                  <div className="bg-blue-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {formatCurrency(calculationResults?.totalValue || contractData.totalContractValue || 0)}
                    </div>
                    <div className="text-sm text-blue-800 mt-1">إجمالي قيمة العقد</div>
                  </div>

                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="text-center">
                      <div className="text-xl font-bold text-green-600">
                        {formatCurrency(calculationResults?.insuranceAmount || 0)}
                      </div>
                      <div className="text-sm text-green-800 mt-1">إجمالي التأمين</div>
                    </div>
                    <div className="text-center mt-2 text-xs text-gray-600">
                      نسبة التأمين: {contractData.finalInsuranceRate || 0}%
                    </div>
                  </div>
                </div>

                {/* الدفعات والغرامات */}
                <div className="space-y-4">
                  <div className="bg-orange-50 p-4 rounded-lg">
                    <div className="text-center">
                      <div className="text-xl font-bold text-orange-600">
                        {formatCurrency(calculationResults?.advancePaymentAmount || contractData.advancePaymentAmount || 0)}
                      </div>
                      <div className="text-sm text-orange-800 mt-1">إجمالي الدفعة المقدمة</div>
                    </div>
                    <div className="text-center mt-2 text-xs text-gray-600">
                      عدد الأشهر: {contractData.advancePaymentMonths || 0} شهر
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="bg-red-50 p-3 rounded text-center">
                      <div className="font-bold text-red-600">
                        {contractData.lateFeeValue || 0}%
                      </div>
                      <div className="text-xs text-red-800">نسبة غرامة التأخير</div>
                    </div>
                    <div className="bg-red-50 p-3 rounded text-center">
                      <div className="font-bold text-red-600">
                        {formatCurrency(contractData.bouncedCheckFeeValue || 0)}
                      </div>
                      <div className="text-xs text-red-800">قيمة غرامة الارتداد</div>
                    </div>
                  </div>
                </div>
              </div>

            </CardContent>
          </Card>

          {/* المقطع الرابع: ملخص الإجماليات السنوية */}
          {calculationResults?.yearlyAmounts && calculationResults.yearlyAmounts.length > 0 && (
            <Card className="border-2 border-indigo-200">
              <CardHeader className="bg-indigo-50">
                <CardTitle className="text-center flex items-center justify-center gap-2 text-indigo-800">
                  <Calendar className="h-6 w-6" />
ملخص الإجماليات السنوية
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6" dir="rtl">
                <div className="grid gap-4">
                  {calculationResults.yearlyAmounts.map((yearData: any, index: number) => (
                    <div key={index} className="bg-gradient-to-r from-indigo-50 to-blue-50 p-4 rounded-lg border">
                      <div className="grid grid-cols-4 gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-bold text-indigo-600 text-lg">السنة {yearData.year}</div>
                          <div className="text-xs text-gray-600">
                            {new Date().getFullYear() + index} م
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-gray-800">
                            {formatCurrency(yearData.totalAmount)}
                          </div>
                          <div className="text-xs text-gray-600">إجمالي السنة</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-green-600">
                            {yearData.increaseRate ? `${yearData.increaseRate}%` : 'لا توجد زيادة'}
                          </div>
                          <div className="text-xs text-gray-600">نسبة الزيادة</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-blue-600">
                            {yearData.installmentsCount || 12}
                          </div>
                          <div className="text-xs text-gray-600">عدد الأقساط</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* المقطع السادس: الإيرادات السنوية بالسنوات الميلادية */}
          {(calculationResults?.calendarRevenues && calculationResults.calendarRevenues.length > 0) ? (
            <Card className="border-2 border-emerald-200">
              <CardHeader className="bg-emerald-50">
                <CardTitle className="text-center flex items-center justify-center gap-2 text-emerald-800">
                  <TrendingUp className="h-6 w-6" />
الإيرادات السنوية بالسنوات الميلادية
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6" dir="rtl">
                <div className="grid grid-cols-1 gap-4">
                  {calculationResults.calendarRevenues.map((yearData: any, index: number) => (
                    <div key={index} className="flex justify-between items-center p-4 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border">
                      <div className="text-right">
                        <div className="text-2xl font-bold text-emerald-600">
                          {formatCurrency(yearData.totalRevenue)}
                        </div>
                        <div className="text-sm text-gray-600">إجمالي الإيراد</div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="bg-emerald-100 p-3 rounded-full">
                          <span className="font-bold text-emerald-700 text-xl">
                            {yearData.calendarYear}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* إجمالي الإيرادات */}
                <div className="mt-6 p-4 bg-gradient-to-r from-emerald-100 to-green-100 rounded-lg border-2 border-emerald-300">
                  <div className="flex justify-between items-center">
                    <div className="text-3xl font-bold text-emerald-700">
                      {formatCurrency(calculationResults.calendarRevenues.reduce((sum: number, year: any) => sum + year.totalRevenue, 0))}
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-emerald-800">إجمالي الإيرادات</div>
                      <div className="text-sm text-emerald-600">جميع السنوات الميلادية</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (calculationResults?.yearlyAmounts && calculationResults.yearlyAmounts.length > 0) && (
            <Card className="border-2 border-emerald-200">
              <CardHeader className="bg-emerald-50">
                <CardTitle className="text-center flex items-center justify-center gap-2 text-emerald-800">
                  <TrendingUp className="h-6 w-6" />
الإيرادات السنوية
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6" dir="rtl">
                <div className="grid grid-cols-1 gap-4">
                  {calculationResults.yearlyAmounts.map((yearData: any, index: number) => {
                    // حساب السنة الميلادية بناءً على تاريخ بداية العقد
                    const contractStartYear = contractData.products?.[0]?.activationDate
                      ? new Date(contractData.products[0].activationDate).getFullYear()
                      : new Date().getFullYear();
                    const calendarYear = contractStartYear + index;

                    return (
                      <div key={index} className="flex justify-between items-center p-4 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border">
                        <div className="text-right">
                          <div className="text-2xl font-bold text-emerald-600">
                            {formatCurrency(yearData.totalAmount)}
                          </div>
                          <div className="text-sm text-gray-600">إجمالي الإيراد</div>
                        </div>
                        <div className="flex items-center gap-4">
                          <div>
                            <div className="font-semibold text-gray-800">السنة الميلادية</div>
                            <div className="text-sm text-gray-600">السنة {yearData.year} من العقد</div>
                          </div>
                          <div className="bg-emerald-100 p-3 rounded-full">
                            <span className="font-bold text-emerald-700">
                              {calendarYear}
                            </span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* إجمالي الإيرادات */}
                <div className="mt-6 p-4 bg-gradient-to-r from-emerald-100 to-green-100 rounded-lg border-2 border-emerald-300">
                  <div className="flex justify-between items-center">
                    <div className="text-3xl font-bold text-emerald-700">
                      {formatCurrency(calculationResults.totalValue || 0)}
                    </div>
                    <div className="font-bold text-lg text-emerald-800">
                      إجمالي الإيرادات المتوقعة
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* المقطع الخامس: جدول الأقساط والاستحقاقات */}
          <Card className="border-2 border-amber-200">
            <CardHeader className="bg-amber-50">
              <CardTitle className="text-center flex items-center justify-center gap-2 text-amber-800">
                <FileText className="h-6 w-6" />
جدول الأقساط والاستحقاقات
              </CardTitle>
            </CardHeader>
            <CardContent dir="rtl">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b bg-gray-50">
                      <th className="p-2 text-center text-xs">رقم القسط</th>
                      <th className="p-2 text-center text-xs">سنة العقد</th>
                      <th className="p-2 text-center text-xs">المنتج</th>
                      <th className="p-2 text-center text-xs">نوع الفوترة</th>
                      <th className="p-2 text-center text-xs">مبلغ القسط</th>
                      <th className="p-2 text-center text-xs">تاريخ الاستحقاق</th>
                    </tr>
                  </thead>
                  <tbody>
                    {installments.map((installment, index) => (
                      <tr key={index} className="border-b hover:bg-gray-50">
                        <td className="p-2 text-center font-medium text-sm">{installment.number}</td>
                        <td className="p-2 text-center text-sm">
                          <Badge variant="outline" className="bg-blue-50 text-blue-700 text-xs">
                            السنة {installment.year}
                          </Badge>
                        </td>
                        <td className="p-2 text-center text-sm">{installment.productLabel}</td>
                        <td className="p-2 text-center text-sm">{installment.billingType}</td>
                        <td className="p-2 text-center text-green-600 font-bold text-sm">
                          {formatCurrency(installment.amount)}
                        </td>
                        <td className="p-2 text-center text-sm">{formatDate(installment.dueDate.toISOString().split('T')[0])}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>




            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
