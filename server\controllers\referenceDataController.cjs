const referenceDataService = require('../services/referenceDataService.cjs');

exports.getAllReferenceLists = async (req, res) => {
  try {
    const lists = await referenceDataService.getAllReferenceLists();
    res.json(lists);
  } catch (error) {
    res.status(500).json({ error: 'فشل في جلب القوائم المرجعية', details: error.message });
  }
};

exports.getReferenceDataByModuleAndList = async (req, res) => {
  try {
    const { module, listName } = req.params;
    const data = await referenceDataService.getReferenceDataByModuleAndList(module, listName);
    res.json(data);
  } catch (error) {
    res.status(500).json({ error: 'فشل في جلب البيانات المرجعية', details: error.message });
  }
};

exports.createReferenceList = async (req, res) => {
  try {
    const newList = await referenceDataService.createReferenceList(req.body);
    res.status(201).json(newList);
  } catch (error) {
    res.status(400).json({ error: 'فشل في إنشاء القائمة المرجعية', details: error.message });
  }
};

exports.updateReferenceListItem = async (req, res) => {
  try {
    const updatedItem = await referenceDataService.updateReferenceListItem(req.params.id, req.body);
    res.json(updatedItem);
  } catch (error) {
    res.status(400).json({ error: 'فشل في تحديث العنصر المرجعي', details: error.message });
  }
};

exports.deleteReferenceList = async (req, res) => {
  try {
    await referenceDataService.deleteReferenceList(req.params.module, req.params.listName);
    res.status(204).end();
  } catch (error) {
    res.status(400).json({ error: 'فشل في حذف القائمة المرجعية', details: error.message });
  }
};

exports.deleteReferenceDataItem = async (req, res) => {
  try {
    await referenceDataService.deleteReferenceDataItem(req.params.id);
    res.status(204).end();
  } catch (error) {
    res.status(400).json({ error: 'فشل في حذف العنصر المرجعي', details: error.message });
  }
};
