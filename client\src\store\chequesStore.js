// ===== CHEQUES STORE =====
// Author: Augment Code
// Description: Centralized state management for cheques using Zustand

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import apiMiddleware from '../lib/apiMiddleware';

// Initial state
const initialState = {
  // Cheques data
  cheques: [],
  treasuryCheques: [],
  custodyCheques: [],
  collectionCheques: [],
  collectedCheques: [],
  bouncedCheques: [],
  selectedCheque: null,
  
  // Search and filters
  searchQuery: '',
  statusFilter: '',
  bankFilter: '',
  clientFilter: '',
  dateRangeFilter: { start: null, end: null },
  amountRangeFilter: { min: null, max: null },
  
  // Loading states
  isLoading: false,
  isAdding: false,
  isUpdating: false,
  isDeleting: false,
  isUpdatingStatus: false,
  
  // Error states
  error: null,
  addError: null,
  updateError: null,
  deleteError: null,
  statusError: null,
  
  // Cache and metadata
  lastFetched: null,
  totalCount: 0,
  
  // UI state
  showChequeDetails: false,
  
  // Cheque statistics
  chequeStats: null,
  statsLoading: false,
  statsError: null
};

// Create the cheques store
export const useChequesStore = create()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Actions
        actions: {
          // Fetch all cheques
          async fetchCheques(forceRefresh = false) {
            const state = get();
            
            if (state.isLoading || (!forceRefresh && state.lastFetched && 
                Date.now() - new Date(state.lastFetched).getTime() < 30000)) {
              return state.cheques;
            }

            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const cheques = await apiMiddleware.get('/api/cheques', {
                loadingMessage: 'جاري تحميل الشيكات...',
                showErrorNotification: true
              });

              set((state) => {
                state.cheques = cheques || [];
                state.totalCount = cheques?.length || 0;
                state.isLoading = false;
                state.lastFetched = new Date().toISOString();
                state.error = null;
              });

              return cheques;
            } catch (error) {
              console.error('Failed to fetch cheques:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحميل الشيكات';
              });

              throw error;
            }
          },

          // Fetch cheques by status
          async fetchChequesByStatus(status) {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const cheques = await apiMiddleware.get(`/api/cheques/status/${status}`, {
                loadingMessage: `جاري تحميل الشيكات ${status}...`,
                showErrorNotification: true
              });

              set((state) => {
                switch (status) {
                  case 'treasury':
                    state.treasuryCheques = cheques || [];
                    break;
                  case 'custody':
                    state.custodyCheques = cheques || [];
                    break;
                  case 'collection':
                    state.collectionCheques = cheques || [];
                    break;
                  case 'collected':
                    state.collectedCheques = cheques || [];
                    break;
                  case 'bounced':
                    state.bouncedCheques = cheques || [];
                    break;
                }
                state.isLoading = false;
                state.error = null;
              });

              return cheques;
            } catch (error) {
              console.error(`Failed to fetch ${status} cheques:`, error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || `فشل في تحميل الشيكات ${status}`;
              });

              throw error;
            }
          },

          // Search cheques
          async searchCheques(query) {
            set((state) => {
              state.searchQuery = query;
              state.isLoading = true;
              state.error = null;
            });

            try {
              const url = query 
                ? `/api/cheques/search?q=${encodeURIComponent(query)}`
                : '/api/cheques';

              const cheques = await apiMiddleware.get(url, {
                loadingMessage: query ? 'جاري البحث...' : 'جاري تحميل الشيكات...',
                showErrorNotification: true
              });

              set((state) => {
                state.cheques = cheques || [];
                state.totalCount = cheques?.length || 0;
                state.isLoading = false;
                state.error = null;
              });

              return cheques;
            } catch (error) {
              console.error('Failed to search cheques:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في البحث عن الشيكات';
              });

              throw error;
            }
          },

          // Add new cheque
          async addCheque(chequeData) {
            set((state) => {
              state.isAdding = true;
              state.addError = null;
            });

            try {
              const newCheque = await apiMiddleware.post('/api/cheques', chequeData, {
                loadingMessage: 'جاري إضافة الشيك...',
                successMessage: 'تم إضافة الشيك بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.cheques.unshift(newCheque);
                state.totalCount += 1;
                
                // Add to appropriate status array
                switch (newCheque.chequeStatus) {
                  case 'treasury':
                    state.treasuryCheques.unshift(newCheque);
                    break;
                  case 'custody':
                    state.custodyCheques.unshift(newCheque);
                    break;
                  case 'collection':
                    state.collectionCheques.unshift(newCheque);
                    break;
                  case 'collected':
                    state.collectedCheques.unshift(newCheque);
                    break;
                  case 'bounced':
                    state.bouncedCheques.unshift(newCheque);
                    break;
                }
                
                state.isAdding = false;
                state.addError = null;
              });

              return { success: true, data: newCheque };
            } catch (error) {
              console.error('Failed to add cheque:', error);
              
              set((state) => {
                state.isAdding = false;
                state.addError = error.message || 'فشل في إضافة الشيك';
              });

              return { success: false, error: error.message };
            }
          },

          // Update cheque
          async updateCheque(chequeId, updates) {
            set((state) => {
              state.isUpdating = true;
              state.updateError = null;
            });

            try {
              const updatedCheque = await apiMiddleware.put(`/api/cheques/${chequeId}`, updates, {
                loadingMessage: 'جاري تحديث الشيك...',
                successMessage: 'تم تحديث الشيك بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                // Update in main cheques array
                const index = state.cheques.findIndex(cheque => cheque.id === chequeId);
                if (index !== -1) {
                  state.cheques[index] = updatedCheque;
                }
                
                // Update in status-specific arrays
                const updateInArray = (array) => {
                  const idx = array.findIndex(cheque => cheque.id === chequeId);
                  if (idx !== -1) {
                    array[idx] = updatedCheque;
                  }
                };
                
                updateInArray(state.treasuryCheques);
                updateInArray(state.custodyCheques);
                updateInArray(state.collectionCheques);
                updateInArray(state.collectedCheques);
                updateInArray(state.bouncedCheques);
                
                if (state.selectedCheque?.id === chequeId) {
                  state.selectedCheque = updatedCheque;
                }
                
                state.isUpdating = false;
                state.updateError = null;
              });

              return { success: true, data: updatedCheque };
            } catch (error) {
              console.error('Failed to update cheque:', error);
              
              set((state) => {
                state.isUpdating = false;
                state.updateError = error.message || 'فشل في تحديث الشيك';
              });

              return { success: false, error: error.message };
            }
          },

          // Update cheque status
          async updateChequeStatus(chequeId, newStatus, statusData = {}) {
            set((state) => {
              state.isUpdatingStatus = true;
              state.statusError = null;
            });

            try {
              const updatedCheque = await apiMiddleware.put(`/api/cheques/${chequeId}/status`, {
                status: newStatus,
                ...statusData
              }, {
                loadingMessage: 'جاري تحديث حالة الشيك...',
                successMessage: 'تم تحديث حالة الشيك بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                // Update in main cheques array
                const index = state.cheques.findIndex(cheque => cheque.id === chequeId);
                if (index !== -1) {
                  state.cheques[index] = updatedCheque;
                }
                
                // Remove from old status array and add to new one
                const removeFromArray = (array) => {
                  return array.filter(cheque => cheque.id !== chequeId);
                };
                
                state.treasuryCheques = removeFromArray(state.treasuryCheques);
                state.custodyCheques = removeFromArray(state.custodyCheques);
                state.collectionCheques = removeFromArray(state.collectionCheques);
                state.collectedCheques = removeFromArray(state.collectedCheques);
                state.bouncedCheques = removeFromArray(state.bouncedCheques);
                
                // Add to new status array
                switch (newStatus) {
                  case 'treasury':
                    state.treasuryCheques.unshift(updatedCheque);
                    break;
                  case 'custody':
                    state.custodyCheques.unshift(updatedCheque);
                    break;
                  case 'collection':
                    state.collectionCheques.unshift(updatedCheque);
                    break;
                  case 'collected':
                    state.collectedCheques.unshift(updatedCheque);
                    break;
                  case 'bounced':
                    state.bouncedCheques.unshift(updatedCheque);
                    break;
                }
                
                if (state.selectedCheque?.id === chequeId) {
                  state.selectedCheque = updatedCheque;
                }
                
                state.isUpdatingStatus = false;
                state.statusError = null;
              });

              return { success: true, data: updatedCheque };
            } catch (error) {
              console.error('Failed to update cheque status:', error);
              
              set((state) => {
                state.isUpdatingStatus = false;
                state.statusError = error.message || 'فشل في تحديث حالة الشيك';
              });

              return { success: false, error: error.message };
            }
          },

          // Delete cheque
          async deleteCheque(chequeId) {
            set((state) => {
              state.isDeleting = true;
              state.deleteError = null;
            });

            try {
              await apiMiddleware.delete(`/api/cheques/${chequeId}`, {
                loadingMessage: 'جاري حذف الشيك...',
                successMessage: 'تم حذف الشيك بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                // Remove from all arrays
                state.cheques = state.cheques.filter(cheque => cheque.id !== chequeId);
                state.treasuryCheques = state.treasuryCheques.filter(cheque => cheque.id !== chequeId);
                state.custodyCheques = state.custodyCheques.filter(cheque => cheque.id !== chequeId);
                state.collectionCheques = state.collectionCheques.filter(cheque => cheque.id !== chequeId);
                state.collectedCheques = state.collectedCheques.filter(cheque => cheque.id !== chequeId);
                state.bouncedCheques = state.bouncedCheques.filter(cheque => cheque.id !== chequeId);
                
                state.totalCount -= 1;
                
                if (state.selectedCheque?.id === chequeId) {
                  state.selectedCheque = null;
                  state.showChequeDetails = false;
                }
                
                state.isDeleting = false;
                state.deleteError = null;
              });

              return { success: true };
            } catch (error) {
              console.error('Failed to delete cheque:', error);
              
              set((state) => {
                state.isDeleting = false;
                state.deleteError = error.message || 'فشل في حذف الشيك';
              });

              return { success: false, error: error.message };
            }
          },

          // Fetch cheque statistics
          async fetchChequeStats() {
            set((state) => {
              state.statsLoading = true;
              state.statsError = null;
            });

            try {
              const stats = await apiMiddleware.get('/api/cheques/statistics', {
                showErrorNotification: false
              });

              set((state) => {
                state.chequeStats = stats;
                state.statsLoading = false;
                state.statsError = null;
              });

              return stats;
            } catch (error) {
              console.error('Failed to fetch cheque stats:', error);
              
              set((state) => {
                state.chequeStats = null;
                state.statsLoading = false;
                state.statsError = error.message;
              });

              return null;
            }
          },

          // Set selected cheque
          setSelectedCheque(cheque) {
            set((state) => {
              state.selectedCheque = cheque;
              state.showChequeDetails = !!cheque;
            });
          },

          // Set filters
          setSearchQuery(query) {
            set((state) => {
              state.searchQuery = query;
            });
          },

          setStatusFilter(status) {
            set((state) => {
              state.statusFilter = status;
            });
          },

          setBankFilter(bankId) {
            set((state) => {
              state.bankFilter = bankId;
            });
          },

          setClientFilter(clientId) {
            set((state) => {
              state.clientFilter = clientId;
            });
          },

          setDateRangeFilter(dateRange) {
            set((state) => {
              state.dateRangeFilter = dateRange;
            });
          },

          setAmountRangeFilter(amountRange) {
            set((state) => {
              state.amountRangeFilter = amountRange;
            });
          },

          // Clear errors
          clearErrors() {
            set((state) => {
              state.error = null;
              state.addError = null;
              state.updateError = null;
              state.deleteError = null;
              state.statusError = null;
              state.statsError = null;
            });
          },

          // Get filtered cheques
          getFilteredCheques() {
            const state = get();
            const { 
              cheques, 
              searchQuery, 
              statusFilter, 
              bankFilter, 
              clientFilter,
              dateRangeFilter,
              amountRangeFilter 
            } = state;

            return cheques.filter(cheque => {
              // Search filter
              const matchesSearch = !searchQuery ||
                cheque.chequeNumber?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                cheque.clientName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                cheque.bankName?.toLowerCase().includes(searchQuery.toLowerCase());

              // Status filter
              const matchesStatus = !statusFilter || statusFilter === "all" ||
                cheque.chequeStatus === statusFilter;

              // Bank filter
              const matchesBank = !bankFilter || bankFilter === "all" ||
                cheque.bankId === bankFilter;

              // Client filter
              const matchesClient = !clientFilter || clientFilter === "all" ||
                cheque.clientId === clientFilter;

              // Date range filter
              const matchesDateRange = !dateRangeFilter.start || !dateRangeFilter.end ||
                (new Date(cheque.chequeDate) >= new Date(dateRangeFilter.start) &&
                 new Date(cheque.chequeDate) <= new Date(dateRangeFilter.end));

              // Amount range filter
              const matchesAmountRange = (!amountRangeFilter.min || cheque.amount >= amountRangeFilter.min) &&
                (!amountRangeFilter.max || cheque.amount <= amountRangeFilter.max);

              return matchesSearch && matchesStatus && matchesBank && matchesClient && 
                     matchesDateRange && matchesAmountRange;
            });
          },

          // Get cheque by ID
          getChequeById(chequeId) {
            const state = get();
            return state.cheques.find(cheque => cheque.id === chequeId);
          },

          // Refresh data
          async refresh() {
            return get().actions.fetchCheques(true);
          }
        }
      })),
      {
        name: 'cheques-store',
        partialize: (state) => ({
          cheques: state.cheques,
          lastFetched: state.lastFetched,
          searchQuery: state.searchQuery,
          statusFilter: state.statusFilter,
          bankFilter: state.bankFilter,
          clientFilter: state.clientFilter
        })
      }
    ),
    {
      name: 'cheques-store'
    }
  )
);

// Selectors
export const useCheques = () => useChequesStore((state) => state.cheques);
export const useTreasuryCheques = () => useChequesStore((state) => state.treasuryCheques);
export const useCustodyCheques = () => useChequesStore((state) => state.custodyCheques);
export const useCollectionCheques = () => useChequesStore((state) => state.collectionCheques);
export const useCollectedCheques = () => useChequesStore((state) => state.collectedCheques);
export const useBouncedCheques = () => useChequesStore((state) => state.bouncedCheques);
export const useChequesLoading = () => useChequesStore((state) => state.isLoading);
export const useChequesError = () => useChequesStore((state) => state.error);
export const useSelectedCheque = () => useChequesStore((state) => state.selectedCheque);
export const useChequeStats = () => useChequesStore((state) => state.chequeStats);
export const useChequesActions = () => useChequesStore((state) => state.actions);

export const useFilteredCheques = () => useChequesStore((state) => 
  state.actions.getFilteredCheques()
);

export const useChequesStatus = () => useChequesStore((state) => ({
  isLoading: state.isLoading,
  isAdding: state.isAdding,
  isUpdating: state.isUpdating,
  isDeleting: state.isDeleting,
  isUpdatingStatus: state.isUpdatingStatus,
  statsLoading: state.statsLoading,
  error: state.error,
  addError: state.addError,
  updateError: state.updateError,
  deleteError: state.deleteError,
  statusError: state.statusError,
  statsError: state.statsError,
  totalCount: state.totalCount,
  lastFetched: state.lastFetched
}));

export default useChequesStore;
