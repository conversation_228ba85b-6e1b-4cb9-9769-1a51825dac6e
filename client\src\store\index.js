// ===== STORE INDEX =====
// Author: Augment Code
// Description: Central export for all Zustand stores

// Export all stores
export { 
  useSettingsStore,
  useSettings,
  useSettingsLoading,
  useSettingsSaving,
  useSettingsError,
  useSettingsSaveError,
  useSettingsActions,
  useHasUnsavedChanges,
  useIsSettingsReady,
  useSettingsStatus
} from './settingsStore';

export {
  useReferenceDataStore,
  useReferenceLists,
  useReferenceData,
  useReferenceLoading,
  useReferenceErrors,
  useReferenceActions,
  useReferenceGlobalError,
  useIsReferenceInitialized,
  useListItems,
  useListLoading,
  useListError,
  useListConfig
} from './referenceDataStore';

export {
  useAppStore,
  useIsLoading,
  useLoadingMessage,
  useNotifications,
  useGlobalError,
  useSidebarCollapsed,
  useTheme,
  useCurrentPage,
  useBreadcrumbs,
  useIsOnline,
  useConnectionError,
  useAppActions,
  useModal,
  useIsModalOpen,
  usePerformanceMetrics
} from './appStore';

export {
  useClientsStore,
  useClients,
  useClientsLoading,
  useClientsError,
  useSelectedClient,
  useShowClientCard,
  useClientContracts,
  useClientsActions,
  useClientsFilters,
  useFilteredClients,
  useClientsStatus
} from './clientsStore';

export {
  useContractsStore,
  useContracts,
  useContractsLoading,
  useContractsError,
  useSelectedContract,
  useContractComponents,
  useContractCalculations,
  useContractsActions,
  useFilteredContracts,
  useContractsStatus
} from './contractsStore';

export {
  usePaymentsStore,
  usePayments,
  useTreasuryPayments,
  useBankPayments,
  useCashReceipts,
  useChequeReceipts,
  usePaymentsLoading,
  usePaymentsError,
  useSelectedPayment,
  usePaymentStats,
  usePaymentsActions,
  useFilteredPayments,
  usePaymentsStatus
} from './paymentsStore';

export {
  useReceivablesStore,
  useReceivables,
  useOverdueReceivables,
  useUpcomingReceivables,
  usePaidReceivables,
  useReceivablesLoading,
  useReceivablesError,
  useSelectedReceivable,
  useReceivablesStats,
  useReceivablesActions,
  useFilteredReceivables,
  useReceivablesStatus
} from './receivablesStore';

export {
  useChequesStore,
  useCheques,
  useTreasuryCheques,
  useCustodyCheques,
  useCollectionCheques,
  useCollectedCheques,
  useBouncedCheques,
  useChequesLoading,
  useChequesError,
  useSelectedCheque,
  useChequeStats,
  useChequesActions,
  useFilteredCheques,
  useChequesStatus
} from './chequesStore';

export {
  useUsersStore,
  useUsers,
  useCurrentUser,
  useUsersLoading,
  useUsersError,
  useSelectedUser,
  useUserPermissions,
  useAvailableRoles,
  useUsersActions,
  useFilteredUsers,
  useUsersStatus
} from './usersStore';

export {
  useReportsStore,
  useReports,
  useDashboardStats,
  useFinancialReports,
  useContractReports,
  useClientReports,
  usePaymentReports,
  useReportsLoading,
  useReportsError,
  useSelectedReport,
  useReportConfigs,
  useReportsActions,
  useFilteredReports,
  useReportsStatus
} from './reportsStore';

// Store initialization helper
export const initializeAllStores = async () => {
  const { useAppStore } = await import('./appStore');
  const { useSettingsStore } = await import('./settingsStore');
  const { useReferenceDataStore } = await import('./referenceDataStore');
  const { useClientsStore } = await import('./clientsStore');
  const { useContractsStore } = await import('./contractsStore');
  const { usePaymentsStore } = await import('./paymentsStore');
  const { useReceivablesStore } = await import('./receivablesStore');
  const { useChequesStore } = await import('./chequesStore');
  const { useUsersStore } = await import('./usersStore');
  const { useReportsStore } = await import('./reportsStore');

  try {
    // Initialize app store
    useAppStore.getState().actions.initialize();

    // Initialize settings store
    await useSettingsStore.getState().actions.initialize();

    // Initialize reference data store
    await useReferenceDataStore.getState().actions.initialize();

    // Initialize core data stores
    await Promise.allSettled([
      useClientsStore.getState().actions.fetchClients(),
      useContractsStore.getState().actions.fetchContracts(),
      usePaymentsStore.getState().actions.fetchPayments(),
      useReceivablesStore.getState().actions.fetchReceivables(),
      useChequesStore.getState().actions.fetchCheques(),
      useUsersStore.getState().actions.fetchUsers(),
      useReportsStore.getState().actions.fetchDashboardStats()
    ]);

    console.log('✅ All stores initialized successfully');
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to initialize stores:', error);
    return { success: false, error };
  }
};

// Store reset helper
export const resetAllStores = () => {
  // Reset stores to initial state
  localStorage.removeItem('settings-store');
  localStorage.removeItem('reference-data-store');
  localStorage.removeItem('clients-store');
  localStorage.removeItem('contracts-store');
  localStorage.removeItem('payments-store');
  localStorage.removeItem('receivables-store');
  localStorage.removeItem('cheques-store');
  localStorage.removeItem('users-store');
  localStorage.removeItem('reports-store');

  // Reload page to reinitialize
  window.location.reload();
};

// Store status helper
export const getStoresStatus = () => {
  const { useAppStore } = require('./appStore');
  const { useSettingsStore } = require('./settingsStore');
  const { useReferenceDataStore } = require('./referenceDataStore');

  return {
    app: {
      initialized: true,
      isLoading: useAppStore.getState().isLoading
    },
    settings: {
      initialized: useSettingsStore.getState().isInitialized,
      isLoading: useSettingsStore.getState().isLoading,
      hasUnsavedChanges: useSettingsStore.getState().hasUnsavedChanges
    },
    referenceData: {
      initialized: useReferenceDataStore.getState().isInitialized,
      isInitializing: useReferenceDataStore.getState().isInitializing
    }
  };
};
