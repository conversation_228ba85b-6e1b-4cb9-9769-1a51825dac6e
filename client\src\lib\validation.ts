import { z } from "zod";

// ===== UNIFIED FRONTEND VALIDATION SCHEMAS =====
// Author: Augment Code
// Description: Unified validation schemas that match backend exactly

// Phone validation (matches backend exactly)
const phoneSchema = z.string()
  .regex(/^[\+]?[0-9\-\(\)\s]+$/, 'رقم هاتف غير صحيح')
  .min(8, 'رقم الهاتف قصير جداً')
  .max(20, 'رقم الهاتف طويل جداً');

// Email validation
const emailSchema = z.string()
  .email('البريد الإلكتروني غير صحيح')
  .optional()
  .or(z.literal(''));

// ===== CLIENT SCHEMAS =====
export const clientSchema = z.object({
  clientId: z.string()
    .min(1, 'رقم العميل مطلوب')
    .max(50, 'رقم العميل طويل جداً'),
  
  clientType: z.string()
    .min(1, 'نوع العميل مطلوب')
    .refine((val) => ['individual', 'company', 'أفراد', 'شركات'].includes(val), {
      message: 'نوع العميل يجب أن يكون فرد أو شركة'
    }),
  
  clientName: z.string()
    .min(2, 'اسم العميل قصير جداً')
    .max(200, 'اسم العميل طويل جداً'),
  
  clientAddress: z.string()
    .max(500, 'العنوان طويل جداً')
    .optional()
    .or(z.literal('')),
  
  clientPhoneWhatsapp: phoneSchema,
  clientPhone2: phoneSchema.optional().or(z.literal('')),
  clientPhone3: phoneSchema.optional().or(z.literal('')),
  clientEmail: emailSchema,
  
  clientNotes: z.string()
    .max(1000, 'الملاحظات طويلة جداً')
    .optional()
    .or(z.literal('')),
  
  clientFinancialGuarantee: z.string()
    .max(200, 'الضمان المالي طويل جداً')
    .optional()
    .or(z.literal('')),
  
  clientFinancial_Category: z.string()
    .max(100, 'الفئة المالية طويلة جداً')
    .optional()
    .or(z.literal('')),
  
  clientLegal_Rep: z.string()
    .max(200, 'الممثل القانوني طويل جداً')
    .optional()
    .or(z.literal('')),
  
  clientPartner: z.string()
    .max(200, 'الشريك طويل جداً')
    .optional()
    .or(z.literal('')),
  
  clientReg_Number: z.string()
    .max(50, 'رقم التسجيل طويل جداً')
    .optional()
    .or(z.literal('')),
  
  clientTaxReg_Number: z.string()
    .max(50, 'رقم التسجيل الضريبي طويل جداً')
    .optional()
    .or(z.literal('')),
  
  clientLegal_Status: z.string()
    .max(100, 'الوضع القانوني طويل جداً')
    .optional()
    .or(z.literal('')),
  
  clientRemarks: z.string()
    .max(1000, 'الملاحظات طويلة جداً')
    .optional()
    .or(z.literal('')),
  
  clientID_Image: z.string()
    .optional()
    .or(z.literal('')),
  
  clientDocuments: z.string()
    .optional()
    .or(z.literal('')),
  
  isActive: z.boolean().optional().default(true)
});

export type ClientFormData = z.infer<typeof clientSchema>;

// Export for compatibility with existing code
export const frontendClientSchema = clientSchema;
export const insertClientSchema = clientSchema;
