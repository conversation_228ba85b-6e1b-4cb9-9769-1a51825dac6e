// ===== APP STORE =====
// Author: Augment Code
// Description: Centralized state management for global app state using Zustand

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Initial state
const initialState = {
  // Global loading state
  isLoading: false,
  loadingMessage: '',
  loadingOperations: [],

  // Notifications/Toasts
  notifications: [],
  notificationCounter: 0,

  // Global error state
  globalError: null,
  
  // UI state
  sidebarCollapsed: false,
  theme: 'light',
  
  // Navigation state
  currentPage: '',
  breadcrumbs: [],
  
  // Modal state
  modals: {},
  
  // Connection state
  isOnline: navigator.onLine,
  connectionError: null,
  
  // Performance tracking
  performanceMetrics: {
    pageLoadTime: null,
    apiResponseTimes: {},
    errorCount: 0
  }
};

// Create the app store
export const useAppStore = create()(
  devtools(
    immer((set, get) => ({
      ...initialState,

      // Actions
      actions: {
        // Loading management
        setLoading(isLoading, message = '') {
          set((state) => {
            state.isLoading = isLoading;
            state.loadingMessage = message;
          });
        },

        addLoadingOperation(operationId, message = '') {
          set((state) => {
            if (!state.loadingOperations.includes(operationId)) {
              state.loadingOperations.push(operationId);
            }
            state.isLoading = state.loadingOperations.length > 0;
            if (message) {
              state.loadingMessage = message;
            }
          });
        },

        removeLoadingOperation(operationId) {
          set((state) => {
            const index = state.loadingOperations.indexOf(operationId);
            if (index > -1) {
              state.loadingOperations.splice(index, 1);
            }
            state.isLoading = state.loadingOperations.length > 0;
            if (state.loadingOperations.length === 0) {
              state.loadingMessage = '';
            }
          });
        },

        // Notification management
        addNotification(notification) {
          const id = get().notificationCounter + 1;
          const newNotification = {
            id,
            timestamp: new Date().toISOString(),
            autoClose: true,
            duration: 3000,
            ...notification
          };

          set((state) => {
            state.notifications.push(newNotification);
            state.notificationCounter = id;
          });

          // Auto-remove notification if autoClose is enabled
          if (newNotification.autoClose) {
            setTimeout(() => {
              get().actions.removeNotification(id);
            }, newNotification.duration);
          }

          return id;
        },

        removeNotification(id) {
          set((state) => {
            state.notifications = state.notifications.filter(n => n.id !== id);
          });
        },

        clearAllNotifications() {
          set((state) => {
            state.notifications = [];
          });
        },

        // Convenience methods for different notification types
        showSuccess(message, options = {}) {
          return get().actions.addNotification({
            type: 'success',
            title: 'نجح',
            message,
            ...options
          });
        },

        showError(message, options = {}) {
          return get().actions.addNotification({
            type: 'error',
            title: 'خطأ',
            message,
            autoClose: false,
            ...options
          });
        },

        showWarning(message, options = {}) {
          return get().actions.addNotification({
            type: 'warning',
            title: 'تحذير',
            message,
            ...options
          });
        },

        showInfo(message, options = {}) {
          return get().actions.addNotification({
            type: 'info',
            title: 'معلومات',
            message,
            ...options
          });
        },

        // Global error management
        setGlobalError(error) {
          set((state) => {
            state.globalError = error;
          });

          // Also show as notification
          if (error) {
            get().actions.showError(error.message || error);
          }
        },

        clearGlobalError() {
          set((state) => {
            state.globalError = null;
          });
        },

        // UI state management
        toggleSidebar() {
          set((state) => {
            state.sidebarCollapsed = !state.sidebarCollapsed;
          });
        },

        setSidebarCollapsed(collapsed) {
          set((state) => {
            state.sidebarCollapsed = collapsed;
          });
        },

        setTheme(theme) {
          set((state) => {
            state.theme = theme;
          });
          
          // Apply theme to document
          document.documentElement.setAttribute('data-theme', theme);
          localStorage.setItem('theme', theme);
        },

        // Navigation management
        setCurrentPage(page, breadcrumbs = []) {
          set((state) => {
            state.currentPage = page;
            state.breadcrumbs = breadcrumbs;
          });
        },

        addBreadcrumb(breadcrumb) {
          set((state) => {
            state.breadcrumbs.push(breadcrumb);
          });
        },

        // Modal management
        openModal(modalId, props = {}) {
          set((state) => {
            state.modals[modalId] = {
              isOpen: true,
              props
            };
          });
        },

        closeModal(modalId) {
          set((state) => {
            if (state.modals[modalId]) {
              state.modals[modalId].isOpen = false;
            }
          });
        },

        closeAllModals() {
          set((state) => {
            Object.keys(state.modals).forEach(modalId => {
              state.modals[modalId].isOpen = false;
            });
          });
        },

        // Connection state management
        setOnlineStatus(isOnline) {
          set((state) => {
            state.isOnline = isOnline;
            if (isOnline) {
              state.connectionError = null;
            }
          });
        },

        setConnectionError(error) {
          set((state) => {
            state.connectionError = error;
            state.isOnline = false;
          });
        },

        // Performance tracking
        recordPageLoadTime(time) {
          set((state) => {
            state.performanceMetrics.pageLoadTime = time;
          });
        },

        recordApiResponseTime(endpoint, time) {
          set((state) => {
            state.performanceMetrics.apiResponseTimes[endpoint] = time;
          });
        },

        incrementErrorCount() {
          set((state) => {
            state.performanceMetrics.errorCount++;
          });
        },

        resetPerformanceMetrics() {
          set((state) => {
            state.performanceMetrics = {
              pageLoadTime: null,
              apiResponseTimes: {},
              errorCount: 0
            };
          });
        },

        // Utility methods
        getModalState(modalId) {
          const state = get();
          return state.modals[modalId] || { isOpen: false, props: {} };
        },

        isModalOpen(modalId) {
          const state = get();
          return state.modals[modalId]?.isOpen || false;
        },

        hasActiveNotifications() {
          const state = get();
          return state.notifications.length > 0;
        },

        getNotificationsByType(type) {
          const state = get();
          return state.notifications.filter(n => n.type === type);
        },

        // Initialize app
        initialize() {
          // Set up online/offline listeners
          window.addEventListener('online', () => {
            get().actions.setOnlineStatus(true);
            get().actions.showSuccess('تم استعادة الاتصال بالإنترنت');
          });

          window.addEventListener('offline', () => {
            get().actions.setOnlineStatus(false);
            get().actions.showWarning('تم فقدان الاتصال بالإنترنت');
          });

          // Load theme from localStorage
          const savedTheme = localStorage.getItem('theme') || 'light';
          get().actions.setTheme(savedTheme);

          // Load sidebar state from localStorage
          const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
          get().actions.setSidebarCollapsed(sidebarCollapsed);

          console.log('🚀 App store initialized');
        }
      }
    })),
    {
      name: 'app-store'
    }
  )
);

// Selectors for easy access to specific parts of the state
export const useIsLoading = () => useAppStore((state) => state.isLoading);
export const useLoadingMessage = () => useAppStore((state) => state.loadingMessage);
export const useNotifications = () => useAppStore((state) => state.notifications);
export const useGlobalError = () => useAppStore((state) => state.globalError);
export const useSidebarCollapsed = () => useAppStore((state) => state.sidebarCollapsed);
export const useTheme = () => useAppStore((state) => state.theme);
export const useCurrentPage = () => useAppStore((state) => state.currentPage);
export const useBreadcrumbs = () => useAppStore((state) => state.breadcrumbs);
export const useIsOnline = () => useAppStore((state) => state.isOnline);
export const useConnectionError = () => useAppStore((state) => state.connectionError);
export const useAppActions = () => useAppStore((state) => state.actions);

// Modal selectors
export const useModal = (modalId) => useAppStore((state) => 
  state.modals[modalId] || { isOpen: false, props: {} }
);

export const useIsModalOpen = (modalId) => useAppStore((state) => 
  state.modals[modalId]?.isOpen || false
);

// Performance selectors
export const usePerformanceMetrics = () => useAppStore((state) => state.performanceMetrics);

export default useAppStore;
