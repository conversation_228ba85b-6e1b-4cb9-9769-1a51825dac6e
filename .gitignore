node_modules
dist
.DS_Store
server/public
vite.config.ts.*
*.tar.gz

# Environment Variables
.env
.env.local
.env.production
.env.staging

# Database Files
*.sqlite
*.sqlite3
*.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Backup files
backups/
*.backup

# Temporary files
temp/
tmp/
*.tmp

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test files (temporary)
*test*.tsx
*test*.ts
*test*.js
*debug*.tsx
*debug*.ts
*debug*.js

# Documentation drafts
*-draft.md
*-temp.md
*-old.md

# Server variations
server-*.cjs
server-*.js
*-server.cjs
*-server.js

# SQLite WAL files
*.sqlite-shm
*.sqlite-wal

# Build artifacts
dist/
build/