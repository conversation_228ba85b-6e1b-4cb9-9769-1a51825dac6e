import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON> } from "wouter";
import { Button } from "../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { apiRequest } from "../lib/queryClient";
import {
  LayoutDashboard,
  Users,
  FileText,
  Calendar,
  CreditCard,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  DollarSign,
  Target,
  Clock,
  BarChart3,
  Bell,
  Eye,
  ArrowRight,
  AlertTriangle
} from "lucide-react";
import labels from "../i18n";

const Dashboard = () => {
  const [lang] = useState<"ar" | "en">((localStorage.getItem("lang") as "ar" | "en") || "ar");
  const t = labels[lang];

  // Fetch dashboard data
  const { data: dashboardData, isLoading } = useQuery({
    queryKey: ["/api/dashboard"],
    queryFn: () => apiRequest("/api/dashboard"),
  });

  // Fetch financial summary
  const { data: financialSummary } = useQuery({
    queryKey: ["/api/reports/financial-summary"],
    queryFn: () => apiRequest("/api/reports/financial-summary"),
  });

  // Fetch alert statistics
  const { data: alertStats } = useQuery({
    queryKey: ["/api/alerts/stats"],
    queryFn: () => apiRequest("/api/alerts/stats"),
  });

  const stats = [
    {
      title: t.clients,
      value: dashboardData?.totalClients || 0,
      icon: Users,
      color: "bg-blue-500",
      link: "/clients"
    },
    {
      title: t.contracts,
      value: dashboardData?.totalContracts || 0,
      icon: FileText,
      color: "bg-green-500",
      link: "/contracts"
    },
    {
      title: t.dues,
      value: dashboardData?.totalDues || 0,
      icon: Calendar,
      color: "bg-orange-500",
      link: "/dues"
    },
    {
      title: t.payments,
      value: dashboardData?.totalPayments || 0,
      icon: CreditCard,
      color: "bg-purple-500",
      link: "/payments"
    }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <div className="flex items-center space-x-reverse space-x-2">
              <LayoutDashboard className="h-5 w-5 text-primary" />
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {t.dashboard}
              </h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Link key={index} href={stat.link}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {stat.title}
                        </p>
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          {stat.value.toLocaleString()}
                        </p>
                      </div>
                      <div className={`p-3 rounded-full ${stat.color}`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>

        {/* Financial Summary */}
        {financialSummary && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي قيمة العقود</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {new Intl.NumberFormat('ar-EG', {
                        style: 'currency',
                        currency: 'EGP',
                        minimumFractionDigits: 0,
                      }).format(financialSummary.totalContractValue)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {financialSummary.totalContracts} عقد
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">المبلغ المحصل</p>
                    <p className="text-2xl font-bold text-green-600">
                      {new Intl.NumberFormat('ar-EG', {
                        style: 'currency',
                        currency: 'EGP',
                        minimumFractionDigits: 0,
                      }).format(financialSummary.totalCollected)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      معدل التحصيل: {Math.round(financialSummary.collectionRate)}%
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">المتأخرات</p>
                    <p className="text-2xl font-bold text-red-600">
                      {new Intl.NumberFormat('ar-EG', {
                        style: 'currency',
                        currency: 'EGP',
                        minimumFractionDigits: 0,
                      }).format(financialSummary.totalOverdue)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      العقود النشطة: {financialSummary.activeContracts}
                    </p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                {lang === "ar" ? "إجراءات سريعة" : "Quick Actions"}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/clients/new">
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  {lang === "ar" ? "إضافة عميل جديد" : "Add New Client"}
                </Button>
              </Link>
              <Link href="/contracts/new">
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="h-4 w-4 mr-2" />
                  {lang === "ar" ? "إنشاء عقد جديد" : "Create New Contract"}
                </Button>
              </Link>
              <Link href="/payments/new">
                <Button variant="outline" className="w-full justify-start">
                  <CreditCard className="h-4 w-4 mr-2" />
                  {lang === "ar" ? "تسجيل دفعة جديدة" : "Record New Payment"}
                </Button>
              </Link>
              <Link href="/reports">
                <Button variant="outline" className="w-full justify-start">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  {lang === "ar" ? "التقارير المالية" : "Financial Reports"}
                </Button>
              </Link>
              <Link href="/alerts">
                <Button variant="outline" className="w-full justify-start">
                  <Bell className="h-4 w-4 mr-2" />
                  {lang === "ar" ? "مركز التنبيهات" : "Alerts Center"}
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  {lang === "ar" ? "التنبيهات" : "Alerts"}
                </div>
                {alertStats && alertStats.unreadAlerts > 0 && (
                  <Link href="/alerts">
                    <Button variant="outline" size="sm" className="gap-2">
                      <Eye className="h-4 w-4" />
                      عرض الكل
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </Link>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {alertStats ? (
                <div className="space-y-4">
                  {/* Alert Statistics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                      <div>
                        <p className="text-sm font-medium text-red-800">غير مقروءة</p>
                        <p className="text-lg font-bold text-red-600">{alertStats.unreadAlerts}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                      <Clock className="h-5 w-5 text-orange-600" />
                      <div>
                        <p className="text-sm font-medium text-orange-800">أولوية عالية</p>
                        <p className="text-lg font-bold text-orange-600">{alertStats.highPriorityAlerts}</p>
                      </div>
                    </div>
                  </div>

                  {/* Recent Alerts */}
                  {dashboardData?.alerts?.length > 0 ? (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-gray-700">أحدث التنبيهات</h4>
                      {dashboardData.alerts.slice(0, 3).map((alert: any, index: number) => (
                        <div key={index} className="flex items-start gap-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                          <AlertCircle className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                          <div className="flex-1">
                            <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                              {alert.title}
                            </p>
                            <p className="text-xs text-yellow-600 dark:text-yellow-300">
                              {alert.message}
                            </p>
                          </div>
                        </div>
                      ))}
                      {dashboardData.alerts.length > 3 && (
                        <Link href="/alerts">
                          <Button variant="ghost" size="sm" className="w-full mt-2">
                            عرض {dashboardData.alerts.length - 3} تنبيهات أخرى
                          </Button>
                        </Link>
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                      <p className="text-sm text-green-800 dark:text-green-200">
                        {lang === "ar" ? "لا توجد تنبيهات جديدة" : "No new alerts"}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-20">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>
              {lang === "ar" ? "النشاط الأخير" : "Recent Activity"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {dashboardData?.recentActivity?.length > 0 ? (
              <div className="space-y-4">
                {dashboardData.recentActivity.map((activity: any, index: number) => (
                  <div key={index} className="flex items-center gap-4 p-3 border rounded-lg">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                {lang === "ar" ? "لا يوجد نشاط حديث" : "No recent activity"}
              </p>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default Dashboard;