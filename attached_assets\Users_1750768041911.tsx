import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "../components/ui/card";
import { UserCheck } from "lucide-react";
import labels from "../i18n";

const Users = () => {
  const [lang] = useState<"ar" | "en">((localStorage.getItem("lang") as "ar" | "en") || "ar");
  const t = labels[lang];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <div className="flex items-center space-x-reverse space-x-2">
              <UserCheck className="h-5 w-5 text-primary" />
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {t.users}
              </h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader>
            <CardTitle>{t.users}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500 dark:text-gray-400">
              {lang === "ar" ? "قريباً..." : "Coming soon..."}
            </p>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default Users;