import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Calendar } from 'lucide-react';
import { useDateContext } from '@/contexts/date-context';
import { useLanguage } from '@/hooks/use-language';

interface SimpleDateInputProps {
  value?: string;
  onChange?: (date: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  id?: string;
}

export const SimpleDateInput: React.FC<SimpleDateInputProps> = ({
  value,
  onChange,
  placeholder,
  className = '',
  disabled = false,
  required = false,
  id
}) => {
  const { formatDate, parseDate } = useDateContext();
  const { language } = useLanguage();
  const hiddenInputRef = useRef<HTMLInputElement>(null);
  const [inputValue, setInputValue] = useState('');

  // Determine text direction based on language
  const isRTL = language === 'ar';
  const textDirection = isRTL ? 'rtl' : 'ltr';

  // Update input value when prop value changes
  useEffect(() => {
    setInputValue(value ? formatDate(value) : '');
  }, [value, formatDate]);

  // Handle native date input change
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isoDate = e.target.value; // Always YYYY-MM-DD from input[type="date"]
    onChange?.(isoDate);
    setInputValue(isoDate ? formatDate(isoDate) : '');
  };

  // Handle manual text input with auto-formatting
  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let textValue = e.target.value;

    // Remove any non-digit characters except slashes
    textValue = textValue.replace(/[^\d\/]/g, '');

    // Auto-format as DD/MM/YYYY with strict validation
    const parts = textValue.split('/');
    let formattedValue = '';

    // Day part (max 2 digits, max value 31)
    if (parts[0]) {
      let day = parts[0].substring(0, 2);
      if (parseInt(day) > 31) day = '31';
      formattedValue = day;
    }

    // Month part (max 2 digits, max value 12)
    if (parts[1] !== undefined) {
      let month = parts[1].substring(0, 2);
      if (parseInt(month) > 12) month = '12';
      formattedValue += '/' + month;
    }

    // Year part (exactly 4 digits, range 2020-2030)
    if (parts[2] !== undefined) {
      let year = parts[2].substring(0, 4);
      formattedValue += '/' + year;
    }

    // Auto-add slashes
    if (textValue.length >= 2 && !textValue.includes('/')) {
      formattedValue = textValue.substring(0, 2) + '/' + textValue.substring(2);
    } else if (textValue.length >= 5 && textValue.split('/').length === 2) {
      const dayMonth = textValue.substring(0, 5);
      const year = textValue.substring(5);
      formattedValue = dayMonth + '/' + year.substring(0, 4);
    } else {
      formattedValue = textValue;
    }

    // Limit to DD/MM/YYYY format (10 characters)
    if (formattedValue.length > 10) {
      formattedValue = formattedValue.substring(0, 10);
    }

    setInputValue(formattedValue);

    // Try to parse the date when format is complete
    if (formattedValue.length === 10 && formattedValue.split('/').length === 3) {
      const parsedDate = parseDate(formattedValue);
      if (parsedDate) {
        // Use local date without timezone conversion
        const year = parsedDate.getFullYear();
        const month = (parsedDate.getMonth() + 1).toString().padStart(2, '0');
        const day = parsedDate.getDate().toString().padStart(2, '0');
        const isoDate = `${year}-${month}-${day}`;
        onChange?.(isoDate);
      }
    }
  };

  // Handle click on calendar icon
  const handleCalendarClick = () => {
    if (hiddenInputRef.current) {
      hiddenInputRef.current.showPicker?.();
    }
  };

  return (
    <div className="relative">
      {/* Text input for manual typing with DD/MM/YYYY format */}
      <Input
        id={id}
        type="text"
        value={inputValue}
        onChange={handleTextChange}
        placeholder={placeholder || "مثال: 07/05/2025"}
        className={`${className} bg-white ${isRTL ? 'text-right' : 'text-left'}`}
        style={{
          direction: textDirection,
          textAlign: isRTL ? 'right' : 'left',
          unicodeBidi: 'plaintext'
        }}
        disabled={disabled}
        required={required}
        maxLength={10}
        onKeyDown={(e) => {
          // Allow only numbers and navigation keys (slashes will be auto-added)
          const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];
          if (!allowedKeys.includes(e.key) && !/[0-9]/.test(e.key)) {
            e.preventDefault();
          }
        }}
      />

      {/* Calendar icon */}
      <Calendar
        className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
        onClick={handleCalendarClick}
      />

      {/* Hidden native date input for calendar picker */}
      <input
        ref={hiddenInputRef}
        type="date"
        className="absolute opacity-0 pointer-events-none"
        value={value || ''}
        onChange={handleDateChange}
        tabIndex={-1}
      />
    </div>
  );
};

export default SimpleDateInput;
