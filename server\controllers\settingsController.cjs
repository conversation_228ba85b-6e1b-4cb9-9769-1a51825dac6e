const settingsService = require('../services/settingsService.cjs');

class SettingsController {
  // جلب الإعدادات
  async getSettings(req, res) {
    try {
      console.log('📋 Getting settings...');
      const settings = await settingsService.getSettings();
      
      if (settings) {
        console.log('✅ Settings retrieved successfully');
        res.json({
          success: true,
          data: settings
        });
      } else {
        console.log('⚠️ No settings found, returning empty settings');
        res.json({
          success: true,
          data: {}
        });
      }
    } catch (error) {
      console.error('❌ Error getting settings:', error);
      res.status(500).json({
        success: false,
        error: 'فشل في جلب الإعدادات',
        details: [error.message]
      });
    }
  }

  // حفظ الإعدادات
  async saveSettings(req, res) {
    try {
      console.log('💾 Saving settings...');
      console.log('📦 Received data keys:', Object.keys(req.body));
      
      // التحقق من صحة البيانات
      if (!req.body || Object.keys(req.body).length === 0) {
        return res.status(400).json({
          success: false,
          error: 'لا توجد بيانات للحفظ',
          details: ['البيانات المرسلة فارغة']
        });
      }

      const savedSettings = await settingsService.saveSettings(req.body);
      
      console.log('✅ Settings saved successfully');
      res.json({
        success: true,
        message: 'تم حفظ الإعدادات بنجاح',
        data: savedSettings
      });
      
    } catch (error) {
      console.error('❌ Error saving settings:', error);
      res.status(500).json({
        success: false,
        error: 'فشل في حفظ الإعدادات',
        details: [error.message]
      });
    }
  }

  // إعادة تعيين الإعدادات للافتراضية
  async resetSettings(req, res) {
    try {
      console.log('🔄 Resetting settings to defaults...');
      
      const defaultSettings = {
        companyName: 'شركة إدارة العقود المصرية',
        programName: 'نظام إدارة العقود المتقدم',
        language: 'ar',
        currency: 'جنيه مصري',
        currencySymbol: 'ج.م',
        country: 'مصر',
        dateFormat: 'yyyy/mm/dd',
        timeFormat: '24',
        timeZone: 'Africa/Cairo',
        theme: 'system',
        fontSize: 'medium',
        primaryColor: '#3b82f6',
        fontFamily: 'Cairo',
        workDays: ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس"],
        defaultContractDuration: '12',
        defaultPaymentTerms: 'شهري',
        defaultTaxRate: '14',
        contractNumberFormat: 'C-{YYYY}-{####}',
        clientNumberFormat: 'CL-{####}',
        paymentNumberFormat: 'P-{YYYY}-{####}',
        invoiceNumberFormat: 'INV-{YYYY}-{####}',
        receiptNumberFormat: 'REC-{YYYY}-{####}'
      };

      const savedSettings = await settingsService.saveSettings(defaultSettings);
      
      console.log('✅ Settings reset successfully');
      res.json({
        success: true,
        message: 'تم إعادة تعيين الإعدادات بنجاح',
        data: savedSettings
      });
      
    } catch (error) {
      console.error('❌ Error resetting settings:', error);
      res.status(500).json({
        success: false,
        error: 'فشل في إعادة تعيين الإعدادات',
        details: [error.message]
      });
    }
  }
}

module.exports = new SettingsController();
