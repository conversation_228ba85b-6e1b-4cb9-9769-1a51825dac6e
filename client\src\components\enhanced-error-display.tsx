import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  XCircle, 
  AlertCircle, 
  Info, 
  RefreshCw, 
  ExternalLink,
  Settings,
  Search,
  Plus,
  HelpCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ErrorInfo {
  message: string;
  guidance: string;
  severity: 'critical' | 'error' | 'warning' | 'info';
  action?: string;
  retryable?: boolean;
  confirmable?: boolean;
  technical?: boolean;
  context?: any;
  operation?: string;
  title?: string;
}

interface EnhancedErrorDisplayProps {
  error: ErrorInfo;
  onRetry?: () => void;
  onAction?: (action: string) => void;
  onDismiss?: () => void;
  className?: string;
  compact?: boolean;
}

const severityConfig = {
  critical: {
    icon: XCircle,
    variant: 'destructive' as const,
    bgColor: 'bg-red-50 dark:bg-red-950/20',
    borderColor: 'border-red-200 dark:border-red-800',
    iconColor: 'text-red-600 dark:text-red-400',
    textColor: 'text-red-900 dark:text-red-100'
  },
  error: {
    icon: AlertTriangle,
    variant: 'destructive' as const,
    bgColor: 'bg-red-50 dark:bg-red-950/20',
    borderColor: 'border-red-200 dark:border-red-800',
    iconColor: 'text-red-600 dark:text-red-400',
    textColor: 'text-red-900 dark:text-red-100'
  },
  warning: {
    icon: AlertCircle,
    variant: 'warning' as const,
    bgColor: 'bg-yellow-50 dark:bg-yellow-950/20',
    borderColor: 'border-yellow-200 dark:border-yellow-800',
    iconColor: 'text-yellow-600 dark:text-yellow-400',
    textColor: 'text-yellow-900 dark:text-yellow-100'
  },
  info: {
    icon: Info,
    variant: 'info' as const,
    bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
    iconColor: 'text-blue-600 dark:text-blue-400',
    textColor: 'text-blue-900 dark:text-blue-100'
  }
};

const actionConfig = {
  search_client: {
    label: 'البحث عن العميل الموجود',
    icon: Search,
    variant: 'default' as const
  },
  search_existing: {
    label: 'البحث عن العميل الموجود',
    icon: Search,
    variant: 'default' as const
  },
  add_client: {
    label: 'إضافة عميل جديد',
    icon: Plus,
    variant: 'default' as const
  },
  generate_new_id: {
    label: 'إنشاء رقم عميل جديد',
    icon: RefreshCw,
    variant: 'outline' as const
  },
  add_product: {
    label: 'إضافة منتج',
    icon: Plus,
    variant: 'default' as const
  },
  open_settings: {
    label: 'فتح الإعدادات',
    icon: Settings,
    variant: 'outline' as const
  },
  setup_reference_data: {
    label: 'إعداد البيانات المرجعية',
    icon: Settings,
    variant: 'default' as const
  },
  get_help: {
    label: 'المساعدة',
    icon: HelpCircle,
    variant: 'ghost' as const
  }
};

export function EnhancedErrorDisplay({
  error,
  onRetry,
  onAction,
  onDismiss,
  className,
  compact = false
}: EnhancedErrorDisplayProps) {
  const config = severityConfig[error.severity] || severityConfig.error;
  const IconComponent = config.icon;

  const handleAction = (actionType: string) => {
    if (onAction) {
      onAction(actionType);
    }
  };

  const renderActionButton = (actionType: string) => {
    const actionInfo = actionConfig[actionType];
    if (!actionInfo) return null;

    const ActionIcon = actionInfo.icon;

    return (
      <Button
        variant={actionInfo.variant}
        size="sm"
        onClick={() => handleAction(actionType)}
        className="gap-2"
      >
        <ActionIcon className="w-4 h-4" />
        {actionInfo.label}
      </Button>
    );
  };

  if (compact) {
    return (
      <div className={cn(
        "flex items-center gap-3 p-3 rounded-lg border",
        config.bgColor,
        config.borderColor,
        className
      )}>
        <IconComponent className={cn("w-5 h-5 flex-shrink-0", config.iconColor)} />
        <div className="flex-1 min-w-0">
          <p className={cn("text-sm font-medium", config.textColor)}>
            {error.title || error.message}
          </p>
          {error.guidance && (
            <p className={cn("text-xs mt-1 opacity-90", config.textColor)}>
              {error.guidance}
            </p>
          )}
        </div>
        <div className="flex items-center gap-2">
          {error.action && renderActionButton(error.action)}
          {error.retryable && onRetry && (
            <Button variant="ghost" size="sm" onClick={onRetry}>
              <RefreshCw className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <Alert variant={config.variant} className={cn(className, config.bgColor, config.borderColor)}>
      <div className="flex items-start gap-3">
        <IconComponent className={cn("w-5 h-5 mt-0.5 flex-shrink-0", config.iconColor)} />
        
        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-2">
            <AlertTitle className={cn("text-base font-semibold", config.textColor)}>
              {error.title || error.message}
            </AlertTitle>
            
            {error.severity === 'critical' && (
              <Badge variant="destructive" className="text-xs">
                حرج
              </Badge>
            )}
            
            {error.technical && (
              <Badge variant="secondary" className="text-xs">
                تقني
              </Badge>
            )}
          </div>

          {error.guidance && (
            <AlertDescription className={cn("text-sm leading-relaxed", config.textColor)}>
              {error.guidance}
            </AlertDescription>
          )}

          {error.context && Object.keys(error.context).length > 0 && (
            <details className="mt-2">
              <summary className={cn("text-xs cursor-pointer opacity-75 hover:opacity-100", config.textColor)}>
                تفاصيل إضافية
              </summary>
              <div className="mt-1 text-xs font-mono bg-black/5 dark:bg-white/5 p-2 rounded border">
                <pre className="whitespace-pre-wrap">
                  {JSON.stringify(error.context, null, 2)}
                </pre>
              </div>
            </details>
          )}

          <div className="flex items-center gap-2 pt-2">
            {error.action && renderActionButton(error.action)}
            
            {error.retryable && onRetry && (
              <Button variant="outline" size="sm" onClick={onRetry} className="gap-2">
                <RefreshCw className="w-4 h-4" />
                إعادة المحاولة
              </Button>
            )}

            {error.confirmable && (
              <Button variant="outline" size="sm" onClick={() => handleAction('confirm')} className="gap-2">
                المتابعة رغم ذلك
              </Button>
            )}

            <Button variant="ghost" size="sm" onClick={() => handleAction('get_help')} className="gap-2">
              <HelpCircle className="w-4 h-4" />
              مساعدة
            </Button>

            {onDismiss && (
              <Button variant="ghost" size="sm" onClick={onDismiss} className="mr-auto">
                إغلاق
              </Button>
            )}
          </div>
        </div>
      </div>
    </Alert>
  );
}

// Hook for using enhanced error display with toast
export function useEnhancedErrorDisplay() {
  const showEnhancedError = (error: ErrorInfo, options?: {
    onRetry?: () => void;
    onAction?: (action: string) => void;
  }) => {
    // This would integrate with your existing toast system
    // For now, we'll return the error info for manual handling
    return {
      error,
      ...options
    };
  };

  return {
    showEnhancedError
  };
}

// Utility function to create error info from different sources
export function createErrorInfo(
  category: string,
  code: string,
  context?: any,
  operation?: string
): ErrorInfo {
  // This would integrate with the ErrorMessages.cjs file
  // For now, return a basic structure
  return {
    message: 'حدث خطأ',
    guidance: 'يرجى المحاولة مرة أخرى',
    severity: 'error',
    context,
    operation
  };
}

export default EnhancedErrorDisplay;
