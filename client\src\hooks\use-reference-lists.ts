import { useQuery } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { useEffect } from 'react';

export interface ReferenceListConfig {
  id: number;
  listName: string;
  displayName: string;
  description: string;
  linkedPages: string[];
  fieldMapping: Record<string, string>;
  isRequired: boolean;
  isActive: boolean;
}

export interface ReferenceListStatus {
  listName: string;
  displayName: string;
  description: string;
  isRequired: boolean;
  itemCount: number;
  isConfigured: boolean;
  linkedPages: string[];
  fieldMapping: Record<string, string>;
}

export interface ReferenceListsStatusResponse {
  status: ReferenceListStatus[];
  hasIssues: boolean;
  missingRequired: ReferenceListStatus[];
  summary: {
    total: number;
    configured: number;
    required: number;
    missingRequired: number;
  };
}

// Hook to get all reference lists configuration
export function useReferenceListsConfig() {
  return useQuery<ReferenceListConfig[]>({
    queryKey: ['reference-lists-config'],
    queryFn: async () => {
      const response = await fetch('/api/reference-lists-config');
      if (!response.ok) throw new Error('Failed to fetch reference lists config');
      return response.json();
    }
  });
}

// Hook to get reference lists for a specific page
export function useReferenceListsForPage(pageName: string) {
  return useQuery<ReferenceListConfig[]>({
    queryKey: ['reference-lists-for-page', pageName],
    queryFn: async () => {
      const response = await fetch(`/api/reference-lists-for-page/${pageName}`);
      if (!response.ok) throw new Error('Failed to fetch reference lists for page');
      return response.json();
    },
    enabled: !!pageName
  });
}

// Hook to check reference lists status for a page
export function useReferenceListsStatus(pageName: string) {
  return useQuery<ReferenceListsStatusResponse>({
    queryKey: ['reference-lists-status', pageName],
    queryFn: async () => {
      const response = await fetch(`/api/reference-lists-status/${pageName}`);
      if (!response.ok) throw new Error('Failed to fetch reference lists status');
      return response.json();
    },
    enabled: !!pageName
  });
}

// Hook to get reference data for a specific list
export function useReferenceData(module: string, listName: string) {
  return useQuery({
    queryKey: ['reference-data', module, listName],
    queryFn: async () => {
      const response = await fetch(`/api/reference-data/${module}/${listName}`);
      if (!response.ok) throw new Error('Failed to fetch reference data');
      return response.json();
    },
    enabled: !!module && !!listName
  });
}

// Hook with automatic alerts for missing required lists
export function useReferenceListsWithAlerts(pageName: string, showAlerts: boolean = true) {
  const { toast } = useToast();
  const { data: status, isLoading, error } = useReferenceListsStatus(pageName);

  useEffect(() => {
    if (showAlerts && status?.hasIssues && status.missingRequired.length > 0) {
      const missingLists = status.missingRequired.map(list => list.displayName).join('، ');
      
      toast({
        title: '⚠️ قوائم مرجعية مطلوبة',
        description: `يجب إعداد القوائم التالية أولاً: ${missingLists}`,
        variant: 'destructive',
        duration: 8000,
        action: {
          label: 'إعداد الآن',
          onClick: () => {
            window.open('/reference-data', '_blank');
          }
        }
      });
    }
  }, [status, showAlerts, toast]);

  return {
    status,
    isLoading,
    error,
    hasIssues: status?.hasIssues || false,
    missingRequired: status?.missingRequired || [],
    summary: status?.summary
  };
}

// Hook to get dropdown options for a specific field
export function useReferenceDropdownOptions(pageName: string, fieldName: string) {
  const { data: listsForPage } = useReferenceListsForPage(pageName);

  // Find the list that maps to this field
  const relevantList = listsForPage?.find(list =>
    list.fieldMapping[pageName] === fieldName
  );

  // Determine the correct module based on the list name
  const module = relevantList?.listName === 'contractType' ? 'contracts' : 'general';

  const { data: options = [] } = useReferenceData(
    module,
    relevantList?.listName || ''
  );

  return {
    options: options.map((item: any) => ({
      value: item.itemValue,
      label: item.itemLabel
    })),
    isLoading: !relevantList || !options,
    listName: relevantList?.listName,
    displayName: relevantList?.displayName
  };
}

// Utility function to format options for Select components
export function formatReferenceOptions(data: any[]) {
  return data.map(item => ({
    value: item.itemValue,
    label: item.itemLabel
  }));
}

// Utility function to get field mapping for a page
export function getFieldMapping(configs: ReferenceListConfig[], pageName: string) {
  const mapping: Record<string, string> = {};
  
  configs.forEach(config => {
    if (config.linkedPages.includes(pageName)) {
      const fieldName = config.fieldMapping[pageName];
      if (fieldName) {
        mapping[fieldName] = config.listName;
      }
    }
  });
  
  return mapping;
}
