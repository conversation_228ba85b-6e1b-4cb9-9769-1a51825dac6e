// ===== REFERENCE DATA STORE =====
// Author: Augment Code
// Description: Centralized state management for reference data using Zustand

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// API service for reference data
const referenceAPI = {
  async getLists() {
    const response = await fetch('/api/reference-lists');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  },

  async getListItems(listName) {
    const response = await fetch(`/api/reference-data/${listName}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  },

  async addListItem(listName, item) {
    const response = await fetch(`/api/reference-data/${listName}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(item),
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  },

  async updateListItem(listName, itemId, item) {
    const response = await fetch(`/api/reference-data/${listName}/${itemId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(item),
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  },

  async deleteListItem(listName, itemId) {
    const response = await fetch(`/api/reference-data/${listName}/${itemId}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }
};

// Initial state
const initialState = {
  // Reference lists configuration
  lists: {},
  
  // Reference data items organized by list name
  data: {},
  
  // Loading states for each list
  loading: {},
  
  // Error states for each list
  errors: {},
  
  // Global loading state
  isInitializing: false,
  
  // Global error state
  globalError: null,
  
  // Cache timestamps
  lastFetched: {},
  
  // Status flags
  isInitialized: false
};

// Create the reference data store
export const useReferenceDataStore = create()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Actions
        actions: {
          // Initialize the store by fetching all reference lists
          async initialize() {
            const state = get();
            if (state.isInitializing || state.isInitialized) return;

            set((state) => {
              state.isInitializing = true;
              state.globalError = null;
            });

            try {
              const lists = await referenceAPI.getLists();
              
              set((state) => {
                state.lists = lists.reduce((acc, list) => {
                  acc[list.listName] = list;
                  return acc;
                }, {});
                state.isInitializing = false;
                state.isInitialized = true;
                state.globalError = null;
              });

              // Fetch data for all lists
              const listNames = Object.keys(get().lists);
              await Promise.allSettled(
                listNames.map(listName => get().actions.fetchListData(listName))
              );

            } catch (error) {
              console.error('Failed to initialize reference data:', error);
              
              set((state) => {
                state.isInitializing = false;
                state.globalError = error.message || 'فشل في تحميل البيانات المرجعية';
              });

              throw error;
            }
          },

          // Fetch data for a specific list
          async fetchListData(listName) {
            const state = get();
            if (state.loading[listName]) return; // Prevent multiple simultaneous requests

            set((state) => {
              state.loading[listName] = true;
              state.errors[listName] = null;
            });

            try {
              const items = await referenceAPI.getListItems(listName);
              
              set((state) => {
                state.data[listName] = items;
                state.loading[listName] = false;
                state.lastFetched[listName] = new Date().toISOString();
                state.errors[listName] = null;
              });

              return items;
            } catch (error) {
              console.error(`Failed to fetch ${listName} data:`, error);
              
              set((state) => {
                state.loading[listName] = false;
                state.errors[listName] = error.message || `فشل في تحميل ${listName}`;
              });

              throw error;
            }
          },

          // Add new item to a list
          async addItem(listName, item) {
            try {
              const newItem = await referenceAPI.addListItem(listName, item);
              
              set((state) => {
                if (!state.data[listName]) {
                  state.data[listName] = [];
                }
                state.data[listName].push(newItem);
              });

              return newItem;
            } catch (error) {
              console.error(`Failed to add item to ${listName}:`, error);
              throw error;
            }
          },

          // Update an existing item in a list
          async updateItem(listName, itemId, updates) {
            try {
              const updatedItem = await referenceAPI.updateListItem(listName, itemId, updates);
              
              set((state) => {
                if (state.data[listName]) {
                  const index = state.data[listName].findIndex(item => item.id === itemId);
                  if (index !== -1) {
                    state.data[listName][index] = updatedItem;
                  }
                }
              });

              return updatedItem;
            } catch (error) {
              console.error(`Failed to update item in ${listName}:`, error);
              throw error;
            }
          },

          // Delete an item from a list
          async deleteItem(listName, itemId) {
            try {
              await referenceAPI.deleteListItem(listName, itemId);
              
              set((state) => {
                if (state.data[listName]) {
                  state.data[listName] = state.data[listName].filter(item => item.id !== itemId);
                }
              });

              return true;
            } catch (error) {
              console.error(`Failed to delete item from ${listName}:`, error);
              throw error;
            }
          },

          // Get items for a specific list
          getListItems(listName) {
            const state = get();
            return state.data[listName] || [];
          },

          // Get a specific item by ID
          getItem(listName, itemId) {
            const state = get();
            const items = state.data[listName] || [];
            return items.find(item => item.id === itemId);
          },

          // Search items in a list
          searchItems(listName, searchTerm) {
            const state = get();
            const items = state.data[listName] || [];
            
            if (!searchTerm) return items;
            
            const term = searchTerm.toLowerCase();
            return items.filter(item => 
              item.itemLabel?.toLowerCase().includes(term) ||
              item.itemValue?.toLowerCase().includes(term)
            );
          },

          // Get list configuration
          getListConfig(listName) {
            const state = get();
            return state.lists[listName];
          },

          // Check if a list is loading
          isListLoading(listName) {
            const state = get();
            return state.loading[listName] || false;
          },

          // Get error for a specific list
          getListError(listName) {
            const state = get();
            return state.errors[listName];
          },

          // Clear error for a specific list
          clearListError(listName) {
            set((state) => {
              state.errors[listName] = null;
            });
          },

          // Clear all errors
          clearAllErrors() {
            set((state) => {
              state.errors = {};
              state.globalError = null;
            });
          },

          // Refresh a specific list
          async refreshList(listName) {
            return get().actions.fetchListData(listName);
          },

          // Refresh all lists
          async refreshAll() {
            const state = get();
            const listNames = Object.keys(state.lists);
            
            await Promise.allSettled(
              listNames.map(listName => get().actions.fetchListData(listName))
            );
          },

          // Get lists that are required but empty
          getMissingRequiredLists() {
            const state = get();
            const missingLists = [];

            Object.entries(state.lists).forEach(([listName, config]) => {
              if (config.isRequired) {
                const items = state.data[listName] || [];
                if (items.length === 0) {
                  missingLists.push({
                    listName,
                    displayName: config.displayName,
                    description: config.description
                  });
                }
              }
            });

            return missingLists;
          },

          // Check if all required lists have data
          areRequiredListsComplete() {
            return get().actions.getMissingRequiredLists().length === 0;
          },

          // Get statistics about the reference data
          getStatistics() {
            const state = get();
            const stats = {
              totalLists: Object.keys(state.lists).length,
              loadedLists: Object.keys(state.data).length,
              totalItems: 0,
              requiredLists: 0,
              missingRequiredLists: 0
            };

            Object.values(state.lists).forEach(list => {
              if (list.isRequired) {
                stats.requiredLists++;
              }
            });

            Object.values(state.data).forEach(items => {
              stats.totalItems += items.length;
            });

            stats.missingRequiredLists = get().actions.getMissingRequiredLists().length;

            return stats;
          }
        }
      })),
      {
        name: 'reference-data-store',
        partialize: (state) => ({
          lists: state.lists,
          data: state.data,
          lastFetched: state.lastFetched,
          isInitialized: state.isInitialized
        })
      }
    ),
    {
      name: 'reference-data-store'
    }
  )
);

// Selectors for easy access to specific parts of the state
export const useReferenceLists = () => useReferenceDataStore((state) => state.lists);
export const useReferenceData = () => useReferenceDataStore((state) => state.data);
export const useReferenceLoading = () => useReferenceDataStore((state) => state.loading);
export const useReferenceErrors = () => useReferenceDataStore((state) => state.errors);
export const useReferenceActions = () => useReferenceDataStore((state) => state.actions);
export const useReferenceGlobalError = () => useReferenceDataStore((state) => state.globalError);
export const useIsReferenceInitialized = () => useReferenceDataStore((state) => state.isInitialized);

// Specific list selectors
export const useListItems = (listName) => useReferenceDataStore((state) => 
  state.data[listName] || []
);

export const useListLoading = (listName) => useReferenceDataStore((state) => 
  state.loading[listName] || false
);

export const useListError = (listName) => useReferenceDataStore((state) => 
  state.errors[listName]
);

export const useListConfig = (listName) => useReferenceDataStore((state) => 
  state.lists[listName]
);

export default useReferenceDataStore;
