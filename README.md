# نظام إدارة العقود - Contract Management System

نظام شامل لإدارة العقود والعملاء والمدفوعات مبني بتقنيات حديثة.

## 🚀 التشغيل السريع - Quick Start

### الطريقة الأولى: التشغيل المباشر
```bash
# انقر مرتين على ملف run.bat أو استخدم الأمر التالي
.\run.bat
```

### الطريقة الثانية: الأمر المباشر
```bash
npm run dev:full
```

## 🌐 روابط الوصول - Access URLs

- **الواجهة الأمامية**: http://localhost:5000
- **واجهة برمجة التطبيقات**: http://localhost:5001

## ✅ المشاكل المحلولة - Fixed Issues

- ✅ تم حل مشكلة تضارب المنافذ (السيرفر يعمل الآن على المنفذ 5001)
- ✅ تم إصلاح مشاكل بدء تشغيل السيرفر
- ✅ تم إضافة طرق متعددة للتشغيل
- ✅ تم تحسين معالجة الأخطاء

**ملاحظة هامة:** تم تحديث هذا الملف ليعكس البنية الحالية للمشروع التي تستخدم خادمًا بسيطًا مع تخزين البيانات في SQLite.

## المتطلبات الأساسية

- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn

## التشغيل السريع

### 1. تشغيل الخادم الخلفي (Backend)
```bash
# تثبيت المتطلبات (مرة واحدة فقط)
npm install

# تشغيل الخادم
npm run dev:server
# أو
node server-simple.cjs
```

### Linux/Mac
```bash
# إعطاء صلاحية التنفيذ للملف
chmod +x start.sh

# تشغيل الملف
./start.sh
```

### التشغيل اليدوي
```bash
# تثبيت المتطلبات
npm install

# تشغيل الخادم في وضع التطوير
npm run dev

# أو تشغيل الخادم في وضع الإنتاج
npm run build
npm start
```

## إعداد قاعدة البيانات

### استخدام PostgreSQL (موصى به للإنتاج)
1. قم بإنشاء قاعدة بيانات PostgreSQL
2. قم بتعيين متغير البيئة `DATABASE_URL`:
```bash
export DATABASE_URL="postgresql://username:password@localhost:5432/contract_management"
```

### استخدام التخزين في الذاكرة (للتطوير والاختبار)
إذا لم يتم تعيين `DATABASE_URL`، سيستخدم النظام التخزين في الذاكرة تلقائياً.

## الوصول للنظام

بعد التشغيل، يمكن الوصول للنظام عبر:
- **الرابط**: http://localhost:5000
- **المنفذ**: 5000

## الميزات الرئيسية

### إدارة العملاء
- إضافة وتعديل وحذف العملاء
- دعم العملاء الأفراد والشركات
- تتبع البيانات المالية والقانونية

### إدارة العقود
- إنشاء عقود جديدة
- تتبع حالة العقود (نشط، منتهي، ملغي، معلق)
- ربط العقود بالعملاء
- حساب القيم المالية تلقائياً

### إدارة المدفوعات
- تتبع الأقساط والمدفوعات
- تحديد المدفوعات المتأخرة
- دعم طرق دفع متعددة
- حساب الرسوم المتأخرة

### التقارير والإحصائيات
- لوحة تحكم شاملة
- تقارير مالية
- إحصائيات العملاء والعقود

### البيانات المرجعية
- إدارة أنواع العملاء
- إدارة أنواع العقود
- إدارة طرق الدفع
- إعدادات النظام

## البنية التقنية

### الواجهة الأمامية (Frontend)
- **React 18** مع TypeScript
- **Vite** كأداة البناء
- **Tailwind CSS** للتصميم
- **Radix UI** للمكونات
- **React Query** لإدارة البيانات
- **Wouter** للتوجيه

### الواجهة الخلفية (Backend)
- **Node.js** مع **Express.js**
- **TypeScript** مع دعم ESM
- **Drizzle ORM** لقاعدة البيانات
- **PostgreSQL** كقاعدة بيانات رئيسية
- **Zod** للتحقق من البيانات

### قاعدة البيانات
- **PostgreSQL** (الإنتاج)
- **In-Memory Storage** (التطوير)
- **Drizzle ORM** مع TypeScript

## الأوامر المتاحة

```bash
# تشغيل الخادم في وضع التطوير
npm run dev

# بناء المشروع للإنتاج
npm run build

# تشغيل الخادم في وضع الإنتاج
npm start

# فحص الأنواع
npm run check

# دفع تغييرات قاعدة البيانات
npm run db:push
```

## استكشاف الأخطاء

### المشاكل الشائعة

1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من صحة `DATABASE_URL`
   - تأكد من تشغيل خادم PostgreSQL

2. **خطأ في تثبيت المتطلبات**
   - احذف مجلد `node_modules` و `package-lock.json`
   - قم بتشغيل `npm install` مرة أخرى

3. **المنفذ 5000 مستخدم**
   - أوقف أي تطبيق يستخدم المنفذ 5000
   - أو غير المنفذ في ملف `server/index.ts`

## المساهمة

نرحب بالمساهمات! يرجى:
1. إنشاء fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.
