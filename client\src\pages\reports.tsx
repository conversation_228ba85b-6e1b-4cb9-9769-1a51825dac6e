// Integrated Reports Page - صفحة التقارير المتكاملة
// تجمع جميع أنواع التقارير (العامة والمالية) في مكان واحد مترابط مع النظام

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { 
  BarChart3, 
  TrendingUp, 
  Download, 
  Calendar,
  DollarSign,
  FileText,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Filter,
  <PERSON><PERSON>hart,
  Printer,
  Eye,
  ArrowRight,
  CheckCircle,
  Clock,
  CreditCard,
  AlertTriangle
} from "lucide-react";
import { formatCurrency, formatNumber, formatDate } from "@/lib/formatters";
import { useLanguage } from "@/hooks/use-language";
import { useDateFormat } from "@/hooks/use-date-format";
import { useSettingsFormatters } from "@/hooks/use-settings";
import labels from "@/lib/i18n";
import type { DashboardStats } from "@shared/schema";

export default function Reports() {
  const { language } = useLanguage();
  const t = labels[language];
  const { formatCurrency: formatCurrencyHook } = useSettingsFormatters();
  const { formatDate: formatDateHook } = useDateFormat();

  // State for filters
  const [selectedReport, setSelectedReport] = useState("overview");
  const [selectedPeriod, setSelectedPeriod] = useState("thisMonth");
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());

  // Data queries
  const { data: stats, isLoading: statsLoading } = useQuery<DashboardStats>({
    queryKey: ['/api/dashboard/stats'],
  });

  const { data: clients, isLoading: clientsLoading } = useQuery({
    queryKey: ['/api/clients'],
  });

  const { data: contracts, isLoading: contractsLoading } = useQuery({
    queryKey: ['/api/contracts'],
  });

  const { data: receivables, isLoading: receivablesLoading } = useQuery({
    queryKey: ['/api/receivables'],
    select: (data) => data?.receivables || [],
  });

  const { data: payments, isLoading: paymentsLoading } = useQuery({
    queryKey: ['/api/payments'],
  });

  // Report types
  const reportTypes = [
    { value: "overview", label: "نظرة عامة" },
    { value: "financial", label: "التقارير المالية" },
    { value: "clients", label: "تقارير العملاء" },
    { value: "contracts", label: "تقارير العقود" },
    { value: "receivables", label: "تقارير الاستحقاقات" },
    { value: "payments", label: "تقارير المدفوعات" }
  ];

  const periods = [
    { value: "today", label: "اليوم" },
    { value: "thisWeek", label: "هذا الأسبوع" },
    { value: "thisMonth", label: "هذا الشهر" },
    { value: "thisQuarter", label: "هذا الربع" },
    { value: "thisYear", label: "هذا العام" },
    { value: "custom", label: "فترة مخصصة" }
  ];

  const handleExportReport = (format: 'pdf' | 'excel') => {
    console.log(`Exporting report as ${format}`);
    // TODO: Implement export functionality
  };

  const handlePrintReport = () => {
    window.print();
  };

  // Calculate integrated metrics
  const integratedMetrics = {
    totalClients: clients?.length || 0,
    activeContracts: contracts?.filter((c: any) => c.contractStatus === 'نشط').length || 0,
    totalContractValue: contracts?.reduce((sum: number, c: any) => sum + (c.totalContractValue || 0), 0) || 0,
    overdueReceivables: receivables?.filter((r: any) => r.status === 'متأخر').length || 0,
    paidReceivables: receivables?.filter((r: any) => r.status === 'مدفوع').length || 0,
    totalReceivables: receivables?.length || 0,
    todayPayments: payments?.filter((p: any) => {
      const today = new Date().toISOString().split('T')[0];
      return p.paymentDate?.startsWith(today);
    }).length || 0,
    monthlyRevenue: payments?.reduce((sum: number, p: any) => {
      const paymentDate = new Date(p.paymentDate);
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      if (paymentDate.getMonth() === currentMonth && paymentDate.getFullYear() === currentYear) {
        return sum + (p.amount || 0);
      }
      return sum;
    }, 0) || 0
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl flex items-center gap-2">
                <BarChart3 className="h-6 w-6" />
                التقارير المتكاملة
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                تقارير شاملة ومترابطة لجميع أجزاء النظام
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => handleExportReport('pdf')} className="gap-2">
                <Download className="h-4 w-4" />
                تصدير PDF
              </Button>
              <Button variant="outline" onClick={() => handleExportReport('excel')} className="gap-2">
                <Download className="h-4 w-4" />
                تصدير Excel
              </Button>
              <Button variant="outline" onClick={handlePrintReport} className="gap-2">
                <Printer className="h-4 w-4" />
                طباعة
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Report Filters */}
      <Card>
        <CardContent className="p-6 bg-muted/30">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">نوع التقرير</label>
              <Select value={selectedReport} onValueChange={setSelectedReport}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {reportTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">الفترة الزمنية</label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {periods.map((period) => (
                    <SelectItem key={period.value} value={period.value}>
                      {period.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">السنة</label>
              <Select value={selectedYear} onValueChange={setSelectedYear}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 5 }, (_, i) => {
                    const year = new Date().getFullYear() - i;
                    return (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Integrated Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">إجمالي العملاء</p>
                <p className="text-2xl font-bold">{formatNumber(integratedMetrics.totalClients)}</p>
                <div className="flex items-center gap-1">
                  <span className="text-xs text-green-500">+12%</span>
                  <span className="text-xs text-muted-foreground">من الشهر الماضي</span>
                </div>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">العقود النشطة</p>
                <p className="text-2xl font-bold">{formatNumber(integratedMetrics.activeContracts)}</p>
                <div className="flex items-center gap-1">
                  <span className="text-xs text-green-500">+8%</span>
                  <span className="text-xs text-muted-foreground">من الشهر الماضي</span>
                </div>
              </div>
              <FileText className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">إجمالي قيمة العقود</p>
                <p className="text-2xl font-bold">{formatCurrencyHook(integratedMetrics.totalContractValue)}</p>
                <div className="flex items-center gap-1">
                  <span className="text-xs text-green-500">+15%</span>
                  <span className="text-xs text-muted-foreground">من الشهر الماضي</span>
                </div>
              </div>
              <DollarSign className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">الإيرادات الشهرية</p>
                <p className="text-2xl font-bold">{formatCurrencyHook(integratedMetrics.monthlyRevenue)}</p>
                <div className="flex items-center gap-1">
                  <span className="text-xs text-green-500">+22%</span>
                  <span className="text-xs text-muted-foreground">من الشهر الماضي</span>
                </div>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Reports Tabs */}
      <Tabs value={selectedReport} onValueChange={setSelectedReport} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="financial">مالية</TabsTrigger>
          <TabsTrigger value="clients">عملاء</TabsTrigger>
          <TabsTrigger value="contracts">عقود</TabsTrigger>
          <TabsTrigger value="receivables">استحقاقات</TabsTrigger>
          <TabsTrigger value="payments">مدفوعات</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  توزيع الاستحقاقات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>مدفوع</span>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="font-medium">{integratedMetrics.paidReceivables}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>متأخر</span>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span className="font-medium">{integratedMetrics.overdueReceivables}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>مستحق</span>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <span className="font-medium">
                        {integratedMetrics.totalReceivables - integratedMetrics.paidReceivables - integratedMetrics.overdueReceivables}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  الأداء الشهري
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-48 flex items-center justify-center bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <LineChart className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600">مخطط الأداء الشهري</p>
                    <p className="text-sm text-gray-500">سيتم إضافة الرسوم البيانية التفاعلية</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Financial Tab */}
        <TabsContent value="financial" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>الملخص المالي</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>إجمالي الإيرادات</span>
                    <span className="font-semibold text-green-600">
                      {formatCurrencyHook(integratedMetrics.monthlyRevenue)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>قيمة العقود النشطة</span>
                    <span className="font-semibold">
                      {formatCurrencyHook(integratedMetrics.totalContractValue)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>معدل التحصيل</span>
                    <span className="font-semibold text-blue-600">
                      {integratedMetrics.totalReceivables > 0
                        ? Math.round((integratedMetrics.paidReceivables / integratedMetrics.totalReceivables) * 100)
                        : 0}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>تحليل الاستحقاقات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>إجمالي الاستحقاقات</span>
                    <span className="font-semibold">{integratedMetrics.totalReceivables}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>مدفوع</span>
                    <span className="font-semibold text-green-600">{integratedMetrics.paidReceivables}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>متأخر</span>
                    <span className="font-semibold text-red-600">{integratedMetrics.overdueReceivables}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>مؤشرات الأداء</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>متوسط قيمة العقد</span>
                    <span className="font-semibold">
                      {formatCurrencyHook(
                        integratedMetrics.activeContracts > 0
                          ? integratedMetrics.totalContractValue / integratedMetrics.activeContracts
                          : 0
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>مدفوعات اليوم</span>
                    <span className="font-semibold text-blue-600">{integratedMetrics.todayPayments}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>نمو العملاء</span>
                    <span className="font-semibold text-green-600">+12%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Clients Tab */}
        <TabsContent value="clients" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                تقرير العملاء المفصل
              </CardTitle>
            </CardHeader>
            <CardContent>
              {clientsLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                      <Skeleton className="h-6 w-20" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {clients?.slice(0, 10).map((client: any) => {
                    const clientContracts = contracts?.filter((c: any) => c.clientId === client.id) || [];
                    const clientReceivables = receivables?.filter((r: any) => r.clientId === client.id) || [];

                    return (
                      <div key={client.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <Users className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium">{client.clientName}</p>
                            <p className="text-sm text-muted-foreground">
                              {client.clientType} • {clientContracts.length} عقد • {clientReceivables.length} استحقاق
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">
                            {formatCurrencyHook(
                              clientContracts.reduce((sum: number, c: any) => sum + (c.totalContractValue || 0), 0)
                            )}
                          </p>
                          <Badge variant={clientContracts.length > 0 ? "default" : "secondary"}>
                            {clientContracts.length > 0 ? "نشط" : "جديد"}
                          </Badge>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Contracts Tab */}
        <TabsContent value="contracts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                تقرير العقود المفصل
              </CardTitle>
            </CardHeader>
            <CardContent>
              {contractsLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {contracts?.slice(0, 10).map((contract: any) => (
                    <div key={contract.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          <FileText className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium">{contract.contractNumber}</p>
                          <p className="text-sm text-muted-foreground">
                            {contract.clientName} • {contract.contractType}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrencyHook(contract.totalContractValue || 0)}</p>
                        <Badge variant={
                          contract.contractStatus === 'نشط' ? 'default' :
                          contract.contractStatus === 'منتهي' ? 'destructive' :
                          'secondary'
                        }>
                          {contract.contractStatus}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Receivables Tab */}
        <TabsContent value="receivables" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                تقرير الاستحقاقات المفصل
              </CardTitle>
            </CardHeader>
            <CardContent>
              {receivablesLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {receivables?.slice(0, 10).map((receivable: any) => (
                    <div key={receivable.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                      <div className="flex items-center gap-3">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                          receivable.status === 'مدفوع' ? 'bg-green-100' :
                          receivable.status === 'متأخر' ? 'bg-red-100' :
                          receivable.status === 'مستحق' ? 'bg-yellow-100' :
                          'bg-blue-100'
                        }`}>
                          <DollarSign className={`h-5 w-5 ${
                            receivable.status === 'مدفوع' ? 'text-green-600' :
                            receivable.status === 'متأخر' ? 'text-red-600' :
                            receivable.status === 'مستحق' ? 'text-yellow-600' :
                            'text-blue-600'
                          }`} />
                        </div>
                        <div>
                          <p className="font-medium">{receivable.contractNumber}</p>
                          <p className="text-sm text-muted-foreground">
                            {receivable.clientName} • {formatDateHook(receivable.dueDate)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrencyHook(receivable.amount || 0)}</p>
                        <Badge variant={
                          receivable.status === 'مدفوع' ? 'default' :
                          receivable.status === 'متأخر' ? 'destructive' :
                          receivable.status === 'مستحق' ? 'secondary' :
                          'outline'
                        }>
                          {receivable.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payments Tab */}
        <TabsContent value="payments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                تقرير المدفوعات المفصل
              </CardTitle>
            </CardHeader>
            <CardContent>
              {paymentsLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {payments?.slice(0, 10).map((payment: any) => (
                    <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          <CreditCard className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium">دفعة #{payment.id}</p>
                          <p className="text-sm text-muted-foreground">
                            {payment.contractNumber} • {formatDateHook(payment.paymentDate)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrencyHook(payment.amount || 0)}</p>
                        <Badge variant="default">
                          {payment.paymentMethod || 'نقدي'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
