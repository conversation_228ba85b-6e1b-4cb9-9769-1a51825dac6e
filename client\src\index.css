/* Custom fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --base-font-size: 13px;
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;

  /* Chart colors */
  --chart-1: hsl(207, 90%, 54%);
  --chart-2: hsl(142, 76%, 36%);
  --chart-3: hsl(45, 93%, 47%);
  --chart-4: hsl(10, 79%, 63%);
  --chart-5: hsl(271, 81%, 56%);

  /* Sidebar colors */
  --sidebar-background: hsl(0, 0%, 100%);
  --sidebar-foreground: hsl(20, 14.3%, 4.1%);
  --sidebar-primary: hsl(207, 90%, 54%);
  --sidebar-primary-foreground: hsl(211, 100%, 99%);
  --sidebar-accent: hsl(60, 4.8%, 95.9%);
  --sidebar-accent-foreground: hsl(24, 9.8%, 10%);
  --sidebar-border: hsl(20, 5.9%, 90%);
  --sidebar-ring: hsl(20, 14.3%, 4.1%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --base-font-size: 13px;

  /* Sidebar colors dark */
  --sidebar-background: hsl(240, 10%, 3.9%);
  --sidebar-foreground: hsl(0, 0%, 98%);
  --sidebar-primary: hsl(207, 90%, 54%);
  --sidebar-primary-foreground: hsl(211, 100%, 99%);
  --sidebar-accent: hsl(240, 3.7%, 15.9%);
  --sidebar-accent-foreground: hsl(0, 0%, 98%);
  --sidebar-border: hsl(240, 3.7%, 15.9%);
  --sidebar-ring: hsl(240, 4.9%, 83.9%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-size: var(--base-font-size, 16px);
    direction: rtl;
    text-align: right;
  }

  /* Apply font size scaling to common elements */
  h1 { font-size: calc(var(--base-font-size, 16px) * 2); }
  h2 { font-size: calc(var(--base-font-size, 16px) * 1.75); }
  h3 { font-size: calc(var(--base-font-size, 16px) * 1.5); }
  h4 { font-size: calc(var(--base-font-size, 16px) * 1.25); }
  h5 { font-size: calc(var(--base-font-size, 16px) * 1.125); }
  h6 { font-size: var(--base-font-size, 16px); }

  /* RTL Support */
  [dir="rtl"] {
    font-family: "Cairo", sans-serif;
  }

  [dir="ltr"] {
    font-family: "Inter", sans-serif;
  }

  /* RTL Text Alignment */
  [dir="rtl"] .text-justify {
    text-align: justify;
    text-align-last: right;
  }

  [dir="ltr"] .text-justify {
    text-align: justify;
    text-align-last: left;
  }

  /* RTL Flex Direction */
  [dir="rtl"] .flex-row-reverse {
    flex-direction: row-reverse;
  }

  [dir="ltr"] .flex-row {
    flex-direction: row;
  }

  /* RTL Spacing */
  [dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(var(--tw-space-x) * var(--tw-space-x-reverse));
    margin-left: calc(var(--tw-space-x) * calc(1 - var(--tw-space-x-reverse)));
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: var(--muted);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--muted-foreground);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--foreground);
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .print-break {
      page-break-before: always;
    }
    
    .print-avoid {
      page-break-inside: avoid;
    }
  }
}

/* Custom utility classes */
@layer utilities {
  .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(var(--tw-space-x) * var(--tw-space-x-reverse));
    margin-left: calc(var(--tw-space-x) * calc(1 - var(--tw-space-x-reverse)));
  }

  .text-balance {
    text-wrap: balance;
  }
}

/* Custom Scrollbar */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: hsl(var(--border));
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground));
}

.scrollbar-thumb-sidebar-border::-webkit-scrollbar-thumb {
  background-color: hsl(var(--sidebar-border, var(--border)));
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background: transparent;
}

/* Loading animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}



/* Print Styles - نفس أسلوب البرنامج القديم */
@media print {
  .client-attachment-image {
    max-width: 200px !important;
    max-height: 150px !important;
    page-break-inside: avoid;
  }
  .print-hide {
    display: none !important;
  }
}

/* RTL/LTR Direction Support */
.tabs-list[dir="rtl"] {
  direction: rtl;
}

.tabs-list[dir="ltr"] {
  direction: ltr;
}

/* Ensure proper tab order for RTL */
.tabs-list[dir="rtl"] {
  flex-direction: row-reverse;
}

.tabs-list[dir="ltr"] {
  flex-direction: row;
}

/* Force RTL layout for Arabic */
.rtl-tabs {
  direction: rtl !important;
}

.ltr-tabs {
  direction: ltr !important;
}

/* Force flex direction for tabs */
.tabs-container-rtl {
  display: flex !important;
  flex-direction: row-reverse !important;
  direction: rtl !important;
}

.tabs-container-ltr {
  display: flex !important;
  flex-direction: row !important;
  direction: ltr !important;
}

/* Navigation buttons RTL support */
[dir="rtl"] .navigation-buttons {
  flex-direction: row-reverse;
}

[dir="rtl"] .navigation-buttons .previous-btn {
  order: 2;
}

[dir="rtl"] .navigation-buttons .next-btn {
  order: 1;
}

/* RTL Date Input Styles */
input[type="date"].text-right {
  text-align: right !important;
  direction: rtl !important;
  unicode-bidi: plaintext !important;
}

input[type="date"].text-right::-webkit-datetime-edit {
  text-align: right !important;
  direction: rtl !important;
  unicode-bidi: plaintext !important;
}

input[type="date"].text-right::-webkit-datetime-edit-fields-wrapper {
  text-align: right !important;
  direction: rtl !important;
  unicode-bidi: plaintext !important;
}

input[type="date"].text-right::-webkit-datetime-edit-text {
  text-align: right !important;
  direction: rtl !important;
  unicode-bidi: plaintext !important;
}

input[type="date"].text-right::-webkit-datetime-edit-month-field,
input[type="date"].text-right::-webkit-datetime-edit-day-field,
input[type="date"].text-right::-webkit-datetime-edit-year-field {
  text-align: right !important;
  direction: rtl !important;
  unicode-bidi: plaintext !important;
}

/* Firefox RTL Date Input */
input[type="date"].text-right::-moz-placeholder {
  text-align: right !important;
  direction: rtl !important;
  unicode-bidi: plaintext !important;
}

/* General RTL Text Input */
.text-right {
  text-align: right !important;
  direction: rtl !important;
  unicode-bidi: plaintext !important;
}

/* RTL Date Picker Calendar */
.react-datepicker.custom-datepicker[dir="rtl"] {
  direction: rtl !important;
}

.react-datepicker.custom-datepicker[dir="rtl"] .react-datepicker__header {
  text-align: right !important;
}

.react-datepicker.custom-datepicker[dir="rtl"] .react-datepicker__current-month {
  text-align: center !important;
}

/* Custom Scrollbar */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: hsl(var(--sidebar-border));
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--sidebar-foreground) / 0.3);
}

/* Sidebar hover animations */
.sidebar-item-tooltip {
  transform: translateX(-50%);
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}

/* RTL specific adjustments */
[dir="rtl"] .sidebar-item-tooltip {
  transform: translateX(50%);
}

[dir="rtl"] @keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(50%) scale(1);
  }
}

/* Arabic Numbers and Text Direction */
.arabic-numbers {
  direction: ltr;
  text-align: right;
  unicode-bidi: bidi-override;
}

/* Force numbers to display right-to-left in Arabic context */
input[type="number"],
input[type="tel"],
.currency-amount,
.number-display,
.amount-field,
.price-field,
.numeric-input {
  direction: ltr;
  text-align: right;
  unicode-bidi: bidi-override;
}

/* Ensure all text inputs are RTL by default */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
textarea,
.text-input {
  direction: rtl;
  text-align: right;
}

/* Form labels should be RTL */
label,
.form-label,
.field-label {
  direction: rtl;
  text-align: right;
}

/* Button text should be RTL */
button,
.btn {
  direction: rtl;
  text-align: center;
}

/* Card content should be RTL */
.card-content,
.card-header,
.card-title {
  direction: rtl;
  text-align: right;
}

/* Table content should be RTL */
table,
.table {
  direction: rtl;
}

table th,
table td,
.table-header,
.table-cell {
  text-align: right;
}

/* Grid layouts should be RTL */
.grid,
.flex {
  direction: rtl;
}

/* Dropdown and select content should be RTL */
.dropdown-content,
.select-content,
.menu-content {
  direction: rtl;
  text-align: right;
}

/* Modal and dialog content should be RTL */
.modal-content,
.dialog-content {
  direction: rtl;
  text-align: right;
}

/* Navigation items should be RTL */
.nav-item,
.menu-item,
.sidebar-item {
  direction: rtl;
  text-align: right;
}

/* Date inputs should also be LTR but right-aligned */
input[type="date"],
input[type="datetime-local"],
input[type="time"] {
  direction: ltr;
  text-align: right;
}

/* Table cells with numbers */
td.numeric,
th.numeric,
.table-number,
.stat-number,
.badge-number {
  direction: ltr;
  text-align: right;
  unicode-bidi: bidi-override;
}

/* Currency and percentage displays */
.currency,
.percentage,
.decimal {
  direction: ltr;
  text-align: right;
  unicode-bidi: bidi-override;
}

/* Ensure Arabic text flows correctly */
.arabic-text {
  direction: rtl;
  text-align: right;
}

/* Mixed content (Arabic text with numbers) */
.mixed-content {
  direction: rtl;
  text-align: right;
}

.mixed-content .number {
  direction: ltr;
  unicode-bidi: bidi-override;
  display: inline-block;
}

/* Fix for form labels and inputs */
.form-field {
  direction: rtl;
  text-align: right;
}

.form-field input[type="number"],
.form-field input[type="tel"] {
  direction: ltr;
  text-align: right;
}

/* Specific classes for common UI elements */
.receipt-number,
.contract-number,
.client-id,
.phone-number {
  direction: ltr;
  text-align: right;
  unicode-bidi: bidi-override;
}

/* Table headers and cells for numeric data */
.table-header-numeric,
.table-cell-numeric {
  direction: ltr;
  text-align: right;
  unicode-bidi: bidi-override;
}

/* Statistics and dashboard numbers */
.stat-value,
.dashboard-number,
.metric-value {
  direction: ltr;
  text-align: right;
  unicode-bidi: bidi-override;
  font-variant-numeric: tabular-nums;
}