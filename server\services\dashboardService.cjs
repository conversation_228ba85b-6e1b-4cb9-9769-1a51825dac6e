const database = require('../models/db.cjs');

class DashboardService {
  // جلب ملخص شامل للداشبورد في استعلام واحد - أسرع بكثير!
  async getDashboardSummary() {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      
      // استعلام واحد موحد لجميع البيانات
      const sql = `
        WITH 
        -- إحصائيات العملاء
        ClientStats AS (
          SELECT 
            COUNT(*) as totalClients,
            COUNT(CASE WHEN clientType = 'أفراد' THEN 1 END) as individualClients,
            COUNT(CASE WHEN clientType = 'شركات' THEN 1 END) as corporateClients
          FROM Clients 
          WHERE isActive = 1
        ),
        -- إحصائيات العقود
        ContractStats AS (
          SELECT 
            COUNT(*) as totalContracts,
            COUNT(CASE WHEN contractStatus = 'نشط' THEN 1 END) as activeContracts,
            SUM(totalContractValue) as totalValue,
            AVG(totalContractValue) as avgValue
          FROM Contracts 
          WHERE isActive = 1
        ),
        -- إحصائيات الاستحقاقات
        ReceivableStats AS (
          SELECT 
            COUNT(*) as totalReceivables,
            SUM(amount) as totalAmount,
            COUNT(CASE WHEN status = 'مستحق' THEN 1 END) as dueCount,
            SUM(CASE WHEN status = 'مستحق' THEN amount ELSE 0 END) as dueAmount,
            COUNT(CASE WHEN status = 'متأخر' THEN 1 END) as overdueCount,
            SUM(CASE WHEN status = 'متأخر' THEN amount ELSE 0 END) as overdueAmount
          FROM ContractReceivables 
          WHERE isActive = 1
        ),
        -- أحدث العقود
        RecentContracts AS (
          SELECT 
            c.id, c.contractNumber, c.contractSubject, c.totalContractValue,
            c.startDate, c.contractStatus, cl.clientName, cl.clientType,
            ROW_NUMBER() OVER (ORDER BY c.createdAt DESC) as rn
          FROM Contracts c
          LEFT JOIN Clients cl ON c.clientId = cl.id
          WHERE c.isActive = 1
        ),
        -- أحدث العملاء
        RecentClients AS (
          SELECT
            id, clientId, clientName, clientType, clientPhoneWhatsapp,
            createdAt,
            ROW_NUMBER() OVER (ORDER BY createdAt DESC) as rn
          FROM Clients
          WHERE isActive = 1
        ),
        -- أحدث الاستحقاقات
        RecentReceivables AS (
          SELECT
            cr.id, cr.receivableNumber, cr.dueDate, cr.amount, cr.status,
            cr.description, c.contractNumber, cl.clientName,
            ROW_NUMBER() OVER (ORDER BY cr.dueDate DESC) as rn
          FROM ContractReceivables cr
          LEFT JOIN Contracts c ON cr.contractId = c.id
          LEFT JOIN Clients cl ON cr.clientId = cl.id
          WHERE cr.isActive = 1
        ),
        -- المدفوعات المتأخرة
        OverduePayments AS (
          SELECT
            cr.id, cr.receivableNumber, cr.dueDate, cr.amount, cr.status,
            cr.description, c.contractNumber, cl.clientName,
            ROW_NUMBER() OVER (ORDER BY cr.dueDate ASC) as rn
          FROM ContractReceivables cr
          LEFT JOIN Contracts c ON cr.contractId = c.id
          LEFT JOIN Clients cl ON cr.clientId = cl.id
          WHERE cr.isActive = 1 AND cr.status = 'متأخر'
        )
        
        SELECT 
          -- إحصائيات العملاء
          cs.totalClients, cs.individualClients, cs.corporateClients,
          -- إحصائيات العقود  
          cos.totalContracts, cos.activeContracts, cos.totalValue, cos.avgValue,
          -- إحصائيات الاستحقاقات
          rs.totalReceivables, rs.totalAmount as receivablesTotalAmount, 
          rs.dueCount, rs.dueAmount, rs.overdueCount, rs.overdueAmount,
          -- أحدث العقود (JSON)
          (
            SELECT json_group_array(
              json_object(
                'id', id, 'contractNumber', contractNumber, 
                'contractSubject', contractSubject, 'totalContractValue', totalContractValue,
                'startDate', startDate, 'contractStatus', contractStatus,
                'clientName', clientName, 'clientType', clientType
              )
            )
            FROM RecentContracts WHERE rn <= 5
          ) as recentContractsJson,
          -- أحدث العملاء (JSON)
          (
            SELECT json_group_array(
              json_object(
                'id', id, 'clientId', clientId, 'clientName', clientName,
                'clientType', clientType, 'clientPhoneWhatsapp', clientPhoneWhatsapp,
                'createdAt', createdAt
              )
            )
            FROM RecentClients WHERE rn <= 5
          ) as recentClientsJson,
          -- أحدث الاستحقاقات (JSON)
          (
            SELECT json_group_array(
              json_object(
                'id', id, 'receivableNumber', receivableNumber, 'dueDate', dueDate,
                'amount', amount, 'status', status, 'description', description,
                'contractNumber', contractNumber, 'clientName', clientName
              )
            )
            FROM RecentReceivables WHERE rn <= 10
          ) as recentReceivablesJson,
          -- المدفوعات المتأخرة (JSON)
          (
            SELECT json_group_array(
              json_object(
                'id', id, 'receivableNumber', receivableNumber, 'dueDate', dueDate,
                'amount', amount, 'status', status, 'description', description,
                'contractNumber', contractNumber, 'clientName', clientName
              )
            )
            FROM OverduePayments WHERE rn <= 10
          ) as overduePaymentsJson
          
        FROM ClientStats cs, ContractStats cos, ReceivableStats rs
      `;
      
      db.get(sql, [], (err, row) => {
        if (err) {
          console.error('Dashboard summary error:', err);
          return reject(err);
        }
        
        try {
          // تحويل JSON strings إلى arrays
          const recentContracts = row.recentContractsJson ? JSON.parse(row.recentContractsJson) : [];
          const recentClients = row.recentClientsJson ? JSON.parse(row.recentClientsJson) : [];
          const recentReceivables = row.recentReceivablesJson ? JSON.parse(row.recentReceivablesJson) : [];
          const overduePayments = row.overduePaymentsJson ? JSON.parse(row.overduePaymentsJson) : [];
          
          const summary = {
            stats: {
              totalClients: row.totalClients || 0,
              individualClients: row.individualClients || 0,
              corporateClients: row.corporateClients || 0,
              totalContracts: row.totalContracts || 0,
              activeContracts: row.activeContracts || 0,
              totalContractValue: row.totalValue || 0,
              avgContractValue: row.avgValue || 0,
              totalReceivables: row.totalReceivables || 0,
              receivablesTotalAmount: row.receivablesTotalAmount || 0,
              dueReceivables: row.dueCount || 0,
              dueAmount: row.dueAmount || 0,
              overdueReceivables: row.overdueCount || 0,
              overdueAmount: row.overdueAmount || 0
            },
            recentContracts: recentContracts,
            recentClients: recentClients,
            recentReceivables: recentReceivables,
            overduePayments: overduePayments,
            lastUpdated: new Date().toISOString()
          };
          
          resolve(summary);
        } catch (parseError) {
          console.error('JSON parse error:', parseError);
          reject(parseError);
        }
      });
    });
  }

  // إحصائيات سريعة للداشبورد
  async getQuickStats() {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      
      const sql = `
        SELECT 
          (SELECT COUNT(*) FROM Clients WHERE isActive = 1) as totalClients,
          (SELECT COUNT(*) FROM Contracts WHERE isActive = 1) as totalContracts,
          (SELECT COUNT(*) FROM ContractReceivables WHERE isActive = 1 AND status = 'مستحق') as dueReceivables,
          (SELECT COUNT(*) FROM ContractReceivables WHERE isActive = 1 AND status = 'متأخر') as overdueReceivables
      `;
      
      db.get(sql, [], (err, row) => {
        if (err) return reject(err);
        resolve(row);
      });
    });
  }
}

module.exports = new DashboardService();
