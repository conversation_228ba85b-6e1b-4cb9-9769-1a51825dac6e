import React from "react";
import { useQuery } from "@tanstack/react-query";
import { useParams, useLocation } from "wouter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { useDateFormat } from "@/hooks/use-date-format";
import { 
  FileText, 
  ArrowLeft, 
  History,
  Eye,
  RefreshCw,
  Clock,
  User,
  Edit
} from "lucide-react";

interface ContractVersion {
  id: number;
  contractNumber: string;
  versionNumber: number;
  isCurrentVersion: boolean;
  editCount: number;
  editHistory: any[];
  editReason: string;
  createdAt: string;
  updatedAt: string;
  contractDescription: string;
  contractSubject: string;
  contractStatus: string;
}

export default function ContractVersions() {
  const { id } = useParams();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { formatDate } = useDateFormat();

  // Fetch contract versions
  const { data: versions = [], isLoading, error } = useQuery<ContractVersion[]>({
    queryKey: ['/api/contracts', id, 'versions'],
    queryFn: async () => {
      const response = await fetch(`/api/contracts/${id}/versions`);
      if (!response.ok) throw new Error('Failed to fetch contract versions');
      return response.json();
    },
    enabled: !!id
  });

  // Get current contract info
  const currentVersion = versions.find(v => v.isCurrentVersion);

  const handleBack = () => {
    setLocation(`/contracts/view/${id}`);
  };

  const handleViewVersion = (versionId: number) => {
    // سيتم تطوير عرض النسخة المحددة لاحقاً
    toast({
      title: "قريباً",
      description: "سيتم تطوير عرض النسخة المحددة",
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري تحميل النسخ السابقة...</p>
        </div>
      </div>
    );
  }

  if (error || !versions.length) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <History className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h2 className="text-xl font-semibold mb-2">لا توجد نسخ سابقة</h2>
          <p className="text-gray-600 mb-4">لم يتم العثور على نسخ سابقة لهذا العقد</p>
          <Button onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة لكشف حساب العقد
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 space-x-reverse">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة
          </Button>
          <div>
            <h1 className="text-2xl font-bold">النسخ السابقة للعقد</h1>
            <p className="text-gray-600">{currentVersion?.contractNumber}</p>
            <p className="text-sm text-gray-500">
              إجمالي النسخ: {versions.length} | عدد التعديلات: {currentVersion?.editCount || 0}
            </p>
          </div>
        </div>
      </div>

      {/* Versions List */}
      <div className="space-y-4">
        {versions.map((version, index) => (
          <Card key={version.id} className={`${version.isCurrentVersion ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">
                      {version.contractNumber}
                      {version.isCurrentVersion && (
                        <Badge variant="default" className="mr-2">النسخة الحالية</Badge>
                      )}
                    </CardTitle>
                    <p className="text-sm text-gray-600">
                      النسخة {version.versionNumber} | {version.contractStatus}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewVersion(version.id)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    عرض
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              {/* Version Info */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="h-4 w-4" />
                  <span>تاريخ الإنشاء: {formatDate(version.createdAt)}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Edit className="h-4 w-4" />
                  <span>آخر تحديث: {formatDate(version.updatedAt)}</span>
                </div>
              </div>

              {/* Edit Reason */}
              {version.editReason && (
                <div className="mb-4">
                  <h4 className="font-medium text-gray-800 mb-2">سبب التعديل:</h4>
                  <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                    {version.editReason}
                  </p>
                </div>
              )}

              {/* Edit History */}
              {version.editHistory && version.editHistory.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">تاريخ التعديلات:</h4>
                  <div className="space-y-2">
                    {version.editHistory.map((edit: any, editIndex: number) => (
                      <div key={editIndex} className="bg-gray-50 p-3 rounded-lg text-sm">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium text-gray-800">
                            {edit.editReason || 'تعديل عام'}
                          </span>
                          <span className="text-gray-500">
                            {formatDate(edit.editDate)}
                          </span>
                        </div>
                        {edit.editedBy && (
                          <div className="flex items-center gap-1 text-gray-600">
                            <User className="h-3 w-3" />
                            <span>بواسطة: {edit.editedBy}</span>
                          </div>
                        )}
                        {edit.previousVersion && (
                          <p className="text-gray-600 mt-1">
                            النسخة السابقة: {edit.previousVersion}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Contract Description */}
              {version.contractDescription && (
                <>
                  <Separator className="my-4" />
                  <div>
                    <h4 className="font-medium text-gray-800 mb-2">وصف العقد:</h4>
                    <p className="text-sm text-gray-600">
                      {version.contractDescription}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Summary Card */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <History className="h-5 w-5" />
            ملخص النسخ
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">{versions.length}</div>
              <div className="text-sm text-blue-800">إجمالي النسخ</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">{currentVersion?.editCount || 0}</div>
              <div className="text-sm text-green-800">عدد التعديلات</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {currentVersion?.versionNumber || 1}
              </div>
              <div className="text-sm text-purple-800">النسخة الحالية</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
