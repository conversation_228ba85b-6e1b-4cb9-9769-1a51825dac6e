import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, FileText, Building2, CheckCircle, XCircle, AlertCircle, Scale } from 'lucide-react';
import { useEnhancedToast } from "@/hooks/use-enhanced-toast";
import { useDateFormat } from "@/hooks/use-date-format";

interface Cheque {
  id: number;
  chequeNumber: string;
  contractId: number;
  clientId: number;
  receivableId?: number;
  bankName: string;
  chequeAmount: number;
  chequeDate: string;
  dueDate: string;
  status: string;
  receivedDate: string;
  statusDate?: string;
  entryNumber?: string;
  contractStatusAtReceipt?: string;
  shouldDepositToBank: boolean;
  bounceReason?: string;
  withdrawalReason?: string;
  notes?: string;
  clientName?: string;
  contractNumber?: string;
  contractSubject?: string;
  receivableNumber?: string;
  receivableDescription?: string;
}

interface Bank {
  value: string;
  label: string;
}

const ChequesManagementPage: React.FC = () => {
  const [cheques, setCheques] = useState<Cheque[]>([]);
  const [banks, setBanks] = useState<Bank[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('خزينة');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBank, setSelectedBank] = useState('all');
  const { showSuccess, showError } = useEnhancedToast();

  // Status configurations
  const statusConfig = {
    'خزينة': { 
      label: 'شيكات الخزينة', 
      icon: Building2, 
      color: 'bg-blue-100 text-blue-800',
      description: 'شيكات مستلمة تحتاج توزيع'
    },
    'عهدة': { 
      label: 'أوراق القبض', 
      icon: FileText, 
      color: 'bg-yellow-100 text-yellow-800',
      description: 'شيكات محتفظ بها كأوراق قبض'
    },
    'تحت_التحصيل': { 
      label: 'تحت التحصيل', 
      icon: Building2, 
      color: 'bg-purple-100 text-purple-800',
      description: 'شيكات مودعة بالبنوك'
    },
    'محصل': { 
      label: 'محصلة', 
      icon: CheckCircle, 
      color: 'bg-green-100 text-green-800',
      description: 'شيكات تم تحصيلها بنجاح'
    },
    'مسحوب': { 
      label: 'مسحوبة', 
      icon: XCircle, 
      color: 'bg-orange-100 text-orange-800',
      description: 'شيكات مسحوبة من البنك'
    },
    'مرتد': { 
      label: 'مرتدة', 
      icon: AlertCircle, 
      color: 'bg-red-100 text-red-800',
      description: 'شيكات مرتدة تحتاج معالجة'
    },
    'قانونية': { 
      label: 'قانونية', 
      icon: Scale, 
      color: 'bg-gray-100 text-gray-800',
      description: 'شيكات محولة للإدارة القانونية'
    }
  };

  useEffect(() => {
    fetchCheques();
    fetchBanks();
  }, [activeTab, searchTerm, selectedBank]);

  const fetchCheques = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (activeTab !== 'all') params.append('status', activeTab);
      if (searchTerm) params.append('search', searchTerm);
      if (selectedBank && selectedBank !== 'all') params.append('bankName', selectedBank);

      const response = await fetch(`/api/cheques?${params}`);
      if (!response.ok) throw new Error('Failed to fetch cheques');
      
      const data = await response.json();
      setCheques(data);
    } catch (error) {
      console.error('Error fetching cheques:', error);
      showError('خطأ في جلب بيانات الشيكات');
    } finally {
      setLoading(false);
    }
  };

  const fetchBanks = async () => {
    try {
      const response = await fetch('/api/cheques/banks');
      if (!response.ok) throw new Error('Failed to fetch banks');
      
      const data = await response.json();
      setBanks(data);
    } catch (error) {
      console.error('Error fetching banks:', error);
    }
  };

  const updateChequeStatus = async (chequeId: number, newStatus: string, entryNumber?: string, reason?: string) => {
    try {
      const response = await fetch(`/api/cheques/${chequeId}/status`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: newStatus,
          entryNumber,
          reason
        })
      });

      if (!response.ok) throw new Error('Failed to update cheque status');
      
      const result = await response.json();
      showSuccess(result.message);
      fetchCheques();
    } catch (error) {
      console.error('Error updating cheque status:', error);
      showError('خطأ في تحديث حالة الشيك');
    }
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return <Badge variant="secondary">{status}</Badge>;
    
    return (
      <Badge className={config.color}>
        <config.icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP'
    }).format(amount);
  };

  // استخدام hook التنسيق الموحد للتواريخ
  const { formatDate } = useDateFormat();

  const getChequesStats = () => {
    const stats = Object.keys(statusConfig).reduce((acc, status) => {
      acc[status] = cheques.filter(c => c.status === status).length;
      return acc;
    }, {} as Record<string, number>);
    
    return stats;
  };

  const stats = getChequesStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">مركز إدارة الشيكات</CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                إدارة شاملة لجميع الشيكات ومراحل التحصيل
              </p>
            </div>
            <div className="text-sm text-muted-foreground">
              <p>💡 لإضافة شيكات جديدة:</p>
              <p>استخدم تسجيل مدفوعات الخزينة → إيصال شيكات</p>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        {Object.entries(statusConfig).reverse().map(([status, config]) => (
          <Card key={status} className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => setActiveTab(status)}>
            <CardContent className="p-3">
              <div className="flex flex-col items-center text-center space-y-2">
                <config.icon className="w-6 h-6 text-muted-foreground" />
                <div className="text-xl font-bold">{stats[status] || 0}</div>
                <div className="text-xs text-muted-foreground">{config.label}</div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">فلاتر البحث</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="search">البحث</Label>
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  id="search"
                  placeholder="رقم الشيك، العقد، أو العميل..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>

            <div>
              <Label>البنك</Label>
              <Select value={selectedBank} onValueChange={setSelectedBank}>
                <SelectTrigger>
                  <SelectValue placeholder="جميع البنوك" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع البنوك</SelectItem>
                  {banks.map((bank) => (
                    <SelectItem key={bank.value} value={bank.value}>
                      {bank.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button variant="outline" onClick={() => {
                setSearchTerm('');
                setSelectedBank('all');
              }} className="w-full">
                <Filter className="w-4 h-4 mr-2" />
                تفريغ
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cheques Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-7 h-auto">
          {Object.entries(statusConfig).reverse().map(([status, config]) => (
            <TabsTrigger key={status} value={status} className="text-xs p-2 flex flex-col gap-1">
              <span>{config.label}</span>
              {stats[status] > 0 && (
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  {stats[status]}
                </Badge>
              )}
            </TabsTrigger>
          ))}
        </TabsList>

        {Object.entries(statusConfig).reverse().map(([status, config]) => (
          <TabsContent key={status} value={status}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <config.icon className="w-5 h-5" />
                  {config.label}
                  <Badge variant="outline">{stats[status] || 0} شيك</Badge>
                </CardTitle>
                <p className="text-sm text-gray-600">{config.description}</p>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">جاري التحميل...</div>
                ) : cheques.filter(c => c.status === status).length === 0 ? (
                  <div className="text-center py-8 space-y-3">
                    <FileText className="w-12 h-12 mx-auto text-muted-foreground" />
                    <h3 className="text-lg font-medium">لا توجد شيكات</h3>
                    <p className="text-muted-foreground">
                      لا توجد شيكات في حالة "{statusConfig[status]?.label}" حالياً
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {cheques.filter(c => c.status === status).map((cheque) => (
                      <Card key={cheque.id} className="hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start">
                            <div className="space-y-3 flex-1">
                              <div className="flex items-center gap-3">
                                <h3 className="font-medium">شيك رقم: {cheque.chequeNumber}</h3>
                                {getStatusBadge(cheque.status)}
                              </div>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm text-muted-foreground">
                                <div className="flex justify-between">
                                  <span>المبلغ:</span>
                                  <span className="font-medium">{formatCurrency(cheque.chequeAmount)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>البنك:</span>
                                  <span className="font-medium">{cheque.bankName}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>الاستحقاق:</span>
                                  <span className="font-medium">{formatDate(cheque.dueDate)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>العميل:</span>
                                  <span className="font-medium">{cheque.clientName}</span>
                                </div>
                              </div>
                              {cheque.contractNumber && (
                                <div className="text-sm text-muted-foreground">
                                  <span>العقد: </span>
                                  <span className="font-medium">{cheque.contractNumber} - {cheque.contractSubject}</span>
                                </div>
                              )}
                              {cheque.shouldDepositToBank && cheque.status === 'خزينة' && (
                                <Badge variant="outline" className="text-blue-600 w-fit">
                                  يُنصح بالإيداع بالبنك
                                </Badge>
                              )}
                            </div>

                            <div className="flex flex-col gap-2 ml-4">
                              {/* Action buttons based on status */}
                              {cheque.status === 'خزينة' && (
                                <div className="flex gap-2">
                                  <Button size="sm" variant="outline"
                                          onClick={() => updateChequeStatus(cheque.id, 'عهدة', 'ENT001')}>
                                    أوراق قبض
                                  </Button>
                                  <Button size="sm"
                                          onClick={() => updateChequeStatus(cheque.id, 'تحت_التحصيل', 'ENT002')}>
                                    إيداع بالبنك
                                  </Button>
                                </div>
                              )}

                              {cheque.status === 'تحت_التحصيل' && (
                                <div className="flex gap-2 flex-wrap">
                                  <Button size="sm" variant="outline"
                                          onClick={() => updateChequeStatus(cheque.id, 'محصل', 'ENT003')}>
                                    محصل
                                  </Button>
                                  <Button size="sm" variant="outline"
                                          onClick={() => updateChequeStatus(cheque.id, 'مسحوب', 'ENT004', 'تغيير شيكات')}>
                                    مسحوب
                                  </Button>
                                  <Button size="sm" variant="destructive"
                                          onClick={() => updateChequeStatus(cheque.id, 'مرتد', 'ENT005', 'رصيد غير كافي')}>
                                    مرتد
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default ChequesManagementPage;
