// ===== AUDIT TRAIL MIGRATION =====
// Author: Augment Code
// Description: Add audit trail functionality to track changes

const createAuditTables = (db) => {
  console.log('Creating audit trail tables...');

  // Audit log table
  db.run(`
    CREATE TABLE IF NOT EXISTS AuditLog (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      tableName TEXT NOT NULL,
      recordId INTEGER NOT NULL,
      action TEXT NOT NULL CHECK(action IN ('INSERT', 'UPDATE', 'DELETE')),
      oldValues TEXT, -- JSON string of old values
      newValues TEXT, -- JSON string of new values
      changedFields TEXT, -- JSON array of changed field names
      userId INTEGER,
      userIP TEXT,
      userAgent TEXT,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      sessionId TEXT
    )
  `);

  // Create indexes for audit log
  db.run(`CREATE INDEX IF NOT EXISTS idx_audit_table_record ON AuditLog(tableName, recordId)`);
  db.run(`CREATE INDEX IF NOT EXISTS idx_audit_action ON AuditLog(action)`);
  db.run(`CREATE INDEX IF NOT EXISTS idx_audit_user ON AuditLog(userId)`);
  db.run(`CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON AuditLog(timestamp)`);
  db.run(`CREATE INDEX IF NOT EXISTS idx_audit_session ON AuditLog(sessionId)`);

  // Add audit columns to existing tables
  const auditColumns = [
    'createdBy INTEGER',
    'updatedBy INTEGER',
    'deletedAt DATETIME',
    'deletedBy INTEGER'
  ];

  const tables = ['Clients', 'Contracts', 'Payments', 'ReferenceData'];

  tables.forEach(tableName => {
    auditColumns.forEach(column => {
      try {
        db.run(`ALTER TABLE ${tableName} ADD COLUMN ${column}`);
      } catch (error) {
        // Column might already exist, ignore error
        console.log(`Column ${column} already exists in ${tableName} or error occurred:`, error.message);
      }
    });
  });

  // Create triggers for automatic audit logging
  tables.forEach(tableName => {
    // INSERT trigger
    db.run(`
      CREATE TRIGGER IF NOT EXISTS audit_${tableName.toLowerCase()}_insert
      AFTER INSERT ON ${tableName}
      BEGIN
        INSERT INTO AuditLog (tableName, recordId, action, newValues, timestamp)
        VALUES ('${tableName}', NEW.id, 'INSERT', json_object(
          'id', NEW.id,
          'data', 'Record created'
        ), datetime('now'));
      END
    `);

    // UPDATE trigger
    db.run(`
      CREATE TRIGGER IF NOT EXISTS audit_${tableName.toLowerCase()}_update
      AFTER UPDATE ON ${tableName}
      BEGIN
        INSERT INTO AuditLog (tableName, recordId, action, oldValues, newValues, timestamp)
        VALUES ('${tableName}', NEW.id, 'UPDATE', 
          json_object('id', OLD.id, 'updatedAt', OLD.updatedAt),
          json_object('id', NEW.id, 'updatedAt', NEW.updatedAt),
          datetime('now'));
      END
    `);

    // Soft delete trigger (when deletedAt is set)
    db.run(`
      CREATE TRIGGER IF NOT EXISTS audit_${tableName.toLowerCase()}_delete
      AFTER UPDATE OF deletedAt ON ${tableName}
      WHEN NEW.deletedAt IS NOT NULL AND OLD.deletedAt IS NULL
      BEGIN
        INSERT INTO AuditLog (tableName, recordId, action, oldValues, newValues, timestamp)
        VALUES ('${tableName}', NEW.id, 'DELETE',
          json_object('id', OLD.id, 'deletedAt', OLD.deletedAt),
          json_object('id', NEW.id, 'deletedAt', NEW.deletedAt),
          datetime('now'));
      END
    `);
  });

  console.log('Audit trail tables and triggers created successfully');
};

const dropAuditTables = (db) => {
  console.log('Dropping audit trail tables...');

  // Drop triggers
  const tables = ['Clients', 'Contracts', 'Payments', 'ReferenceData'];
  tables.forEach(tableName => {
    db.run(`DROP TRIGGER IF EXISTS audit_${tableName.toLowerCase()}_insert`);
    db.run(`DROP TRIGGER IF EXISTS audit_${tableName.toLowerCase()}_update`);
    db.run(`DROP TRIGGER IF EXISTS audit_${tableName.toLowerCase()}_delete`);
  });

  // Drop audit table
  db.run(`DROP TABLE IF EXISTS AuditLog`);

  console.log('Audit trail tables dropped successfully');
};

module.exports = {
  up: createAuditTables,
  down: dropAuditTables
};
