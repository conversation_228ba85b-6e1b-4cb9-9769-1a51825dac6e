// ===== CLIENTS STORE =====
// Author: Augment Code
// Description: Centralized state management for clients using Zustand

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import apiMiddleware from '../lib/apiMiddleware';

// Initial state
const initialState = {
  // Clients data
  clients: [],
  selectedClient: null,
  
  // Search and filters
  searchQuery: '',
  clientTypeFilter: '',
  financialCategoryFilter: '',
  
  // Loading states
  isLoading: false,
  isAdding: false,
  isUpdating: false,
  isDeleting: false,
  isCheckingDeletion: false,
  
  // Error states
  error: null,
  addError: null,
  updateError: null,
  deleteError: null,
  
  // Cache and metadata
  lastFetched: null,
  totalCount: 0,
  
  // UI state
  showClientCard: false,
  
  // Contracts for selected client
  clientContracts: [],
  contractsLoading: false,
  contractsError: null
};

// Create the clients store
export const useClientsStore = create()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Actions
        actions: {
          // Fetch all clients
          async fetchClients(forceRefresh = false) {
            const state = get();
            
            // Skip if already loading or recently fetched (unless forced)
            if (state.isLoading || (!forceRefresh && state.lastFetched && 
                Date.now() - new Date(state.lastFetched).getTime() < 30000)) {
              return state.clients;
            }

            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const clients = await apiMiddleware.get('/api/clients', {
                loadingMessage: 'جاري تحميل العملاء...',
                showErrorNotification: true
              });

              set((state) => {
                state.clients = clients || [];
                state.totalCount = clients?.length || 0;
                state.isLoading = false;
                state.lastFetched = new Date().toISOString();
                state.error = null;
              });

              return clients;
            } catch (error) {
              console.error('Failed to fetch clients:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحميل العملاء';
              });

              throw error;
            }
          },

          // Search clients
          async searchClients(query) {
            set((state) => {
              state.searchQuery = query;
              state.isLoading = true;
              state.error = null;
            });

            try {
              const url = query 
                ? `/api/clients/search?q=${encodeURIComponent(query)}`
                : '/api/clients';

              const clients = await apiMiddleware.get(url, {
                loadingMessage: query ? 'جاري البحث...' : 'جاري تحميل العملاء...',
                showErrorNotification: true
              });

              set((state) => {
                state.clients = clients || [];
                state.totalCount = clients?.length || 0;
                state.isLoading = false;
                state.error = null;
              });

              return clients;
            } catch (error) {
              console.error('Failed to search clients:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في البحث عن العملاء';
              });

              throw error;
            }
          },

          // Add new client
          async addClient(clientData) {
            set((state) => {
              state.isAdding = true;
              state.addError = null;
            });

            try {
              const newClient = await apiMiddleware.post('/api/clients', clientData, {
                loadingMessage: 'جاري إضافة العميل...',
                successMessage: 'تم إضافة العميل بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.clients.unshift(newClient);
                state.totalCount += 1;
                state.isAdding = false;
                state.addError = null;
              });

              return { success: true, data: newClient };
            } catch (error) {
              console.error('Failed to add client:', error);
              
              set((state) => {
                state.isAdding = false;
                state.addError = error.message || 'فشل في إضافة العميل';
              });

              return { success: false, error: error.message };
            }
          },

          // Update client
          async updateClient(clientId, updates) {
            set((state) => {
              state.isUpdating = true;
              state.updateError = null;
            });

            try {
              const updatedClient = await apiMiddleware.put(`/api/clients/${clientId}`, updates, {
                loadingMessage: 'جاري تحديث العميل...',
                successMessage: 'تم تحديث العميل بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                const index = state.clients.findIndex(client => client.id === clientId);
                if (index !== -1) {
                  state.clients[index] = updatedClient;
                }
                
                // Update selected client if it's the one being updated
                if (state.selectedClient?.id === clientId) {
                  state.selectedClient = updatedClient;
                }
                
                state.isUpdating = false;
                state.updateError = null;
              });

              return { success: true, data: updatedClient };
            } catch (error) {
              console.error('Failed to update client:', error);
              
              set((state) => {
                state.isUpdating = false;
                state.updateError = error.message || 'فشل في تحديث العميل';
              });

              return { success: false, error: error.message };
            }
          },

          // Check if client can be deleted
          async checkClientDeletion(clientId) {
            set((state) => {
              state.isCheckingDeletion = true;
            });

            try {
              const result = await apiMiddleware.get(`/api/clients/${clientId}/can-delete`, {
                showErrorNotification: true
              });

              set((state) => {
                state.isCheckingDeletion = false;
              });

              return result;
            } catch (error) {
              set((state) => {
                state.isCheckingDeletion = false;
              });

              throw error;
            }
          },

          // Delete client
          async deleteClient(clientId) {
            set((state) => {
              state.isDeleting = true;
              state.deleteError = null;
            });

            try {
              await apiMiddleware.delete(`/api/clients/${clientId}`, {
                loadingMessage: 'جاري حذف العميل...',
                successMessage: 'تم حذف العميل بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.clients = state.clients.filter(client => client.id !== clientId);
                state.totalCount -= 1;
                
                // Clear selected client if it was the deleted one
                if (state.selectedClient?.id === clientId) {
                  state.selectedClient = null;
                  state.showClientCard = false;
                  state.clientContracts = [];
                }
                
                state.isDeleting = false;
                state.deleteError = null;
              });

              return { success: true };
            } catch (error) {
              console.error('Failed to delete client:', error);
              
              set((state) => {
                state.isDeleting = false;
                state.deleteError = error.message || 'فشل في حذف العميل';
              });

              return { success: false, error: error.message };
            }
          },

          // Fetch contracts for a client
          async fetchClientContracts(clientId) {
            if (!clientId) return;

            set((state) => {
              state.contractsLoading = true;
              state.contractsError = null;
            });

            try {
              const contracts = await apiMiddleware.get(`/api/contracts?clientId=${clientId}`, {
                showErrorNotification: false // Don't show error notification for contracts
              });

              set((state) => {
                state.clientContracts = contracts || [];
                state.contractsLoading = false;
                state.contractsError = null;
              });

              return contracts;
            } catch (error) {
              console.error('Failed to fetch client contracts:', error);
              
              set((state) => {
                state.clientContracts = [];
                state.contractsLoading = false;
                state.contractsError = error.message;
              });

              return [];
            }
          },

          // Set selected client
          setSelectedClient(client) {
            set((state) => {
              state.selectedClient = client;
              state.showClientCard = !!client;
              
              // Clear previous contracts
              if (client) {
                state.clientContracts = [];
                state.contractsError = null;
              }
            });

            // Fetch contracts for the selected client
            if (client?.id) {
              get().actions.fetchClientContracts(client.id);
            }
          },

          // Set search query
          setSearchQuery(query) {
            set((state) => {
              state.searchQuery = query;
            });
          },

          // Set filters
          setClientTypeFilter(filter) {
            set((state) => {
              state.clientTypeFilter = filter;
            });
          },

          setFinancialCategoryFilter(filter) {
            set((state) => {
              state.financialCategoryFilter = filter;
            });
          },

          // Toggle client card
          toggleClientCard() {
            set((state) => {
              state.showClientCard = !state.showClientCard;
              if (!state.showClientCard) {
                state.selectedClient = null;
                state.clientContracts = [];
              }
            });
          },

          // Clear errors
          clearErrors() {
            set((state) => {
              state.error = null;
              state.addError = null;
              state.updateError = null;
              state.deleteError = null;
              state.contractsError = null;
            });
          },

          // Get filtered clients
          getFilteredClients() {
            const state = get();
            const { clients, searchQuery, clientTypeFilter, financialCategoryFilter } = state;

            return clients.filter(client => {
              // Search filter
              const matchesSearch = !searchQuery ||
                client.clientName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                client.clientId?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                client.clientPhoneWhatsapp?.includes(searchQuery) ||
                client.clientEmail?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                client.clientFinancial_Category?.toLowerCase().includes(searchQuery.toLowerCase());

              // Type filter
              const normalizeClientType = (clientType) => {
                if (clientType === 'فرد') return 'أفراد';
                if (clientType === 'شركة') return 'شركات';
                return clientType;
              };

              const matchesType = !clientTypeFilter || clientTypeFilter === "all" ||
                normalizeClientType(client.clientType) === clientTypeFilter;

              // Category filter
              const matchesCategory = !financialCategoryFilter || financialCategoryFilter === "all" || 
                client.clientFinancial_Category === financialCategoryFilter;

              return matchesSearch && matchesType && matchesCategory;
            });
          },

          // Get client by ID
          getClientById(clientId) {
            const state = get();
            return state.clients.find(client => client.id === clientId);
          },

          // Refresh data
          async refresh() {
            return get().actions.fetchClients(true);
          }
        }
      })),
      {
        name: 'clients-store',
        partialize: (state) => ({
          clients: state.clients,
          lastFetched: state.lastFetched,
          searchQuery: state.searchQuery,
          clientTypeFilter: state.clientTypeFilter,
          financialCategoryFilter: state.financialCategoryFilter
        })
      }
    ),
    {
      name: 'clients-store'
    }
  )
);

// Selectors for easy access to specific parts of the state
export const useClients = () => useClientsStore((state) => state.clients);
export const useClientsLoading = () => useClientsStore((state) => state.isLoading);
export const useClientsError = () => useClientsStore((state) => state.error);
export const useSelectedClient = () => useClientsStore((state) => state.selectedClient);
export const useShowClientCard = () => useClientsStore((state) => state.showClientCard);
export const useClientContracts = () => useClientsStore((state) => state.clientContracts);
export const useClientsActions = () => useClientsStore((state) => state.actions);
export const useClientsFilters = () => useClientsStore((state) => ({
  searchQuery: state.searchQuery,
  clientTypeFilter: state.clientTypeFilter,
  financialCategoryFilter: state.financialCategoryFilter
}));

// Computed selectors
export const useFilteredClients = () => useClientsStore((state) => 
  state.actions.getFilteredClients()
);

export const useClientsStatus = () => useClientsStore((state) => ({
  isLoading: state.isLoading,
  isAdding: state.isAdding,
  isUpdating: state.isUpdating,
  isDeleting: state.isDeleting,
  isCheckingDeletion: state.isCheckingDeletion,
  contractsLoading: state.contractsLoading,
  error: state.error,
  addError: state.addError,
  updateError: state.updateError,
  deleteError: state.deleteError,
  contractsError: state.contractsError,
  totalCount: state.totalCount,
  lastFetched: state.lastFetched
}));

export default useClientsStore;
