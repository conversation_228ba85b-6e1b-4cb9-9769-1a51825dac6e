import React, { createContext, useContext, useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';

export interface GlobalSettings {
  // Company Info
  companyName?: string;
  programName?: string;
  companyRegNo?: string;
  taxId?: string;
  about?: string;
  companyLogo?: string;

  // Localization
  language?: string;
  country?: string;
  currency?: string;
  currencySymbol?: string;
  timeFormat?: string;
  decimalPlaces?: string;
  numberSeparator?: string;
  workDays?: string[];

  // Numbering Formats
  contractNumberFormat?: string;
  clientNumberFormat?: string;
  paymentNumberFormat?: string;

  // Notifications
  notificationEmail?: string;
  enableEmailNotifications?: boolean;
  enableSMSNotifications?: boolean;

  // Theme & UI
  theme?: string;
  primaryColor?: string;
  fontSize?: string;

  // Business Settings
  defaultContractDuration?: string;
  defaultPaymentTerms?: string;
  defaultTaxRate?: string;
  enableMultiCurrency?: boolean;
  enableAdvancedReports?: boolean;
}

interface SettingsContextType {
  settings: GlobalSettings;
  isLoading: boolean;
  error: Error | null;
  formatCurrency: (amount: number) => string;
  formatDate: (date: string | Date) => string;
  formatNumber: (number: number) => string;
  generateContractNumber: () => string;
  generateClientNumber: () => string;
  generatePaymentNumber: () => string;
  refreshSettings: () => void;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const [settings, setSettings] = useState<GlobalSettings>({
    companyName: "شركة إدارة العقود",
    programName: "نظام إدارة العقود",
    language: "ar",
    country: "السعودية",
    currency: "ريال سعودي",
    currencySymbol: "ر.س",
    timeFormat: "24",
    decimalPlaces: "2",
    numberSeparator: ",",
    workDays: ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس"],
    contractNumberFormat: "C-{YYYY}-{####}",
    clientNumberFormat: "CL-{####}",
    paymentNumberFormat: "P-{YYYY}-{####}",
    theme: "system",
    primaryColor: "#3b82f6",
    fontSize: "medium",
    defaultContractDuration: "12",
    defaultPaymentTerms: "شهري",
    defaultTaxRate: "15",
    enableMultiCurrency: false,
    enableAdvancedReports: true,
  });

  // Fetch settings from API
  const { data: apiSettings, isLoading, error, refetch } = useQuery({
    queryKey: ['/api/settings'],
    queryFn: async () => {
      console.log('🔄 Settings Context: Fetching settings...');
      const response = await fetch('/api/settings');
      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }
      const data = await response.json();
      console.log('📥 Settings Context: Received data:', data);

      // تأكد من أن البيانات في الشكل الصحيح
      if (data?.success && data.data) {
        return data.data;
      } else if (data && !data.success) {
        return data;
      }

      return data;
    },
    staleTime: 0, // Always fetch fresh data
    cacheTime: 0, // Don't cache
  });

  // Update settings when API data changes
  useEffect(() => {
    console.log('🔄 Settings Context: useEffect triggered:', { apiSettings, isLoading });
    if (apiSettings && !isLoading) {
      console.log('📝 Settings Context: Updating settings with:', apiSettings);
      setSettings(prev => {
        const newSettings = { ...prev, ...apiSettings };
        console.log('✅ Settings Context: New settings state:', newSettings);
        return newSettings;
      });
    }
  }, [apiSettings, isLoading]);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;
    
    if (settings.theme === 'dark') {
      root.classList.add('dark');
    } else if (settings.theme === 'light') {
      root.classList.remove('dark');
    } else {
      // System theme
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      if (mediaQuery.matches) {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }
    }

    // Apply primary color
    if (settings.primaryColor) {
      root.style.setProperty('--primary', settings.primaryColor);
    }

    // Apply font size
    if (settings.fontSize) {
      const fontSizeMap = {
        small: '14px',
        medium: '16px',
        large: '18px'
      };
      root.style.setProperty('--base-font-size', fontSizeMap[settings.fontSize as keyof typeof fontSizeMap] || '16px');
    }
  }, [settings.theme, settings.primaryColor, settings.fontSize]);

  // Format currency
  const formatCurrency = (amount: number): string => {
    const decimals = parseInt(settings.decimalPlaces || '2');
    const separator = settings.numberSeparator || ',';
    const symbol = settings.currencySymbol || 'ر.س';
    
    const formatted = amount.toFixed(decimals);
    const parts = formatted.split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator);
    
    return `${parts.join('.')} ${symbol}`;
  };

  // Format date based on language
  const formatDate = (date: string | Date): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const language = settings.language || 'ar';

    if (language === 'ar') {
      // العربي: yyyy/mm/dd
      return new Intl.DateTimeFormat('ar-EG', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).format(dateObj).replace(/(\d{2})\/(\d{2})\/(\d{4})/, '$3/$2/$1');
    } else {
      // الإنجليزي: dd/mm/yyyy
      return new Intl.DateTimeFormat('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(dateObj);
    }
  };

  // Format number
  const formatNumber = (number: number): string => {
    const decimals = parseInt(settings.decimalPlaces || '2');
    const separator = settings.numberSeparator || ',';
    
    const formatted = number.toFixed(decimals);
    const parts = formatted.split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator);
    
    return parts.join('.');
  };

  // Generate contract number
  const generateContractNumber = (): string => {
    const format = settings.contractNumberFormat || 'C-{YYYY}-{####}';
    const year = new Date().getFullYear().toString();
    const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
    const randomNum = Math.floor(Math.random() * 9999) + 1;
    
    return format
      .replace('{YYYY}', year)
      .replace('{MM}', month)
      .replace('{####}', randomNum.toString().padStart(4, '0'));
  };

  // Generate client number
  const generateClientNumber = (): string => {
    const format = settings.clientNumberFormat || 'CL-{####}';
    const randomNum = Math.floor(Math.random() * 9999) + 1;
    
    return format.replace('{####}', randomNum.toString().padStart(4, '0'));
  };

  // Generate payment number
  const generatePaymentNumber = (): string => {
    const format = settings.paymentNumberFormat || 'P-{YYYY}-{####}';
    const year = new Date().getFullYear().toString();
    const randomNum = Math.floor(Math.random() * 9999) + 1;
    
    return format
      .replace('{YYYY}', year)
      .replace('{####}', randomNum.toString().padStart(4, '0'));
  };

  const value: SettingsContextType = {
    settings,
    isLoading,
    error,
    formatCurrency,
    formatDate,
    formatNumber,
    generateContractNumber,
    generateClientNumber,
    generatePaymentNumber,
    refreshSettings: refetch,
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
}

export function useSettings() {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
}

export default SettingsContext;
