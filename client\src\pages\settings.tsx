import React, { useState, useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { useSettings, useSettings as useSettingsContext } from "@/contexts/settings-context";
import { useLanguage } from "@/hooks/use-language";
import { cn } from "@/lib/utils";
import labels from "@/lib/i18n";
import * as z from "zod";

import {
  Settings as SettingsIcon,
  Building,
  Globe,
  Hash,
  Bell,
  Upload,
  X,
  Save,
  Edit,
  Palette,
  FileText,
  Database,
  Shield,
  Users,
  Server,
  Key,
  Download,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Image,
  Calendar,
  DollarSign,
  Languages,
  MapPin,
  Clock,
  Wrench,
  Monitor,
  Smartphone,
  Mail,
  Plus,
  Trash2,
  Eye,
  EyeOff,
  Copy
} from "lucide-react";

import { useLocation } from "wouter";
// Comprehensive settings schema
const settingsSchema = z.object({
  // Company Information
  companyName: z.string().min(1, "اسم الشركة مطلوب"),
  programName: z.string().min(1, "اسم البرنامج مطلوب"),
  companyRegNo: z.string().optional(),
  taxId: z.string().optional(),
  about: z.string().optional(),
  companyLogo: z.string().optional(),
  companyAddress: z.string().optional(),
  companyPhone: z.string().optional(),
  companyEmail: z.string().email().optional().or(z.literal('')),
  companyWebsite: z.string().optional(),

  // Localization & Regional
  language: z.enum(["ar", "en"]).default("ar"),
  country: z.string().default("السعودية"),
  currency: z.string().default("ريال سعودي"),
  currencySymbol: z.string().default("ر.س"),
  timeFormat: z.string().default("24"),
  timeZone: z.string().default("Asia/Riyadh"),
  fiscalYearStart: z.string().default("01/01"),
  decimalPlaces: z.string().default("2"),
  numberSeparator: z.string().default(","),
  workDays: z.array(z.string()).default(["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس"]),

  // Numbering & Formatting
  contractNumberFormat: z.string().default("C-{YYYY}-{####}"),
  clientNumberFormat: z.string().default("CL-{####}"),
  paymentNumberFormat: z.string().default("P-{YYYY}-{####}"),
  invoiceNumberFormat: z.string().default("INV-{YYYY}-{####}"),
  receiptNumberFormat: z.string().default("REC-{YYYY}-{####}"),
  numberingFormat: z.string().optional(),



  // Notifications & Communication
  notificationEmail: z.string().email().optional().or(z.literal('')),
  enableEmailNotifications: z.boolean().default(false),
  enableSMSNotifications: z.boolean().default(false),
  enablePushNotifications: z.boolean().default(true),
  notificationFrequency: z.enum(["immediate", "daily", "weekly"]).default("daily"),
  
  // Theme & UI
  theme: z.enum(["light", "dark", "system"]).default("system"),
  primaryColor: z.string().default("#3b82f6"),
  fontSize: z.enum(["small", "medium", "large"]).default("medium"),
  fontFamily: z.string().default("Cairo"),
  sidebarCollapsed: z.boolean().default(false),
  showAnimations: z.boolean().default(true),

  // Business & Financial
  defaultContractDuration: z.string().default("12"),
  defaultPaymentTerms: z.string().default("شهري"),
  defaultTaxRate: z.string().default("15"),
  enableMultiCurrency: z.boolean().default(false),
  enableAdvancedReports: z.boolean().default(true),
  autoBackup: z.boolean().default(true),
  backupFrequency: z.enum(["daily", "weekly", "monthly"]).default("daily"),

  // Security & Access
  sessionTimeout: z.string().default("30"),
  enableTwoFactor: z.boolean().default(false),
  passwordPolicy: z.enum(["basic", "medium", "strong"]).default("medium"),
  enableAuditLog: z.boolean().default(true),
  maxLoginAttempts: z.string().default("5"),

  // System & Performance
  enableCaching: z.boolean().default(true),
  cacheTimeout: z.string().default("60"),
  enableCompression: z.boolean().default(true),
  maxFileSize: z.string().default("10"),
  enableDebugMode: z.boolean().default(false),
});

type SettingsFormData = z.infer<typeof settingsSchema>;

export default function Settings() {
  const { toast } = useToast();
  const { settings, refreshSettings } = useSettings();
  const { refreshSettings: refreshSettingsContext } = useSettingsContext();
  const { language, isRTL } = useLanguage();
  const t = labels[language];
  const queryClient = useQueryClient();
  const [logoPreview, setLogoPreview] = useState<string>("");
  const [activeTab, setActiveTab] = useState("company");
  const [isLoading, setIsLoading] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [, setLocation] = useLocation();
  const [savedData, setSavedData] = useState<SettingsFormData | null>(null);

  // استعلام منفصل للإعدادات لضمان الحصول على أحدث البيانات
  const { data: apiSettings, isLoading: settingsLoading, refetch: refetchSettings } = useQuery({
    queryKey: ['/api/settings'],
    queryFn: async () => {
      console.log('🔄 Fetching settings for settings page...');
      const response = await fetch('/api/settings');
      if (!response.ok) throw new Error('Failed to fetch settings');
      const data = await response.json();
      console.log('📥 Settings page data:', data);

      // تأكد من أن البيانات في الشكل الصحيح
      if (data?.success && data.data) {
        return data.data;
      } else if (data && !data.success) {
        return data;
      }

      return data;
    },
    staleTime: 0, // Always fetch fresh data
    cacheTime: 0, // Don't cache
  });

  const form = useForm<SettingsFormData>({
    resolver: zodResolver(settingsSchema),
    defaultValues: apiSettings || {
      companyName: "",
      programName: "",
      companyRegNo: "",
      taxId: "",
      about: "",
      companyLogo: "",
      companyAddress: "",
      companyPhone: "",
      companyEmail: "",
      companyWebsite: "",
      language: "ar",
      country: "",
      currency: "",
      currencySymbol: "",
      timeFormat: "24",
      timeZone: "",
      fiscalYearStart: "",
      decimalPlaces: "2",
      numberSeparator: ",",
      workDays: [],
      contractNumberFormat: "",
      clientNumberFormat: "",
      paymentNumberFormat: "",
      invoiceNumberFormat: "",
      receiptNumberFormat: "",
      numberingFormat: "",
      notificationEmail: "",
      enableEmailNotifications: false,
      enableSMSNotifications: false,
      enablePushNotifications: false,
      notificationFrequency: "daily",
      theme: "system",
      primaryColor: "#3b82f6",
      fontSize: "medium",
      fontFamily: "Cairo",
      sidebarCollapsed: false,
      showAnimations: true,
      defaultContractDuration: "",
      defaultPaymentTerms: "",
      defaultTaxRate: "",
      enableMultiCurrency: false,
      enableAdvancedReports: true,
      autoBackup: true,
      backupFrequency: "daily",
      sessionTimeout: "30",
      enableTwoFactor: false,
      passwordPolicy: "medium",
      enableAuditLog: true,
      maxLoginAttempts: "5",
      enableCaching: true,
      cacheTimeout: "60",
      enableCompression: true,
      maxFileSize: "10",
      enableDebugMode: false,
    },
  });

  // Update form when settings are loaded
  useEffect(() => {
    console.log('🔄 Settings useEffect triggered:', { apiSettings, settingsLoading });
    if (apiSettings && !settingsLoading) {
      console.log('📝 Updating form with settings:', apiSettings);
      const mergedData = { ...form.getValues(), ...apiSettings };
      form.reset(mergedData);
      setSavedData(mergedData);
      setIsEditMode(false); // البيانات محفوظة، فنبدأ في وضع القراءة
      if (apiSettings.companyLogo) {
        setLogoPreview(apiSettings.companyLogo);
      }
      console.log('✅ Form updated with settings');
    }
  }, [apiSettings, settingsLoading, form]);

  // وظائف إدارة حالة التعديل (تم التبسيط)
  const handleEnterEditMode = () => setIsEditMode(true);

  // دالة حفظ مباشرة كبديل
  const handleDirectSave = async () => {
    console.log('🔥🔥🔥 DIRECT SAVE CALLED!');
    const formData = form.getValues();

    console.log('🔥 Form data:', formData);
    console.log('🔥 Form data keys:', Object.keys(formData));

    try {
      setIsLoading(true);

      // تنظيف البيانات
      const cleanedData = Object.fromEntries(
        Object.entries(formData).filter(([_, value]) => value !== null && value !== undefined)
      );

      console.log('🔥 Cleaned data:', cleanedData);

      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanedData),
      });

      console.log('🔥 Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Response error:', errorText);
        throw new Error(errorText || `فشل الحفظ - Status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Save successful:', result);

      toast({
        title: "تم الحفظ بنجاح ✅",
        description: "تم حفظ جميع الإعدادات بنجاح",
      });

      setIsEditMode(false);
      refreshSettings();

    } catch (error) {
      console.error('❌ Save error:', error);
      toast({
        title: "خطأ في الحفظ ❌",
        description: error instanceof Error ? error.message : "فشل في حفظ الإعدادات",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const cancelEdit = () => {
    console.log('🚫 cancelEdit called');
    const dataToRestore = savedData || apiSettings || form.formState.defaultValues;
    if (dataToRestore) {
      console.log('🔄 Restoring data:', dataToRestore);
      form.reset(dataToRestore);
      setIsEditMode(false);
      toast({
        title: "تم إلغاء التعديل",
        description: "تم استعادة البيانات المحفوظة",
      });
    }
  };

  // دالة لإعادة تحميل البيانات يدوياً
  const handleRefreshData = async () => {
    console.log('🔄 Manual refresh triggered');
    await queryClient.invalidateQueries({ queryKey: ['/api/settings'] });
    await refreshSettings();
    await refreshSettingsContext();
    await refetchSettings();
    toast({
      title: "تم تحديث البيانات",
      description: "تم تحميل أحدث البيانات من السيرفر",
    });
  };

  // مكونات مساعدة للحقول المعطلة (لا تغيير هنا)
  const DisabledInput = ({ field, placeholder, ...props }: any) => (
    <Input
      placeholder={placeholder}
      disabled={!isEditMode}
      className={cn(
        !isEditMode ? "bg-gray-50 text-gray-700 cursor-not-allowed" : "",
        "text-right"
      )}
      {...field}
      value={field.value || ""}
      {...props}
    />
  );

  const DisabledTextarea = ({ field, placeholder, ...props }: any) => (
    <Textarea
      placeholder={placeholder}
      disabled={!isEditMode}
      className={cn(
        !isEditMode ? "bg-gray-50 text-gray-700 cursor-not-allowed" : "",
        "text-right"
      )}
      {...field}
      value={field.value || ""}
      {...props}
    />
  );

  const DisabledSelect = ({ field, children, ...props }: any) => (
    <Select
      disabled={!isEditMode}
      onValueChange={field.onChange}
      defaultValue={field.value}
      {...props}
    >
      <SelectTrigger className={cn(
        !isEditMode ? "bg-gray-50 text-gray-700 cursor-not-allowed" : "",
        "text-right"
      )}>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {children}
      </SelectContent>
    </Select>
  );

  const DisabledCheckbox = ({ checked, onCheckedChange, ...props }: any) => (
    <Checkbox
      checked={checked}
      onCheckedChange={isEditMode ? onCheckedChange : undefined}
      disabled={!isEditMode}
      className={!isEditMode ? "cursor-not-allowed opacity-60" : ""}
      {...props}
    />
  );

  // Save settings mutation (تم التحديث ليكون المصدر الوحيد للحفظ)
  const saveSettingsMutation = useMutation({
    mutationFn: async (data: SettingsFormData) => {
      console.log('💾 Starting save mutation with data:', data);
      setIsLoading(true);

      const cleanedData = Object.fromEntries(
        Object.entries(data).filter(([_, value]) => value !== null && value !== undefined)
      );

      const response = await fetch('/api/settings', {
        method: 'POST', // استخدام POST كما هو مطلوب في الخادم
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanedData),
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response ok:', response.ok);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Server Response error:', errorText);
        throw new Error(errorText || `فشل الحفظ - Status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Save successful:', result);
      return result;
    },
    onSuccess: async (savedResult) => {
      console.log('🎉 Settings saved successfully:', savedResult);

      // إجبار تحديث البيانات من السيرفر والـ context
      await queryClient.invalidateQueries({ queryKey: ['/api/settings'] });
      await refreshSettings();
      await refreshSettingsContext();
      await refetchSettings();

      // انتظار قليل للتأكد من تحديث البيانات
      setTimeout(async () => {
        const freshData = await refetchSettings();
        const dataToUse = freshData?.data || savedResult?.data;

        if (dataToUse) {
          console.log('🔄 Updating form with fresh data:', dataToUse);
          form.reset(dataToUse);
          setSavedData(dataToUse);
        }
      }, 500);

      toast({
        title: "تم الحفظ بنجاح ✅",
        description: "تم حفظ جميع الإعدادات بنجاح وتحديث الواجهة",
      });

      setIsEditMode(false);
      setIsLoading(false);
    },
    onError: (error) => {
      toast({
        title: "خطأ في الحفظ ❌",
        description: error.message || "فشل في حفظ الإعدادات، يرجى المحاولة مرة أخرى",
        variant: "destructive",
      });
      setIsLoading(false);
    },
  });

  // هذه الدالة هي التي سيتم استدعاؤها عند تقديم النموذج
  const onSubmit = (data: SettingsFormData) => {
    console.log('🚀🚀🚀 SETTINGS onSubmit called!');
    console.log('🚀 Form submitted with data:', data);
    console.log('🚀 Data keys:', Object.keys(data));
    console.log('🚀 saveSettingsMutation:', saveSettingsMutation);
    saveSettingsMutation.mutate(data);
  };

  // Handle logo upload
  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setLogoPreview(result);
        form.setValue("companyLogo", result);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeLogo = () => {
    setLogoPreview("");
    form.setValue("companyLogo", "");
  };

  if (settingsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <SettingsIcon className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري تحميل الإعدادات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", isRTL ? "text-right" : "text-left")}>
      <Form {...form}>
        <form id="settings-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {/* Header Actions */}
          <div className={cn("flex items-center space-x-4", isRTL ? "justify-start space-x-reverse" : "justify-end")}>
            {isEditMode ? (
              <>
                <Button
                  type="button"
                  variant="outline"
                  onClick={cancelEdit}
                  disabled={isLoading}
                >
                  إلغاء التعديل
                </Button>
                <Button
                  type="button"
                  variant="default"
                  disabled={isLoading || saveSettingsMutation.isPending}
                  onClick={handleDirectSave}
                  className="gap-2"
                >
                  <Save className="h-4 w-4" />
                  {isLoading || saveSettingsMutation.isPending ? (language === 'ar' ? "جاري الحفظ..." : "Saving...") : t.saveSettings}
                </Button>
              </>
            ) : (
              <>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setLocation('/')} // Or any other default page
                >
                  {language === 'ar' ? 'العودة' : 'Back'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleRefreshData}
                  className="gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  تحديث البيانات
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  disabled={isLoading || saveSettingsMutation.isPending}
                  onClick={handleEnterEditMode}
                  className="gap-2"
                >
                  <Edit className="h-4 w-4" />
                  {t.editSettings}
                </Button>
              </>
            )}
          </div>
            <Tabs value={activeTab} onValueChange={setActiveTab} dir={isRTL ? "rtl" : "ltr"}>
              <TabsList className={cn("grid w-full grid-cols-9 h-auto p-1 bg-muted rounded-lg", isRTL ? "rtl-tabs" : "ltr-tabs")}>
                <TabsTrigger
                  value="company"
                  className="flex flex-col items-center gap-1 py-2 px-2 text-xs data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all"
                >
                  <Building className="h-4 w-4" />
                  <span className="hidden sm:block text-center leading-tight">{t.companyInfo}</span>
                  <span className="sm:hidden">{language === 'ar' ? 'الشركة' : 'Company'}</span>
                </TabsTrigger>

                <TabsTrigger 
                  value="numbering" 
                  className="flex flex-col items-center gap-1 py-2 px-2 text-xs data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all"
                >
                  <Hash className="h-4 w-4" />
                  <span className="hidden sm:block text-center leading-tight">{language === 'ar' ? 'الترقيم' : 'Numbering'}</span>
                  <span className="sm:hidden">{language === 'ar' ? 'الأرقام' : 'Numbers'}</span>
                </TabsTrigger>
                <TabsTrigger
                  value="notifications"
                  className="flex flex-col items-center gap-1 py-2 px-2 text-xs data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all"
                >
                  <Bell className="h-4 w-4" />
                  <span className="hidden sm:block text-center leading-tight">{language === 'ar' ? 'الإشعارات' : 'Notifications'}</span>
                  <span className="sm:hidden">{language === 'ar' ? 'إشعارات' : 'Alerts'}</span>
                </TabsTrigger>
                <TabsTrigger
                  value="theme"
                  className="flex flex-col items-center gap-1 py-2 px-2 text-xs data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all"
                >
                  <Palette className="h-4 w-4" />
                  <span className="hidden sm:block text-center leading-tight">{language === 'ar' ? 'المظهر' : 'Theme'}</span>
                  <span className="sm:hidden">{language === 'ar' ? 'الثيم' : 'Theme'}</span>
                </TabsTrigger>
                <TabsTrigger
                  value="business"
                  className="flex flex-col items-center gap-1 py-2 px-2 text-xs data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all"
                >
                  <FileText className="h-4 w-4" />
                  <span className="hidden sm:block text-center leading-tight">{language === 'ar' ? 'العمل' : 'Business'}</span>
                  <span className="sm:hidden">{language === 'ar' ? 'العمل' : 'Business'}</span>
                </TabsTrigger>
                <TabsTrigger
                  value="security"
                  className="flex flex-col items-center gap-1 py-2 px-2 text-xs data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all"
                >
                  <Shield className="h-4 w-4" />
                  <span className="hidden sm:block text-center leading-tight">{language === 'ar' ? 'الأمان' : 'Security'}</span>
                  <span className="sm:hidden">{language === 'ar' ? 'الأمان' : 'Security'}</span>
                </TabsTrigger>
                <TabsTrigger
                  value="system"
                  className="flex flex-col items-center gap-1 py-2 px-2 text-xs data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all"
                >
                  <Server className="h-4 w-4" />
                  <span className="hidden sm:block text-center leading-tight">{language === 'ar' ? 'النظام' : 'System'}</span>
                  <span className="sm:hidden">{language === 'ar' ? 'النظام' : 'System'}</span>
                </TabsTrigger>

                <TabsTrigger
                  value="database"
                  className="flex flex-col items-center gap-1 py-2 px-2 text-xs data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all"
                >
                  <Database className="h-4 w-4" />
                  <span className="hidden sm:block text-center leading-tight">{language === 'ar' ? 'قاعدة البيانات' : 'Database'}</span>
                  <span className="sm:hidden">{language === 'ar' ? 'البيانات' : 'Database'}</span>
                </TabsTrigger>

              </TabsList>

              {/* Tab 1: Company Information */}
              <TabsContent value="company" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Building className="h-5 w-5" />
                      {t.companyInfoDesc}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="companyName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t.companyName} *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder={language === 'ar' ? "أدخل اسم الشركة" : "Enter company name"}
                                disabled={!isEditMode}
                                className={cn(
                                  !isEditMode ? "bg-gray-50 text-gray-700" : "",
                                  "text-right"
                                )}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="programName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t.programName} *</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder={language === 'ar' ? "أدخل اسم البرنامج" : "Enter program name"} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="companyRegNo"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t.companyRegNo}</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder={language === 'ar' ? "أدخل رقم السجل التجاري" : "Enter commercial registration number"} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="taxId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t.taxId}</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder={language === 'ar' ? "أدخل الرقم الضريبي" : "Enter tax ID"} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="companyPhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t.companyPhone}</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder={language === 'ar' ? "أدخل هاتف الشركة" : "Enter company phone"} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="companyEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t.companyEmail}</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} type="email" placeholder="<EMAIL>" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="companyWebsite"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t.companyWebsite}</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder="https://www.company.com" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="companyAddress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>عنوان الشركة</FormLabel>
                          <FormControl>
                            <DisabledTextarea
                              field={field}
                              placeholder="أدخل عنوان الشركة الكامل"
                              className="min-h-[80px]"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="about"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>نبذة عن الشركة</FormLabel>
                          <FormControl>
                            <DisabledTextarea
                              field={field}
                              placeholder="أدخل نبذة عن الشركة ونشاطها"
                              className="min-h-[100px]"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Company Logo */}
                    <div className="space-y-4">
                      <FormLabel>شعار الشركة</FormLabel>
                      <div className="flex items-center space-x-reverse space-x-4">
                        {logoPreview ? (
                          <div className="relative">
                            <img
                              src={logoPreview}
                              alt="Company Logo"
                              className="h-24 w-24 object-contain border rounded-lg"
                            />
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                              onClick={removeLogo}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <div className="h-24 w-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                            <Image className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                        <div>
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleLogoUpload}
                            className="hidden"
                            id="logo-upload"
                          />
                          <label htmlFor="logo-upload">
                            <Button type="button" variant="outline" className="gap-2" asChild>
                              <span>
                                <Upload className="h-4 w-4" />
                                رفع شعار
                              </span>
                            </Button>
                          </label>
                          <p className="text-sm text-gray-500 mt-1">
                            PNG, JPG, GIF حتى 2MB
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Localization Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="h-5 w-5" />
                      الإعدادات الإقليمية
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="language"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>اللغة الأساسية</FormLabel>
                            <DisabledSelect field={field}>
                              <SelectItem value="ar">العربية</SelectItem>
                              <SelectItem value="en">English</SelectItem>
                            </DisabledSelect>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="country"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>البلد</FormLabel>
                            <DisabledSelect field={field}>
                              <SelectItem value="السعودية">السعودية</SelectItem>
                              <SelectItem value="الإمارات">الإمارات</SelectItem>
                              <SelectItem value="الكويت">الكويت</SelectItem>
                              <SelectItem value="قطر">قطر</SelectItem>
                              <SelectItem value="البحرين">البحرين</SelectItem>
                              <SelectItem value="عمان">عمان</SelectItem>
                              <SelectItem value="الأردن">الأردن</SelectItem>
                              <SelectItem value="مصر">مصر</SelectItem>
                            </DisabledSelect>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="currency"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>العملة</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder="ريال سعودي" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="currencySymbol"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>رمز العملة</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder="ر.س" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="timeZone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>المنطقة الزمنية</FormLabel>
                            <DisabledSelect field={field}>
                              <SelectItem value="Asia/Riyadh">الرياض (GMT+3)</SelectItem>
                              <SelectItem value="Asia/Dubai">دبي (GMT+4)</SelectItem>
                              <SelectItem value="Asia/Kuwait">الكويت (GMT+3)</SelectItem>
                              <SelectItem value="Asia/Qatar">قطر (GMT+3)</SelectItem>
                              <SelectItem value="Asia/Bahrain">البحرين (GMT+3)</SelectItem>
                              <SelectItem value="Asia/Muscat">مسقط (GMT+4)</SelectItem>
                              <SelectItem value="Asia/Amman">عمان (GMT+3)</SelectItem>
                              <SelectItem value="Africa/Cairo">القاهرة (GMT+2)</SelectItem>
                            </DisabledSelect>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="fiscalYearStart"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>بداية السنة المالية</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder="01/01" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>

              </TabsContent>



              {/* Tab 3: Numbering */}
              <TabsContent value="numbering" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Hash className="h-5 w-5" />
                      {t.numberingSettings}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="contractNumberFormat"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>تنسيق رقم العقد</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder="C-{YYYY}-{####}" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="clientNumberFormat"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>تنسيق رقم العميل</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder="CL-{####}" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="paymentNumberFormat"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>تنسيق رقم الدفعة</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder="P-{YYYY}-{####}" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="invoiceNumberFormat"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>تنسيق رقم الفاتورة</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder="INV-{YYYY}-{####}" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="receiptNumberFormat"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>تنسيق رقم الإيصال</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} placeholder="REC-{YYYY}-{####}" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="numberingFormat"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>تنسيق الترقيم العام</FormLabel>
                            <FormControl>
                              <DisabledInput
                                field={field}
                                placeholder="مثال: {YYYY}-{####}"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="decimalPlaces"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>عدد الخانات العشرية</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="اختر عدد الخانات" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="0">0</SelectItem>
                                <SelectItem value="1">1</SelectItem>
                                <SelectItem value="2">2</SelectItem>
                                <SelectItem value="3">3</SelectItem>
                                <SelectItem value="4">4</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="numberSeparator"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>فاصل الآلاف</FormLabel>
                            <DisabledSelect field={field}>
                              <SelectItem value=",">,</SelectItem>
                              <SelectItem value=".">.</SelectItem>
                              <SelectItem value=" ">مسافة</SelectItem>
                              <SelectItem value="none">بدون فاصل</SelectItem>
                            </DisabledSelect>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                      <h4 className="font-medium mb-2">أمثلة على التنسيق:</h4>
                      <ul className="text-sm space-y-1 text-gray-600 dark:text-gray-300">
                        <li>• C-2024-0001 (للعقود)</li>
                        <li>• CL-0001 (للعملاء)</li>
                        <li>• P-2024-0001 (للدفعات)</li>
                        <li>• INV-2024-0001 (للفواتير)</li>
                        <li>• REC-2024-0001 (للإيصالات)</li>
                      </ul>
                      <p className="text-xs text-gray-500 mt-2">
                        استخدم {"{YYYY}"} للسنة، {"{MM}"} للشهر، {"{####}"} للرقم التسلسلي
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Tab 4: Notifications */}
              <TabsContent value="notifications" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bell className="h-5 w-5" />
                      {t.notificationSettings}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <FormField
                      control={form.control}
                      name="notificationEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>البريد الإلكتروني للإشعارات</FormLabel>
                          <FormControl>
                            <DisabledInput
                              field={field}
                              type="email"
                              placeholder="<EMAIL>"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="notificationFrequency"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>تكرار الإشعارات</FormLabel>
                            <DisabledSelect field={field}>
                              <SelectItem value="immediate">فوري</SelectItem>
                              <SelectItem value="daily">يومي</SelectItem>
                              <SelectItem value="weekly">أسبوعي</SelectItem>
                            </DisabledSelect>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="enableEmailNotifications"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                إشعارات البريد الإلكتروني
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                إرسال إشعارات عبر البريد الإلكتروني
                              </p>
                            </div>
                            <FormControl>
                              <DisabledCheckbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="enableSMSNotifications"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                إشعارات الرسائل النصية
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                إرسال إشعارات عبر الرسائل النصية
                              </p>
                            </div>
                            <FormControl>
                              <DisabledCheckbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="enablePushNotifications"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                الإشعارات المنبثقة
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                إظهار إشعارات منبثقة في المتصفح
                              </p>
                            </div>
                            <FormControl>
                              <DisabledCheckbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Tab 5: Theme & UI */}
              <TabsContent value="theme" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Palette className="h-5 w-5" />
                      {t.themeSettings}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="theme"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>المظهر العام</FormLabel>
                            <DisabledSelect field={field}>
                              <SelectItem value="light">فاتح</SelectItem>
                              <SelectItem value="dark">داكن</SelectItem>
                              <SelectItem value="system">تلقائي (حسب النظام)</SelectItem>
                            </DisabledSelect>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="fontSize"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>حجم الخط</FormLabel>
                            <DisabledSelect field={field}>
                              <SelectItem value="small">صغير</SelectItem>
                              <SelectItem value="medium">متوسط</SelectItem>
                              <SelectItem value="large">كبير</SelectItem>
                            </DisabledSelect>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="fontFamily"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>نوع الخط</FormLabel>
                            <DisabledSelect field={field}>
                              <SelectItem value="Cairo">Cairo</SelectItem>
                              <SelectItem value="Tajawal">Tajawal</SelectItem>
                              <SelectItem value="Amiri">Amiri</SelectItem>
                              <SelectItem value="Noto Sans Arabic">Noto Sans Arabic</SelectItem>
                            </DisabledSelect>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="primaryColor"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>اللون الأساسي</FormLabel>
                            <FormControl>
                              <div className="flex items-center space-x-reverse space-x-2">
                                <DisabledInput
                                  field={field}
                                  type="color"
                                  className="w-16 h-10 p-1 border rounded"
                                />
                                <DisabledInput
                                  field={field}
                                  placeholder="#3b82f6"
                                />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="sidebarCollapsed"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                طي الشريط الجانبي
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                إخفاء الشريط الجانبي افتراضياً
                              </p>
                            </div>
                            <FormControl>
                              <DisabledCheckbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="showAnimations"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                الحركات والانتقالات
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                تفعيل الحركات والانتقالات السلسة
                              </p>
                            </div>
                            <FormControl>
                              <DisabledCheckbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Tab 6: Business Settings */}
              <TabsContent value="business" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      {t.businessSettings}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="defaultContractDuration"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>مدة العقد الافتراضية (بالأشهر)</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} type="number" placeholder="12" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="defaultPaymentTerms"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>شروط الدفع الافتراضية</FormLabel>
                            <DisabledSelect field={field}>
                              <SelectItem value="شهري">شهري</SelectItem>
                              <SelectItem value="ربع سنوي">ربع سنوي</SelectItem>
                              <SelectItem value="نصف سنوي">نصف سنوي</SelectItem>
                              <SelectItem value="سنوي">سنوي</SelectItem>
                            </DisabledSelect>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="defaultTaxRate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>معدل الضريبة الافتراضي (%)</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} type="number" placeholder="15" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="backupFrequency"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>تكرار النسخ الاحتياطي</FormLabel>
                            <DisabledSelect field={field}>
                              <SelectItem value="daily">يومي</SelectItem>
                              <SelectItem value="weekly">أسبوعي</SelectItem>
                              <SelectItem value="monthly">شهري</SelectItem>
                            </DisabledSelect>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="enableMultiCurrency"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                دعم العملات المتعددة
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                السماح بالتعامل مع عملات مختلفة
                              </p>
                            </div>
                            <FormControl>
                              <DisabledCheckbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="enableAdvancedReports"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                التقارير المتقدمة
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                تفعيل ميزات التقارير المتقدمة والتحليلات
                              </p>
                            </div>
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="autoBackup"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                النسخ الاحتياطي التلقائي
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                إنشاء نسخ احتياطية تلقائياً
                              </p>
                            </div>
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Tab 7: Security */}
              <TabsContent value="security" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5" />
                      {t.securitySettings}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="sessionTimeout"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>انتهاء الجلسة (بالدقائق)</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} type="number" placeholder="30" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="maxLoginAttempts"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>عدد محاولات تسجيل الدخول</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} type="number" placeholder="5" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="passwordPolicy"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>سياسة كلمة المرور</FormLabel>
                            <DisabledSelect field={field}>
                              <SelectItem value="basic">أساسية (6 أحرف)</SelectItem>
                              <SelectItem value="medium">متوسطة (8 أحرف + أرقام)</SelectItem>
                              <SelectItem value="strong">قوية (12 حرف + رموز)</SelectItem>
                            </DisabledSelect>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="enableTwoFactor"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                المصادقة الثنائية
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                تفعيل المصادقة الثنائية لحماية إضافية
                              </p>
                            </div>
                            <FormControl>
                              <DisabledCheckbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="enableAuditLog"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                سجل المراجعة
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                تسجيل جميع العمليات والتغييرات
                              </p>
                            </div>
                            <FormControl>
                              <DisabledCheckbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Tab 8: System */}
              <TabsContent value="system" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Server className="h-5 w-5" />
                      {t.systemSettings}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="cacheTimeout"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>مهلة التخزين المؤقت (بالدقائق)</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} type="number" placeholder="60" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="maxFileSize"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>حد حجم الملف (بالميجابايت)</FormLabel>
                            <FormControl>
                              <DisabledInput field={field} type="number" placeholder="10" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="enableCaching"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                التخزين المؤقت
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                تفعيل التخزين المؤقت لتحسين الأداء
                              </p>
                            </div>
                            <FormControl>
                              <DisabledCheckbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="enableCompression"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                ضغط البيانات
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                ضغط البيانات لتوفير مساحة التخزين
                              </p>
                            </div>
                            <FormControl>
                              <DisabledCheckbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="enableDebugMode"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                وضع التطوير
                              </FormLabel>
                              <p className="text-sm text-gray-500">
                                تفعيل وضع التطوير لاستكشاف الأخطاء
                              </p>
                            </div>
                            <FormControl>
                              <DisabledCheckbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    <Separator />

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">أدوات النظام</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Button type="button" variant="outline" className="gap-2">
                          <Download className="h-4 w-4" />
                          تصدير البيانات
                        </Button>
                        <Button type="button" variant="outline" className="gap-2">
                          <Upload className="h-4 w-4" />
                          استيراد البيانات
                        </Button>
                        <Button type="button" variant="outline" className="gap-2">
                          <RefreshCw className="h-4 w-4" />
                          إعادة تشغيل النظام
                        </Button>
                        <Button type="button" variant="outline" className="gap-2">
                          <Database className="h-4 w-4" />
                          تحسين قاعدة البيانات
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Tab 9: Database Settings */}
              <TabsContent value="database" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Database className="h-5 w-5" />
                      {language === 'ar' ? 'إعدادات قاعدة البيانات' : 'Database Settings'}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {language === 'ar'
                        ? 'إدارة إعدادات قاعدة البيانات والجداول المستخدمة في النظام'
                        : 'Manage database settings and tables used in the system'
                      }
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="p-4 bg-muted/50 rounded-lg">
                        <h4 className="font-medium mb-2">
                          {language === 'ar' ? 'النظام الموحد لقاعدة البيانات' : 'Unified Database System'}
                        </h4>
                        <p className="text-sm text-muted-foreground mb-3">
                          {language === 'ar'
                            ? 'النظام يستخدم طبقة موحدة للتعامل مع قاعدة البيانات لضمان الاستقرار والأداء.'
                            : 'The system uses a unified layer for database operations to ensure stability and performance.'
                          }
                        </p>
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                          <span className="text-sm text-green-600 font-medium">
                            {language === 'ar' ? 'النظام يعمل بشكل طبيعي' : 'System operating normally'}
                          </span>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="p-3 border rounded-lg">
                          <div className="text-sm font-medium">
                            {language === 'ar' ? 'حالة قاعدة البيانات' : 'Database Status'}
                          </div>
                          <div className="text-lg font-bold text-green-600">
                            {language === 'ar' ? 'متصلة' : 'Connected'}
                          </div>
                        </div>

                        <div className="p-3 border rounded-lg">
                          <div className="text-sm font-medium">
                            {language === 'ar' ? 'نوع قاعدة البيانات' : 'Database Type'}
                          </div>
                          <div className="text-lg font-bold text-primary">
                            SQLite
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

            </Tabs>

            {/* Footer Actions */}
            <div className="flex justify-end items-center space-x-reverse space-x-4 pt-6 border-t">
              {isEditMode ? (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={cancelEdit}
                    disabled={isLoading}
                  >
                    إلغاء التعديل
                  </Button>
                  <Button
                    type="button"
                    disabled={isLoading || saveSettingsMutation.isPending}
                    onClick={handleDirectSave}
                    className="gap-2"
                  >
                    <Save className="h-4 w-4" />
                    {isLoading || saveSettingsMutation.isPending ? "جاري الحفظ..." : "حفظ الإعدادات"}
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setLocation('/')} // Or any other default page
                  >
                    العودة
                  </Button>
                  <Button
                    type="button"
                    variant="secondary"
                    disabled={isLoading || saveSettingsMutation.isPending}
                    onClick={handleEnterEditMode}
                    className="gap-2"
                  >
                    <Edit className="h-4 w-4" />
                    تعديل الإعدادات
                  </Button>
                </>
              )}
            </div>
          </form>
        </Form>
    </div>
  );
}



