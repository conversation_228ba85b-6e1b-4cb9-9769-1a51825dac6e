// ===== RECEIVABLES STORE =====
// Author: Augment Code
// Description: Centralized state management for receivables using Zustand

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import apiMiddleware from '../lib/apiMiddleware';

// Initial state
const initialState = {
  // Receivables data
  receivables: [],
  overdueReceivables: [],
  upcomingReceivables: [],
  paidReceivables: [],
  selectedReceivable: null,
  
  // Search and filters
  searchQuery: '',
  statusFilter: '',
  clientFilter: '',
  contractFilter: '',
  dueDateFilter: '',
  amountRangeFilter: { min: null, max: null },
  
  // Loading states
  isLoading: false,
  isUpdating: false,
  isMarkingPaid: false,
  isGeneratingInvoice: false,
  
  // Error states
  error: null,
  updateError: null,
  
  // Cache and metadata
  lastFetched: null,
  totalCount: 0,
  
  // UI state
  showReceivableDetails: false,
  
  // Receivables statistics
  receivablesStats: null,
  statsLoading: false,
  statsError: null
};

// Create the receivables store
export const useReceivablesStore = create()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Actions
        actions: {
          // Fetch all receivables
          async fetchReceivables(forceRefresh = false) {
            const state = get();
            
            if (state.isLoading || (!forceRefresh && state.lastFetched && 
                Date.now() - new Date(state.lastFetched).getTime() < 30000)) {
              return state.receivables;
            }

            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const receivables = await apiMiddleware.get('/api/receivables', {
                loadingMessage: 'جاري تحميل الاستحقاقات...',
                showErrorNotification: true
              });

              set((state) => {
                state.receivables = receivables || [];
                state.totalCount = receivables?.length || 0;
                state.isLoading = false;
                state.lastFetched = new Date().toISOString();
                state.error = null;
              });

              return receivables;
            } catch (error) {
              console.error('Failed to fetch receivables:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحميل الاستحقاقات';
              });

              throw error;
            }
          },

          // Fetch receivables by status
          async fetchReceivablesByStatus(status) {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const receivables = await apiMiddleware.get(`/api/receivables/status/${status}`, {
                loadingMessage: `جاري تحميل الاستحقاقات ${status}...`,
                showErrorNotification: true
              });

              set((state) => {
                switch (status) {
                  case 'overdue':
                    state.overdueReceivables = receivables || [];
                    break;
                  case 'upcoming':
                    state.upcomingReceivables = receivables || [];
                    break;
                  case 'paid':
                    state.paidReceivables = receivables || [];
                    break;
                }
                state.isLoading = false;
                state.error = null;
              });

              return receivables;
            } catch (error) {
              console.error(`Failed to fetch ${status} receivables:`, error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || `فشل في تحميل الاستحقاقات ${status}`;
              });

              throw error;
            }
          },

          // Search receivables
          async searchReceivables(query) {
            set((state) => {
              state.searchQuery = query;
              state.isLoading = true;
              state.error = null;
            });

            try {
              const url = query 
                ? `/api/receivables/search?q=${encodeURIComponent(query)}`
                : '/api/receivables';

              const receivables = await apiMiddleware.get(url, {
                loadingMessage: query ? 'جاري البحث...' : 'جاري تحميل الاستحقاقات...',
                showErrorNotification: true
              });

              set((state) => {
                state.receivables = receivables || [];
                state.totalCount = receivables?.length || 0;
                state.isLoading = false;
                state.error = null;
              });

              return receivables;
            } catch (error) {
              console.error('Failed to search receivables:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في البحث عن الاستحقاقات';
              });

              throw error;
            }
          },

          // Update receivable status
          async updateReceivableStatus(receivableId, status, paymentData = null) {
            set((state) => {
              state.isUpdating = true;
              state.updateError = null;
            });

            try {
              const updatedReceivable = await apiMiddleware.put(`/api/receivables/${receivableId}/status`, {
                status,
                paymentData
              }, {
                loadingMessage: 'جاري تحديث حالة الاستحقاق...',
                successMessage: 'تم تحديث حالة الاستحقاق بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                // Update in main receivables array
                const index = state.receivables.findIndex(receivable => receivable.id === receivableId);
                if (index !== -1) {
                  state.receivables[index] = updatedReceivable;
                }
                
                // Update in status-specific arrays
                const updateInArray = (array) => {
                  const idx = array.findIndex(receivable => receivable.id === receivableId);
                  if (idx !== -1) {
                    array[idx] = updatedReceivable;
                  }
                };
                
                updateInArray(state.overdueReceivables);
                updateInArray(state.upcomingReceivables);
                updateInArray(state.paidReceivables);
                
                if (state.selectedReceivable?.id === receivableId) {
                  state.selectedReceivable = updatedReceivable;
                }
                
                state.isUpdating = false;
                state.updateError = null;
              });

              return { success: true, data: updatedReceivable };
            } catch (error) {
              console.error('Failed to update receivable status:', error);
              
              set((state) => {
                state.isUpdating = false;
                state.updateError = error.message || 'فشل في تحديث حالة الاستحقاق';
              });

              return { success: false, error: error.message };
            }
          },

          // Mark receivable as paid
          async markReceivableAsPaid(receivableId, paymentData) {
            set((state) => {
              state.isMarkingPaid = true;
              state.updateError = null;
            });

            try {
              const result = await apiMiddleware.post(`/api/receivables/${receivableId}/mark-paid`, paymentData, {
                loadingMessage: 'جاري تسجيل الدفعة...',
                successMessage: 'تم تسجيل الدفعة بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                // Update receivable status
                const index = state.receivables.findIndex(receivable => receivable.id === receivableId);
                if (index !== -1) {
                  state.receivables[index].status = 'مسدد';
                  state.receivables[index].paidAmount = paymentData.amount;
                  state.receivables[index].paidDate = paymentData.paymentDate;
                }
                
                if (state.selectedReceivable?.id === receivableId) {
                  state.selectedReceivable.status = 'مسدد';
                  state.selectedReceivable.paidAmount = paymentData.amount;
                  state.selectedReceivable.paidDate = paymentData.paymentDate;
                }
                
                state.isMarkingPaid = false;
                state.updateError = null;
              });

              return { success: true, data: result };
            } catch (error) {
              console.error('Failed to mark receivable as paid:', error);
              
              set((state) => {
                state.isMarkingPaid = false;
                state.updateError = error.message || 'فشل في تسجيل الدفعة';
              });

              return { success: false, error: error.message };
            }
          },

          // Generate invoice for receivable
          async generateInvoice(receivableId) {
            set((state) => {
              state.isGeneratingInvoice = true;
            });

            try {
              const invoiceData = await apiMiddleware.get(`/api/receivables/${receivableId}/invoice`, {
                loadingMessage: 'جاري إنشاء الفاتورة...',
                successMessage: 'تم إنشاء الفاتورة بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.isGeneratingInvoice = false;
              });

              return { success: true, data: invoiceData };
            } catch (error) {
              console.error('Failed to generate invoice:', error);
              
              set((state) => {
                state.isGeneratingInvoice = false;
              });

              return { success: false, error: error.message };
            }
          },

          // Fetch receivables statistics
          async fetchReceivablesStats() {
            set((state) => {
              state.statsLoading = true;
              state.statsError = null;
            });

            try {
              const stats = await apiMiddleware.get('/api/receivables/statistics', {
                showErrorNotification: false
              });

              set((state) => {
                state.receivablesStats = stats;
                state.statsLoading = false;
                state.statsError = null;
              });

              return stats;
            } catch (error) {
              console.error('Failed to fetch receivables stats:', error);
              
              set((state) => {
                state.receivablesStats = null;
                state.statsLoading = false;
                state.statsError = error.message;
              });

              return null;
            }
          },

          // Set selected receivable
          setSelectedReceivable(receivable) {
            set((state) => {
              state.selectedReceivable = receivable;
              state.showReceivableDetails = !!receivable;
            });
          },

          // Set filters
          setSearchQuery(query) {
            set((state) => {
              state.searchQuery = query;
            });
          },

          setStatusFilter(status) {
            set((state) => {
              state.statusFilter = status;
            });
          },

          setClientFilter(clientId) {
            set((state) => {
              state.clientFilter = clientId;
            });
          },

          setContractFilter(contractId) {
            set((state) => {
              state.contractFilter = contractId;
            });
          },

          setDueDateFilter(dueDateFilter) {
            set((state) => {
              state.dueDateFilter = dueDateFilter;
            });
          },

          setAmountRangeFilter(amountRange) {
            set((state) => {
              state.amountRangeFilter = amountRange;
            });
          },

          // Clear errors
          clearErrors() {
            set((state) => {
              state.error = null;
              state.updateError = null;
              state.statsError = null;
            });
          },

          // Get filtered receivables
          getFilteredReceivables() {
            const state = get();
            const { 
              receivables, 
              searchQuery, 
              statusFilter, 
              clientFilter, 
              contractFilter,
              dueDateFilter,
              amountRangeFilter 
            } = state;

            return receivables.filter(receivable => {
              // Search filter
              const matchesSearch = !searchQuery ||
                receivable.invoiceNumber?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                receivable.clientName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                receivable.contractNumber?.toLowerCase().includes(searchQuery.toLowerCase());

              // Status filter
              const matchesStatus = !statusFilter || statusFilter === "all" ||
                receivable.status === statusFilter;

              // Client filter
              const matchesClient = !clientFilter || clientFilter === "all" ||
                receivable.clientId === clientFilter;

              // Contract filter
              const matchesContract = !contractFilter || contractFilter === "all" ||
                receivable.contractId === contractFilter;

              // Due date filter
              const today = new Date();
              const dueDate = new Date(receivable.dueDate);
              let matchesDueDate = true;
              
              if (dueDateFilter === 'overdue') {
                matchesDueDate = dueDate < today && receivable.status !== 'مسدد';
              } else if (dueDateFilter === 'due-today') {
                matchesDueDate = dueDate.toDateString() === today.toDateString();
              } else if (dueDateFilter === 'due-this-week') {
                const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
                matchesDueDate = dueDate >= today && dueDate <= weekFromNow;
              } else if (dueDateFilter === 'due-this-month') {
                matchesDueDate = dueDate.getMonth() === today.getMonth() && 
                               dueDate.getFullYear() === today.getFullYear();
              }

              // Amount range filter
              const matchesAmountRange = (!amountRangeFilter.min || receivable.amount >= amountRangeFilter.min) &&
                (!amountRangeFilter.max || receivable.amount <= amountRangeFilter.max);

              return matchesSearch && matchesStatus && matchesClient && 
                     matchesContract && matchesDueDate && matchesAmountRange;
            });
          },

          // Get receivable by ID
          getReceivableById(receivableId) {
            const state = get();
            return state.receivables.find(receivable => receivable.id === receivableId);
          },

          // Get overdue receivables count
          getOverdueCount() {
            const state = get();
            const today = new Date();
            return state.receivables.filter(receivable => 
              new Date(receivable.dueDate) < today && receivable.status !== 'مسدد'
            ).length;
          },

          // Get total outstanding amount
          getTotalOutstanding() {
            const state = get();
            return state.receivables
              .filter(receivable => receivable.status !== 'مسدد')
              .reduce((total, receivable) => total + (receivable.amount || 0), 0);
          },

          // Refresh data
          async refresh() {
            return get().actions.fetchReceivables(true);
          }
        }
      })),
      {
        name: 'receivables-store',
        partialize: (state) => ({
          receivables: state.receivables,
          lastFetched: state.lastFetched,
          searchQuery: state.searchQuery,
          statusFilter: state.statusFilter,
          clientFilter: state.clientFilter,
          contractFilter: state.contractFilter
        })
      }
    ),
    {
      name: 'receivables-store'
    }
  )
);

// Selectors
export const useReceivables = () => useReceivablesStore((state) => state.receivables);
export const useOverdueReceivables = () => useReceivablesStore((state) => state.overdueReceivables);
export const useUpcomingReceivables = () => useReceivablesStore((state) => state.upcomingReceivables);
export const usePaidReceivables = () => useReceivablesStore((state) => state.paidReceivables);
export const useReceivablesLoading = () => useReceivablesStore((state) => state.isLoading);
export const useReceivablesError = () => useReceivablesStore((state) => state.error);
export const useSelectedReceivable = () => useReceivablesStore((state) => state.selectedReceivable);
export const useReceivablesStats = () => useReceivablesStore((state) => state.receivablesStats);
export const useReceivablesActions = () => useReceivablesStore((state) => state.actions);

export const useFilteredReceivables = () => useReceivablesStore((state) => 
  state.actions.getFilteredReceivables()
);

export const useReceivablesStatus = () => useReceivablesStore((state) => ({
  isLoading: state.isLoading,
  isUpdating: state.isUpdating,
  isMarkingPaid: state.isMarkingPaid,
  isGeneratingInvoice: state.isGeneratingInvoice,
  statsLoading: state.statsLoading,
  error: state.error,
  updateError: state.updateError,
  statsError: state.statsError,
  totalCount: state.totalCount,
  lastFetched: state.lastFetched
}));

export default useReceivablesStore;
