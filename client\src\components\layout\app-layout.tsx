import { ReactNode } from "react";
import { Sidebar } from "./sidebar";
import { Header } from "./header";
import { useLanguage } from "@/hooks/use-language";
import { cn } from "@/lib/utils";

interface AppLayoutProps {
  children: ReactNode;
  title: string;
  subtitle?: string;
}

export function AppLayout({ children, title, subtitle }: AppLayoutProps) {
  const { isRTL } = useLanguage();

  return (
    <div className="min-h-screen flex bg-background dark:bg-background">
      <Sidebar />
      <main className={cn(
        "flex-1 transition-all duration-300 relative",
        isRTL ? "mr-16" : "ml-16"
      )}>
        <Header title={title} subtitle={subtitle} />
        <div className={cn(
          "p-6",
          isRTL ? "text-right" : "text-left"
        )}>
          <div className={cn(
            "prose max-w-none",
            isRTL ? "text-justify" : "text-justify"
          )}>
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}
