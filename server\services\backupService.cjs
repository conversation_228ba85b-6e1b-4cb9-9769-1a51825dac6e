// ===== BACKUP SERVICE =====
// Author: Augment Code
// Description: Service for database backup and recovery operations

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { formatDateArabic, generateUUID } = require('../utils/index.cjs');

class BackupService {
  constructor(db) {
    this.db = db;
    this.backupDir = path.join(process.cwd(), 'backups');
    this.ensureBackupDirectory();
  }

  async ensureBackupDirectory() {
    try {
      await fs.access(this.backupDir);
    } catch {
      await fs.mkdir(this.backupDir, { recursive: true });
    }
  }

  /**
   * Create a full database backup
   * @param {string} description - Backup description
   * @param {number} userId - User ID creating the backup
   * @returns {Promise<Object>} Backup metadata
   */
  async createFullBackup(description = 'نسخة احتياطية كاملة', userId = null) {
    const backupName = `full_backup_${Date.now()}`;
    const fileName = `${backupName}.sql`;
    const filePath = path.join(this.backupDir, fileName);

    try {
      // Create backup metadata record
      const backupId = await this.createBackupRecord(backupName, 'FULL', description, filePath, userId);

      // Log backup start
      await this.logBackupEvent(backupId, 'INFO', 'بدء إنشاء النسخة الاحتياطية الكاملة');

      // Get all table data
      const tables = await this.getAllTables();
      let sqlContent = '';
      let totalRecords = 0;

      // Add header
      sqlContent += `-- نسخة احتياطية كاملة\n`;
      sqlContent += `-- تاريخ الإنشاء: ${formatDateArabic(new Date())}\n`;
      sqlContent += `-- معرف النسخة: ${backupName}\n\n`;

      // Backup each table
      for (const table of tables) {
        await this.logBackupEvent(backupId, 'INFO', `نسخ جدول: ${table}`);
        
        const tableData = await this.getTableData(table);
        const tableRecords = tableData.length;
        totalRecords += tableRecords;

        sqlContent += `-- جدول: ${table} (${tableRecords} سجل)\n`;
        sqlContent += `DELETE FROM ${table};\n`;

        if (tableRecords > 0) {
          const columns = Object.keys(tableData[0]);
          const columnsList = columns.join(', ');

          for (const row of tableData) {
            const values = columns.map(col => {
              const value = row[col];
              if (value === null) return 'NULL';
              if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
              return value;
            }).join(', ');

            sqlContent += `INSERT INTO ${table} (${columnsList}) VALUES (${values});\n`;
          }
        }

        sqlContent += '\n';
      }

      // Write backup file
      await fs.writeFile(filePath, sqlContent, 'utf8');

      // Calculate file size and checksum
      const stats = await fs.stat(filePath);
      const fileSize = stats.size;
      const checksum = await this.calculateFileChecksum(filePath);

      // Update backup metadata
      await this.updateBackupRecord(backupId, {
        status: 'COMPLETED',
        fileSize,
        recordCount: totalRecords,
        checksum,
        completedAt: new Date().toISOString()
      });

      await this.logBackupEvent(backupId, 'INFO', `اكتملت النسخة الاحتياطية بنجاح. السجلات: ${totalRecords}, الحجم: ${fileSize} بايت`);

      return {
        id: backupId,
        name: backupName,
        filePath,
        fileSize,
        recordCount: totalRecords,
        checksum
      };

    } catch (error) {
      await this.logBackupEvent(backupId, 'ERROR', `فشل في إنشاء النسخة الاحتياطية: ${error.message}`);
      await this.updateBackupRecord(backupId, { status: 'FAILED' });
      throw error;
    }
  }

  /**
   * Restore database from backup
   * @param {string} backupName - Name of backup to restore
   * @returns {Promise<Object>} Restore result
   */
  async restoreFromBackup(backupName) {
    try {
      // Get backup metadata
      const backup = await this.getBackupMetadata(backupName);
      if (!backup) {
        throw new Error(`النسخة الاحتياطية غير موجودة: ${backupName}`);
      }

      // Verify backup file exists
      const backupExists = await fs.access(backup.filePath).then(() => true).catch(() => false);
      if (!backupExists) {
        throw new Error(`ملف النسخة الاحتياطية غير موجود: ${backup.filePath}`);
      }

      // Verify checksum
      const currentChecksum = await this.calculateFileChecksum(backup.filePath);
      if (currentChecksum !== backup.checksum) {
        throw new Error('فشل في التحقق من سلامة ملف النسخة الاحتياطية');
      }

      // Read and execute backup SQL
      const sqlContent = await fs.readFile(backup.filePath, 'utf8');
      const statements = sqlContent.split(';').filter(stmt => stmt.trim() && !stmt.trim().startsWith('--'));

      let executedStatements = 0;
      for (const statement of statements) {
        if (statement.trim()) {
          await this.executeSQL(statement.trim());
          executedStatements++;
        }
      }

      return {
        backupName,
        statementsExecuted: executedStatements,
        recordsRestored: backup.recordCount
      };

    } catch (error) {
      throw new Error(`فشل في استعادة النسخة الاحتياطية: ${error.message}`);
    }
  }

  /**
   * List all available backups
   * @returns {Promise<Array>} List of backups
   */
  async listBackups() {
    return new Promise((resolve, reject) => {
      this.db.all(`
        SELECT * FROM BackupMetadata 
        ORDER BY createdAt DESC
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  /**
   * Delete old backups based on retention policy
   * @param {number} retentionDays - Number of days to keep backups
   * @returns {Promise<number>} Number of deleted backups
   */
  async cleanupOldBackups(retentionDays = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const oldBackups = await new Promise((resolve, reject) => {
      this.db.all(`
        SELECT * FROM BackupMetadata 
        WHERE createdAt < ? AND status = 'COMPLETED'
      `, [cutoffDate.toISOString()], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    let deletedCount = 0;
    for (const backup of oldBackups) {
      try {
        // Delete file
        await fs.unlink(backup.filePath);
        
        // Delete metadata record
        await new Promise((resolve, reject) => {
          this.db.run(`DELETE FROM BackupMetadata WHERE id = ?`, [backup.id], (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        deletedCount++;
      } catch (error) {
        console.error(`فشل في حذف النسخة الاحتياطية ${backup.backupName}:`, error);
      }
    }

    return deletedCount;
  }

  // Helper methods
  async getAllTables() {
    return new Promise((resolve, reject) => {
      this.db.all(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
        AND name NOT IN ('MigrationHistory', 'BackupMetadata', 'BackupLog')
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows.map(row => row.name));
      });
    });
  }

  async getTableData(tableName) {
    return new Promise((resolve, reject) => {
      this.db.all(`SELECT * FROM ${tableName}`, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  async createBackupRecord(name, type, description, filePath, userId) {
    return new Promise((resolve, reject) => {
      this.db.run(`
        INSERT INTO BackupMetadata (backupName, backupType, description, filePath, createdBy, status)
        VALUES (?, ?, ?, ?, ?, 'IN_PROGRESS')
      `, [name, type, description, filePath, userId], function(err) {
        if (err) reject(err);
        else resolve(this.lastID);
      });
    });
  }

  async updateBackupRecord(id, updates) {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    
    return new Promise((resolve, reject) => {
      this.db.run(`UPDATE BackupMetadata SET ${fields} WHERE id = ?`, [...values, id], (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async logBackupEvent(backupId, level, message, details = null) {
    return new Promise((resolve, reject) => {
      this.db.run(`
        INSERT INTO BackupLog (backupId, logLevel, message, details)
        VALUES (?, ?, ?, ?)
      `, [backupId, level, message, details], (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async getBackupMetadata(backupName) {
    return new Promise((resolve, reject) => {
      this.db.get(`
        SELECT * FROM BackupMetadata WHERE backupName = ?
      `, [backupName], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  async calculateFileChecksum(filePath) {
    const fileBuffer = await fs.readFile(filePath);
    return crypto.createHash('sha256').update(fileBuffer).digest('hex');
  }

  async executeSQL(sql) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }
}

module.exports = BackupService;
