// ===== TEST SETUP =====
// Author: Augment Code
// Description: Test environment setup and configuration

const path = require('path');

// Set test environment
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = ':memory:'; // Use in-memory database for tests

// Mock console methods to reduce noise during tests
const originalConsole = { ...console };

const setupTestEnvironment = () => {
  // Suppress console output during tests unless explicitly needed
  if (!process.env.VERBOSE_TESTS) {
    console.log = jest.fn();
    console.info = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
  }
};

const teardownTestEnvironment = () => {
  // Restore console methods
  Object.assign(console, originalConsole);
};

// Global test utilities
global.testUtils = {
  // Mock user for authentication tests
  mockUser: {
    id: 1,
    username: 'testuser',
    role: 'admin',
    permissions: ['read', 'write', 'delete', 'admin']
  },

  // Mock client data
  mockClient: {
    clientId: 'TEST001',
    clientType: 'individual',
    clientName: 'عميل تجريبي',
    clientAddress: 'عنوان تجريبي',
    clientPhoneWhatsapp: '0501234567',
    clientEmail: '<EMAIL>',
    isActive: true
  },

  // Mock contract data
  mockContract: {
    contractNumber: 'CONTRACT001',
    contractDescription: 'عقد تجريبي',
    clientId: 1,
    contractType: 'إيجار',
    startDate: '2024-01-01',
    contractDurationYears: 1,
    totalContractValue: 12000,
    monthlyAmount: 1000,
    paymentDay: 1,
    isActive: true
  },

  // Mock payment data
  mockPayment: {
    contractId: 1,
    paymentType: 'installment',
    amount: 1000,
    dueDate: '2024-01-01',
    status: 'pending',
    installmentNumber: 1
  },

  // Generate test data
  generateTestClient: (overrides = {}) => ({
    ...global.testUtils.mockClient,
    clientId: `TEST${Date.now()}`,
    ...overrides
  }),

  generateTestContract: (overrides = {}) => ({
    ...global.testUtils.mockContract,
    contractNumber: `CONTRACT${Date.now()}`,
    ...overrides
  }),

  generateTestPayment: (overrides = {}) => ({
    ...global.testUtils.mockPayment,
    ...overrides
  })
};

// Jest setup
beforeAll(() => {
  setupTestEnvironment();
});

afterAll(() => {
  teardownTestEnvironment();
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

module.exports = {
  setupTestEnvironment,
  teardownTestEnvironment
};
