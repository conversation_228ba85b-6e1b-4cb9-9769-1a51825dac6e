import React, { useState, useMemo, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { formatFileSize } from "@/lib/image-utils";
import { formatContractStatus } from "@/lib/formatters";
import { useCurrency } from "@/hooks/use-currency";
import { useDateFormat } from "@/hooks/use-date-format";
import {
  User,
  Phone,
  Mail,
  MapPin,
  Building2,
  FileText,
  CreditCard,
  Eye,
  Printer,
  Calendar,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Image,
  Download,
  ZoomIn,
  Edit,
  X
} from "lucide-react";
import { type Client, type Contract } from "@shared/schema";
import { type DocumentFile } from "@/components/document-upload";

interface ClientCardProps {
  client: Client;
  contracts?: Contract[];
  showPrintButton?: boolean;
  showEditButton?: boolean;
  onViewContract?: (contractId: number) => void;
  onPrint?: () => void;
  onEdit?: () => void;
}

export const ClientCard: React.FC<ClientCardProps> = ({
  client,
  contracts = [],
  showPrintButton = true,
  showEditButton = true,
  onViewContract,
  onPrint,
  onEdit
}) => {
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<DocumentFile | null>(null);

  // Helper function to get default value
  const getDefaultValue = (value: string | null | undefined): string => {
    return value && value.trim() !== "" ? value : "لا يوجد";
  };

  // Default print function
  const handlePrint = () => {
    if (onPrint) {
      onPrint();
    } else {
      // استخدام وظيفة الطباعة الافتراضية
      window.print();
    }
  };

  // Handle ESC key to close modals
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (showDocumentModal) {
          setShowDocumentModal(false);
        } else if (showImageModal) {
          setShowImageModal(false);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showDocumentModal, showImageModal]);

  // Parse documents from JSON string
  const documents: DocumentFile[] = useMemo(() => {
    try {
      if (!client.clientDocuments) return [];
      const parsed = JSON.parse(client.clientDocuments);
      return Array.isArray(parsed) ? parsed : [];
    } catch (e) {
      console.warn('Failed to parse client documents:', e);
      return [];
    }
  }, [client.clientDocuments]);

  const { formatCurrency } = useCurrency();
  const { formatDate } = useDateFormat();

  const getTotalContractValue = () => {
    return contracts.reduce((total, contract) => total + contract.totalContractValue, 0);
  };

  const getActiveContractsCount = () => {
    return contracts.filter(contract => contract.contractStatus === 'نشط').length;
  };

  const getTotalOutstanding = () => {
    return contracts.reduce((total, contract) => total + (contract.outstandingAmount || 0), 0);
  };



  const handleImageView = (imageData: string) => {
    setSelectedImage(imageData);
    setShowImageModal(true);
  };

  const handleDocumentClick = (doc: DocumentFile) => {
    setSelectedDocument(doc);
    setShowDocumentModal(true);
  };

  const printDocument = (doc: DocumentFile) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const isImage = doc.type.startsWith('image/');

      printWindow.document.write(`
        <html>
          <head>
            <title>طباعة المستند - ${doc.name}</title>
            <style>
              body {
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
                text-align: center;
              }
              .header {
                margin-bottom: 20px;
                border-bottom: 2px solid #333;
                padding-bottom: 10px;
              }
              .document-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
              }
              .document-info {
                font-size: 14px;
                color: #666;
                margin-bottom: 20px;
              }
              img {
                max-width: 100%;
                max-height: 80vh;
                border: 1px solid #ddd;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              }
              iframe {
                width: 100%;
                height: 80vh;
                border: 1px solid #ddd;
              }
              @media print {
                body { padding: 0; }
                .header { page-break-inside: avoid; }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <div class="document-title">${doc.name}</div>
              <div class="document-info">تاريخ الرفع: ${new Date(doc.uploadDate).toLocaleDateString('ar-EG')}</div>
            </div>
            ${isImage
              ? `<img src="${doc.data}" alt="${doc.name}" onload="window.print(); window.close();" />`
              : `<iframe src="${doc.data}" onload="window.print(); window.close();"></iframe>`
            }
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  const downloadDocument = (doc: DocumentFile) => {
    const link = document.createElement('a');
    link.href = doc.data;
    link.download = doc.name;
    link.click();
  };

  const handlePrintImage = (imageData: string) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>طباعة المرفق - ${client.clientName}</title>
            <style>
              body {
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
                text-align: center;
              }
              .header {
                margin-bottom: 20px;
                border-bottom: 2px solid #333;
                padding-bottom: 10px;
              }
              .client-info {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
              }
              .document-title {
                font-size: 16px;
                color: #666;
                margin-bottom: 20px;
              }
              img {
                max-width: 100%;
                max-height: 80vh;
                border: 1px solid #ddd;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              }
              @media print {
                body { padding: 0; }
                .header { page-break-inside: avoid; }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <div class="client-info">مرفق العميل: ${client.clientName}</div>
              <div class="document-title">رقم العميل: ${client.clientId} | نوع العميل: ${client.clientType}</div>
            </div>
            <img src="${imageData}" alt="مرفق العميل" onload="window.print(); window.close();" />
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  return (
    <>
      <style>{`
        @media print {
          .client-attachment-image {
            max-width: 200px !important;
            max-height: 150px !important;
            page-break-inside: avoid;
          }
          .print-hide {
            display: none !important;
          }
        }
      `}</style>
      <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 print:shadow-none print:p-4 client-card-print">
      {/* Header */}
      <div className="flex justify-between items-start mb-6">
        <div className="flex items-center space-x-reverse space-x-4">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center print-hide">
            <User className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{client.clientName}</h1>
            <p className="text-gray-600 dark:text-gray-400">رقم العميل: {client.clientId}</p>
            <Badge variant={client.clientType === 'أفراد' ? 'default' : 'secondary'}>
              {client.clientType}
            </Badge>
          </div>
        </div>
        <div className="flex gap-2 print-hide">
          {showEditButton && onEdit && (
            <Button onClick={onEdit} variant="default" className="gap-2">
              <Edit className="h-4 w-4" />
              تعديل البيانات
            </Button>
          )}
          {showPrintButton && (
            <Button onClick={handlePrint} variant="outline" className="gap-2">
              <Printer className="h-4 w-4" />
              طباعة الكارت
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5 print-hide" />
              البيانات الأساسية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start gap-3">
              <MapPin className="h-4 w-4 text-gray-500 mt-1" />
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">العنوان</p>
                <p className="text-gray-900 dark:text-gray-100">{getDefaultValue(client.clientAddress)}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Phone className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">رقم الهاتف/واتساب</p>
                <p className="text-gray-900 dark:text-gray-100">{getDefaultValue(client.clientPhoneWhatsapp)}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Phone className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">رقم هاتف إضافي</p>
                <p className="text-gray-900 dark:text-gray-100">{getDefaultValue(client.clientPhone2)}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Phone className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">رقم هاتف ثالث</p>
                <p className="text-gray-900 dark:text-gray-100">{getDefaultValue(client.clientPhone3)}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Mail className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">البريد الإلكتروني</p>
                <p className="text-gray-900 dark:text-gray-100">{getDefaultValue(client.clientEmail)}</p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <FileText className="h-4 w-4 text-gray-500 mt-1" />
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">ملاحظات</p>
                <p className="text-gray-900 dark:text-gray-100">{getDefaultValue(client.clientNotes)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Financial & Legal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5 print-hide" />
              المعلومات المالية والقانونية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <CreditCard className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">التصنيف المالي</p>
                <p className="text-gray-900 dark:text-gray-100">{getDefaultValue(client.clientFinancial_Category)}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <User className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">الممثل القانوني</p>
                <p className="text-gray-900 dark:text-gray-100">{getDefaultValue(client.clientLegal_Rep)}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <FileText className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">رقم التسجيل</p>
                <p className="text-gray-900 dark:text-gray-100">{getDefaultValue(client.clientReg_Number)}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <FileText className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">رقم التسجيل الضريبي</p>
                <p className="text-gray-900 dark:text-gray-100">{getDefaultValue(client.clientTaxReg_Number)}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Building2 className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">الحالة القانونية</p>
                <p className="text-gray-900 dark:text-gray-100">{getDefaultValue(client.clientLegal_Status)}</p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <FileText className="h-4 w-4 text-gray-500 mt-1" />
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">ملاحظات إضافية</p>
                <p className="text-gray-900 dark:text-gray-100">{getDefaultValue(client.clientRemarks)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* ID Card Image */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5 print-hide" />
              صورة بطاقة الهوية
            </CardTitle>
          </CardHeader>
          <CardContent>
            {client.clientID_Image ? (
              <>
                <div className="relative group cursor-pointer print:cursor-default" onClick={() => handleImageView(client.clientID_Image!)}>
                  <img
                    src={client.clientID_Image}
                    alt="صورة بطاقة الهوية"
                    className="w-full h-48 object-cover rounded-lg border-2 border-gray-200 client-attachment-image print:cursor-default"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                    <ZoomIn className="h-8 w-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  </div>
                </div>
                <div className="flex gap-2 mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleImageView(client.clientID_Image!);
                    }}
                    className="flex-1 gap-2"
                  >
                    <Eye className="h-4 w-4" />
                    عرض
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      const link = document.createElement('a');
                      link.href = client.clientID_Image!;
                      link.download = `ID_Card_${client.clientName}.jpg`;
                      link.click();
                    }}
                    className="flex-1 gap-2"
                  >
                    <Download className="h-4 w-4" />
                    تحميل
                  </Button>
                </div>
              </>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <CreditCard className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>لا يوجد صورة بطاقة هوية</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Additional Documents */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 print-hide" />
              المستندات الإضافية ({documents.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {documents.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {documents.map((doc) => (
                  <div
                    key={doc.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer print:cursor-default"
                    onClick={() => handleDocumentClick(doc)}
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <div className="flex-shrink-0 text-gray-500 print-hide">
                        {doc.type.startsWith('image/') ? (
                          <Image className="h-6 w-6" />
                        ) : (
                          <FileText className="h-6 w-6" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{doc.name}</p>
                        <p className="text-xs text-gray-500 print-hide">
                          {formatFileSize(doc.size)} • {new Date(doc.uploadDate).toLocaleDateString('ar-EG')}
                        </p>
                      </div>
                    </div>

                    {doc.type.startsWith('image/') && (
                      <div className="mb-3">
                        <img
                          src={doc.data}
                          alt={doc.name}
                          className="w-full h-24 object-cover rounded border client-attachment-image"
                        />
                      </div>
                    )}

                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDocumentClick(doc);
                        }}
                        className="flex-1 h-8 text-xs"
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        عرض
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          printDocument(doc);
                        }}
                        className="flex-1 h-8 text-xs"
                      >
                        <Printer className="h-3 w-3 mr-1" />
                        طباعة
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          downloadDocument(doc);
                        }}
                        className="flex-1 h-8 text-xs"
                      >
                        <Download className="h-3 w-3 mr-1" />
                        تحميل
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p className="text-lg font-medium">لا يوجد مستندات إضافية</p>
                <p className="text-sm mt-1">يمكن إضافة المستندات عند تعديل بيانات العميل</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Contracts Summary */}
        {contracts.length > 0 && (
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                ملخص العقود
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {contracts.length}
                  </div>
                  <div className="text-sm text-blue-600 dark:text-blue-400">إجمالي العقود</div>
                </div>
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {getActiveContractsCount()}
                  </div>
                  <div className="text-sm text-green-600 dark:text-green-400">العقود النشطة</div>
                </div>
                <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {formatCurrency(getTotalContractValue())}
                  </div>
                  <div className="text-sm text-purple-600 dark:text-purple-400">إجمالي القيمة</div>
                </div>
              </div>

              <div className="space-y-4">
                {contracts.slice(0, 3).map((contract) => (
                  <div key={contract.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">{contract.contractNumber}</h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{contract.contractType}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(contract.totalContractValue)}</p>
                      <Badge variant={formatContractStatus(contract.contractStatus).variant}>
                        {formatContractStatus(contract.contractStatus).text}
                      </Badge>
                    </div>
                    {onViewContract && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onViewContract(contract.id)}
                        className="ml-4"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                {contracts.length > 3 && (
                  <p className="text-center text-sm text-gray-500 dark:text-gray-400">
                    و {contracts.length - 3} عقد آخر...
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Image Modal */}
      {showImageModal && selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 print:hidden"
          onClick={() => setShowImageModal(false)}
        >
          <div
            className="relative max-w-4xl max-h-full p-4"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button - زر الإغلاق الوحيد */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowImageModal(false)}
              className="absolute top-2 right-2 text-white hover:bg-white/20 bg-black/50 rounded-full w-10 h-10 p-0 border border-white/30"
              title="إغلاق (ESC)"
            >
              <X className="h-6 w-6" />
            </Button>

            <img
              src={selectedImage}
              alt="مرفق العميل"
              className="max-w-full max-h-[90vh] object-contain"
            />
          </div>
        </div>
      )}

      {/* Document Modal */}
      {showDocumentModal && selectedDocument && (
        <div
          className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"
          onClick={() => setShowDocumentModal(false)}
        >
          <div
            className="relative max-w-4xl max-h-[95vh] p-4 w-full"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button - زر الإغلاق الوحيد */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDocumentModal(false)}
              className="absolute top-2 right-2 text-white hover:bg-white/20 z-10 bg-black/50 rounded-full w-10 h-10 p-0 border border-white/30"
              title="إغلاق (ESC)"
            >
              <X className="h-6 w-6" />
            </Button>

            <div className="bg-white rounded-lg overflow-hidden max-h-full">
              <div className="p-4 border-b">
                <h3 className="font-medium text-center">{selectedDocument.name}</h3>
                <p className="text-sm text-gray-500 text-center">
                  {formatFileSize(selectedDocument.size)} • {new Date(selectedDocument.uploadDate).toLocaleDateString('ar-EG')}
                </p>
              </div>

              <div className="p-4 max-h-[70vh] overflow-auto flex items-center justify-center">
                {selectedDocument.type.startsWith('image/') ? (
                  <img
                    src={selectedDocument.data}
                    alt={selectedDocument.name}
                    className="max-w-full max-h-full object-contain"
                  />
                ) : (
                  <iframe
                    src={selectedDocument.data}
                    className="w-full h-96 border-0"
                    title={selectedDocument.name}
                  />
                )}
              </div>

              <div className="p-4 border-t flex gap-2 justify-center">
                <Button
                  variant="outline"
                  onClick={() => printDocument(selectedDocument)}
                  className="gap-2"
                >
                  <Printer className="h-4 w-4" />
                  طباعة
                </Button>
                <Button
                  variant="outline"
                  onClick={() => downloadDocument(selectedDocument)}
                  className="gap-2"
                >
                  <Download className="h-4 w-4" />
                  تحميل
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
    </>
  );
};