const database = require('../models/db.cjs');

class SettingsService {
  // جلب الإعدادات
  getSettings() {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      
      db.get('SELECT * FROM Settings ORDER BY id DESC LIMIT 1', [], (err, row) => {
        if (err) return reject(err);
        
        if (row) {
          // تحويل JSON strings إلى objects
          try {
            if (row.workDays && typeof row.workDays === 'string') {
              row.workDays = JSON.parse(row.workDays);
            }
            if (row.regions && typeof row.regions === 'string') {
              row.regions = JSON.parse(row.regions);
            }
            if (row.owners && typeof row.owners === 'string') {
              row.owners = JSON.parse(row.owners);
            }
            if (row.governorates && typeof row.governorates === 'string') {
              row.governorates = JSON.parse(row.governorates);
            }
          } catch (parseError) {
            console.warn('JSON parse warning in settings:', parseError);
          }
        }
        
        resolve(row);
      });
    });
  }

  // حفظ الإعدادات
  saveSettings(settingsData) {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      
      // التحقق من وجود سجل
      db.get('SELECT COUNT(*) as count FROM Settings', [], (err, result) => {
        if (err) return reject(err);
        
        const isUpdate = result.count > 0;
        
        // تحضير البيانات
        const data = {
          // Company Information
          companyName: settingsData.companyName || '',
          programName: settingsData.programName || '',
          companyRegNo: settingsData.companyRegNo || '',
          taxId: settingsData.taxId || '',
          about: settingsData.about || '',
          companyLogo: settingsData.companyLogo || '',
          companyAddress: settingsData.companyAddress || '',
          companyPhone: settingsData.companyPhone || '',
          companyEmail: settingsData.companyEmail || '',
          companyWebsite: settingsData.companyWebsite || '',
          
          // Localization
          language: settingsData.language || 'ar',
          country: settingsData.country || '',
          currency: settingsData.currency || '',
          currencySymbol: settingsData.currencySymbol || '',
          dateFormat: settingsData.dateFormat || '',
          timeFormat: settingsData.timeFormat || '24',
          timeZone: settingsData.timeZone || 'Asia/Riyadh',
          fiscalYearStart: settingsData.fiscalYearStart || '01/01',
          calendarType: settingsData.calendarType || 'gregorian',
          
          // UI Settings
          theme: settingsData.theme || 'system',
          fontSize: settingsData.fontSize || 'medium',
          primaryColor: settingsData.primaryColor || '#3b82f6',
          fontFamily: settingsData.fontFamily || 'Cairo',
          
          // Number Formats
          decimalPlaces: settingsData.decimalPlaces || '2',
          numberSeparator: settingsData.numberSeparator || ',',
          numberingFormat: settingsData.numberingFormat || '',
          contractNumberFormat: settingsData.contractNumberFormat || 'C-{YYYY}-{####}',
          clientNumberFormat: settingsData.clientNumberFormat || 'CL-{####}',
          paymentNumberFormat: settingsData.paymentNumberFormat || 'P-{YYYY}-{####}',
          invoiceNumberFormat: settingsData.invoiceNumberFormat || 'INV-{YYYY}-{####}',
          receiptNumberFormat: settingsData.receiptNumberFormat || 'REC-{YYYY}-{####}',
          
          // Notifications
          notificationEmail: settingsData.notificationEmail || '',
          notificationFrequency: settingsData.notificationFrequency || 'daily',
          enableEmailNotifications: settingsData.enableEmailNotifications ? 1 : 0,
          enableSMSNotifications: settingsData.enableSMSNotifications ? 1 : 0,
          
          // Business Settings
          defaultContractDuration: settingsData.defaultContractDuration || '12',
          defaultPaymentTerms: settingsData.defaultPaymentTerms || 'شهري',
          defaultTaxRate: settingsData.defaultTaxRate || '15',
          backupFrequency: settingsData.backupFrequency || 'daily',
          enableMultiCurrency: settingsData.enableMultiCurrency ? 1 : 0,
          
          // Security Settings
          sessionTimeout: settingsData.sessionTimeout || '30',
          maxLoginAttempts: settingsData.maxLoginAttempts || '5',
          passwordPolicy: settingsData.passwordPolicy || 'medium',
          enableTwoFactor: settingsData.enableTwoFactor ? 1 : 0,
          enableAuditLog: settingsData.enableAuditLog ? 1 : 0,
          
          // System Settings
          cacheTimeout: settingsData.cacheTimeout || '60',
          maxFileSize: settingsData.maxFileSize || '10',
          enableCaching: settingsData.enableCaching ? 1 : 0,
          enableCompression: settingsData.enableCompression ? 1 : 0,
          enableDebugMode: settingsData.enableDebugMode ? 1 : 0,
          
          // Reference Data
          regions: JSON.stringify(settingsData.regions || []),
          owners: JSON.stringify(settingsData.owners || []),
          governorates: JSON.stringify(settingsData.governorates || []),
          workDays: JSON.stringify(settingsData.workDays || [])
        };
        
        if (isUpdate) {
          this.updateSettings(data, resolve, reject);
        } else {
          this.insertSettings(data, resolve, reject);
        }
      });
    });
  }

  // إدراج إعدادات جديدة
  insertSettings(data, resolve, reject) {
    const db = database.getConnection();
    
    const sql = `
      INSERT INTO Settings (
        companyName, programName, companyRegNo, taxId, about, companyLogo,
        companyAddress, companyPhone, companyEmail, companyWebsite,
        language, country, currency, currencySymbol, dateFormat,
        timeFormat, timeZone, fiscalYearStart, calendarType,
        theme, fontSize, primaryColor, fontFamily,
        decimalPlaces, numberSeparator, numberingFormat,
        contractNumberFormat, clientNumberFormat, paymentNumberFormat,
        invoiceNumberFormat, receiptNumberFormat,
        notificationEmail, notificationFrequency, enableEmailNotifications, enableSMSNotifications,
        defaultContractDuration, defaultPaymentTerms, defaultTaxRate, backupFrequency, enableMultiCurrency,
        sessionTimeout, maxLoginAttempts, passwordPolicy, enableTwoFactor, enableAuditLog,
        cacheTimeout, maxFileSize, enableCaching, enableCompression, enableDebugMode,
        regions, owners, governorates, workDays,
        createdAt, updatedAt
      ) VALUES (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      )
    `;
    
    const values = [
      data.companyName, data.programName, data.companyRegNo, data.taxId, data.about, data.companyLogo,
      data.companyAddress, data.companyPhone, data.companyEmail, data.companyWebsite,
      data.language, data.country, data.currency, data.currencySymbol, data.dateFormat,
      data.timeFormat, data.timeZone, data.fiscalYearStart, data.calendarType,
      data.theme, data.fontSize, data.primaryColor, data.fontFamily,
      data.decimalPlaces, data.numberSeparator, data.numberingFormat,
      data.contractNumberFormat, data.clientNumberFormat, data.paymentNumberFormat,
      data.invoiceNumberFormat, data.receiptNumberFormat,
      data.notificationEmail, data.notificationFrequency, data.enableEmailNotifications, data.enableSMSNotifications,
      data.defaultContractDuration, data.defaultPaymentTerms, data.defaultTaxRate, data.backupFrequency, data.enableMultiCurrency,
      data.sessionTimeout, data.maxLoginAttempts, data.passwordPolicy, data.enableTwoFactor, data.enableAuditLog,
      data.cacheTimeout, data.maxFileSize, data.enableCaching, data.enableCompression, data.enableDebugMode,
      data.regions, data.owners, data.governorates, data.workDays
    ];
    
    db.run(sql, values, function(err) {
      if (err) return reject(err);
      resolve({ id: this.lastID, ...data });
    });
  }

  // تحديث الإعدادات
  updateSettings(data, resolve, reject) {
    const db = database.getConnection();
    
    const sql = `
      UPDATE Settings SET
        companyName=?, programName=?, companyRegNo=?, taxId=?, about=?, companyLogo=?,
        companyAddress=?, companyPhone=?, companyEmail=?, companyWebsite=?,
        language=?, country=?, currency=?, currencySymbol=?, dateFormat=?,
        timeFormat=?, timeZone=?, fiscalYearStart=?, calendarType=?,
        theme=?, fontSize=?, primaryColor=?, fontFamily=?,
        decimalPlaces=?, numberSeparator=?, numberingFormat=?,
        contractNumberFormat=?, clientNumberFormat=?, paymentNumberFormat=?,
        invoiceNumberFormat=?, receiptNumberFormat=?,
        notificationEmail=?, notificationFrequency=?, enableEmailNotifications=?, enableSMSNotifications=?,
        defaultContractDuration=?, defaultPaymentTerms=?, defaultTaxRate=?, backupFrequency=?, enableMultiCurrency=?,
        sessionTimeout=?, maxLoginAttempts=?, passwordPolicy=?, enableTwoFactor=?, enableAuditLog=?,
        cacheTimeout=?, maxFileSize=?, enableCaching=?, enableCompression=?, enableDebugMode=?,
        regions=?, owners=?, governorates=?, workDays=?,
        updatedAt=CURRENT_TIMESTAMP
      WHERE id = (SELECT id FROM Settings ORDER BY id DESC LIMIT 1)
    `;
    
    const values = [
      data.companyName, data.programName, data.companyRegNo, data.taxId, data.about, data.companyLogo,
      data.companyAddress, data.companyPhone, data.companyEmail, data.companyWebsite,
      data.language, data.country, data.currency, data.currencySymbol, data.dateFormat,
      data.timeFormat, data.timeZone, data.fiscalYearStart, data.calendarType,
      data.theme, data.fontSize, data.primaryColor, data.fontFamily,
      data.decimalPlaces, data.numberSeparator, data.numberingFormat,
      data.contractNumberFormat, data.clientNumberFormat, data.paymentNumberFormat,
      data.invoiceNumberFormat, data.receiptNumberFormat,
      data.notificationEmail, data.notificationFrequency, data.enableEmailNotifications, data.enableSMSNotifications,
      data.defaultContractDuration, data.defaultPaymentTerms, data.defaultTaxRate, data.backupFrequency, data.enableMultiCurrency,
      data.sessionTimeout, data.maxLoginAttempts, data.passwordPolicy, data.enableTwoFactor, data.enableAuditLog,
      data.cacheTimeout, data.maxFileSize, data.enableCaching, data.enableCompression, data.enableDebugMode,
      data.regions, data.owners, data.governorates, data.workDays
    ];
    
    db.run(sql, values, function(err) {
      if (err) return reject(err);
      resolve(data);
    });
  }
}

module.exports = new SettingsService();
