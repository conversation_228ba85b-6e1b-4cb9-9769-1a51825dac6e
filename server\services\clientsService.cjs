const database = require('../models/db.cjs');

class ClientsService {

  // دالة مساعدة لتحويل نوع العميل للعرض
  convertClientTypeForDisplay(clientType) {
    if (clientType === 'individual') return 'أفراد';
    if (clientType === 'company') return 'شركات';
    return clientType;
  }

  // دالة مساعدة لتحويل بيانات العميل للعرض
  formatClientForDisplay(client) {
    if (!client) return client;

    return {
      ...client,
      clientType: this.convertClientTypeForDisplay(client.clientType)
    };
  }
  // جلب جميع العملاء
  getAllClients() {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      if (!db) {
        return reject(new Error('Database connection not available'));
      }
      
      db.all('SELECT * FROM Clients WHERE isActive = 1 ORDER BY createdAt DESC', [], (err, rows) => {
        if (err) return reject(err);

        // تحويل نوع العميل للعرض
        const formattedRows = rows.map(client => this.formatClientForDisplay(client));
        resolve(formattedRows);
      });
    });
  }



  // جلب عميل بواسطة clientId
  getClientByClientId(clientId) {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      if (!db) {
        return reject(new Error('Database connection not available'));
      }
      
      db.get('SELECT * FROM Clients WHERE clientId = ? AND isActive = 1', [clientId], (err, row) => {
        if (err) return reject(err);
        resolve(row);
      });
    });
  }

  // البحث في العملاء
  searchClients(query) {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      if (!db) {
        return reject(new Error('Database connection not available'));
      }

      const searchQuery = `%${query}%`;
      db.all(`
        SELECT * FROM Clients
        WHERE (clientId LIKE ? OR clientName LIKE ? OR phone LIKE ?)
        AND isActive = 1
        ORDER BY clientName
      `, [searchQuery, searchQuery, searchQuery], (err, rows) => {
        if (err) return reject(err);

        // تحويل نوع العميل للعرض
        const formattedRows = rows.map(client => this.formatClientForDisplay(client));
        resolve(formattedRows);
      });
    });
  }

  // جلب عميل بواسطة ID
  getClientById(id) {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      if (!db) {
        return reject(new Error('Database connection not available'));
      }
      
      db.get('SELECT * FROM Clients WHERE id = ? AND isActive = 1', [id], (err, row) => {
        if (err) return reject(err);
        resolve(row);
      });
    });
  }

  // إنشاء عميل جديد (مبسط)
  createClient(clientData) {
    console.log('🏪 ClientsService.createClient - البيانات الواردة:', clientData);
    return new Promise((resolve, reject) => {
      // حفظ reference للـ this
      const self = this;
      const db = database.getConnection();
      if (!db) {
        return reject(new Error('Database connection not available'));
      }

      // التحقق البسيط من التكرار - فقط clientId
      const duplicateCheckQuery = 'SELECT clientId, clientName FROM Clients WHERE clientId = ? AND isActive = 1';
      const duplicateCheckParams = [clientData.clientId];

      db.get(duplicateCheckQuery, duplicateCheckParams, (err, existing) => {
        if (err) return reject(err);

        if (existing) {
          return reject(new Error(`العميل "${existing.clientName}" مسجل مسبقاً بنفس البيانات`));
        }

        // إنشاء العميل الجديد (استخدام الـ schema الحقيقي)
        const sql = `
          INSERT INTO Clients (
            clientId, clientType, clientName, address, phone,
            email, nationalId, commercialRegister, city, country,
            isActive, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `;

        const params = [
          clientData.clientId,
          clientData.clientType,
          clientData.clientName,
          clientData.address || '',
          clientData.phone || '',
          clientData.email || '',
          clientData.nationalId || '',
          clientData.commercialRegister || '',
          clientData.city || '',
          clientData.country || 'السعودية'
        ];

        // Debug logging
        require('fs').appendFileSync('debug.log', `[${new Date().toISOString()}] SQL: ${sql}\n`);
        require('fs').appendFileSync('debug.log', `[${new Date().toISOString()}] Params: ${JSON.stringify(params)}\n`);

        console.log('📊 SQL Parameters:', params);
        console.log('🔍 clientType value:', clientData.clientType);

        db.run(sql, params, (err) => {
          if (err) {
            console.error('❌ Database error:', err);
            require('fs').appendFileSync('debug.log', `[${new Date().toISOString()}] ❌ Database error: ${err.message}\n`);
            return reject(err);
          }

          require('fs').appendFileSync('debug.log', `[${new Date().toISOString()}] ✅ Client created successfully with ID: ${db.lastID}\n`);

          const result = { id: db.lastID, ...clientData };
          const formattedResult = self.formatClientForDisplay(result);
          resolve(formattedResult);
        });
      });
    });
  }

  // تحديث عميل (مبسط)
  updateClient(id, clientData) {
    return new Promise((resolve, reject) => {
      // حفظ reference للـ this
      const self = this;
      const db = database.getConnection();
      if (!db) {
        return reject(new Error('Database connection not available'));
      }

      // التحقق من وجود العميل
      db.get('SELECT * FROM Clients WHERE id = ? AND isActive = 1', [id], (err, existing) => {
        if (err) return reject(err);
        if (!existing) return reject(new Error('العميل غير موجود'));

        // تحديث العميل مباشرة
        const sql = `
          UPDATE Clients SET
            clientId = ?, clientType = ?, clientName = ?, address = ?,
            phone = ?, email = ?, nationalId = ?, commercialRegister = ?,
            city = ?, country = ?, updatedAt = CURRENT_TIMESTAMP
          WHERE id = ? AND isActive = 1
        `;

        const params = [
          clientData.clientId,
          clientData.clientType,
          clientData.clientName,
          clientData.address || clientData.clientAddress,
          clientData.phone || clientData.clientPhoneWhatsapp,
          clientData.email || clientData.clientEmail,
          clientData.nationalId,
          clientData.commercialRegister,
          clientData.city,
          clientData.country || 'السعودية',
          id
        ];

        db.run(sql, params, (err) => {
          if (err) return reject(err);
          if (db.changes === 0) return reject(new Error('لم يتم العثور على العميل'));

          const result = { id, ...clientData };
          const formattedResult = self.formatClientForDisplay(result);
          resolve(formattedResult);
        });
      });
    });
  }

  // التحقق من إمكانية حذف العميل
  canDeleteClient(clientId) {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      if (!db) {
        return reject(new Error('Database connection not available'));
      }

      // التحقق من وجود عقود نشطة للعميل
      db.all(
        'SELECT id, contractId FROM Contracts WHERE clientId = ? AND isActive = 1',
        [clientId],
        (err, contracts) => {
          if (err) return reject(err);

          const canDelete = contracts.length === 0;
          resolve({
            canDelete,
            activeContractsCount: contracts.length,
            details: {
              contracts: contracts
            }
          });
        }
      );
    });
  }

  // حذف عميل (soft delete)
  deleteClient(id) {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      if (!db) {
        return reject(new Error('Database connection not available'));
      }

      db.run(
        'UPDATE Clients SET isActive = 0, isDeleted = 1, updatedAt = CURRENT_TIMESTAMP WHERE id = ? AND isActive = 1',
        [id],
        function(err) {
          if (err) return reject(err);
          if (this.changes === 0) return reject(new Error('العميل غير موجود'));
          resolve({ message: 'تم حذف العميل بنجاح' });
        }
      );
    });
  }

  // جلب إحصائيات العملاء
  getClientsStats() {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      if (!db) {
        return reject(new Error('Database connection not available'));
      }
      
      db.all(`
        SELECT
          COUNT(*) as totalClients,
          SUM(CASE WHEN clientType = 'أفراد' THEN 1 ELSE 0 END) as individualClients,
          SUM(CASE WHEN clientType = 'شركات' THEN 1 ELSE 0 END) as corporateClients,
          COUNT(DISTINCT DATE(createdAt)) as activeDays
        FROM Clients
        WHERE isActive = 1
      `, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows[0]);
      });
    });
  }
}

module.exports = new ClientsService();
