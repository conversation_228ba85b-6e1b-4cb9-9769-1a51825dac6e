// ===== FIX CLIENT UNIQUE CONSTRAINT MIGRATION =====
// Author: Augment Code
// Description: Fix clientId unique constraint to allow reusing IDs of deleted clients

const fixClientUniqueConstraint = async (db) => {
  console.log('Fixing client unique constraint...');

  try {
    // Step 1: Create new table with correct constraints (matching current schema exactly)
    await db.run(`
      CREATE TABLE IF NOT EXISTS Clients_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        clientId TEXT NOT NULL,
        clientType TEXT NOT NULL CHECK (clientType IN ('individual', 'company', 'أفراد', 'شركات')),
        clientName TEXT NOT NULL,
        nationalId TEXT,
        commercialRegister TEXT,
        phone TEXT,
        email TEXT,
        address TEXT,
        city TEXT,
        country TEXT DEFAULT 'السعودية',
        isActive INTEGER DEFAULT 1,
        isDeleted INTEGER DEFAULT 0,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedBy INTEGER,
        deletedAt DATETIME,
        deletedBy INTEGER,
        createdBy INTEGER,
        -- Composite unique constraint: clientId can be reused if previous client is inactive
        UNIQUE(clientId, isActive) ON CONFLICT IGNORE
      )
    `);

    // Step 2: Copy data from old table (matching current schema)
    await db.run(`
      INSERT INTO Clients_new (
        id, clientId, clientType, clientName, nationalId, commercialRegister,
        phone, email, address, city, country, isActive, isDeleted,
        createdAt, updatedAt, updatedBy, deletedAt, deletedBy, createdBy
      )
      SELECT
        id, clientId, clientType, clientName, nationalId, commercialRegister,
        phone, email, address, city, country, isActive, isDeleted,
        createdAt, updatedAt, updatedBy, deletedAt, deletedBy, createdBy
      FROM Clients
    `);

    // Step 3: Drop old table
    await db.run(`DROP TABLE Clients`);

    // Step 4: Rename new table
    await db.run(`ALTER TABLE Clients_new RENAME TO Clients`);

    // Step 5: Recreate indexes
    await db.run(`CREATE INDEX IF NOT EXISTS idx_clients_client_id ON Clients(clientId)`);
    await db.run(`CREATE INDEX IF NOT EXISTS idx_clients_active ON Clients(isActive)`);

    console.log('✅ Client unique constraint fixed successfully');
  } catch (error) {
    console.error('❌ Error fixing client unique constraint:', error);
    throw error;
  }
};

const revertClientUniqueConstraint = (db) => {
  console.log('Reverting client unique constraint...');

  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Create table with old constraint (matching current schema exactly)
      db.run(`
        CREATE TABLE IF NOT EXISTS Clients_old (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          clientId TEXT UNIQUE NOT NULL,
          clientType TEXT NOT NULL CHECK (clientType IN ('individual', 'company', 'أفراد', 'شركات')),
          clientName TEXT NOT NULL,
          nationalId TEXT,
          commercialRegister TEXT,
          phone TEXT,
          email TEXT,
          address TEXT,
          city TEXT,
          country TEXT DEFAULT 'السعودية',
          isActive INTEGER DEFAULT 1,
          isDeleted INTEGER DEFAULT 0,
          createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          updatedBy INTEGER,
          deletedAt DATETIME,
          deletedBy INTEGER,
          createdBy INTEGER
        )
      `, (err) => {
        if (err) {
          console.error('❌ Error creating old Clients table:', err);
          reject(err);
          return;
        }

        // Copy only active clients to avoid constraint violations
        db.run(`
          INSERT INTO Clients_old 
          SELECT * FROM Clients WHERE isActive = 1
        `, (err) => {
          if (err) {
            console.error('❌ Error copying data:', err);
            reject(err);
            return;
          }

          db.run(`DROP TABLE Clients`, (err) => {
            if (err) {
              console.error('❌ Error dropping table:', err);
              reject(err);
              return;
            }

            db.run(`ALTER TABLE Clients_old RENAME TO Clients`, (err) => {
              if (err) {
                console.error('❌ Error renaming table:', err);
                reject(err);
                return;
              }

              console.log('✅ Client unique constraint reverted successfully');
              resolve();
            });
          });
        });
      });
    });
  });
};

module.exports = {
  up: fixClientUniqueConstraint,
  down: revertClientUniqueConstraint
};
