import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Link, useLocation } from "wouter";
import { But<PERSON> } from "../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "../components/ui/form";
import { Input } from "../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select";
import { Textarea } from "../components/ui/textarea";
import { useToast } from "../hooks/use-toast";
import { apiRequest, api } from "../lib/queryClient";
import { type InsertClient } from "@shared/schema";
import ClientCard from "../components/ClientCard";
import * as z from "zod";

// Client form schema (updated with new fields)
const clientFormSchema = z.object({
  clientId: z.string().min(1, 'رقم العميل مطلوب'),
  clientType: z.enum(['أفراد', 'شركات']),
  clientName: z.string().min(1, 'اسم العميل مطلوب'),
  clientAddress: z.string().optional(),
  clientPhoneWhatsapp: z.string().min(1, 'رقم الهاتف/واتساب مطلوب'),
  clientPhone2: z.string().optional(),
  clientPhone3: z.string().optional(),
  clientEmail: z.string().email('بريد إلكتروني غير صحيح').optional().or(z.literal('')),
  clientNotes: z.string().optional(),
  clientFinancialGuarantee: z.string().optional(),
  clientID_Image: z.string().optional(),
  clientFinancial_Category: z.string().optional(),
  clientLegal_Rep: z.string().optional(),
  clientPartner: z.string().optional(),
  clientReg_Number: z.string().optional(),
  clientTaxReg_Number: z.string().optional(),
  clientLegal_Status: z.string().optional(),
  clientRemarks: z.string().optional(),
});
import { ArrowRight, Save, Users, Building2, Eye } from "lucide-react";

type ClientFormData = z.infer<typeof clientFormSchema>;

export default function ClientsNew() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showPreview, setShowPreview] = useState(false);

  const form = useForm<ClientFormData>({
    resolver: zodResolver(clientFormSchema),
    defaultValues: {
      clientId: "",
      clientType: "أفراد",
      clientName: "",
      clientAddress: "",
      clientPhoneWhatsapp: "",
      clientPhone2: "",
      clientPhone3: "",
      clientEmail: "",
      clientNotes: "",
      clientFinancialGuarantee: "",
      clientID_Image: "",
      clientFinancial_Category: "",
      clientLegal_Rep: "",
      clientPartner: "",
      clientReg_Number: "",
      clientTaxReg_Number: "",
      clientLegal_Status: "",
      clientRemarks: "",
    },
  });

  const createClientMutation = useMutation({
    mutationFn: async (data: ClientFormData) => {
      return await api.post("/api/clients", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/clients"] });
      toast({
        title: "تم إنشاء العميل بنجاح",
        description: "تم حفظ بيانات العميل الجديد",
      });
      setLocation("/clients");
    },
    onError: (error: Error) => {
      toast({
        title: "خطأ في إنشاء العميل",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: ClientFormData) => {
    createClientMutation.mutate(data);
  };

  const handlePreview = () => {
    setShowPreview(!showPreview);
  };

  const handlePrint = () => {
    window.print();
  };

  // Get current form values for preview
  const currentFormValues = form.watch();

  if (showPreview) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-reverse space-x-4">
                <Button variant="ghost" size="sm" onClick={() => setShowPreview(false)}>
                  <ArrowRight className="h-4 w-4 ml-2" />
                  العودة للتعديل
                </Button>
                <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
                <div className="flex items-center space-x-reverse space-x-2">
                  <Eye className="h-5 w-5 text-primary" />
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                    معاينة كارت العميل
                  </h1>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="py-8">
          <ClientCard
            client={currentFormValues}
            contracts={[]}
            showPrintButton={true}
            showEditButton={false}
            onPrint={handlePrint}
          />
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-reverse space-x-4">
              <Link href="/clients">
                <Button variant="ghost" size="sm">
                  <ArrowRight className="h-4 w-4 ml-2" />
                  العودة للعملاء
                </Button>
              </Link>
              <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
              <div className="flex items-center space-x-reverse space-x-2">
                <Users className="h-5 w-5 text-primary" />
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  إضافة عميل جديد
                </h1>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>بيانات العميل</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Client ID */}
                  <FormField
                    control={form.control}
                    name="clientId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>رقم العميل *</FormLabel>
                        <FormControl>
                          <Input placeholder="مثال: 1234567890" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Client Type */}
                  <FormField
                    control={form.control}
                    name="clientType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>نوع العميل *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="اختر نوع العميل" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="أفراد">أفراد</SelectItem>
                            <SelectItem value="شركات">شركات</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Client Name */}
                <FormField
                  control={form.control}
                  name="clientName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>اسم العميل *</FormLabel>
                      <FormControl>
                        <Input placeholder="أدخل اسم العميل" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Address */}
                <FormField
                  control={form.control}
                  name="clientAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>العنوان</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="أدخل عنوان العميل" 
                          className="resize-none"
                          rows={3}
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Phone WhatsApp */}
                  <FormField
                    control={form.control}
                    name="clientPhoneWhatsapp"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>رقم الهاتف/واتساب *</FormLabel>
                        <FormControl>
                          <Input placeholder="+201234567890" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Phone 2 */}
                  <FormField
                    control={form.control}
                    name="clientPhone2"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>رقم هاتف إضافي</FormLabel>
                        <FormControl>
                          <Input placeholder="+201234567890" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Phone 3 */}
                  <FormField
                    control={form.control}
                    name="clientPhone3"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>رقم هاتف ثالث</FormLabel>
                        <FormControl>
                          <Input placeholder="+201234567890" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Email */}
                <FormField
                  control={form.control}
                  name="clientEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>البريد الإلكتروني</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Notes */}
                <FormField
                  control={form.control}
                  name="clientNotes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ملاحظات</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="أي ملاحظات إضافية عن العميل"
                          className="resize-none"
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Additional Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-reverse space-x-2">
                  <Building2 className="h-5 w-5 text-primary" />
                  <span>معلومات إضافية</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Financial Guarantee */}
                  <FormField
                    control={form.control}
                    name="clientFinancialGuarantee"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الضامن المالي</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="اسم الضامن المالي"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Financial Category */}
                  <FormField
                    control={form.control}
                    name="clientFinancial_Category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>التصنيف المالي</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="الفئة أو التصنيف المالي"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Legal Representative */}
                  <FormField
                    control={form.control}
                    name="clientLegal_Rep"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الممثل القانوني</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="اسم الممثل القانوني"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Partner */}
                  <FormField
                    control={form.control}
                    name="clientPartner"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الشريك المرتبط</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="اسم الشريك المرتبط"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Registration Number */}
                  <FormField
                    control={form.control}
                    name="clientReg_Number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>رقم التسجيل</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="رقم التسجيل"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Tax Registration Number */}
                  <FormField
                    control={form.control}
                    name="clientTaxReg_Number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>رقم التسجيل الضريبي</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="رقم التسجيل الضريبي"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Legal Status */}
                  <FormField
                    control={form.control}
                    name="clientLegal_Status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الحالة القانونية</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="الحالة القانونية"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* ID Image Upload */}
                  <FormField
                    control={form.control}
                    name="clientID_Image"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>صورة الهوية/السجل التجاري</FormLabel>
                        <FormControl>
                          <Input
                            type="file"
                            accept="image/*,.pdf"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                // For now, just store the filename
                                // In production, you'd upload to a server
                                field.onChange(file.name);
                              }
                            }}
                          />
                        </FormControl>
                        <p className="text-xs text-gray-500">
                          يمكن رفع صور أو ملفات PDF
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Additional Remarks */}
                <FormField
                  control={form.control}
                  name="clientRemarks"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ملاحظات إضافية</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="ملاحظات إضافية مفصلة"
                          className="resize-none"
                          rows={4}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end space-x-reverse space-x-4 pt-6">
                  <Link href="/clients">
                    <Button variant="outline" type="button">
                      إلغاء
                    </Button>
                  </Link>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handlePreview}
                    className="gap-2"
                  >
                    <Eye className="h-4 w-4" />
                    معاينة كارت العميل
                  </Button>
                  <Button
                    type="submit"
                    disabled={createClientMutation.isPending}
                    className="bg-primary text-primary-foreground hover:bg-primary/90"
                  >
                    {createClientMutation.isPending ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                    ) : (
                      <Save className="h-4 w-4 ml-2" />
                    )}
                    حفظ العميل
                  </Button>
                </div>
              </CardContent>
            </Card>
          </form>
        </Form>
      </main>
    </div>
  );
}
