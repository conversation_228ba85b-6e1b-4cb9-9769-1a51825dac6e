const clientsService = require('../services/clientsService.cjs');

class ClientsController {
  // جلب جميع العملاء
  async getAllClients(req, res) {
    try {
      const clients = await clientsService.getAllClients();
      res.json({
        success: true,
        data: clients,
        count: clients.length
      });
    } catch (error) {
      console.error('Error getting all clients:', error);
      res.status(500).json({
        success: false,
        error: 'فشل في جلب العملاء',
        details: [error.message]
      });
    }
  }

  // جلب عميل بواسطة clientId
  async getClientByClientId(req, res) {
    try {
      const client = await clientsService.getClientByClientId(req.params.clientId);
      if (!client) {
        return res.status(404).json({
          success: false,
          error: 'العميل غير موجود'
        });
      }
      res.json({
        success: true,
        data: client
      });
    } catch (error) {
      console.error('Error getting client by clientId:', error);
      res.status(500).json({
        success: false,
        error: 'فشل في جلب العميل',
        details: [error.message]
      });
    }
  }

  // البحث في العملاء
  async searchClients(req, res) {
    try {
      const query = req.params.query;
      if (!query || query.trim().length < 2) {
        return res.status(400).json({
          success: false,
          error: 'يجب أن يكون النص المراد البحث عنه أكثر من حرفين'
        });
      }

      const results = await clientsService.searchClients(query.trim());
      res.json({
        success: true,
        data: results,
        count: results.length,
        query: query
      });
    } catch (error) {
      console.error('Error searching clients:', error);
      res.status(500).json({
        success: false,
        error: 'فشل في البحث عن العملاء',
        details: [error.message]
      });
    }
  }

  // جلب عميل بواسطة ID
  async getClientById(req, res) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          success: false,
          error: 'رقم العميل غير صحيح'
        });
      }

      const client = await clientsService.getClientById(id);
      if (!client) {
        return res.status(404).json({
          success: false,
          error: 'العميل غير موجود'
        });
      }

      res.json({
        success: true,
        data: client
      });
    } catch (error) {
      console.error('Error getting client by ID:', error);
      res.status(500).json({
        success: false,
        error: 'فشل في جلب العميل',
        details: [error.message]
      });
    }
  }

  // إنشاء عميل جديد
  async createClient(req, res) {
    // كتابة log في ملف للتأكد من وصول الطلب
    require('fs').appendFileSync('debug.log', `[${new Date().toISOString()}] createClient called with: ${JSON.stringify(req.body)}\n`);

    try {
      // استخدام الـ validation schema
      const { createClientSchema } = require('../validation/schemas.cjs');

      require('fs').appendFileSync('debug.log', `[${new Date().toISOString()}] 🔍 Starting validation for: ${JSON.stringify(req.body)}\n`);

      // التحقق من صحة البيانات
      const validationResult = createClientSchema.safeParse(req.body);

      if (!validationResult.success) {
        console.log('❌ Validation failed:', validationResult.error.errors);
        require('fs').appendFileSync('debug.log', `[${new Date().toISOString()}] ❌ Validation failed: ${JSON.stringify(validationResult.error.errors)}\n`);
        return res.status(400).json({
          success: false,
          error: 'بيانات غير صحيحة',
          details: validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        });
      }

      const validatedData = validationResult.data;
      console.log('✅ Validated data:', validatedData);

      // تحويل البيانات للـ format المتوافق مع الـ database
      const clientData = {
        clientId: validatedData.clientId,
        clientType: validatedData.clientType === 'أفراد' ? 'individual' :
                   validatedData.clientType === 'شركات' ? 'company' :
                   validatedData.clientType,
        clientName: validatedData.clientName,
        // تحويل الأسماء للـ database schema
        address: validatedData.clientAddress || '',
        phone: validatedData.clientPhoneWhatsapp,
        email: validatedData.clientEmail || '',
        nationalId: validatedData.nationalId || '',
        commercialRegister: validatedData.commercialRegister || '',
        city: validatedData.city || '',
        country: validatedData.country || 'السعودية'
      };

      console.log('📤 إرسال البيانات إلى clientsService:', clientData);
      const result = await clientsService.createClient(clientData);
      console.log('📥 نتيجة clientsService:', result);

      // التحقق من نوع العملية (إنشاء جديد أم استرجاع)
      const isRestored = result.restored || false;
      const message = isRestored ? 'تم استرجاع العميل وتحديث بياناته بنجاح' : 'تم إنشاء العميل بنجاح';

      console.log(`✅ نجح العملية - استرجاع: ${isRestored}, الرسالة: ${message}`);

      res.status(201).json({
        success: true,
        data: result,
        message: message,
        restored: isRestored
      });
    } catch (error) {
      console.error('❌ خطأ في إنشاء العميل:', error);
      console.error('❌ تفاصيل الخطأ:', error.message);
      console.error('❌ Stack trace:', error.stack);

      res.status(400).json({
        success: false,
        error: 'فشل في إنشاء العميل',
        details: [error.message]
      });
    }
  }

  // تحديث عميل
  async updateClient(req, res) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          success: false,
          error: 'رقم العميل غير صحيح'
        });
      }

      // تحويل نوع العميل من العربية إلى الإنجليزية
      const clientData = { ...req.body };
      if (clientData.clientType === 'أفراد') {
        clientData.clientType = 'individual';
      } else if (clientData.clientType === 'شركات') {
        clientData.clientType = 'company';
      }

      const updatedClient = await clientsService.updateClient(id, clientData);
      res.json({
        success: true,
        data: updatedClient,
        message: 'تم تحديث العميل بنجاح'
      });
    } catch (error) {
      console.error('Error updating client:', error);
      res.status(400).json({
        success: false,
        error: 'فشل في تحديث العميل',
        details: [error.message]
      });
    }
  }

  // التحقق من إمكانية حذف العميل
  async canDeleteClient(req, res) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          success: false,
          error: 'رقم العميل غير صحيح'
        });
      }

      // الحصول على clientId النصي من قاعدة البيانات
      const client = await clientsService.getClientById(id);
      if (!client) {
        return res.status(404).json({
          success: false,
          error: 'العميل غير موجود'
        });
      }

      const result = await clientsService.canDeleteClient(client.clientId);
      res.json(result);
    } catch (error) {
      console.error('Error checking client deletion:', error);
      res.status(500).json({
        success: false,
        error: 'خطأ في التحقق من إمكانية حذف العميل',
        details: [error.message]
      });
    }
  }

  // حذف عميل
  async deleteClient(req, res) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          success: false,
          error: 'رقم العميل غير صحيح'
        });
      }

      const result = await clientsService.deleteClient(id);
      res.json({
        success: true,
        message: result.message
      });
    } catch (error) {
      console.error('Error deleting client:', error);
      res.status(400).json({
        success: false,
        error: 'فشل في حذف العميل',
        details: [error.message]
      });
    }
  }

  // جلب إحصائيات العملاء
  async getClientsStats(req, res) {
    try {
      const stats = await clientsService.getClientsStats();
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Error getting clients stats:', error);
      res.status(500).json({
        success: false,
        error: 'فشل في جلب إحصائيات العملاء',
        details: [error.message]
      });
    }
  }
}

module.exports = new ClientsController();
