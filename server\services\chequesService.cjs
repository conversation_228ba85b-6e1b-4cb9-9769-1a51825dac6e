const database = require('../models/db.cjs');

class ChequesService {
  // جلب جميع الشيكات
  getAllCheques() {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      const sql = `
        SELECT
          ch.*,
          c.contractNumber,
          c.contractSubject,
          cl.clientName,
          cl.clientType
        FROM Cheques ch
        LEFT JOIN Contracts c ON ch.contractId = c.id
        LEFT JOIN Clients cl ON ch.clientId = cl.id
        WHERE ch.isActive = 1
        ORDER BY ch.chequeDate DESC
      `;

      db.all(sql, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  }

  // جلب شيك بواسطة ID
  getChequeById(id) {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      const sql = `
        SELECT
          ch.*,
          c.contractNumber,
          c.contractSubject,
          cl.clientName,
          cl.clientType
        FROM Cheques ch
        LEFT JOIN Contracts c ON ch.contractId = c.id
        LEFT JOIN Clients cl ON ch.clientId = cl.id
        WHERE ch.id = ? AND ch.isActive = 1
      `;

      db.get(sql, [id], (err, row) => {
        if (err) return reject(err);
        resolve(row);
      });
    });
  }

  // إنشاء شيك جديد
  createCheque(chequeData) {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      const sql = `
        INSERT INTO Cheques (
          chequeNumber, contractId, clientId, receivableId, bankName,
          chequeAmount, chequeDate, dueDate, status, receivedDate,
          statusDate, entryNumber, contractStatusAtReceipt,
          shouldDepositToBank, notes, isActive
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
      `;

      const params = [
        chequeData.chequeNumber,
        chequeData.contractId,
        chequeData.clientId,
        chequeData.receivableId,
        chequeData.bankName,
        chequeData.chequeAmount,
        chequeData.chequeDate,
        chequeData.dueDate,
        chequeData.status || 'خزينة',
        chequeData.receivedDate,
        chequeData.statusDate,
        chequeData.entryNumber,
        chequeData.contractStatusAtReceipt,
        chequeData.shouldDepositToBank || false,
        chequeData.notes
      ];

      db.run(sql, params, function(err) {
        if (err) return reject(err);
        resolve({ id: this.lastID, ...chequeData });
      });
    });
  }
}

module.exports = new ChequesService();
