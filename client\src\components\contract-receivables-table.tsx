import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import { ChevronDown, ChevronUp, Calendar, DollarSign, AlertCircle, CheckCircle, TrendingUp } from "lucide-react";
import { useCurrency } from "@/hooks/use-currency";
import { useDateFormat } from "@/hooks/use-date-format";
import { useLanguage } from "@/hooks/use-language";

interface Receivable {
  id: number;
  contractId: number;
  receivableNumber: string;
  dueDate: string;
  amount: number;
  description: string;
  status: 'مستحق' | 'مدفوع' | 'متأخر' | 'لم يحن موعده';
  paymentFrequency: string;
  installmentNumber: number;
  totalInstallments: number;
  isPaid?: boolean;
  paidAmount?: number;
  remainingAmount?: number;
  paidDate?: string;
  daysOverdue?: number;
}

interface ContractReceivablesTableProps {
  contractId: number;
  isExpanded?: boolean;
  onToggle?: () => void;
  showToggle?: boolean;
  maxRows?: number;
}

function ContractReceivablesTable({
  contractId,
  isExpanded = false,
  onToggle,
  showToggle = true,
  maxRows = 5
}: ContractReceivablesTableProps) {
  const { language } = useLanguage();
  const { formatCurrency } = useCurrency();
  const { formatDate } = useDateFormat();
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const { data: receivables, isLoading } = useQuery<Receivable[]>({
    queryKey: ['/api/contracts', contractId, 'receivables'],
    queryFn: async () => {
      const response = await fetch(`/api/contracts/${contractId}/receivables`);
      if (!response.ok) throw new Error('Failed to fetch receivables');
      return response.json();
    },
    enabled: !!contractId,
  });

  const getStatusBadge = (status: string, daysOverdue?: number) => {
    switch (status) {
      case 'مدفوع':
        return <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
          <CheckCircle className="h-3 w-3 mr-1" />
          مدفوع
        </Badge>;
      case 'متأخر':
        return <Badge variant="destructive">
          <AlertCircle className="h-3 w-3 mr-1" />
          متأخر {daysOverdue ? `(${daysOverdue} يوم)` : ''}
        </Badge>;
      case 'مستحق':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
          <Calendar className="h-3 w-3 mr-1" />
          مستحق
        </Badge>;
      case 'لم يحن موعده':
        return <Badge variant="outline">
          <Calendar className="h-3 w-3 mr-1" />
          لم يحن موعده
        </Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Filter receivables by status
  const filteredReceivables = receivables?.filter(r => {
    if (statusFilter === 'all') return true;
    return r.status === statusFilter;
  });

  const displayedReceivables = isExpanded ? filteredReceivables : filteredReceivables?.slice(0, maxRows);

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            جدول الاستحقاقات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between p-2 border rounded">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-6 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!receivables || receivables.length === 0) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            جدول الاستحقاقات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground text-center py-4">
            لا توجد استحقاقات لهذا العقد
          </p>
        </CardContent>
      </Card>
    );
  }

  // Calculate summary stats
  const totalAmount = receivables.reduce((sum, r) => sum + r.amount, 0);
  const paidAmount = receivables.reduce((sum, r) => sum + (r.paidAmount || 0), 0);
  const overdueCount = receivables.filter(r => r.status === 'متأخر').length;
  const paidCount = receivables.filter(r => r.status === 'مدفوع').length;
  const paymentProgress = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className={`${maxRows > 5 ? 'text-lg' : 'text-sm'} flex items-center gap-2`}>
            <DollarSign className={`${maxRows > 5 ? 'h-5 w-5' : 'h-4 w-4'}`} />
            {maxRows > 5 ? 'جدول الاستحقاقات والأقساط' : 'جدول الاستحقاقات'} ({receivables.length})
          </CardTitle>
          {showToggle && filteredReceivables && filteredReceivables.length > maxRows && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggle}
              className="h-6 px-2"
            >
              {isExpanded ? (
                <>
                  <ChevronUp className="h-3 w-3 mr-1" />
                  إخفاء
                </>
              ) : (
                <>
                  <ChevronDown className="h-3 w-3 mr-1" />
                  عرض الكل
                </>
              )}
            </Button>
          )}
        </div>
        
        {/* Summary Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs mt-2">
          <div className="bg-blue-50 dark:bg-blue-950 p-2 rounded">
            <div className="text-blue-600 dark:text-blue-400 font-medium">المجموع</div>
            <div className="font-bold">{formatCurrency(totalAmount)}</div>
          </div>
          <div className="bg-green-50 dark:bg-green-950 p-2 rounded">
            <div className="text-green-600 dark:text-green-400 font-medium">المدفوع</div>
            <div className="font-bold">{formatCurrency(paidAmount)}</div>
          </div>
          <div className="bg-emerald-50 dark:bg-emerald-950 p-2 rounded">
            <div className="text-emerald-600 dark:text-emerald-400 font-medium">مسدد</div>
            <div className="font-bold">{paidCount} قسط</div>
          </div>
          {overdueCount > 0 && (
            <div className="bg-red-50 dark:bg-red-950 p-2 rounded">
              <div className="text-red-600 dark:text-red-400 font-medium">متأخر</div>
              <div className="font-bold">{overdueCount} قسط</div>
            </div>
          )}
        </div>

        {/* Payment Progress */}
        <div className="mt-3">
          <div className="flex items-center justify-between text-xs mb-1">
            <span className="text-muted-foreground">تقدم السداد</span>
            <span className="font-medium">{Math.round(paymentProgress)}%</span>
          </div>
          <Progress value={paymentProgress} className="h-2" />
        </div>

        {/* Status Filter for detailed view */}
        {maxRows > 5 && (
          <div className="mt-3 flex gap-2 flex-wrap">
            <Button
              variant={statusFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('all')}
              className="text-xs h-7"
            >
              الكل ({receivables?.length || 0})
            </Button>
            <Button
              variant={statusFilter === 'مستحق' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('مستحق')}
              className="text-xs h-7"
            >
              مستحق ({receivables?.filter(r => r.status === 'مستحق').length || 0})
            </Button>
            <Button
              variant={statusFilter === 'متأخر' ? 'destructive' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('متأخر')}
              className="text-xs h-7"
            >
              متأخر ({receivables?.filter(r => r.status === 'متأخر').length || 0})
            </Button>
            <Button
              variant={statusFilter === 'مدفوع' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('مدفوع')}
              className="text-xs h-7 bg-green-600 hover:bg-green-700"
            >
              مدفوع ({receivables?.filter(r => r.status === 'مدفوع').length || 0})
            </Button>
            <Button
              variant={statusFilter === 'لم يحن موعده' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('لم يحن موعده')}
              className="text-xs h-7"
            >
              لم يحن موعده ({receivables?.filter(r => r.status === 'لم يحن موعده').length || 0})
            </Button>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className="text-xs">
                <TableHead className="text-right w-16">#</TableHead>
                <TableHead className="text-right">تاريخ الاستحقاق</TableHead>
                <TableHead className="text-right">المبلغ</TableHead>
                <TableHead className="text-right">الحالة</TableHead>
                {maxRows > 5 && (
                  <>
                    <TableHead className="text-right">المدفوع</TableHead>
                    <TableHead className="text-right">المتبقي</TableHead>
                    <TableHead className="text-right">الوصف</TableHead>
                  </>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {displayedReceivables?.map((receivable) => (
                <TableRow key={receivable.id} className="text-xs">
                  <TableCell className="font-medium">
                    {receivable.installmentNumber}
                  </TableCell>
                  <TableCell>
                    {formatDate(receivable.dueDate)}
                  </TableCell>
                  <TableCell className="font-medium">
                    {formatCurrency(receivable.amount)}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(receivable.status, receivable.daysOverdue)}
                  </TableCell>
                  {maxRows > 5 && (
                    <>
                      <TableCell className="font-medium">
                        {formatCurrency(receivable.paidAmount || 0)}
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(receivable.remainingAmount || receivable.amount)}
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {receivable.description}
                      </TableCell>
                    </>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        
        {!isExpanded && filteredReceivables && filteredReceivables.length > maxRows && (
          <div className="text-center mt-3">
            <Button
              variant="outline"
              size="sm"
              onClick={onToggle}
              className="text-xs"
            >
              عرض {filteredReceivables.length - maxRows} استحقاق إضافي
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default ContractReceivablesTable;
