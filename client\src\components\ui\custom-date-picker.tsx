import React, { forwardRef } from 'react';
import DatePicker from 'react-datepicker';
import { useDateContext } from '@/contexts/date-context';
import { Input } from '@/components/ui/input';
import { Calendar } from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';
import moment from 'moment';
import 'react-datepicker/dist/react-datepicker.css';

interface CustomDatePickerProps {
  value?: string;
  onChange?: (date: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  id?: string;
}

// Custom input component
const CustomInput = forwardRef<HTMLInputElement, any>(({ value, onClick, placeholder, className, disabled, textDirection, isRTL }, ref) => (
  <div className="relative">
    <Input
      ref={ref}
      value={value}
      onClick={onClick}
      placeholder={placeholder}
      className={`${className} cursor-pointer ${isRTL ? 'text-right' : 'text-left'}`}
      style={{
        direction: textDirection,
        textAlign: isRTL ? 'right' : 'left',
        unicodeBidi: 'plaintext'
      }}
      readOnly
      disabled={disabled}
    />
    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
  </div>
));

CustomInput.displayName = 'CustomInput';

export const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
  value,
  onChange,
  placeholder,
  className = '',
  disabled = false,
  required = false,
  id
}) => {
  const { formatDate, getDateFormat, isHijri, parseDate, formatDateForInput } = useDateContext();
  const { language } = useLanguage();

  // Determine text direction based on language
  const isRTL = language === 'ar';
  const textDirection = isRTL ? 'rtl' : 'ltr';

  // Convert string value to Date object
  const dateValue = value ? parseDate(value) : null;

  // Handle date change
  const handleDateChange = (date: Date | null) => {
    console.log('Date selected:', date);
    if (date) {
      // Always save in ISO format (YYYY-MM-DD) for database consistency
      const isoString = formatDateForInput(date);
      console.log('ISO string:', isoString);
      console.log('Formatted display:', formatDate(date));
      onChange?.(isoString);
    } else {
      onChange?.('');
    }
  };

  // Format date for display
  const formatDateForDisplay = (date: Date | null): string => {
    if (!date) return '';
    return formatDate(date);
  };

  // Get placeholder based on date format
  const getPlaceholder = (): string => {
    if (placeholder) return placeholder;
    const format = getDateFormat();
    return `مثال: ${format.replace(/YYYY/g, '2024').replace(/MM/g, '01').replace(/DD/g, '15')}`;
  };



  // Convert date format for react-datepicker
  const getReactDatePickerFormat = (): string => {
    const format = getDateFormat();
    console.log('Original format:', format);

    // Convert our format to react-datepicker format
    let reactFormat = format
      .replace(/YYYY/g, 'yyyy')
      .replace(/MM/g, 'MM')
      .replace(/DD/g, 'dd');

    console.log('React DatePicker format:', reactFormat);
    return reactFormat;
  };

  return (
    <DatePicker
      id={id}
      selected={dateValue}
      onChange={handleDateChange}
      customInput={
        <CustomInput
          placeholder={getPlaceholder()}
          className={className}
          disabled={disabled}
          textDirection={textDirection}
          isRTL={isRTL}
        />
      }
      dateFormat="dd/MM/yyyy"
      disabled={disabled}
      showYearDropdown
      showMonthDropdown
      dropdownMode="select"
      yearDropdownItemNumber={50}
      scrollableYearDropdown
      placeholderText={getPlaceholder()}
      calendarClassName="custom-datepicker"
    />
  );
};

export default CustomDatePicker;
