import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

export default function AdminDatabase() {
  const [isResetting, setIsResetting] = useState(false);
  const [resetResult, setResetResult] = useState<any>(null);
  const { toast } = useToast();

  const handleResetContracts = async () => {
    if (!confirm('هل أنت متأكد من إعادة تعيين جدول العقود؟ سيتم حذف جميع العقود الموجودة!')) {
      return;
    }

    setIsResetting(true);
    setResetResult(null);

    try {
      const response = await fetch('/api/admin/reset-contracts-table', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('فشل في إعادة تعيين الجدول');
      }

      const result = await response.json();
      setResetResult(result);
      
      toast({
        title: "تم بنجاح",
        description: "تم إعادة تعيين جدول العقود بنجاح",
        variant: "default"
      });

    } catch (error) {
      console.error('Error resetting contracts table:', error);
      toast({
        title: "خطأ",
        description: "فشل في إعادة تعيين جدول العقود",
        variant: "destructive"
      });
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <div className="container mx-auto p-6" dir="rtl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-right">إدارة قاعدة البيانات</h1>
        <p className="text-gray-600 text-right mt-2">أدوات إدارة وصيانة قاعدة البيانات</p>
      </div>

      <div className="grid gap-6">
        {/* Reset Contracts Table */}
        <Card>
          <CardHeader>
            <CardTitle className="text-right text-red-600">إعادة تعيين جدول العقود</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertDescription className="text-right">
                <strong>تحذير:</strong> هذا الإجراء سيحذف جميع العقود الموجودة وينشئ جدول عقود جديد ومبسط.
                تأكد من عمل نسخة احتياطية قبل المتابعة.
              </AlertDescription>
            </Alert>

            <div className="flex justify-end">
              <Button
                onClick={handleResetContracts}
                disabled={isResetting}
                variant="destructive"
                className="min-w-[200px]"
              >
                {isResetting ? 'جاري إعادة التعيين...' : 'إعادة تعيين جدول العقود'}
              </Button>
            </div>

            {resetResult && (
              <Alert className="mt-4">
                <AlertDescription className="text-right">
                  <div className="space-y-2">
                    <p><strong>النتيجة:</strong> {resetResult.message}</p>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>حذف الجدول القديم: {resetResult.details?.oldTableDropped ? '✅' : '❌'}</li>
                      <li>إنشاء جدول جديد: {resetResult.details?.newTableCreated ? '✅' : '❌'}</li>
                      <li>تنظيف الجداول المرتبطة: {resetResult.details?.relatedTablesCleared ? '✅' : '❌'}</li>
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Database Schema Info */}
        <Card>
          <CardHeader>
            <CardTitle className="text-right">معلومات الجدول الجديد</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-right space-y-2 text-sm">
              <h4 className="font-semibold">الحقول الأساسية:</h4>
              <ul className="list-disc list-inside space-y-1 text-gray-600">
                <li>معلومات العقد الأساسية (رقم العقد، الموضوع، الوصف، النوع، الحالة)</li>
                <li>معلومات العميل والضامن المالي</li>
                <li>تواريخ العقد (التوقيع، البداية، النهاية، التفعيل المالي)</li>
                <li>إدارة الأصول (الجهة المالكة، نسبة الملكية، الإدارة المسؤولة، المنطقة)</li>
                <li>الشروط المالية (القيمة الإجمالية، المبلغ الشهري، تكرار الدفع)</li>
                <li>التأمين والدفع المقدم</li>
                <li>الغرامات والرسوم</li>
                <li>حالة الشيكات والملاحظات</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
