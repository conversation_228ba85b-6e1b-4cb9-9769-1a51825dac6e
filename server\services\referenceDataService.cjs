const database = require('../models/db.cjs');

exports.getAllReferenceLists = () => {
  return new Promise((resolve, reject) => {
    const db = database.getConnection();
    db.all(`SELECT DISTINCT module, listName, COUNT(*) as itemCount FROM ReferenceData WHERE isActive = 1 AND isDeleted = 0 GROUP BY module, listName ORDER BY module, listName`, [], (err, rows) => {
      if (err) return reject(err);
      resolve(rows);
    });
  });
};

exports.getReferenceDataByModuleAndList = (module, listName) => {
  return new Promise((resolve, reject) => {
    const db = database.getConnection();
    db.all(`SELECT * FROM ReferenceData WHERE module = ? AND listName = ? AND isActive = 1 AND isDeleted = 0 ORDER BY sortOrder, itemLabel`, [module, listName], (err, rows) => {
      if (err) return reject(err);
      resolve(rows);
    });
  });
};

exports.createReferenceList = (listData) => {
  // listData: { module, listName, items }
  return new Promise((resolve, reject) => {
    if (!listData.module || !listData.listName || !Array.isArray(listData.items) || listData.items.length === 0) {
      return reject(new Error('بيانات القائمة المرجعية غير مكتملة'));
    }
    const stmt = db.prepare(`INSERT INTO ReferenceData (module, listName, itemValue, itemLabel, sortOrder, isActive, isDeleted, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`);
    listData.items.forEach(item => {
      stmt.run([listData.module, listData.listName, item.itemValue, item.itemLabel, item.sortOrder || 0]);
    });
    stmt.finalize();
    resolve({ success: true });
  });
};

exports.updateReferenceListItem = (id, itemData) => {
  return new Promise((resolve, reject) => {
    if (!itemData.itemValue || !itemData.itemLabel) {
      return reject(new Error('بيانات العنصر المرجعي غير مكتملة'));
    }
    db.run(`UPDATE ReferenceData SET itemValue = ?, itemLabel = ?, sortOrder = ?, isActive = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ? AND isDeleted = 0`, [itemData.itemValue, itemData.itemLabel, itemData.sortOrder || 0, itemData.isActive !== false ? 1 : 0, id], function(err) {
      if (err) return reject(err);
      resolve({ success: true });
    });
  });
};

exports.deleteReferenceList = (module, listName) => {
  return new Promise((resolve, reject) => {
    db.run(`UPDATE ReferenceData SET isDeleted = 1, updatedAt = CURRENT_TIMESTAMP WHERE module = ? AND listName = ?`, [module, listName], function(err) {
      if (err) return reject(err);
      resolve({ success: true });
    });
  });
};

exports.deleteReferenceDataItem = (id) => {
  return new Promise((resolve, reject) => {
    db.run(`UPDATE ReferenceData SET isDeleted = 1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`, [id], function(err) {
      if (err) return reject(err);
      resolve({ success: true });
    });
  });
};
