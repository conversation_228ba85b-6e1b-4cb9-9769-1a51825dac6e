import React, { useState, useRef } from 'react';
import { useDateContext } from '@/contexts/date-context';
import { Input } from '@/components/ui/input';
import { Calendar } from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';

interface EnhancedDateInputProps {
  value?: string;
  onChange?: (date: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  id?: string;
}

export const EnhancedDateInput: React.FC<EnhancedDateInputProps> = ({
  value,
  onChange,
  placeholder,
  className = '',
  disabled = false,
  required = false,
  id
}) => {
  const { formatDate, getDateFormat } = useDateContext();
  const { language } = useLanguage();
  const hiddenInputRef = useRef<HTMLInputElement>(null);

  // Determine text direction based on language
  const isRTL = language === 'ar';
  const textDirection = isRTL ? 'rtl' : 'ltr';

  // Get placeholder based on date format
  const getPlaceholder = (): string => {
    if (placeholder) return placeholder;
    const format = getDateFormat();
    return `${format.replace(/YYYY/g, '2025').replace(/MM/g, '07').replace(/DD/g, '15')}`;
  };

  // Format the display value
  const displayValue = value ? formatDate(value) : '';

  // Handle native date input change
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isoDate = e.target.value; // Always YYYY-MM-DD
    onChange?.(isoDate);
  };

  // Handle click on display input or calendar icon
  const handleClick = () => {
    if (hiddenInputRef.current) {
      hiddenInputRef.current.showPicker?.();
    }
  };

  return (
    <div className="relative">
      {/* Display input (shows formatted date) */}
      <div className="relative cursor-pointer" onClick={handleClick}>
        <Input
          id={id}
          type="text"
          value={displayValue}
          placeholder={getPlaceholder()}
          className={`${className} cursor-pointer bg-white ${isRTL ? 'text-right' : 'text-left'}`}
          style={{
            direction: textDirection,
            textAlign: isRTL ? 'right' : 'left',
            unicodeBidi: 'plaintext'
          }}
          disabled={disabled}
          required={required}
          readOnly
        />
        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
      </div>

      {/* Hidden native date input */}
      <input
        ref={hiddenInputRef}
        type="date"
        value={value || ''}
        onChange={handleDateChange}
        className={`absolute inset-0 w-full h-full opacity-0 cursor-pointer ${isRTL ? 'text-right' : 'text-left'}`}
        style={{
          direction: textDirection,
          textAlign: isRTL ? 'right' : 'left',
          unicodeBidi: 'plaintext'
        }}
        disabled={disabled}
        required={required}
      />

      {/* Debug info (development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="text-xs text-gray-400 mt-1">
          <div>القيمة: {value || 'فارغ'}</div>
          <div>المعروض: {displayValue || 'فارغ'}</div>
          <div>التنسيق: {getDateFormat()}</div>
        </div>
      )}
    </div>
  );
};

export default EnhancedDateInput;
