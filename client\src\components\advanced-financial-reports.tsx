import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import { useCurrency } from "@/hooks/use-currency";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts";
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Calendar, 
  FileText, 
  Download, 
  Print,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target
} from "lucide-react";

interface AdvancedFinancialReportsProps {
  contractId?: number;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
}

interface ContractSummary {
  id: number;
  contractNumber: string;
  clientName: string;
  totalValue: number;
  paidAmount: number;
  remainingAmount: number;
  paymentProgress: number;
  status: string;
  overdueAmount: number;
  nextPaymentDate: string;
}

interface PaymentAnalytics {
  totalContracts: number;
  totalValue: number;
  totalPaid: number;
  totalRemaining: number;
  totalOverdue: number;
  averagePaymentProgress: number;
  monthlyCollections: Array<{
    month: string;
    amount: number;
    target: number;
  }>;
  paymentMethods: Array<{
    method: string;
    amount: number;
    percentage: number;
  }>;
  contractStatuses: Array<{
    status: string;
    count: number;
    value: number;
  }>;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function AdvancedFinancialReports({
  contractId,
  dateRange
}: AdvancedFinancialReportsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState("current-month");
  const [reportType, setReportType] = useState("overview");

  // Fetch contracts summary
  const { data: contractsSummary = [], isLoading: contractsLoading } = useQuery<ContractSummary[]>({
    queryKey: ['/api/reports/contracts-summary', dateRange],
  });

  // Fetch payment analytics
  const { data: paymentAnalytics, isLoading: analyticsLoading } = useQuery<PaymentAnalytics>({
    queryKey: ['/api/reports/payment-analytics', selectedPeriod, dateRange],
  });

  // Calculate KPIs
  const kpis = React.useMemo(() => {
    if (!paymentAnalytics) return null;

    const collectionRate = paymentAnalytics.totalValue > 0 
      ? (paymentAnalytics.totalPaid / paymentAnalytics.totalValue) * 100 
      : 0;
    
    const overdueRate = paymentAnalytics.totalValue > 0 
      ? (paymentAnalytics.totalOverdue / paymentAnalytics.totalValue) * 100 
      : 0;

    const currentMonthTarget = paymentAnalytics.monthlyCollections[paymentAnalytics.monthlyCollections.length - 1]?.target || 0;
    const currentMonthActual = paymentAnalytics.monthlyCollections[paymentAnalytics.monthlyCollections.length - 1]?.amount || 0;
    const targetAchievement = currentMonthTarget > 0 ? (currentMonthActual / currentMonthTarget) * 100 : 0;

    return {
      collectionRate,
      overdueRate,
      targetAchievement,
      averageContractValue: paymentAnalytics.totalContracts > 0 
        ? paymentAnalytics.totalValue / paymentAnalytics.totalContracts 
        : 0
    };
  }, [paymentAnalytics]);

  const handleExportReport = (format: 'pdf' | 'excel') => {
    // Implementation for exporting reports
    console.log(`Exporting report as ${format}`);
  };

  const handlePrintReport = () => {
    window.print();
  };

  if (contractsLoading || analyticsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل التقارير...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="اختر الفترة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current-month">الشهر الحالي</SelectItem>
              <SelectItem value="last-month">الشهر الماضي</SelectItem>
              <SelectItem value="current-quarter">الربع الحالي</SelectItem>
              <SelectItem value="current-year">السنة الحالية</SelectItem>
              <SelectItem value="custom">فترة مخصصة</SelectItem>
            </SelectContent>
          </Select>

          <Select value={reportType} onValueChange={setReportType}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="نوع التقرير" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="overview">نظرة عامة</SelectItem>
              <SelectItem value="collections">التحصيلات</SelectItem>
              <SelectItem value="overdue">المتأخرات</SelectItem>
              <SelectItem value="performance">الأداء</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handlePrintReport} className="gap-2">
            <Print className="h-4 w-4" />
            طباعة
          </Button>
          <Button variant="outline" onClick={() => handleExportReport('excel')} className="gap-2">
            <Download className="h-4 w-4" />
            تصدير Excel
          </Button>
          <Button onClick={() => handleExportReport('pdf')} className="gap-2">
            <FileText className="h-4 w-4" />
            تصدير PDF
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      {kpis && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">معدل التحصيل</p>
                  <p className="text-2xl font-bold text-green-600">
                    {kpis.collectionRate.toFixed(1)}%
                  </p>
                </div>
                <div className="flex items-center">
                  <TrendingUp className="h-8 w-8 text-green-600" />
                </div>
              </div>
              <Progress value={kpis.collectionRate} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">معدل التأخير</p>
                  <p className="text-2xl font-bold text-red-600">
                    {kpis.overdueRate.toFixed(1)}%
                  </p>
                </div>
                <div className="flex items-center">
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
              </div>
              <Progress value={kpis.overdueRate} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">تحقيق الهدف</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {kpis.targetAchievement.toFixed(1)}%
                  </p>
                </div>
                <div className="flex items-center">
                  <Target className="h-8 w-8 text-blue-600" />
                </div>
              </div>
              <Progress value={kpis.targetAchievement} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">متوسط قيمة العقد</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {formatCurrency(kpis.averageContractValue)}
                  </p>
                </div>
                <div className="flex items-center">
                  <DollarSign className="h-8 w-8 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs value={reportType} onValueChange={setReportType} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="collections">التحصيلات</TabsTrigger>
          <TabsTrigger value="overdue">المتأخرات</TabsTrigger>
          <TabsTrigger value="performance">الأداء</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Monthly Collections Chart */}
            <Card>
              <CardHeader>
                <CardTitle>التحصيلات الشهرية</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={paymentAnalytics?.monthlyCollections || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Legend />
                    <Area type="monotone" dataKey="amount" stackId="1" stroke="#8884d8" fill="#8884d8" name="المحصل" />
                    <Area type="monotone" dataKey="target" stackId="2" stroke="#82ca9d" fill="#82ca9d" name="الهدف" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Payment Methods Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>توزيع طرق السداد</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={paymentAnalytics?.paymentMethods || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ method, percentage }) => `${method} (${percentage.toFixed(1)}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="amount"
                    >
                      {(paymentAnalytics?.paymentMethods || []).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Contract Status Summary */}
          <Card>
            <CardHeader>
              <CardTitle>ملخص حالات العقود</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {(paymentAnalytics?.contractStatuses || []).map((status, index) => (
                  <div key={status.status} className="text-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">{status.status}</p>
                    <p className="text-2xl font-bold" style={{ color: COLORS[index % COLORS.length] }}>
                      {status.count}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(status.value)}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Collections Tab */}
        <TabsContent value="collections" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>تفاصيل التحصيلات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>رقم العقد</TableHead>
                      <TableHead>العميل</TableHead>
                      <TableHead>إجمالي القيمة</TableHead>
                      <TableHead>المحصل</TableHead>
                      <TableHead>المتبقي</TableHead>
                      <TableHead>نسبة التحصيل</TableHead>
                      <TableHead>الحالة</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {contractsSummary.map((contract) => (
                      <TableRow key={contract.id}>
                        <TableCell className="font-medium">
                          {contract.contractNumber}
                        </TableCell>
                        <TableCell>{contract.clientName}</TableCell>
                        <TableCell>{formatCurrency(contract.totalValue)}</TableCell>
                        <TableCell>{formatCurrency(contract.paidAmount)}</TableCell>
                        <TableCell>{formatCurrency(contract.remainingAmount)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Progress value={contract.paymentProgress} className="w-16" />
                            <span className="text-sm">{contract.paymentProgress.toFixed(1)}%</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            className={
                              contract.status === 'نشط' ? 'bg-green-100 text-green-800' :
                              contract.status === 'متأخر' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                            }
                          >
                            {contract.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Overdue Tab */}
        <TabsContent value="overdue" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>العقود المتأخرة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>رقم العقد</TableHead>
                      <TableHead>العميل</TableHead>
                      <TableHead>المبلغ المتأخر</TableHead>
                      <TableHead>تاريخ الاستحقاق</TableHead>
                      <TableHead>أيام التأخير</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {contractsSummary
                      .filter(contract => contract.overdueAmount > 0)
                      .map((contract) => {
                        const daysOverdue = Math.ceil(
                          (new Date().getTime() - new Date(contract.nextPaymentDate).getTime()) / 
                          (1000 * 60 * 60 * 24)
                        );
                        
                        return (
                          <TableRow key={contract.id}>
                            <TableCell className="font-medium">
                              {contract.contractNumber}
                            </TableCell>
                            <TableCell>{contract.clientName}</TableCell>
                            <TableCell className="text-red-600 font-semibold">
                              {formatCurrency(contract.overdueAmount)}
                            </TableCell>
                            <TableCell>
                              {new Date(contract.nextPaymentDate).toLocaleDateString('ar-EG')}
                            </TableCell>
                            <TableCell>
                              <Badge className="bg-red-100 text-red-800">
                                {daysOverdue} يوم
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Button size="sm" variant="outline">
                                إرسال تذكير
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Collection Trend */}
            <Card>
              <CardHeader>
                <CardTitle>اتجاه التحصيلات</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={paymentAnalytics?.monthlyCollections || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Legend />
                    <Line type="monotone" dataKey="amount" stroke="#8884d8" name="المحصل" />
                    <Line type="monotone" dataKey="target" stroke="#82ca9d" name="الهدف" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>مؤشرات الأداء</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">معدل التحصيل الشهري</span>
                  <span className="text-lg font-bold text-green-600">
                    {kpis?.collectionRate.toFixed(1)}%
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">متوسط وقت التحصيل</span>
                  <span className="text-lg font-bold text-blue-600">15 يوم</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">معدل العقود الجديدة</span>
                  <span className="text-lg font-bold text-purple-600">8 عقد/شهر</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">معدل رضا العملاء</span>
                  <span className="text-lg font-bold text-orange-600">4.2/5</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
