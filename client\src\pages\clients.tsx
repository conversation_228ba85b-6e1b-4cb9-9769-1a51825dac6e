import { useState, useEffect, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Link, useLocation } from "wouter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useSettings } from "@/contexts/settings-context";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialog<PERSON>itle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Plus,
  Search,
  Users,
  Phone,
  Mail,
  MapPin,
  Edit,
  Trash2,
  Printer,
  Eye,
  ArrowRight,
  Loader2
} from "lucide-react";
import { ClientCard } from "@/components/client-card";
import { useLanguage } from "@/hooks/use-language";
import { cn } from "@/lib/utils";
import labels from "@/lib/i18n";
import type { Client, Contract } from "@shared/schema";

export default function Clients() {
  const { language, isRTL } = useLanguage();
  const t = labels[language];
  const [, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  const [clientTypeFilter, setClientTypeFilter] = useState("");
  const [financialCategoryFilter, setFinancialCategoryFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20); // 20 عميل في الصفحة

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500); // زيادة المدة لتقليل عدد الطلبات

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [showClientCard, setShowClientCard] = useState(false);

  // Function to normalize client type display
  const normalizeClientType = (clientType: string) => {
    if (clientType === 'individual' || clientType === 'فرد') return 'أفراد';
    if (clientType === 'company' || clientType === 'شركة') return 'شركات';
    return clientType;
  };

  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Check if client can be deleted
  const checkClientDeletionMutation = useMutation({
    mutationFn: async (clientId: number) => {
      const response = await fetch(`/api/clients/${clientId}/can-delete`);
      if (!response.ok) {
        throw new Error('Failed to check client deletion status');
      }
      return response.json();
    },
  });

  // Delete client mutation
  const deleteClientMutation = useMutation({
    mutationFn: async (clientId: number) => {
      const response = await fetch(`/api/clients/${clientId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete client');
      }

      return response.json();
    },
    onSuccess: (data, clientId) => {
      // Refresh the clients list (including search queries)
      queryClient.invalidateQueries({
        queryKey: ['/api/clients'],
        exact: false // This will invalidate all queries that start with ['/api/clients']
      });

      // Show success message
      toast({
        title: "تم حذف العميل بنجاح",
        description: "تم حذف العميل من النظام",
        variant: "default",
      });

      // Close client card if it was showing the deleted client
      if (selectedClient?.id === clientId) {
        setSelectedClient(null);
        setShowClientCard(false);
      }
    },
    onError: (error: Error) => {
      toast({
        title: "خطأ في حذف العميل",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const { data: clientsData, isLoading, error } = useQuery({
    queryKey: ['/api/clients', debouncedSearchQuery, currentPage, pageSize],
    queryFn: async () => {
      try {
        const params = new URLSearchParams({
          page: currentPage.toString(),
          limit: pageSize.toString(),
        });

        if (debouncedSearchQuery && debouncedSearchQuery.trim()) {
          params.append('search', debouncedSearchQuery.trim());
        }

        const url = `/api/clients?${params}`;
        const response = await apiRequest('GET', url);

        const data = await response.json();

        // تأكد من أن البيانات في الشكل الصحيح
        if (data?.success) {
          return {
            clients: Array.isArray(data.data) ? data.data : [],
            pagination: data.pagination || { total: 0, page: 1, pages: 1 }
          };
        } else if (Array.isArray(data)) {
          return {
            clients: data,
            pagination: { total: data.length, page: 1, pages: 1 }
          };
        }

        return { clients: [], pagination: { total: 0, page: 1, pages: 1 } };
      } catch (error) {
        console.error('Query error:', error);
        throw error;
      }
    },
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false
  });

  // Fetch contracts for selected client
  const { data: contracts = [] } = useQuery<Contract[]>({
    queryKey: ['/api/contracts', selectedClient?.id],
    queryFn: async () => {
      const response = await fetch(`/api/contracts?clientId=${selectedClient?.id}`);
      if (!response.ok) return [];
      return response.json();
    },
    enabled: !!selectedClient?.id,
  });

  const filteredClients = useMemo(() => {
    const clientsList = clientsData?.clients || [];
    if (!clientsList || !Array.isArray(clientsList)) return [];

    return clientsList.filter(client => {
      const matchesType = !clientTypeFilter || clientTypeFilter === "all" ||
        client.clientType === clientTypeFilter;
      const matchesCategory = !financialCategoryFilter || financialCategoryFilter === "all" ||
        client.clientFinancial_Category === financialCategoryFilter;

      return matchesType && matchesCategory;
    });
  }, [clientsData, clientTypeFilter, financialCategoryFilter]);

  const handleViewClientCard = (client: Client) => {
    setSelectedClient(client);
    setShowClientCard(true);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleEditClient = (client: Client) => {
    setLocation(`/clients/edit/${client.clientId}`);
  };

  const handleDeleteClient = async (client: Client) => {
    try {
      // First check if client can be deleted
      const checkResult = await checkClientDeletionMutation.mutateAsync(client.id);

      if (!checkResult.canDelete) {
        // Show error message with contract details
        const contractsList = checkResult.details?.contracts?.map((c: any) => c.contractId).join(', ') || '';
        toast({
          title: "لا يمكن حذف العميل",
          description: `العميل مرتبط بـ ${checkResult.activeContractsCount} عقد نشط: ${contractsList}`,
          variant: "destructive",
        });
        return;
      }

      // If can delete, proceed with deletion
      deleteClientMutation.mutate(client.id);
    } catch (error) {
      toast({
        title: "خطأ في التحقق من حالة العميل",
        description: "حدث خطأ أثناء التحقق من إمكانية حذف العميل",
        variant: "destructive",
      });
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="mt-4 text-gray-600">{t.loading}</p>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">{t.error}:</p>
          <p className="text-gray-600 mb-4">{error.message}</p>
          <Button onClick={() => window.location.reload()}>{isRTL ? "إعادة المحاولة" : "Retry"}</Button>
        </div>
      </div>
    );
  }

  // Show client card if selected
  if (showClientCard && selectedClient) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className={cn(
                "flex items-center space-x-4",
                isRTL ? "space-x-reverse" : ""
              )}>
                <Button variant="ghost" size="sm" onClick={() => setShowClientCard(false)}>
                  <ArrowRight className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                  {isRTL ? "العودة للعملاء" : "Back to Clients"}
                </Button>
                <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
                <div className={cn(
                  "flex items-center space-x-2",
                  isRTL ? "space-x-reverse" : ""
                )}>
                  <Printer className="h-5 w-5 text-primary" />
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                    {isRTL ? "كارت العميل - " : "Client Card - "}{selectedClient.clientName}
                  </h1>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="py-8">
          <ClientCard
            client={selectedClient}
            contracts={contracts}
            showPrintButton={true}
            showEditButton={true}
            onPrint={handlePrint}
            onEdit={() => handleEditClient(selectedClient)}
          />
        </main>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <div className={cn(
                "flex items-center space-x-2 mb-2",
                isRTL ? "space-x-reverse" : ""
              )}>
                <Users className="h-5 w-5 text-primary" />
                <CardTitle className="text-xl">{t.clients}</CardTitle>
              </div>
              <p className="text-sm text-muted-foreground">
                {t.clientsSubtitle}
              </p>
            </div>
            <Link href="/clients/new">
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                {t.addClient}
              </Button>
            </Link>
          </div>
        </CardHeader>
      </Card>

      {/* Search and Filter */}
      <Card>
        <CardContent className="p-6 bg-muted/30">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">{t.search}</label>
              <div className="relative">
                <Input
                  placeholder={isRTL ? "البحث في العملاء..." : "Search clients..."}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={cn(isRTL ? "pr-10" : "pl-10")}
                />
                {isLoading && debouncedSearchQuery ? (
                  <Loader2 className={cn(
                    "h-4 w-4 absolute top-1/2 transform -translate-y-1/2 text-muted-foreground animate-spin",
                    isRTL ? "right-3" : "left-3"
                  )} />
                ) : (
                  <Search className={cn(
                    "h-4 w-4 absolute top-1/2 transform -translate-y-1/2 text-muted-foreground",
                    isRTL ? "right-3" : "left-3"
                  )} />
                )}
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">{t.clientType}</label>
              <Select value={clientTypeFilter} onValueChange={setClientTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={isRTL ? "جميع الأنواع" : "All Types"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{isRTL ? "جميع الأنواع" : "All Types"}</SelectItem>
                  <SelectItem value="individual">{t.clientTypes.individuals}</SelectItem>
                  <SelectItem value="company">{t.clientTypes.companies}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">{t.financialCategory}</label>
              <Select value={financialCategoryFilter} onValueChange={setFinancialCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={isRTL ? "جميع التصنيفات" : "All Categories"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{isRTL ? "جميع التصنيفات" : "All Categories"}</SelectItem>
                  <SelectItem value="ممتاز">ممتاز</SelectItem>
                  <SelectItem value="جيد">جيد</SelectItem>
                  <SelectItem value="متوسط">متوسط</SelectItem>
                  <SelectItem value="ضعيف">ضعيف</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error State */}
      {error ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-red-500 mb-4">
              <Users className="h-12 w-12" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              خطأ في تحميل البيانات
            </h3>
            <p className="text-gray-500 dark:text-gray-400 text-center mb-4">
              {error.message || 'حدث خطأ أثناء تحميل العملاء'}
            </p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="gap-2"
            >
              إعادة المحاولة
            </Button>
          </CardContent>
        </Card>
      ) : isLoading ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {debouncedSearchQuery ? 'جاري البحث...' : 'جاري تحميل العملاء...'}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 text-center">
              يرجى الانتظار قليلاً
            </p>
          </CardContent>
        </Card>
      ) : /* Clients Grid */
      filteredClients.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Users className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {searchQuery || clientTypeFilter || financialCategoryFilter
                ? (isRTL ? "لا توجد نتائج" : "No results found")
                : (isRTL ? "لا يوجد عملاء" : "No clients yet")
              }
            </h3>
            <p className="text-gray-500 dark:text-gray-400 text-center mb-4">
              {searchQuery || clientTypeFilter || financialCategoryFilter
                ? (isRTL ? "جرب البحث بكلمات مختلفة" : "Try searching with different terms")
                : (isRTL ? "ابدأ بإضافة عميل جديد" : "Get started by adding a new client")
              }
            </p>
            {!searchQuery && !clientTypeFilter && !financialCategoryFilter && (
              <Link href="/clients/new">
                <Button className="gap-2">
                  <Plus className="h-4 w-4" />
                  {t.addClient}
                </Button>
              </Link>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-3">
          {filteredClients.map((client) => (
            <Card key={client.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-1 px-2 pt-2">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-xs font-semibold truncate">
                      {client.clientName}
                    </CardTitle>
                    <p className="text-[10px] text-muted-foreground truncate">
                      {isRTL ? "رقم:" : "ID:"} {client.clientId}
                    </p>
                    <div className="flex gap-1 mt-1">
                      <Badge variant={normalizeClientType(client.clientType) === "أفراد" ? "default" : "secondary"} className="text-[9px] px-1 py-0">
                        {normalizeClientType(client.clientType)}
                      </Badge>
                      {client.clientFinancial_Category && (
                        <Badge variant="outline" className="text-[9px] px-1 py-0">
                          {client.clientFinancial_Category}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-1 ml-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-5 w-5 p-0"
                      onClick={() => handleViewClientCard(client)}
                    >
                      <Printer className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-5 w-5 p-0"
                      onClick={() => handleEditClient(client)}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-5 w-5 p-0 text-red-600 hover:text-red-700"
                          disabled={deleteClientMutation.isPending || checkClientDeletionMutation.isPending}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>تأكيد حذف العميل</AlertDialogTitle>
                          <AlertDialogDescription>
                            هل أنت متأكد من حذف العميل "{client.clientName}"؟
                            <br />
                            <span className="text-sm text-gray-500 mt-2 block">
                              ملاحظة: سيتم التحقق أولاً من عدم وجود عقود نشطة مرتبطة بالعميل.
                            </span>
                            <span className="text-sm text-gray-500 mt-1 block">
                              الحذف منطقي فقط، ويمكن استرداد العميل لاحقاً من قبل المدير.
                            </span>
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>إلغاء</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteClient(client)}
                            className="bg-red-600 hover:bg-red-700"
                            disabled={deleteClientMutation.isPending || checkClientDeletionMutation.isPending}
                          >
                            {(deleteClientMutation.isPending || checkClientDeletionMutation.isPending) ? "جاري التحقق..." : "حذف العميل"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="px-2 pb-2">
                <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-[10px]">
                  {client.clientPhoneWhatsapp && (
                    <div className="flex justify-between col-span-2">
                      <span className="text-muted-foreground">الهاتف:</span>
                      <span className="font-medium truncate ml-1">{client.clientPhoneWhatsapp}</span>
                    </div>
                  )}

                  {client.clientEmail && (
                    <div className="flex justify-between col-span-2">
                      <span className="text-muted-foreground">البريد:</span>
                      <span className="font-medium truncate ml-1">{client.clientEmail}</span>
                    </div>
                  )}

                  {client.clientAddress && (
                    <div className="flex justify-between col-span-2">
                      <span className="text-muted-foreground">العنوان:</span>
                      <span className="font-medium truncate ml-1">{client.clientAddress}</span>
                    </div>
                  )}

                  {client.clientNationalId && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">الهوية:</span>
                      <span className="font-medium">{client.clientNationalId}</span>
                    </div>
                  )}

                  {client.clientCommercialRecord && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">السجل:</span>
                      <span className="font-medium">{client.clientCommercialRecord}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
