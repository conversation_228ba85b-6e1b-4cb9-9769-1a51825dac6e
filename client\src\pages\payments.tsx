import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Plus, 
  CreditCard, 
  Calendar, 
  DollarSign, 
  CheckCircle, 
  Clock,
  AlertTriangle
} from "lucide-react";
import { formatPaymentStatus } from "@/lib/formatters";
import { useCurrency } from "@/hooks/use-currency";
import { useDateFormat } from "@/hooks/use-date-format";
import { useLanguage } from "@/hooks/use-language";
import { cn } from "@/lib/utils";
import labels from "@/lib/i18n";
import type { Payment } from "@shared/schema";

export default function Payments() {
  const { language, isRTL } = useLanguage();
  const t = labels[language];
  const { formatCurrency } = useCurrency();
  const { formatDate } = useDateFormat();

  const { data: payments, isLoading } = useQuery<Payment[]>({
    queryKey: ['/api/payments'],
  });

  const { data: overduePayments, isLoading: overdueLoading } = useQuery<Payment[]>({
    queryKey: ['/api/payments/overdue'],
  });

  const paidPayments = payments?.filter(p => p.isPaid) || [];
  const unpaidPayments = payments?.filter(p => !p.isPaid) || [];

  return (
    <div className={cn("space-y-6", isRTL ? "text-right" : "text-left")}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className={cn("flex items-center justify-between", isRTL ? "flex-row-reverse" : "flex-row")}>
            <div className={cn(isRTL ? "text-right" : "text-left")}>
              <CardTitle className="text-xl">{t.payments}</CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                تتبع وإدارة جميع المدفوعات
              </p>
            </div>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              تسجيل دفعة جديدة
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Payment Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">المدفوعات المكتملة</p>
                <p className="text-2xl font-bold text-green-600">
                  {isLoading ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    paidPayments.length
                  )}
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">المدفوعات المعلقة</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {isLoading ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    unpaidPayments.length
                  )}
                </p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
                <Clock className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{t.overduePayments}</p>
                <p className="text-2xl font-bold text-red-600">
                  {overdueLoading ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    overduePayments?.length || 0
                  )}
                </p>
              </div>
              <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Overdue Payments Alert */}
      {overduePayments && overduePayments.length > 0 && (
        <Card className="border-red-200 dark:border-red-800">
          <CardHeader className="bg-red-50 dark:bg-red-900/20">
            <CardTitle className="text-red-800 dark:text-red-200 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              مدفوعات متأخرة تتطلب متابعة عاجلة
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            <div className="space-y-2">
              {overduePayments.slice(0, 5).map((payment) => (
                <div
                  key={payment.id}
                  className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border"
                >
                  <div>
                    <p className="font-medium">قسط رقم {payment.paymentNumber}</p>
                    <p className="text-sm text-muted-foreground">
                      تاريخ الاستحقاق: {formatDate(payment.dueDate)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-red-600">
                      {formatCurrency(payment.totalAmount)}
                    </p>
                    <Button variant="outline" size="sm" className="mt-1">
                      متابعة
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Payments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            آخر المدفوعات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {isLoading ? (
              Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <Skeleton className="w-10 h-10 rounded-full" />
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  </div>
                  <div className="text-right space-y-1">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              ))
            ) : payments && payments.length > 0 ? (
              payments.slice(0, 10).map((payment) => {
                const statusInfo = formatPaymentStatus(payment.isPaid);
                return (
                  <div
                    key={payment.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center gap-4">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        payment.isPaid 
                          ? 'bg-green-100 dark:bg-green-900/30' 
                          : 'bg-yellow-100 dark:bg-yellow-900/30'
                      }`}>
                        {payment.isPaid ? (
                          <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                        ) : (
                          <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium">قسط رقم {payment.paymentNumber}</p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>تاريخ الاستحقاق: {formatDate(payment.dueDate)}</span>
                          {payment.isPaid && payment.paidDate && (
                            <span>تاريخ الدفع: {formatDate(payment.paidDate)}</span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold">
                        {formatCurrency(payment.totalAmount)}
                      </p>
                      <Badge variant={statusInfo.variant} className="mt-1">
                        {statusInfo.text}
                      </Badge>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-12">
                <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium text-muted-foreground">{t.noData}</p>
                <p className="text-sm text-muted-foreground mt-2">
                  لا توجد مدفوعات مسجلة حتى الآن
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
