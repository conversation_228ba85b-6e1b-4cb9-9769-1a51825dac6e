// ===== ERROR HANDLING MIDDLEWARE =====
// Author: Augment Code
// Description: Centralized error handling middleware

/**
 * Custom error class for application errors
 */
class AppError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR', details = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Async error wrapper
 * Wraps async route handlers to catch errors automatically
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Not found middleware
 * Handles 404 errors for undefined routes
 */
const notFound = (req, res, next) => {
  const error = new AppError(
    `المسار ${req.originalUrl} غير موجود`,
    404,
    'NOT_FOUND'
  );
  next(error);
};

/**
 * Global error handling middleware
 * Handles all errors in the application
 */
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  console.error('Error:', {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'المورد غير موجود';
    error = new AppError(message, 404, 'RESOURCE_NOT_FOUND');
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'البيانات مكررة';
    error = new AppError(message, 400, 'DUPLICATE_FIELD');
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message).join(', ');
    error = new AppError(message, 400, 'VALIDATION_ERROR');
  }

  // SQLite errors
  if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
    error = new AppError('البيانات مكررة', 400, 'DUPLICATE_FIELD');
  }

  if (err.code === 'SQLITE_CONSTRAINT_FOREIGNKEY') {
    error = new AppError('مرجع غير صحيح', 400, 'INVALID_REFERENCE');
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error = new AppError('رمز غير صحيح', 401, 'INVALID_TOKEN');
  }

  if (err.name === 'TokenExpiredError') {
    error = new AppError('انتهت صلاحية الرمز', 401, 'TOKEN_EXPIRED');
  }

  // Default error response
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(error.statusCode || 500).json({
    success: false,
    error: error.message || 'خطأ داخلي في الخادم',
    code: error.code || 'INTERNAL_ERROR',
    ...(error.details && { details: error.details }),
    ...(isDevelopment && { stack: err.stack })
  });
};

/**
 * Request timeout middleware
 */
const timeout = (seconds = 30) => {
  return (req, res, next) => {
    req.setTimeout(seconds * 1000, () => {
      const error = new AppError(
        'انتهت مهلة الطلب',
        408,
        'REQUEST_TIMEOUT'
      );
      next(error);
    });
    next();
  };
};

/**
 * Rate limiting middleware
 */
const rateLimit = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100, // limit each IP to 100 requests per windowMs
    message = 'تم تجاوز الحد المسموح من الطلبات'
  } = options;

  const requests = new Map();

  return (req, res, next) => {
    const key = req.ip;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old entries
    for (const [ip, timestamps] of requests.entries()) {
      const validTimestamps = timestamps.filter(time => time > windowStart);
      if (validTimestamps.length === 0) {
        requests.delete(ip);
      } else {
        requests.set(ip, validTimestamps);
      }
    }

    // Get current requests for this IP
    const currentRequests = requests.get(key) || [];
    const validRequests = currentRequests.filter(time => time > windowStart);

    if (validRequests.length >= max) {
      return res.status(429).json({
        success: false,
        error: message,
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }

    // Add current request
    validRequests.push(now);
    requests.set(key, validRequests);

    next();
  };
};

module.exports = {
  AppError,
  asyncHandler,
  notFound,
  errorHandler,
  timeout,
  rateLimit
};
