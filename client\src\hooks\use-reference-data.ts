import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import type { ReferenceDataItem } from "@shared/schema";

/**
 * Hook to fetch reference data for a specific module and list
 */
export function useReferenceData(module: string, listName: string) {
  return useQuery<ReferenceDataItem[]>({
    queryKey: ["/api/reference-data", module, listName],
    queryFn: async () => {
      const response = await apiRequest("GET", `/api/reference-data/${module}/${listName}`);
      return response.json();
    },
    enabled: !!module && !!listName,
  });
}

/**
 * Hook to get reference data as options for select components
 */
export function useReferenceDataOptions(module: string, listName: string) {
  const { data, isLoading, error } = useReferenceData(module, listName);
  
  const options = data?.map(item => ({
    value: item.itemValue,
    label: item.itemLabel,
  })) || [];

  return {
    options,
    isLoading,
    error,
  };
}

/**
 * Common reference data lists for different modules
 */
export const REFERENCE_LISTS = {
  clients: {
    types: "أنواع العملاء",
    categories: "تصنيفات العملاء", 
    sources: "مصادر العملاء",
    statuses: "حالات العملاء",
  },
  contracts: {
    types: "أنواع العقود",
    statuses: "حالات العقود",
    categories: "تصنيفات العقود",
    paymentMethods: "طرق الدفع",
  },
  receivables: {
    types: "أنواع الاستحقاقات",
    statuses: "حالات الاستحقاقات",
    priorities: "أولويات الاستحقاقات",
  },
  payments: {
    methods: "طرق الدفع",
    statuses: "حالات المدفوعات",
    types: "أنواع المدفوعات",
  },
  cheques: {
    statuses: "حالات الشيكات",
    types: "أنواع الشيكات",
    banks: "البنوك",
  },
} as const;
