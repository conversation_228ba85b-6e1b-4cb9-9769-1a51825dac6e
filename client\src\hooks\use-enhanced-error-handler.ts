import { useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

// Error message dictionary (client-side version)
const ERROR_MESSAGES = {
  VALIDATION: {
    CLIENT_ID_REQUIRED: {
      message: 'رقم العميل مطلوب',
      guidance: 'يرجى إدخال رقم العميل. يمكن أن يكون رقم الهوية أو رقم مخصص',
      severity: 'warning' as const,
      action: 'focus_field'
    },
    CLIENT_ID_EXISTS: {
      message: 'رقم العميل مستخدم من قبل',
      guidance: 'هذا الرقم مسجل لعميل آخر. يرجى اختيار رقم مختلف أو البحث عن العميل الموجود',
      severity: 'error' as const,
      action: 'search_client'
    },
    CLIENT_NAME_REQUIRED: {
      message: 'اسم العميل مطلوب',
      guidance: 'يرجى إدخال الاسم الكامل للعميل',
      severity: 'warning' as const,
      action: 'focus_field'
    },
    CLIENT_PHONE_INVALID: {
      message: 'رقم الهاتف غير صحيح',
      guidance: 'يرجى إدخال رقم هاتف صحيح (مثال: 0501234567 أو +************)',
      severity: 'warning' as const,
      action: 'focus_field'
    },
    CLIENT_EMAIL_INVALID: {
      message: 'البريد الإلكتروني غير صحيح',
      guidance: 'يرجى إدخال عنوان بريد إلكتروني صحيح (مثال: <EMAIL>)',
      severity: 'warning' as const,
      action: 'focus_field'
    },
    CONTRACT_CLIENT_REQUIRED: {
      message: 'يجب اختيار عميل للعقد',
      guidance: 'يرجى اختيار عميل من القائمة أو إضافة عميل جديد',
      severity: 'error' as const,
      action: 'add_client'
    },
    CONTRACT_NO_PRODUCTS: {
      message: 'العقد لا يحتوي على منتجات',
      guidance: 'يجب إضافة منتج واحد على الأقل للعقد',
      severity: 'error' as const,
      action: 'add_product'
    },
    REFERENCE_DATA_MISSING: {
      message: 'بيانات مرجعية مطلوبة',
      guidance: 'يجب إعداد البيانات المرجعية أولاً قبل المتابعة',
      severity: 'error' as const,
      action: 'setup_reference_data'
    }
  },
  DATABASE: {
    CONNECTION_FAILED: {
      message: 'فشل الاتصال بقاعدة البيانات',
      guidance: 'تعذر الاتصال بقاعدة البيانات. يرجى التحقق من إعدادات النظام أو الاتصال بالدعم الفني',
      severity: 'critical' as const,
      technical: true
    },
    CONSTRAINT_VIOLATION: {
      message: 'البيانات موجودة مسبقاً',
      guidance: 'هذه البيانات مسجلة من قبل. يرجى التحقق من البيانات المدخلة',
      severity: 'error' as const
    },
    DATABASE_BUSY: {
      message: 'قاعدة البيانات مشغولة',
      guidance: 'النظام مشغول حالياً. يرجى المحاولة مرة أخرى',
      severity: 'warning' as const,
      retryable: true
    }
  },
  NETWORK: {
    CONNECTION_FAILED: {
      message: 'فشل الاتصال بالخادم',
      guidance: 'تعذر الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى',
      severity: 'error' as const,
      retryable: true
    },
    TIMEOUT: {
      message: 'انتهت مهلة الاتصال',
      guidance: 'استغرقت العملية وقتاً أطول من المتوقع. يرجى المحاولة مرة أخرى',
      severity: 'warning' as const,
      retryable: true
    },
    SERVER_ERROR: {
      message: 'خطأ في الخادم',
      guidance: 'حدث خطأ في الخادم. يرجى المحاولة لاحقاً أو الاتصال بالدعم الفني',
      severity: 'error' as const,
      technical: true
    },
    UNAUTHORIZED: {
      message: 'غير مصرح بالوصول',
      guidance: 'ليس لديك صلاحية لتنفيذ هذا الإجراء',
      severity: 'error' as const
    },
    NOT_FOUND: {
      message: 'البيانات غير موجودة',
      guidance: 'البيانات المطلوبة غير موجودة أو تم حذفها',
      severity: 'warning' as const
    }
  }
};

interface ErrorInfo {
  message: string;
  guidance: string;
  severity: 'critical' | 'error' | 'warning' | 'info';
  action?: string;
  retryable?: boolean;
  confirmable?: boolean;
  technical?: boolean;
  context?: any;
  operation?: string;
  title?: string;
}

interface ErrorHandlerOptions {
  operation?: string;
  context?: any;
  onRetry?: () => void;
  onAction?: (action: string) => void;
  showToast?: boolean;
  logError?: boolean;
}

export function useEnhancedErrorHandler() {
  const { toast } = useToast();

  const getErrorInfo = useCallback((error: any, category?: string, code?: string): ErrorInfo => {
    // Try to extract error information from different sources
    if (error?.error?.errorCode && error?.error?.message) {
      // Server error with structured format
      return {
        message: error.error.message,
        guidance: error.error.details?.guidance || 'يرجى المحاولة مرة أخرى',
        severity: mapStatusCodeToSeverity(error.error.statusCode),
        context: error.error.details,
        technical: error.error.statusCode >= 500
      };
    }

    if (category && code && ERROR_MESSAGES[category]?.[code]) {
      // Known error from dictionary
      return ERROR_MESSAGES[category][code];
    }

    // Try to parse common error patterns
    const errorMessage = error?.message || error?.toString() || 'خطأ غير معروف';
    
    if (errorMessage.includes('Failed to fetch') || errorMessage.includes('NetworkError')) {
      return ERROR_MESSAGES.NETWORK.CONNECTION_FAILED;
    }
    
    if (errorMessage.includes('timeout') || errorMessage.includes('ETIMEDOUT')) {
      return ERROR_MESSAGES.NETWORK.TIMEOUT;
    }
    
    if (errorMessage.includes('SQLITE_CONSTRAINT') || errorMessage.includes('duplicate')) {
      return ERROR_MESSAGES.DATABASE.CONSTRAINT_VIOLATION;
    }
    
    if (errorMessage.includes('SQLITE_BUSY')) {
      return ERROR_MESSAGES.DATABASE.DATABASE_BUSY;
    }

    // Default error
    return {
      message: errorMessage,
      guidance: 'يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني',
      severity: 'error'
    };
  }, []);

  const mapStatusCodeToSeverity = (statusCode: number): 'critical' | 'error' | 'warning' | 'info' => {
    if (statusCode >= 500) return 'critical';
    if (statusCode >= 400) return 'error';
    if (statusCode >= 300) return 'warning';
    return 'info';
  };

  const getSeverityVariant = (severity: string) => {
    switch (severity) {
      case 'critical':
      case 'error':
        return 'destructive';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return 'destructive';
    }
  };

  const getDurationBySeverity = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 10000; // 10 seconds
      case 'error':
        return 7000;  // 7 seconds
      case 'warning':
        return 5000;  // 5 seconds
      case 'info':
        return 3000;  // 3 seconds
      default:
        return 5000;
    }
  };

  const getOperationTitle = (operation: string) => {
    const operationTitles = {
      'create_client': 'خطأ في إنشاء العميل',
      'update_client': 'خطأ في تحديث العميل',
      'delete_client': 'خطأ في حذف العميل',
      'create_contract': 'خطأ في إنشاء العقد',
      'update_contract': 'خطأ في تحديث العقد',
      'create_payment': 'خطأ في تسجيل الدفعة',
      'load_data': 'خطأ في تحميل البيانات',
      'save_settings': 'خطأ في حفظ الإعدادات'
    };
    
    return operationTitles[operation] || 'خطأ في العملية';
  };

  const handleError = useCallback((
    error: any,
    options: ErrorHandlerOptions = {}
  ) => {
    const {
      operation,
      context,
      onRetry,
      onAction,
      showToast = true,
      logError = true
    } = options;

    // Log error for debugging
    if (logError) {
      console.error('Enhanced Error Handler:', {
        error,
        operation,
        context,
        timestamp: new Date().toISOString()
      });
    }

    const errorInfo = getErrorInfo(error);
    
    // Add operation context
    if (operation) {
      errorInfo.title = getOperationTitle(operation);
      errorInfo.operation = operation;
    }
    
    if (context) {
      errorInfo.context = { ...errorInfo.context, ...context };
    }

    // Show toast notification
    if (showToast) {
      const actions: any = {};
      
      if (errorInfo.retryable && onRetry) {
        actions.action = {
          label: 'إعادة المحاولة',
          onClick: onRetry
        };
      } else if (errorInfo.action && onAction) {
        const actionLabels = {
          'search_client': 'البحث عن العميل',
          'add_client': 'إضافة عميل جديد',
          'add_product': 'إضافة منتج',
          'open_settings': 'فتح الإعدادات',
          'setup_reference_data': 'إعداد البيانات المرجعية'
        };
        
        actions.action = {
          label: actionLabels[errorInfo.action] || 'إجراء',
          onClick: () => onAction(errorInfo.action!)
        };
      }

      toast({
        title: errorInfo.title || errorInfo.message,
        description: errorInfo.guidance,
        variant: getSeverityVariant(errorInfo.severity),
        duration: getDurationBySeverity(errorInfo.severity),
        ...actions
      });
    }

    return errorInfo;
  }, [toast, getErrorInfo]);

  // Specialized handlers for common operations
  const handleValidationError = useCallback((
    field: string,
    code: string,
    context?: any
  ) => {
    return handleError(null, {
      operation: 'validation',
      context: { field, code, ...context }
    });
  }, [handleError]);

  const handleNetworkError = useCallback((
    error: any,
    operation?: string,
    onRetry?: () => void
  ) => {
    return handleError(error, {
      operation,
      onRetry,
      context: { type: 'network' }
    });
  }, [handleError]);

  const handleDatabaseError = useCallback((
    error: any,
    operation?: string
  ) => {
    return handleError(error, {
      operation,
      context: { type: 'database' }
    });
  }, [handleError]);

  return {
    handleError,
    handleValidationError,
    handleNetworkError,
    handleDatabaseError,
    getErrorInfo
  };
}
