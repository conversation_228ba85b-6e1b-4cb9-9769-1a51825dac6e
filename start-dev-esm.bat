@echo off
echo ===== Starting Contract Management System (ESM Mode) =====
echo.

echo Starting backend server (ESM)...
start "Backend Server" cmd /k "node server.mjs"

echo Waiting for backend to start...
timeout /t 3 /nobreak > nul

echo Starting frontend development server...
start "Frontend Dev Server" cmd /k "npm run dev"

echo.
echo ===== Both servers are starting =====
echo Backend: http://localhost:3001
echo Frontend: http://localhost:5173
echo.
echo Press any key to close this window...
pause > nul
