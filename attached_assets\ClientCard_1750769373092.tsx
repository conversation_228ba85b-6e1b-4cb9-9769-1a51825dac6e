import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Separator } from "../components/ui/separator";
import {
  User,
  Phone,
  Mail,
  MapPin,
  Building2,
  FileText,
  CreditCard,
  Eye,
  Printer,
  Calendar,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Image,
  Download,
  ZoomIn,
  Edit
} from "lucide-react";

interface ClientData {
  id?: number;
  clientId: string;
  clientType: string;
  clientName: string;
  clientAddress?: string;
  clientPhoneWhatsapp: string;
  clientPhone2?: string;
  clientPhone3?: string;
  clientEmail?: string;
  clientNotes?: string;
  clientFinancialGuarantee?: string;
  clientID_Image?: string;
  clientFinancial_Category?: string;
  clientLegal_Rep?: string;
  clientPartner?: string;
  clientReg_Number?: string;
  clientTaxReg_Number?: string;
  clientLegal_Status?: string;
  clientRemarks?: string;
}

interface Contract {
  id: number;
  contractNumber: string;
  contractType: string;
  startDate: string;
  contractStatus: string;
  totalContractValue: number;
  monthlyAmount: number;
  contractDurationYears: number;
  outstandingAmount?: number;
  paidAmount?: number;
}

interface ClientCardProps {
  client: ClientData;
  contracts?: Contract[];
  showPrintButton?: boolean;
  showEditButton?: boolean;
  onViewContract?: (contractId: number) => void;
  onPrint?: () => void;
  onEdit?: () => void;
}

export const ClientCard: React.FC<ClientCardProps> = ({
  client,
  contracts = [],
  showPrintButton = true,
  showEditButton = true,
  onViewContract,
  onPrint,
  onEdit
}) => {
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  const getTotalContractValue = () => {
    return contracts.reduce((total, contract) => total + contract.totalContractValue, 0);
  };

  const getActiveContractsCount = () => {
    return contracts.filter(contract => contract.contractStatus === 'نشط').length;
  };

  const getTotalOutstanding = () => {
    return contracts.reduce((total, contract) => total + (contract.outstandingAmount || 0), 0);
  };

  const handleImageView = (imageData: string) => {
    setSelectedImage(imageData);
    setShowImageModal(true);
  };

  const handlePrintImage = (imageData: string) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>طباعة المرفق - ${client.clientName}</title>
            <style>
              body {
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
                text-align: center;
              }
              .header {
                margin-bottom: 20px;
                border-bottom: 2px solid #333;
                padding-bottom: 10px;
              }
              .client-info {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
              }
              .document-title {
                font-size: 16px;
                color: #666;
                margin-bottom: 20px;
              }
              img {
                max-width: 100%;
                max-height: 80vh;
                border: 1px solid #ddd;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              }
              @media print {
                body { padding: 0; }
                .header { page-break-inside: avoid; }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <div class="client-info">مرفق العميل: ${client.clientName}</div>
              <div class="document-title">رقم العميل: ${client.clientId} | نوع العميل: ${client.clientType}</div>
            </div>
            <img src="${imageData}" alt="مرفق العميل" onload="window.print(); window.close();" />
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  return (
    <>
      <style>{`
        @media print {
          .client-attachment-image {
            max-width: 200px !important;
            max-height: 150px !important;
            page-break-inside: avoid;
          }
          .print-hide {
            display: none !important;
          }
        }
      `}</style>
      <div className="max-w-4xl mx-auto p-6 bg-white print:shadow-none print:p-4 client-card-print">
      {/* Header */}
      <div className="flex justify-between items-start mb-6">
        <div className="flex items-center space-x-reverse space-x-4">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
            <User className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{client.clientName}</h1>
            <p className="text-gray-600">رقم العميل: {client.clientId}</p>
            <Badge variant={client.clientType === 'أفراد' ? 'default' : 'secondary'}>
              {client.clientType}
            </Badge>
          </div>
        </div>
        <div className="flex gap-2">
          {showEditButton && onEdit && (
            <Button onClick={onEdit} variant="default" className="gap-2">
              <Edit className="h-4 w-4" />
              تعديل البيانات
            </Button>
          )}
          {showPrintButton && (
            <Button onClick={onPrint} variant="outline" className="gap-2">
              <Printer className="h-4 w-4" />
              طباعة الكارت
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              البيانات الأساسية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {client.clientAddress && (
              <div className="flex items-start gap-3">
                <MapPin className="h-4 w-4 text-gray-500 mt-1" />
                <div>
                  <p className="text-sm text-gray-500">العنوان</p>
                  <p className="text-gray-900">{client.clientAddress}</p>
                </div>
              </div>
            )}
            
            <div className="flex items-center gap-3">
              <Phone className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">رقم الهاتف/واتساب</p>
                <p className="text-gray-900">{client.clientPhoneWhatsapp}</p>
              </div>
            </div>

            {client.clientPhone2 && (
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">رقم هاتف إضافي</p>
                  <p className="text-gray-900">{client.clientPhone2}</p>
                </div>
              </div>
            )}

            {client.clientPhone3 && (
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">رقم هاتف ثالث</p>
                  <p className="text-gray-900">{client.clientPhone3}</p>
                </div>
              </div>
            )}

            {client.clientEmail && (
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">البريد الإلكتروني</p>
                  <p className="text-gray-900">{client.clientEmail}</p>
                </div>
              </div>
            )}

            {client.clientNotes && (
              <div className="flex items-start gap-3">
                <FileText className="h-4 w-4 text-gray-500 mt-1" />
                <div>
                  <p className="text-sm text-gray-500">ملاحظات</p>
                  <p className="text-gray-900">{client.clientNotes}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Financial & Legal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              المعلومات المالية والقانونية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {client.clientFinancial_Category && (
              <div className="flex items-center gap-3">
                <CreditCard className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">التصنيف المالي</p>
                  <p className="text-gray-900">{client.clientFinancial_Category}</p>
                </div>
              </div>
            )}

            {client.clientFinancialGuarantee && (
              <div className="flex items-center gap-3">
                <User className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">الضامن المالي</p>
                  <p className="text-gray-900">{client.clientFinancialGuarantee}</p>
                </div>
              </div>
            )}

            {client.clientLegal_Rep && (
              <div className="flex items-center gap-3">
                <User className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">الممثل القانوني</p>
                  <p className="text-gray-900">{client.clientLegal_Rep}</p>
                </div>
              </div>
            )}

            {client.clientPartner && (
              <div className="flex items-center gap-3">
                <User className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">الشريك المرتبط</p>
                  <p className="text-gray-900">{client.clientPartner}</p>
                </div>
              </div>
            )}

            {client.clientReg_Number && (
              <div className="flex items-center gap-3">
                <FileText className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">رقم التسجيل</p>
                  <p className="text-gray-900">{client.clientReg_Number}</p>
                </div>
              </div>
            )}

            {client.clientTaxReg_Number && (
              <div className="flex items-center gap-3">
                <FileText className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">رقم التسجيل الضريبي</p>
                  <p className="text-gray-900">{client.clientTaxReg_Number}</p>
                </div>
              </div>
            )}

            {client.clientLegal_Status && (
              <div className="flex items-center gap-3">
                <Building2 className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">الحالة القانونية</p>
                  <p className="text-gray-900">{client.clientLegal_Status}</p>
                </div>
              </div>
            )}

            {client.clientRemarks && (
              <div className="flex items-start gap-3">
                <FileText className="h-4 w-4 text-gray-500 mt-1" />
                <div>
                  <p className="text-sm text-gray-500">ملاحظات إضافية</p>
                  <p className="text-gray-900">{client.clientRemarks}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Attachments */}
        {client.clientID_Image && (
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Image className="h-5 w-5" />
                المرفقات والوثائق
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4 p-4 border rounded-lg bg-gray-50">
                <div className="flex-shrink-0">
                  <img
                    src={client.clientID_Image}
                    alt="مرفق العميل"
                    className="w-24 h-24 object-cover rounded-lg border cursor-pointer hover:opacity-80 transition-opacity client-attachment-image print:cursor-default"
                    onClick={() => handleImageView(client.clientID_Image!)}
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-2">صورة البطاقة/الوثيقة</h4>
                  <p className="text-sm text-gray-600 mb-3">اضغط على الصورة للعرض بحجم كبير</p>
                  <div className="flex gap-2 print-hide">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleImageView(client.clientID_Image!)}
                      className="gap-2"
                    >
                      <ZoomIn className="h-4 w-4" />
                      عرض كبير
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePrintImage(client.clientID_Image!)}
                      className="gap-2"
                    >
                      <Printer className="h-4 w-4" />
                      طباعة المرفق
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const link = document.createElement('a');
                        link.href = client.clientID_Image!;
                        link.download = `${client.clientName}_مرفق.jpg`;
                        link.click();
                      }}
                      className="gap-2"
                    >
                      <Download className="h-4 w-4" />
                      تحميل
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Contracts Summary */}
      {contracts.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              ملخص العقود ({contracts.length} عقد)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <DollarSign className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">إجمالي قيمة العقود</p>
                <p className="text-lg font-bold text-blue-600">{formatCurrency(getTotalContractValue())}</p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">العقود النشطة</p>
                <p className="text-lg font-bold text-green-600">{getActiveContractsCount()}</p>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <AlertTriangle className="h-6 w-6 text-orange-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">المستحقات</p>
                <p className="text-lg font-bold text-orange-600">{formatCurrency(getTotalOutstanding())}</p>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <Calendar className="h-6 w-6 text-gray-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">إجمالي العقود</p>
                <p className="text-lg font-bold text-gray-600">{contracts.length}</p>
              </div>
            </div>

            <Separator className="my-4" />

            <div className="space-y-3">
              {contracts.map((contract) => (
                <div key={contract.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium">{contract.contractNumber}</h4>
                      <Badge variant={contract.contractStatus === 'نشط' ? 'default' : 'secondary'}>
                        {contract.contractStatus}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">النوع:</span> {contract.contractType}
                      </div>
                      <div>
                        <span className="font-medium">تاريخ البداية:</span> {formatDate(contract.startDate)}
                      </div>
                      <div>
                        <span className="font-medium">القيمة:</span> {formatCurrency(contract.totalContractValue)}
                      </div>
                      <div>
                        <span className="font-medium">القسط الشهري:</span> {formatCurrency(contract.monthlyAmount)}
                      </div>
                    </div>
                  </div>
                  {onViewContract && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onViewContract(contract.id)}
                      className="gap-2"
                    >
                      <Eye className="h-4 w-4" />
                      عرض العقد
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {contracts.length === 0 && (
        <Card className="mt-6">
          <CardContent className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">لا توجد عقود مرتبطة بهذا العميل</p>
          </CardContent>
        </Card>
      )}

      {/* Image Modal */}
      {showImageModal && selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 print:hidden"
          onClick={() => setShowImageModal(false)}
        >
          <div className="relative max-w-4xl max-h-[90vh] p-4">
            <button
              type="button"
              onClick={() => setShowImageModal(false)}
              className="absolute -top-2 -right-2 bg-white rounded-full p-2 shadow-lg hover:bg-gray-100 z-10"
            >
              ✕
            </button>
            <img
              src={selectedImage}
              alt="مرفق العميل"
              className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            />
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => handlePrintImage(selectedImage)}
                className="gap-2 bg-white text-black hover:bg-gray-100"
              >
                <Printer className="h-4 w-4" />
                طباعة
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = selectedImage;
                  link.download = `${client.clientName}_مرفق.jpg`;
                  link.click();
                }}
                className="gap-2 bg-white text-black hover:bg-gray-100"
              >
                <Download className="h-4 w-4" />
                تحميل
              </Button>
            </div>
          </div>
        </div>
      )}
      </div>
    </>
  );
};

export default ClientCard;
