// ===== VALIDATION MIDDLEWARE =====
// Author: Augment Code
// Description: Middleware for request validation using Zod schemas

const { z } = require('zod');

/**
 * Generic validation middleware factory
 * @param {Object} schemas - Object containing validation schemas for different parts of request
 * @param {z.ZodSchema} schemas.body - Schema for request body
 * @param {z.ZodSchema} schemas.params - Schema for request params
 * @param {z.ZodSchema} schemas.query - Schema for request query
 */
const validate = (schemas) => {
  return (req, res, next) => {
    const errors = [];

    try {
      // Validate request body
      if (schemas.body) {
        const bodyResult = schemas.body.safeParse(req.body);
        if (!bodyResult.success) {
          errors.push({
            field: 'body',
            errors: bodyResult.error.errors.map(err => ({
              path: err.path.join('.'),
              message: err.message,
              code: err.code
            }))
          });
        } else {
          req.body = bodyResult.data;
        }
      }

      // Validate request params
      if (schemas.params) {
        const paramsResult = schemas.params.safeParse(req.params);
        if (!paramsResult.success) {
          errors.push({
            field: 'params',
            errors: paramsResult.error.errors.map(err => ({
              path: err.path.join('.'),
              message: err.message,
              code: err.code
            }))
          });
        } else {
          req.params = paramsResult.data;
        }
      }

      // Validate request query
      if (schemas.query) {
        const queryResult = schemas.query.safeParse(req.query);
        if (!queryResult.success) {
          errors.push({
            field: 'query',
            errors: queryResult.error.errors.map(err => ({
              path: err.path.join('.'),
              message: err.message,
              code: err.code
            }))
          });
        } else {
          req.query = queryResult.data;
        }
      }

      // If there are validation errors, return them
      if (errors.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'بيانات غير صحيحة',
          code: 'VALIDATION_ERROR',
          details: errors
        });
      }

      next();
    } catch (error) {
      console.error('Validation middleware error:', error);
      return res.status(500).json({
        success: false,
        error: 'خطأ في التحقق من البيانات',
        code: 'VALIDATION_MIDDLEWARE_ERROR'
      });
    }
  };
};

/**
 * Common validation schemas
 */
const commonSchemas = {
  // ID parameter validation
  idParam: z.object({
    id: z.string().regex(/^\d+$/, 'ID must be a number').transform(Number)
  }),

  // Pagination query validation
  pagination: z.object({
    page: z.string().regex(/^\d+$/).transform(Number).optional().default(1),
    limit: z.string().regex(/^\d+$/).transform(Number).optional().default(10),
    sort: z.string().optional(),
    order: z.enum(['asc', 'desc']).optional().default('asc')
  }),

  // Search query validation
  search: z.object({
    q: z.string().min(1, 'Search query is required').optional(),
    category: z.string().optional(),
    status: z.string().optional()
  })
};

/**
 * Specific validation middleware for common use cases
 */
const validateId = validate({ params: commonSchemas.idParam });
const validatePagination = validate({ query: commonSchemas.pagination });
const validateSearch = validate({ query: commonSchemas.search });

module.exports = {
  validate,
  commonSchemas,
  validateId,
  validatePagination,
  validateSearch
};
