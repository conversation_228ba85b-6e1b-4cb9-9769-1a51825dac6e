// ===== REFERENCE DATA ROUTES =====
// Author: Augment Code
// Description: Routes for managing reference data and lists

const express = require('express');
const router = express.Router();

// Import database connection pool
const { query, get, run, transaction } = require('../db.js');

// Get all reference lists
router.get('/reference-lists', async (req, res) => {
  console.log('GET /api/reference-lists request');

  try {
    const rows = await query(`
      SELECT DISTINCT module, listName, COUNT(*) as itemCount
      FROM ReferenceData
      WHERE isActive = 1 AND isDeleted = 0
      GROUP BY module, listName
      ORDER BY module, listName
    `);

    console.log('Reference lists retrieved:', rows?.length || 0, 'lists');
    res.json(rows || []);
  } catch (err) {
    console.error('Get reference lists error:', err);
    res.status(500).json({ error: err.message });
  }
});

// Get reference data by module and list
router.get('/reference-data/:module/:listName', async (req, res) => {
  console.log('GET /api/reference-data/:module/:listName request');
  const { module, listName } = req.params;

  try {
    const rows = await query(`
      SELECT * FROM ReferenceData
      WHERE module = ? AND listName = ? AND isActive = 1 AND isDeleted = 0
      ORDER BY sortOrder, itemLabel
    `, [module, listName]);

    console.log('Reference data retrieved:', rows?.length || 0, 'items');
    res.json(rows || []);
  } catch (err) {
    console.error('Get reference data error:', err);
    res.status(500).json({ error: err.message });
  }
});

  // Create new reference list
  router.post('/reference-lists', (req, res) => {
    console.log('POST /api/reference-lists request');
    console.log('Received data:', JSON.stringify(req.body, null, 2));

    const { module, listName, items } = req.body;

    if (!module || !listName || !items || !Array.isArray(items) || items.length === 0) {
      console.log('Validation failed: missing required fields');
      return res.status(400).json({ error: 'Module, listName, and items are required' });
    }

    // Insert items one by one
    const stmt = db.prepare(`
      INSERT INTO ReferenceData (module, listName, itemValue, itemLabel, sortOrder, isActive, isDeleted, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `);

    let insertedCount = 0;
    let errors = [];

    items.forEach((item, index) => {
      const sortOrder = item.sortOrder || index + 1;
      stmt.run([module, listName, item.itemValue, item.itemLabel, sortOrder], function(err) {
        if (err) {
          console.error(`Error inserting item ${index + 1}:`, err);
          errors.push(err);
        } else {
          insertedCount++;
        }

        // Check if all items have been processed
        if (insertedCount + errors.length === items.length) {
          stmt.finalize();
          console.log(`Reference list created: ${insertedCount} items inserted`);
          if (errors.length > 0) {
            console.error(`${errors.length} errors occurred`);
            res.status(500).json({ error: 'Some items failed to insert', details: errors });
          } else {
            res.json({ success: true, message: 'Reference list created successfully', insertedCount });
          }
        }
      });
    });
  });

  // Update reference list item
  router.put('/reference-data/:id', (req, res) => {
    console.log('PUT /api/reference-data/:id request');
    const { id } = req.params;
    const { itemValue, itemLabel, sortOrder, isActive } = req.body;

    if (!itemValue || !itemLabel) {
      return res.status(400).json({ error: 'Item value and label are required' });
    }

    db.run(`
      UPDATE ReferenceData SET
        itemValue = ?,
        itemLabel = ?,
        sortOrder = ?,
        isActive = ?,
        updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [itemValue, itemLabel, sortOrder || 0, isActive !== undefined ? isActive : 1, id], function(err) {
      if (err) {
        console.error('Update reference data error:', err);
        res.status(500).json({ error: err.message });
      } else if (this.changes === 0) {
        res.status(404).json({ error: 'Reference data item not found' });
      } else {
        console.log('Reference data updated successfully');
        res.json({ success: true });
      }
    });
  });

  // Delete reference list (soft delete)
  router.delete('/reference-lists/:module/:listName', (req, res) => {
    console.log('DELETE /api/reference-lists/:module/:listName request');
    const { module, listName } = req.params;

    db.run(`
      UPDATE ReferenceData SET isDeleted = 1, updatedAt = CURRENT_TIMESTAMP
      WHERE module = ? AND listName = ?
    `, [module, listName], function(err) {
      if (err) {
        console.error('Delete reference list error:', err);
        res.status(500).json({ error: err.message });
      } else {
        console.log('Reference list deleted successfully');
        res.json({ success: true });
      }
    });
  });

  // Delete reference data item (soft delete)
  router.delete('/reference-data/:id', (req, res) => {
    console.log('DELETE /api/reference-data/:id request');
    const { id } = req.params;

    db.run(`
      UPDATE ReferenceData SET isDeleted = 1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?
    `, [id], function(err) {
      if (err) {
        console.error('Delete reference data error:', err);
        res.status(500).json({ error: err.message });
      } else {
        console.log('Reference data deleted successfully');
        res.json({ success: true });
      }
    });
  });

  // Get reference lists configuration
  router.get('/reference-lists-config', (req, res) => {
    console.log('GET /api/reference-lists-config request');

    const query = `
      SELECT * FROM ReferenceListsConfig
      WHERE isActive = 1
      ORDER BY displayName ASC
    `;

    db.all(query, [], (err, rows) => {
      if (err) {
        console.error('Error getting reference lists config:', err);
        return res.status(500).json({ error: err.message });
      }

      // Parse JSON fields
      const configs = rows.map(row => ({
        ...row,
        linkedPages: JSON.parse(row.linkedPages || '[]'),
        fieldMapping: JSON.parse(row.fieldMapping || '{}')
      }));

      console.log(`Reference lists config retrieved successfully: ${configs.length} items`);
      res.json(configs);
    });
  });

  // Create new reference list configuration
  router.post('/reference-lists-config', (req, res) => {
    console.log('POST /api/reference-lists-config request');
    console.log('Received data:', JSON.stringify(req.body, null, 2));

    const { listName, displayName, description, linkedPages, isRequired } = req.body;

    if (!listName || !displayName) {
      return res.status(400).json({ error: 'List name and display name are required' });
    }

    const query = `
      INSERT INTO ReferenceListsConfig (listName, displayName, description, linkedPages, fieldMapping, isRequired, isActive, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `;

    db.run(query, [
      listName,
      displayName,
      description || '',
      JSON.stringify(linkedPages || []),
      JSON.stringify({}), // Empty field mapping for now
      isRequired || false
    ], function(err) {
      if (err) {
        console.error('Error creating reference list config:', err);
        if (err.message.includes('UNIQUE constraint failed')) {
          return res.status(400).json({ error: 'Reference list configuration already exists' });
        }
        return res.status(500).json({ error: err.message });
      }

      console.log(`Reference list config created successfully with ID: ${this.lastID}`);
      res.json({
        success: true,
        message: 'Reference list configuration created successfully',
        id: this.lastID
      });
    });
  });

  // Get reference lists for a specific page
  router.get('/reference-lists-for-page/:pageName', (req, res) => {
    const { pageName } = req.params;
    console.log(`GET /api/reference-lists-for-page/${pageName} request`);

    const query = `
      SELECT * FROM ReferenceListsConfig
      WHERE isActive = 1 AND linkedPages LIKE ?
      ORDER BY displayName ASC
    `;

    db.all(query, [`%"${pageName}"%`], (err, rows) => {
      if (err) {
        console.error('Error getting reference lists for page:', err);
        return res.status(500).json({ error: err.message });
      }

      // Parse JSON fields
      const configs = rows.map(row => ({
        ...row,
        linkedPages: JSON.parse(row.linkedPages || '[]'),
        fieldMapping: JSON.parse(row.fieldMapping || '{}')
      }));

      console.log(`Reference lists for page ${pageName} retrieved successfully: ${configs.length} items`);
      res.json(configs);
    });
  });

  // Check if required reference lists are configured for a page
  router.get('/reference-lists-status/:pageName', (req, res) => {
    const { pageName } = req.params;
    console.log(`GET /api/reference-lists-status/${pageName} request`);

    const query = `
      SELECT rlc.*,
             (SELECT COUNT(*) FROM ReferenceData rd WHERE rd.listName = rlc.listName AND rd.isDeleted = 0 AND rd.isActive = 1) as itemCount
      FROM ReferenceListsConfig rlc
      WHERE rlc.isActive = 1 AND rlc.linkedPages LIKE ?
      ORDER BY rlc.isRequired DESC, rlc.displayName ASC
    `;

    db.all(query, [`%"${pageName}"%`], (err, rows) => {
      if (err) {
        console.error('Error getting reference lists status:', err);
        return res.status(500).json({ error: err.message });
      }

      // Parse JSON fields and add status
      const configs = rows.map(row => ({
        ...row,
        linkedPages: JSON.parse(row.linkedPages || '[]'),
        fieldMapping: JSON.parse(row.fieldMapping || '{}'),
        status: row.isRequired && row.itemCount === 0 ? 'missing' : 'ok',
        hasItems: row.itemCount > 0
      }));

      const missingRequired = configs.filter(c => c.status === 'missing').length;

      console.log(`Reference lists status for page ${pageName}: ${configs.length} lists, ${missingRequired} missing required`);
      res.json({
        lists: configs,
        summary: {
          total: configs.length,
          missingRequired: missingRequired,
          allRequiredConfigured: missingRequired === 0
        }
      });
    });
  });

  // Add new reference data item
  router.post('/reference-data/:module/:listName', (req, res) => {
    const { module, listName } = req.params;
    const { itemValue, itemLabel, sortOrder } = req.body;
    console.log(`POST /api/reference-data/${module}/${listName} request`, req.body);

    const query = `
      INSERT INTO ReferenceData (module, listName, itemValue, itemLabel, sortOrder, isActive, isDeleted, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `;

    db.run(query, [module, listName, itemValue, itemLabel, sortOrder || 0], function(err) {
      if (err) {
        console.error('Error adding reference data item:', err);
        return res.status(500).json({ error: err.message });
      }

      console.log(`Reference data item added successfully with ID: ${this.lastID}`);
      res.json({
        success: true,
        message: 'Reference data item added successfully',
        id: this.lastID
      });
    });
  });

  // Get contract type properties
  router.get('/contract-types/:contractType/properties', (req, res) => {
    const { contractType } = req.params;
    console.log(`GET /api/contract-types/${contractType}/properties request`);

    const query = `
      SELECT * FROM ContractTypeProperties
      WHERE contractType = ? AND isActive = 1
      ORDER BY propertyName ASC
    `;

    db.all(query, [contractType], (err, rows) => {
      if (err) {
        console.error('Error getting contract type properties:', err);
        return res.status(500).json({ error: err.message });
      }

      console.log(`Contract type properties retrieved successfully: ${rows.length} items`);
      res.json(rows);
    });
  });

  // Get all contract types with their properties
  router.get('/contract-types-with-properties', (req, res) => {
    console.log('GET /api/contract-types-with-properties request');

    const query = `
      SELECT
        rd.itemValue as contractType,
        rd.itemLabel as contractTypeLabel,
        GROUP_CONCAT(
          CASE WHEN ctp.id IS NOT NULL THEN
            json_object(
              'id', ctp.id,
              'propertyName', ctp.propertyName,
              'propertyType', ctp.propertyType,
              'defaultValue', ctp.defaultValue,
              'isRequired', ctp.isRequired
            )
          END
        ) as properties
      FROM ReferenceData rd
      LEFT JOIN ContractTypeProperties ctp ON rd.itemValue = ctp.contractType AND ctp.isActive = 1
      WHERE rd.module = 'contracts' AND rd.listName = 'contractTypes' AND rd.isActive = 1 AND rd.isDeleted = 0
      GROUP BY rd.itemValue, rd.itemLabel
      ORDER BY rd.itemLabel ASC
    `;

    db.all(query, [], (err, rows) => {
      if (err) {
        console.error('Error getting contract types with properties:', err);
        return res.status(500).json({ error: err.message });
      }

      // Parse properties JSON
      const contractTypes = rows.map(row => ({
        ...row,
        properties: row.properties ? JSON.parse(`[${row.properties}]`) : []
      }));

      console.log(`Contract types with properties retrieved successfully: ${contractTypes.length} items`);
      res.json(contractTypes);
    });
  });

  return router;
};
