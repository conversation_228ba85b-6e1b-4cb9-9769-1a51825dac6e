// ===== UNIFIED DATABASE LAYER =====
// نظام ربط موحد وثابت لقاعدة البيانات
// لا يوجد تبديل بين جداول - نظام واحد مستقر

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  success?: boolean;
}

export interface QueryFilters {
  [key: string]: string | number | boolean | undefined;
}

class UnifiedDatabase {
  private static baseUrl = '/api';
  
  // ===== CORE DATABASE METHODS =====
  
  /**
   * طريقة موحدة لجلب البيانات
   */
  private static async fetchData<T>(
    endpoint: string, 
    options?: RequestInit
  ): Promise<T> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`[Database] Error fetching ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * طريقة موحدة لإرسال البيانات
   */
  private static async postData<T>(
    endpoint: string,
    data: any,
    method: 'POST' | 'PUT' | 'DELETE' = 'POST'
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: method !== 'DELETE' ? JSON.stringify(data) : undefined,
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || `HTTP ${response.status}: ${response.statusText}`
        };
      }

      return {
        success: true,
        data: result,
        message: result.message || 'Operation completed successfully'
      };
    } catch (error) {
      console.error(`[Database] Error posting to ${endpoint}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * بناء query string من الفلاتر
   */
  private static buildQueryString(filters?: QueryFilters): string {
    if (!filters) return '';
    
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value));
      }
    });
    
    return params.toString() ? `?${params.toString()}` : '';
  }

  // ===== CONTRACTS OPERATIONS =====

  /**
   * جلب جميع العقود
   */
  static async getContracts(filters?: QueryFilters): Promise<any[]> {
    const queryString = this.buildQueryString(filters);
    return this.fetchData<any[]>(`/contracts${queryString}`);
  }

  /**
   * جلب عقد محدد
   */
  static async getContract(id: string): Promise<any> {
    if (!id) throw new Error('Contract ID is required');
    return this.fetchData<any>(`/contracts/${id}`);
  }

  /**
   * إنشاء عقد جديد
   */
  static async createContract(data: any): Promise<ApiResponse> {
    return this.postData('/contracts', data, 'POST');
  }



  /**
   * حذف عقد
   */
  static async deleteContract(id: string): Promise<ApiResponse> {
    if (!id) throw new Error('Contract ID is required');
    return this.postData(`/contracts/${id}`, {}, 'DELETE');
  }

  // ===== CONTRACT RELATED DATA =====

  /**
   * جلب استحقاقات العقد
   */
  static async getContractReceivables(contractId: string): Promise<any[]> {
    if (!contractId) throw new Error('Contract ID is required');
    return this.fetchData<any[]>(`/contracts/${contractId}/receivables`);
  }

  /**
   * جلب أقساط العقد
   */
  static async getContractInstallments(contractId: string): Promise<any[]> {
    if (!contractId) throw new Error('Contract ID is required');
    return this.fetchData<any[]>(`/contracts/${contractId}/installments`);
  }

  /**
   * جلب منتجات العقد
   */
  static async getContractProducts(contractId: string): Promise<any[]> {
    if (!contractId) throw new Error('Contract ID is required');
    return this.fetchData<any[]>(`/contracts/${contractId}/products`);
  }

  /**
   * جلب شركاء العقد
   */
  static async getContractPartners(contractId: string): Promise<any[]> {
    if (!contractId) throw new Error('Contract ID is required');
    return this.fetchData<any[]>(`/contracts/${contractId}/partners`);
  }

  /**
   * توليد استحقاقات العقد
   */
  static async generateContractReceivables(contractId: string): Promise<ApiResponse> {
    if (!contractId) throw new Error('Contract ID is required');
    return this.postData(`/contracts/${contractId}/generate-receivables`, {});
  }

  // ===== CLIENTS OPERATIONS =====

  /**
   * جلب جميع العملاء
   */
  static async getClients(filters?: QueryFilters): Promise<any[]> {
    const queryString = this.buildQueryString(filters);
    return this.fetchData<any[]>(`/clients${queryString}`);
  }

  /**
   * جلب عميل محدد
   */
  static async getClient(id: string): Promise<any> {
    if (!id) throw new Error('Client ID is required');
    return this.fetchData<any>(`/clients/${id}`);
  }

  /**
   * إنشاء عميل جديد
   */
  static async createClient(data: any): Promise<ApiResponse> {
    return this.postData('/clients', data, 'POST');
  }

  /**
   * تحديث عميل موجود
   */
  static async updateClient(id: string, data: any): Promise<ApiResponse> {
    if (!id) throw new Error('Client ID is required');
    return this.postData(`/clients/${id}`, data, 'PUT');
  }

  // ===== RECEIVABLES OPERATIONS =====

  /**
   * جلب جميع الاستحقاقات
   */
  static async getReceivables(filters?: QueryFilters): Promise<any[]> {
    const queryString = this.buildQueryString(filters);
    return this.fetchData<any[]>(`/receivables${queryString}`);
  }

  /**
   * تحديث حالة استحقاق
   */
  static async updateReceivableStatus(id: string, status: string): Promise<ApiResponse> {
    if (!id) throw new Error('Receivable ID is required');
    return this.postData(`/receivables/${id}/status`, { status }, 'PUT');
  }

  // ===== PAYMENTS OPERATIONS =====

  /**
   * جلب جميع المدفوعات
   */
  static async getPayments(filters?: QueryFilters): Promise<any[]> {
    const queryString = this.buildQueryString(filters);
    return this.fetchData<any[]>(`/payments${queryString}`);
  }

  /**
   * إنشاء دفعة جديدة
   */
  static async createPayment(data: any): Promise<ApiResponse> {
    return this.postData('/payments', data, 'POST');
  }

  /**
   * جلب إيصالات الخزينة
   */
  static async getCashReceipts(filters?: QueryFilters): Promise<any[]> {
    const queryString = this.buildQueryString(filters);
    return this.fetchData<any[]>(`/cash-receipts${queryString}`);
  }

  /**
   * إنشاء إيصال خزينة
   */
  static async createCashReceipt(data: any): Promise<ApiResponse> {
    return this.postData('/cash-receipts', data, 'POST');
  }

  // ===== CHEQUES OPERATIONS =====

  /**
   * جلب جميع الشيكات
   */
  static async getCheques(filters?: QueryFilters): Promise<any[]> {
    const queryString = this.buildQueryString(filters);
    return this.fetchData<any[]>(`/cheques${queryString}`);
  }

  /**
   * إنشاء شيك جديد
   */
  static async createCheque(data: any): Promise<ApiResponse> {
    return this.postData('/cheques', data, 'POST');
  }

  /**
   * تحديث حالة شيك
   */
  static async updateChequeStatus(id: string, status: string): Promise<ApiResponse> {
    if (!id) throw new Error('Cheque ID is required');
    return this.postData(`/cheques/${id}/status`, { status }, 'PUT');
  }

  // ===== REPORTS OPERATIONS =====

  /**
   * جلب تقرير مالي
   */
  static async getFinancialReport(filters?: QueryFilters): Promise<any> {
    const queryString = this.buildQueryString(filters);
    return this.fetchData<any>(`/reports/financial${queryString}`);
  }

  /**
   * جلب إحصائيات لوحة التحكم
   */
  static async getDashboardStats(filters?: QueryFilters): Promise<any> {
    const queryString = this.buildQueryString(filters);
    return this.fetchData<any>(`/dashboard/stats${queryString}`);
  }

  // ===== SETTINGS OPERATIONS =====

  /**
   * جلب الإعدادات
   */
  static async getSettings(): Promise<any> {
    return this.fetchData<any>('/settings');
  }

  /**
   * حفظ الإعدادات
   */
  static async saveSettings(data: any): Promise<ApiResponse> {
    return this.postData('/settings', data, 'POST');
  }

  // ===== REFERENCE DATA OPERATIONS =====

  /**
   * جلب البيانات المرجعية
   */
  static async getReferenceData(listName?: string): Promise<any[]> {
    const queryString = listName ? `?listName=${listName}` : '';
    return this.fetchData<any[]>(`/reference-data${queryString}`);
  }

  /**
   * حفظ البيانات المرجعية
   */
  static async saveReferenceData(data: any): Promise<ApiResponse> {
    return this.postData('/reference-data', data, 'POST');
  }

  // ===== UTILITY METHODS =====

  /**
   * فحص حالة الاتصال بالخادم
   */
  static async checkConnection(): Promise<boolean> {
    try {
      await this.fetchData('/health');
      return true;
    } catch {
      return false;
    }
  }

  /**
   * جلب معلومات النظام
   */
  static async getSystemInfo(): Promise<any> {
    return this.fetchData<any>('/system/info');
  }
}

// Export the unified database instance
export const db = UnifiedDatabase;
export default UnifiedDatabase;
