import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { formatCurrency, formatDate, formatContractStatus, formatDuration, formatPaymentMethod } from "@/lib/formatters";
import { isContractActive, calculateContractEndDate } from "@/lib/calculations";
import {
  Building2,
  Calendar,
  CreditCard,
  User,
  Phone,
  MapPin,
  FileText,
  Clock,
  DollarSign,
  TrendingUp,
  Shield,
  AlertTriangle,
  CheckCircle,
  Target
} from "lucide-react";
import type { Contract } from "@shared/schema";

interface ContractSummaryProps {
  contract: Contract;
}

const ContractSummary: React.FC<ContractSummaryProps> = ({ contract }) => {
  const lang = (localStorage.getItem("lang") as "ar" | "en") || "ar";

  // Calculate end date using utility function
  const endDate = calculateContractEndDate(contract.startDate, contract.contractDurationYears);
  const isActive = isContractActive(contract);

  // Calculate remaining time
  const now = new Date();
  const timeRemaining = endDate.getTime() - now.getTime();
  const daysRemaining = Math.ceil(timeRemaining / (1000 * 60 * 60 * 24));

  const statusInfo = formatContractStatus(contract.contractStatus);

  return (
    <div className="space-y-6">
      {/* Contract Basic Info */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {lang === "ar" ? "معلومات العقد الأساسية" : "Basic Contract Information"}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant={statusInfo.variant as any}>
                {statusInfo.text}
              </Badge>
              {isActive && (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  {lang === "ar" ? "فعال" : "Active"}
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {lang === "ar" ? "رقم العقد" : "Contract Number"}
              </label>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {contract.contractNumber}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {lang === "ar" ? "نوع العقد" : "Contract Type"}
              </label>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {contract.contractType}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {lang === "ar" ? "مدة العقد" : "Contract Duration"}
              </label>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {formatDuration(contract.contractDurationYears)}
              </p>
            </div>

            {contract.contractInternalId && (
              <div>
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {lang === "ar" ? "الرقم الداخلي" : "Internal ID"}
                </label>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {contract.contractInternalId}
                </p>
              </div>
            )}

            {contract.contractSubject && (
              <div>
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {lang === "ar" ? "موضوع العقد" : "Contract Subject"}
                </label>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {contract.contractSubject}
                </p>
              </div>
            )}
            
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {lang === "ar" ? "تاريخ البداية" : "Start Date"}
              </label>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {formatDate(contract.startDate)}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {lang === "ar" ? "تاريخ الانتهاء" : "End Date"}
              </label>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {formatDate(endDate)}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {lang === "ar" ? "الأيام المتبقية" : "Days Remaining"}
              </label>
              <p className={`text-lg font-semibold ${
                daysRemaining > 30 ? 'text-green-600 dark:text-green-400' :
                daysRemaining > 0 ? 'text-yellow-600 dark:text-yellow-400' :
                'text-red-600 dark:text-red-400'
              }`}>
                {daysRemaining > 0 ? daysRemaining : 0} {lang === "ar" ? "يوم" : "days"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Financial Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            {lang === "ar" ? "المعلومات المالية" : "Financial Information"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {lang === "ar" ? "إجمالي قيمة العقد" : "Total Contract Value"}
              </label>
              <p className="text-xl font-bold text-primary">
                {formatCurrency(contract.totalContractValue)}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {lang === "ar" ? "القسط الشهري" : "Monthly Amount"}
              </label>
              <p className="text-xl font-bold text-blue-600 dark:text-blue-400">
                {formatCurrency(contract.monthlyAmount)}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {lang === "ar" ? "يوم السداد" : "Payment Day"}
              </label>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {contract.paymentDay} {lang === "ar" ? "من كل شهر" : "of each month"}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {lang === "ar" ? "غرامة التأخير" : "Late Fee"}
              </label>
              <p className="text-lg font-semibold text-red-600 dark:text-red-400">
                {contract.lateFeePercentage || contract.lateFeeValue || 0}
                {contract.lateFeeType === 'نسبة مئوية' ? '%' : ' ' + (lang === "ar" ? "ج.م" : "EGP")}
              </p>
            </div>

            {contract.paymentMethod && (
              <div>
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {lang === "ar" ? "طريقة الدفع" : "Payment Method"}
                </label>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {formatPaymentMethod(contract.paymentMethod)}
                </p>
              </div>
            )}

            {contract.annualIncreaseType && contract.annualIncreaseType !== 'لا يوجد' && (
              <div>
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {lang === "ar" ? "الزيادة السنوية" : "Annual Increase"}
                </label>
                <p className="text-lg font-semibold text-blue-600 dark:text-blue-400 flex items-center gap-1">
                  <TrendingUp className="h-4 w-4" />
                  {contract.annualIncreaseValue}
                  {contract.annualIncreaseType === 'نسبة مئوية' || contract.annualIncreaseType === 'نسبة مركبة' ? '%' : ' ' + (lang === "ar" ? "ج.م" : "EGP")}
                </p>
              </div>
            )}

            {contract.gracePeriodDays && contract.gracePeriodDays > 0 && (
              <div>
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {lang === "ar" ? "فترة السماح" : "Grace Period"}
                </label>
                <p className="text-lg font-semibold text-green-600 dark:text-green-400 flex items-center gap-1">
                  <Shield className="h-4 w-4" />
                  {contract.gracePeriodDays} {lang === "ar" ? "يوم" : "days"}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Client Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {lang === "ar" ? "معلومات العميل" : "Client Information"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {lang === "ar" ? "اسم العميل" : "Client Name"}
              </label>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {contract.clientName}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {lang === "ar" ? "نوع العميل" : "Client Type"}
              </label>
              <Badge variant="outline">
                {contract.clientType}
              </Badge>
            </div>
            
            {contract.clientPhoneWhatsapp && (
              <div>
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {lang === "ar" ? "رقم الهاتف" : "Phone Number"}
                </label>
                <p className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  {contract.clientPhoneWhatsapp}
                </p>
              </div>
            )}
            
            {contract.clientAddress && (
              <div>
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {lang === "ar" ? "العنوان" : "Address"}
                </label>
                <p className="text-lg font-semibold text-gray-900 dark:text-white flex items-start gap-2">
                  <MapPin className="h-4 w-4 mt-1" />
                  {contract.clientAddress}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Additional Information */}
      {contract.notes && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {lang === "ar" ? "ملاحظات إضافية" : "Additional Notes"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
              {contract.notes}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ContractSummary;
