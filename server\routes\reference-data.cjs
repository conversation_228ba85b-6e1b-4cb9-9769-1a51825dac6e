const express = require('express');
const router = express.Router();
const referenceDataController = require('../controllers/referenceDataController.cjs');

// Define reference data routes
router.get('/lists', referenceDataController.getAllReferenceLists);
router.get('/:module/:listName', referenceDataController.getReferenceDataByModuleAndList);
router.post('/lists', referenceDataController.createReferenceList);
router.put('/:id', referenceDataController.updateReferenceListItem);
router.delete('/lists/:module/:listName', referenceDataController.deleteReferenceList);
router.delete('/:id', referenceDataController.deleteReferenceDataItem);
// ... add more as needed

module.exports = router;
