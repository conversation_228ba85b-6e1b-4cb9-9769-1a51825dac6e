import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  Plus,
  Wallet,
  Building2,
  Search,
  Filter,
  BarChart3,
  TrendingUp,
  DollarSign,
  CreditCard,
  Receipt,
  Calendar,
  ArrowUpIcon,
  ArrowDownIcon,
  Eye,
  Edit,
  Trash2
} from "lucide-react";
import { useCurrency } from "@/hooks/use-currency";
import { useDateFormat } from "@/hooks/use-date-format";
import { useLanguage } from "@/hooks/use-language";
import { cn } from "@/lib/utils";
import { useLocation } from "wouter";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from "recharts";

export default function PaymentsUnified() {
  const { language, isRTL } = useLanguage();
  const { formatCurrency } = useCurrency();
  const { formatDate } = useDateFormat();
  const [, setLocation] = useLocation();
  
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [filterMethod, setFilterMethod] = useState("all");

  // Fetch treasury payments (cash and cheque receipts)
  const { data: cashReceipts, isLoading: cashLoading } = useQuery({
    queryKey: ['/api/cash-receipts'],
    queryFn: async () => {
      const response = await fetch('/api/cash-receipts');
      if (!response.ok) throw new Error('Failed to fetch cash receipts');
      return response.json();
    }
  });

  const { data: chequeReceipts, isLoading: chequeLoading } = useQuery({
    queryKey: ['/api/cheque-receipts'],
    queryFn: async () => {
      const response = await fetch('/api/cheque-receipts');
      if (!response.ok) throw new Error('Failed to fetch cheque receipts');
      return response.json();
    }
  });

  // Fetch bank payments
  const { data: bankPayments, isLoading: bankLoading } = useQuery({
    queryKey: ['/api/bank-payments'],
    queryFn: async () => {
      const response = await fetch('/api/bank-payments');
      if (!response.ok) throw new Error('Failed to fetch bank payments');
      return response.json();
    }
  });

  // Combine all payments
  const treasuryPayments = [
    ...(cashReceipts?.map((p: any) => ({ ...p, source: 'خزينة', method: 'نقدي' })) || []),
    ...(chequeReceipts?.map((p: any) => ({ ...p, source: 'خزينة', method: 'شيك' })) || [])
  ];

  const allBankPayments = bankPayments?.map((p: any) => ({ ...p, source: 'بنك', method: 'تحويل بنكي' })) || [];
  const allPayments = [...treasuryPayments, ...allBankPayments];

  // Calculate statistics
  const totalTreasuryAmount = treasuryPayments.reduce((sum, p) => sum + (p.totalAmount || 0), 0);
  const totalBankAmount = allBankPayments.reduce((sum, p) => sum + (p.amount || 0), 0);
  const totalAmount = totalTreasuryAmount + totalBankAmount;

  const cashAmount = cashReceipts?.reduce((sum: number, p: any) => sum + (p.totalAmount || 0), 0) || 0;
  const chequeAmount = chequeReceipts?.reduce((sum: number, p: any) => sum + (p.totalAmount || 0), 0) || 0;

  // Sample chart data
  const monthlyData = [
    { month: 'يناير', treasury: 45000, bank: 32000, total: 77000 },
    { month: 'فبراير', treasury: 52000, bank: 28000, total: 80000 },
    { month: 'مارس', treasury: 48000, bank: 35000, total: 83000 },
    { month: 'أبريل', treasury: 61000, bank: 42000, total: 103000 },
    { month: 'مايو', treasury: 55000, bank: 38000, total: 93000 },
    { month: 'يونيو', treasury: 67000, bank: 45000, total: 112000 },
  ];

  const paymentMethodData = [
    { name: 'نقدي', value: cashAmount, color: '#10B981' },
    { name: 'شيك', value: chequeAmount, color: '#3B82F6' },
    { name: 'تحويل بنكي', value: totalBankAmount, color: '#F59E0B' },
  ];

  const isLoading = cashLoading || chequeLoading || bankLoading;

  return (
    <div className={cn("space-y-6", isRTL ? "text-right" : "text-left")}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-indigo-50 border-0 shadow-lg">
        <CardHeader>
          <div className={cn("flex items-center justify-between", isRTL ? "flex-row-reverse" : "flex-row")}>
            <div className={cn(isRTL ? "text-right" : "text-left")}>
              <CardTitle className="text-2xl text-slate-800 flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center">
                  <Wallet className="h-6 w-6 text-white" />
                </div>
                إدارة المدفوعات الموحدة
              </CardTitle>
              <p className="text-sm text-slate-600 mt-2">
                نظرة شاملة على جميع المدفوعات من الخزينة والبنك
              </p>
            </div>
            <div className="flex gap-3">
              <Button 
                variant="outline" 
                onClick={() => setLocation('/treasury-payments')}
                className="gap-2"
              >
                <Wallet className="h-4 w-4" />
                مدفوعات الخزينة
              </Button>
              <Button 
                variant="outline"
                onClick={() => setLocation('/bank-payments')}
                className="gap-2"
              >
                <Building2 className="h-4 w-4" />
                مدفوعات البنك
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-green-700">إجمالي المدفوعات</p>
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  <p className="text-2xl font-bold text-green-800">{formatCurrency(totalAmount)}</p>
                )}
                <div className="flex items-center gap-1">
                  <ArrowUpIcon className="h-3 w-3 text-green-600" />
                  <span className="text-xs text-green-600">+12% من الشهر الماضي</span>
                </div>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-blue-700">مدفوعات الخزينة</p>
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  <p className="text-2xl font-bold text-blue-800">{formatCurrency(totalTreasuryAmount)}</p>
                )}
                <div className="flex items-center gap-1">
                  <ArrowUpIcon className="h-3 w-3 text-blue-600" />
                  <span className="text-xs text-blue-600">+8% من الشهر الماضي</span>
                </div>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Wallet className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-orange-700">مدفوعات البنك</p>
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  <p className="text-2xl font-bold text-orange-800">{formatCurrency(totalBankAmount)}</p>
                )}
                <div className="flex items-center gap-1">
                  <ArrowUpIcon className="h-3 w-3 text-orange-600" />
                  <span className="text-xs text-orange-600">+15% من الشهر الماضي</span>
                </div>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                <Building2 className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-purple-700">عدد المعاملات</p>
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  <p className="text-2xl font-bold text-purple-800">{allPayments.length}</p>
                )}
                <div className="flex items-center gap-1">
                  <ArrowUpIcon className="h-3 w-3 text-purple-600" />
                  <span className="text-xs text-purple-600">+5% من الشهر الماضي</span>
                </div>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Receipt className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Monthly Payments Trend */}
        <Card className="lg:col-span-2 bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-slate-800">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              اتجاهات المدفوعات الشهرية
            </CardTitle>
            <p className="text-sm text-slate-600">مقارنة مدفوعات الخزينة والبنك</p>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" />
                  <XAxis
                    dataKey="month"
                    stroke="#64748B"
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    stroke="#64748B"
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={(value) => `${value / 1000}ك`}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}
                    formatter={(value: any, name: string) => [
                      formatCurrency(value),
                      name === 'treasury' ? 'الخزينة' : name === 'bank' ? 'البنك' : 'الإجمالي'
                    ]}
                  />
                  <Bar dataKey="treasury" fill="#3B82F6" radius={[4, 4, 0, 0]} name="treasury" />
                  <Bar dataKey="bank" fill="#F59E0B" radius={[4, 4, 0, 0]} name="bank" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Payment Methods Distribution */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-slate-800">
              <BarChart3 className="h-5 w-5 text-green-600" />
              توزيع طرق الدفع
            </CardTitle>
            <p className="text-sm text-slate-600">نسب طرق الدفع المختلفة</p>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={paymentMethodData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {paymentMethodData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}
                    formatter={(value: any) => [formatCurrency(value), 'المبلغ']}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-1 gap-2 mt-4">
              {paymentMethodData.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-sm text-slate-600">{item.name}</span>
                  </div>
                  <span className="text-sm font-medium text-slate-800">{formatCurrency(item.value)}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payments Table with Tabs */}
      <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-slate-800">
              <Receipt className="h-5 w-5 text-purple-600" />
              سجل المدفوعات
            </CardTitle>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="البحث في المدفوعات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              <Select value={filterMethod} onValueChange={setFilterMethod}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="طريقة الدفع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الطرق</SelectItem>
                  <SelectItem value="نقدي">نقدي</SelectItem>
                  <SelectItem value="شيك">شيك</SelectItem>
                  <SelectItem value="تحويل بنكي">تحويل بنكي</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">جميع المدفوعات</TabsTrigger>
              <TabsTrigger value="treasury">الخزينة</TabsTrigger>
              <TabsTrigger value="bank">البنك</TabsTrigger>
              <TabsTrigger value="recent">الحديثة</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-6">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-right">رقم الإيصال</TableHead>
                      <TableHead className="text-right">التاريخ</TableHead>
                      <TableHead className="text-right">المصدر</TableHead>
                      <TableHead className="text-right">طريقة الدفع</TableHead>
                      <TableHead className="text-right">المبلغ</TableHead>
                      <TableHead className="text-right">العقد</TableHead>
                      <TableHead className="text-right">الحالة</TableHead>
                      <TableHead className="text-right">الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      Array.from({ length: 5 }).map((_, i) => (
                        <TableRow key={i}>
                          <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                        </TableRow>
                      ))
                    ) : (
                      allPayments
                        .filter(payment => {
                          const matchesSearch = searchTerm === "" ||
                            payment.receiptNumber?.toString().includes(searchTerm) ||
                            payment.contractNumber?.includes(searchTerm);
                          const matchesMethod = filterMethod === "all" || payment.method === filterMethod;
                          return matchesSearch && matchesMethod;
                        })
                        .slice(0, 10)
                        .map((payment: any) => (
                          <TableRow key={`${payment.source}-${payment.id}`}>
                            <TableCell className="font-medium">
                              {payment.receiptNumber || payment.transactionReference}
                            </TableCell>
                            <TableCell>{formatDate(payment.receiptDate || payment.paymentDate)}</TableCell>
                            <TableCell>
                              <Badge variant={payment.source === 'خزينة' ? 'default' : 'secondary'}>
                                {payment.source}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">{payment.method}</Badge>
                            </TableCell>
                            <TableCell className="font-medium">
                              {formatCurrency(payment.totalAmount || payment.amount)}
                            </TableCell>
                            <TableCell>{payment.contractNumber || '-'}</TableCell>
                            <TableCell>
                              <Badge variant="default" className="bg-green-100 text-green-800">
                                مكتمل
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-2">
                                <Button variant="ghost" size="sm">
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm">
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="treasury" className="mt-6">
              <div className="text-center py-8 text-slate-600">
                <Wallet className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                <p>عرض مدفوعات الخزينة فقط</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => setLocation('/treasury-payments')}
                >
                  انتقل إلى صفحة مدفوعات الخزينة
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="bank" className="mt-6">
              <div className="text-center py-8 text-slate-600">
                <Building2 className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                <p>عرض مدفوعات البنك فقط</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => setLocation('/bank-payments')}
                >
                  انتقل إلى صفحة مدفوعات البنك
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="recent" className="mt-6">
              <div className="text-center py-8 text-slate-600">
                <Calendar className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                <p>المدفوعات خلال آخر 30 يوماً</p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
