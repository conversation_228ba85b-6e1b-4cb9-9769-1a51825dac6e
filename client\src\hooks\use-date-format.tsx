import { useSettings } from "@/contexts/settings-context";

export function useDateFormat() {
  const { settings } = useSettings();

  const formatDate = (date: string | Date | null | undefined): string => {
    if (!date) return '';

    try {
      let dateObj: Date;

      if (typeof date === 'string') {
        // Handle ISO date strings properly to avoid timezone issues
        if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
          const [year, month, day] = date.split('-').map(Number);
          dateObj = new Date(year, month - 1, day); // month is 0-based
        } else {
          dateObj = new Date(date);
        }
      } else {
        dateObj = date;
      }

      if (isNaN(dateObj.getTime())) return '';

      const language = settings.language || 'ar';

      // تنسيق التاريخ حسب اللغة
      if (language === 'ar') {
        // العربي: yyyy/mm/dd
        const day = dateObj.getDate().toString().padStart(2, '0');
        const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
        const year = dateObj.getFullYear();
        return `${year}/${month}/${day}`;
      } else {
        // الإنجليزي: dd/mm/yyyy
        return new Intl.DateTimeFormat('en-GB', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric'
        }).format(dateObj);
      }
    } catch (error) {
      console.warn('Error formatting date:', error);
      return '';
    }
  };

  const formatDateTime = (date: string | Date | null | undefined): string => {
    if (!date) return '';

    try {
      let dateObj: Date;

      if (typeof date === 'string') {
        // Handle ISO date strings properly to avoid timezone issues
        if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
          const [year, month, day] = date.split('-').map(Number);
          dateObj = new Date(year, month - 1, day); // month is 0-based
        } else {
          dateObj = new Date(date);
        }
      } else {
        dateObj = date;
      }

      if (isNaN(dateObj.getTime())) return '';

      const language = settings.language || 'ar';

      if (language === 'ar') {
        // العربي: yyyy/mm/dd HH:mm
        const day = dateObj.getDate().toString().padStart(2, '0');
        const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
        const year = dateObj.getFullYear();
        const hours = dateObj.getHours().toString().padStart(2, '0');
        const minutes = dateObj.getMinutes().toString().padStart(2, '0');

        return `${year}/${month}/${day} ${hours}:${minutes}`;
      } else {
        // الإنجليزي: dd/mm/yyyy HH:mm
        return new Intl.DateTimeFormat('en-GB', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }).format(dateObj);
      }
    } catch (error) {
      console.warn('Error formatting datetime:', error);
      return '';
    }
  };

  const getCurrentDate = (): string => {
    return formatDate(new Date());
  };

  const getCurrentDateTime = (): string => {
    return formatDateTime(new Date());
  };

  const parseDate = (dateString: string): Date | null => {
    if (!dateString) return null;

    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? null : date;
    } catch (error) {
      console.warn('Error parsing date:', error);
      return null;
    }
  };

  const getDateFormat = (): string => {
    const language = settings.language || 'ar';
    return language === 'ar' ? 'YYYY/MM/DD' : 'DD/MM/YYYY';
  };

  return {
    formatDate,
    formatDateTime,
    parseDate,
    getDateFormat,
    getCurrentDate,
    getCurrentDateTime,
    language: settings.language || 'ar'
  };
}
