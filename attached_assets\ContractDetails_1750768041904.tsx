import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useParams, useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import ContractSummary from "@/components/contract-summary";
import PaymentSchedule from "@/components/payment-schedule";
import PaymentRecorder from "@/components/payment-recorder";
import { formatCurrency, formatDate, formatProgress } from "@/lib/formatters";
import { calculateFinancialSummary, isContractActive } from "@/lib/calculations";
import {
  ArrowRight,
  FileText,
  Building2,
  Calendar,
  Edit,
  Download,
  CreditCard,
  BarChart3
} from "lucide-react";
import type { Contract, Payment, FinancialSummary } from "@shared/schema";
import "../styles/ContractDetails.css";

const ContractDetails = () => {
  const { id } = useParams();
  const [, setLocation] = useLocation();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Fetch contract details
  const { data: contract, isLoading: contractLoading } = useQuery<Contract>({
    queryKey: [`/api/contracts/${id}`],
    queryFn: () => apiRequest(`/api/contracts/${id}`),
    enabled: !!id,
  });

  // Fetch payments (using new structure)
  const { data: payments = [], isLoading: paymentsLoading } = useQuery<Payment[]>({
    queryKey: [`/api/contracts/${id}/payments`],
    queryFn: () => apiRequest(`/api/contracts/${id}/payments`),
    enabled: !!id,
  });

  // Calculate financial summary from payments
  const financialSummary = contract && payments.length > 0
    ? calculateFinancialSummary(contract, payments)
    : null;

  // Generate installments mutation
  const generateInstallmentsMutation = useMutation({
    mutationFn: () => apiRequest(`/api/contracts/${id}/generate-installments`, {
      method: 'POST',
    }),
    onSuccess: () => {
      toast({
        title: "تم إنشاء الأقساط",
        description: "تم إنشاء جدول الأقساط بنجاح!",
      });
      queryClient.invalidateQueries({ queryKey: [`/api/contracts/${id}/installments`] });
      queryClient.invalidateQueries({ queryKey: [`/api/contracts/${id}/financial-summary`] });
    },
    onError: (error: any) => {
      toast({
        title: "خطأ في إنشاء الأقساط",
        description: `خطأ: ${error.message}`,
        variant: "destructive",
      });
    },
  });





  if (contractLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="mt-4 text-gray-600">جاري تحميل تفاصيل العقد...</p>
      </div>
    );
  }

  if (!contract) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">العقد غير موجود</p>
          <Button onClick={() => window.history.back()}>العودة</Button>
        </div>
      </div>
    );
  }

  const isActive = contract ? isContractActive(contract) : false;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-reverse space-x-4">
              <Button variant="ghost" className="p-2" onClick={() => setLocation("/")}>
                <ArrowRight className="h-5 w-5" />
              </Button>
              <div className="flex items-center space-x-reverse space-x-4">
                <div className="bg-primary p-2 rounded-xl">
                  <Building2 className="h-6 w-6 text-primary-foreground" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                    عقد رقم {contract?.contractNumber}
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    تفاصيل العقد والدفعات
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Badge variant={isActive ? "default" : "secondary"} className="text-lg px-4 py-2">
                {isActive ? "نشط" : "منتهي"}
              </Badge>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                تصدير
              </Button>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                تعديل
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Quick Stats */}
        {financialSummary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="bg-blue-100 dark:bg-blue-900 p-2 rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      قيمة العقد
                    </p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {formatCurrency(contract?.totalContractValue || 0)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="bg-green-100 dark:bg-green-900 p-2 rounded-lg">
                    <CreditCard className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      إجمالي المسدد
                    </p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {formatCurrency(financialSummary.totalPaid)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="bg-red-100 dark:bg-red-900 p-2 rounded-lg">
                    <Calendar className="h-5 w-5 text-red-600 dark:text-red-400" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      المتبقي
                    </p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {formatCurrency(financialSummary.totalOutstanding)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="bg-yellow-100 dark:bg-yellow-900 p-2 rounded-lg">
                    <Building2 className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      الدفعات المسددة
                    </p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {financialSummary.paidInstallments} / {financialSummary.totalInstallments}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}



        {/* Tabs */}
        <Tabs defaultValue="summary" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="summary">ملخص العقد</TabsTrigger>
            <TabsTrigger value="schedule">جدول السداد</TabsTrigger>
            <TabsTrigger value="payments">تسجيل الدفعات</TabsTrigger>
            <TabsTrigger value="reports">التقارير</TabsTrigger>
          </TabsList>

          <TabsContent value="summary">
            {contract && <ContractSummary contract={contract} />}
          </TabsContent>

          <TabsContent value="schedule">
            <PaymentSchedule
              payments={payments}
              isLoading={paymentsLoading}
              contractId={parseInt(id || "0")}
            />
          </TabsContent>

          <TabsContent value="payments">
            {contract && (
              <PaymentRecorder
                payments={payments.filter(p => !p.isPaid)}
                contract={contract}
                contractId={parseInt(id || "0")}
              />
            )}
          </TabsContent>

          <TabsContent value="reports">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  التقارير والإحصائيات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Payment Progress */}
                  {financialSummary && (
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        تقدم السداد
                      </h4>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                        <div
                          className="bg-primary h-3 rounded-full transition-all duration-300 progress-bar"
                          style={{
                            '--progress-width': `${financialSummary.paymentProgress}%`
                          } as React.CSSProperties}
                        ></div>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                        {financialSummary.paidInstallments} من {financialSummary.totalInstallments} دفعات مسددة
                        ({Math.round(financialSummary.paymentProgress)}%)
                      </p>
                    </div>
                  )}

                  {/* Print Actions */}
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      طباعة وتصدير
                    </h4>
                    <div className="flex flex-wrap gap-4">
                      <Button
                        variant="outline"
                        onClick={() => window.print()}
                        className="flex items-center space-x-reverse space-x-2"
                      >
                        <FileText className="h-4 w-4" />
                        <span>طباعة العقد</span>
                      </Button>
                      <Button
                        variant="outline"
                        className="flex items-center space-x-reverse space-x-2"
                      >
                        <Calendar className="h-4 w-4" />
                        <span>تصدير جدول السداد</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

      </main>
    </div>
  );
};

export default ContractDetails;
