// ===== CONTRACT CREATION PAGE =====
// This page currently uses the original Contracts table.
// To use the new NewContracts table, change the API endpoint from:
// '/api/contracts' to '/api/new-contracts'
// The new table has better schema and cleaner data handling.
// ===== END NOTES =====

import React, { useState, useEffect, useMemo } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { useEnhancedToast } from "@/hooks/use-enhanced-toast";
import { useEnhancedErrorHandler } from "@/hooks/use-enhanced-error-handler";
import { FormErrorDisplay, useFormErrors } from "@/components/form-error-display";
import { ErrorHelpSystem } from "@/components/error-help-system";
import { useSettings } from "@/contexts/settings-context";
import { insertContractSchema, type InsertContract, type Client } from "@shared/schema";
import { EnhancedContractCalculator, type ContractCalculationData } from "@/lib/enhanced-contract-calculator";
import { useReferenceDataOptions, REFERENCE_LISTS } from "@/hooks/use-reference-data";
import { useReferenceDropdownOptions } from "@/hooks/use-reference-lists";
import { useLanguage } from "@/hooks/use-language";
import { useDateFormat } from "@/hooks/use-date-format";
import { calculateDurationInYears, calculateEndDateFromDuration, formatDetailedDuration } from "@/lib/formatters";
import SimpleDateInput from "@/components/ui/simple-date-input";
import { useContractsStore } from "@/store/contractsStore";

import ContractPreviewModal from "@/components/contract-preview-modal";

import labels from "@/lib/i18n";
import { Plus, Trash2, Calculator, FileText, User, DollarSign, Eye, Save, X, Upload } from "lucide-react";

// Helper function to calculate payment frequency from products
const calculatePaymentFrequency = (products: any[]) => {
  if (!products || products.length === 0) {
    return 'شهري'; // Default to monthly
  }

  // Get unique billing types from all products
  const billingTypes = [...new Set(products.map(p => p.billingType).filter(Boolean))];

  // If all products have the same billing type, use that
  if (billingTypes.length === 1) {
    const billingType = billingTypes[0];
    // For irregular billing, we need to validate the configuration
    if (billingType === 'غير منتظم') {
      // Check if all irregular products have valid configuration
      const hasValidConfig = products.every(p => {
        if (p.billingType !== 'غير منتظم') return true;

        if (p.customPaymentType === 'uniform') {
          return p.totalInstallments > 0 && p.monthsBetweenPayments > 0;
        } else if (p.customPaymentType === 'custom') {
          return p.customIntervals && p.customIntervals.length > 0;
        }
        return false;
      });

      // If configuration is invalid, fall back to monthly
      return hasValidConfig ? 'غير منتظم' : 'شهري';
    }
    return billingType;
  }

  // If mixed billing types, determine the most frequent one
  const billingTypeCounts = products.reduce((acc, product) => {
    const type = product.billingType || 'شهري';
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});

  // Return the most common billing type
  const mostCommonType = Object.entries(billingTypeCounts)
    .sort(([,a], [,b]) => (b as number) - (a as number))[0][0];

  return mostCommonType;
};

export default function ContractsNewSingle() {
  const { settings } = useSettings();
  const { toast } = useToast();
  const { showSaveSuccess, showUpdateSuccess, showSaveError, showUpdateError } = useEnhancedToast();
  const { language, isRTL } = useLanguage();
  const { formatDate: formatDateFromSettings, getDateFormat } = useDateFormat();

  // استخدام القوائم المرجعية
  const { options: contractTypes } = useReferenceDropdownOptions('contracts', 'contractType');
  const { options: departments } = useReferenceDropdownOptions('contracts', 'responsibleDepartment');
  const { options: paymentMethods } = useReferenceDropdownOptions('contracts', 'paymentMethod');
  const { options: assetOwners } = useReferenceDropdownOptions('contracts', 'assetOwner');
  const { options: regions } = useReferenceDropdownOptions('contracts', 'region');

  // Debug: Log asset owners
  console.log('🔍 Asset owners options:', assetOwners);

  // Function to normalize client type display
  const normalizeClientType = (clientType: string) => {
    if (clientType === 'فرد') return 'أفراد';
    if (clientType === 'شركة') return 'شركات';
    return clientType;
  };
  const t = labels[language];
  const queryClient = useQueryClient();
  const [calculationResults, setCalculationResults] = useState<any>(null);
  const [relatedContractsText, setRelatedContractsText] = useState("");
  const [, setLocation] = useLocation();
  const [isNavigating, setIsNavigating] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewData, setPreviewData] = useState<any>(null);

  // This page is now only for creating new contracts

  // Use contracts store for cleanup only
  const {
    actions: { clearSelectedContract }
  } = useContractsStore();

  // Memoize default values to prevent re-renders
  const defaultValues = useMemo(() => {
    const now = new Date();
    return {
      contractNumber: `C-${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}-${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`,
      contractInternalId: `INT-${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}-${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`,
      contractDescription: "",
      contractSubject: "",
      clientId: 0,
      contractType: "",
      contractStatus: "نشط",
      contractDate: "",
      contractSigningDate: "",
      financialActivationDate: "",
      startDate: "",
      actualStartDate: "",
      endDate: "",
      actualEndDate: "",
      assetOwner: "",
      ownershipPercentage: 100,
      responsibleDepartment: "",
      region: "",
      financialGuarantorId: 0,
      parentContractId: 0,
      numberOfProducts: 1,
      hasUnifiedActivationDate: true,
      totalContractValue: 0,
      monthlyAmount: 0,
      paymentDay: 0,
      irregularPaymentMonths: 0,
      firstInstallmentDate: "",
      paymentMethod: "",
      finalInsuranceRate: 0,
      advancePaymentMonths: 0,
      advancePaymentAmount: 0,
      checkStatus: "",
      lateFeeType: "",
      lateFeeValue: 0,
      gracePeriodDays: 0,
      bouncedCheckFeeType: "",
      bouncedCheckFeeValue: 0,
      additionalFees: [],
      importantNotes: "",
      notes: "",
      isActive: true,
      products: [{
        productLabel: "",
        area: 0,
        meterPrice: 0,
        activationDate: "",
        endDate: "",
        duration: 0,
        annualIncrease: 0,
        taxRate: 0,
        billingType: "شهري",
        irregularBillingMonths: 0,
        taxInfo: false,
        financialAccountingStartDate: "",
        financialAccountingEndDate: "",
        accountingDuration: 0,
        hasAnnualIncrease: false,
        increaseStartYear: 2,
        increaseType: "نسبة مئوية",
        increaseValue: 0,
        additionalFees: [],
        // New billing fields
        customPaymentType: "",
        totalInstallments: 0,
        monthsBetweenPayments: 0,
        customIntervals: []
      }]
    };
  }, []);

  const form = useForm({
    mode: "onChange",
    shouldFocusError: false,
    defaultValues,
  });

  // Fetch clients for dropdown
  const { data: clients = [] } = useQuery<Client[]>({
    queryKey: ['/api/clients'],
    queryFn: async () => {
      const response = await fetch('/api/clients');
      if (!response.ok) throw new Error('Failed to fetch clients');
      return response.json();
    },
  });

  // Cleanup when component unmounts
  useEffect(() => {
    return () => {
      clearSelectedContract();
    };
  }, [clearSelectedContract]);

  // This page is now only for creating new contracts

  // Watch form values for calculations
  const watchedProducts = form.watch("products");
  const watchedInsurance = form.watch("finalInsuranceRate");
  const watchedAdvancePayment = form.watch("advancePaymentMonths");
  const watchedGracePeriod = form.watch("gracePeriodDays");

  // This page is now only for creating new contracts - no editing functionality
  // Removed all editing-related code


  // Auto-calculate contract values when products change
  useEffect(() => {
    console.log('🔢 useEffect triggered - watchedProducts:', watchedProducts);

    if (!watchedProducts || watchedProducts.length === 0) {
      console.log('🔢 No products found, skipping calculation');
      return;
    }

    // More flexible validation - allow partial calculations
    const validProducts = watchedProducts.filter(p => {
      const isValid = p && p.area > 0 && p.meterPrice > 0;
      console.log('🔢 Product validation:', {
        product: p?.productLabel || 'unnamed',
        area: p?.area,
        meterPrice: p?.meterPrice,
        billingType: p?.billingType,
        activationDate: p?.activationDate,
        endDate: p?.endDate,
        accountingDuration: p?.accountingDuration,
        isValid
      });
      return isValid;
    });

    console.log('🔢 Valid products for calculation:', validProducts.length, validProducts);

    if (validProducts.length > 0) {
      try {
        // Get current form data
        const formData = form.getValues();

        // Process products without default values
        const processedProducts = validProducts.map(p => {
          // Only use actual values from the form
          const duration = p.accountingDuration && p.accountingDuration > 0 ? p.accountingDuration : 0;

          // For inactive contracts, use contract date as activation date if not provided
          let activationDate = p.activationDate;
          if (formData.contractStatus === "غير نشط" && !activationDate) {
            activationDate = formData.contractDate;
          }

          return {
            ...p,
            activationDate: activationDate,
            accountingDuration: duration,
            // Ensure required fields for calculation exist
            financialAccountingStartDate: activationDate || p.financialAccountingStartDate,
            financialAccountingEndDate: p.endDate || p.financialAccountingEndDate,
            taxInfo: p.taxInfo || false,
            taxRate: p.taxRate || 0,
            hasAnnualIncrease: p.hasAnnualIncrease || false,
            increaseStartYear: p.increaseStartYear || 2,
            increaseType: p.increaseType || 'نسبة مئوية',
            increaseValue: p.increaseValue || 0
          };
        });

        const calculationData: ContractCalculationData = {
          products: processedProducts,
          finalInsuranceRate: watchedInsurance || 0,
          advancePaymentMonths: watchedAdvancePayment || 0,
          gracePeriodDays: watchedGracePeriod || 0
        };

        console.log('🔢 Calculation Input:', {
          products: processedProducts,
          finalInsuranceRate: watchedInsurance || 0,
          advancePaymentMonths: watchedAdvancePayment || 0
        });

        const results = EnhancedContractCalculator.calculateContract(calculationData);

        console.log('🔢 Calculation Results:', {
          totalValue: results.totalValue,
          monthlyAmount: results.monthlyAmount,
          insuranceAmount: results.insuranceAmount,
          advancePaymentAmount: results.advancePaymentAmount,
          contractDuration: results.summary?.totalYears,
          yearlyAmounts: results.yearlyAmounts,
          fullResults: results
        });

        setCalculationResults(results);
        console.log('🔢 Calculation results set in state');

        // Update form values
        form.setValue("totalContractValue", results.totalValue);
        form.setValue("monthlyAmount", results.monthlyAmount);
        form.setValue("advancePaymentAmount", results.advancePaymentAmount);

      } catch (error) {
        console.error('🔢 Calculation error:', error);
        setCalculationResults(null);
      }
    } else {
      console.log('🔢 No valid products found, clearing calculation results');
      setCalculationResults(null);
    }
  }, [watchedProducts, watchedInsurance, watchedAdvancePayment, watchedGracePeriod, form]);

  // Handle preview
  const handlePreview = () => {
    const formData = form.getValues();
    console.log('Preview data:', formData);

    // Get client data - either from loaded contract data or from clients list
    let clientInfo = {};
    let guarantorInfo = {};

    // Get client data from clients list (only for new contracts)
    const selectedClient = clients.find(c => c.id === formData.clientId);
    clientInfo = {
      clientName: selectedClient?.clientName || 'غير محدد',
      clientType: selectedClient?.clientType || 'أفراد',
      clientClassification: selectedClient?.clientFinancial_Category || 'عادي',
      clientAddress: selectedClient?.clientAddress || '',
      clientPhone: selectedClient?.clientPhoneWhatsapp || '',
      clientEmail: selectedClient?.clientEmail || ''
    };

    // Get financial guarantor data
    const financialGuarantor = clients.find(c => c.id === formData.financialGuarantorId);
    guarantorInfo = {
      financialGuarantorName: financialGuarantor?.clientName || ''
    };

    const previewData = {
      ...formData,
      ...clientInfo,
      ...guarantorInfo
    };

    console.log('Preview data with client info:', previewData);

    // Store preview data for the modal
    setPreviewData(previewData);
    setShowPreview(true);
  };

  // Handle form submission
  const onSubmit = async (data: any) => {
    console.log('🔥 onSubmit called with data:', data);

    try {
      setIsNavigating(true);

      // Calculate contract dates from products
      const getContractDates = () => {
        if (!data.products || data.products.length === 0) {
          return { startDate: new Date().toISOString().split('T')[0], endDate: new Date().toISOString().split('T')[0] };
        }

        // For inactive contracts, use contract date as fallback for activation dates
        if (data.contractStatus === "غير نشط") {
          const contractDate = data.contractDate;
          const dates = data.products.map(p => ({
            start: new Date(p.activationDate || contractDate || new Date().toISOString().split('T')[0]),
            end: new Date(p.endDate || new Date().toISOString().split('T')[0])
          }));

          const startDate = new Date(Math.min(...dates.map(d => d.start.getTime())));
          const endDate = new Date(Math.max(...dates.map(d => d.end.getTime())));

          return {
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0]
          };
        }

        // For active contracts, require activation dates
        const dates = data.products
          .filter(p => p.activationDate && p.endDate)
          .map(p => ({
            start: new Date(p.activationDate),
            end: new Date(p.endDate)
          }));

        if (dates.length === 0) {
          return { startDate: new Date().toISOString().split('T')[0], endDate: new Date().toISOString().split('T')[0] };
        }

        const startDate = new Date(Math.min(...dates.map(d => d.start.getTime())));
        const endDate = new Date(Math.max(...dates.map(d => d.end.getTime())));

        return {
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0]
        };
      };

      const { startDate, endDate } = getContractDates();

      console.log('🔍 Date debugging:', {
        'data.contractDate': data.contractDate,
        'startDate': startDate,
        'endDate': endDate,
        'data.contractSigningDate': data.contractSigningDate
      });

      // Prepare contract data with calculation results
      const contractData = {
        // Basic contract info
        contractNumber: data.contractNumber,
        contractInternalId: data.contractInternalId?.trim() || null,
        contractDescription: data.contractDescription || null,
        contractSubject: data.contractSubject,
        clientId: parseInt(data.clientId),
        contractType: data.contractType,
        contractStatus: data.contractStatus || 'نشط',
        contractDate: data.contractDate || startDate || new Date().toISOString().split('T')[0],
        contractSigningDate: data.contractSigningDate || data.contractDate || startDate || new Date().toISOString().split('T')[0],
        financialActivationDate: data.financialActivationDate || null,

        // Contract dates calculated from products
        startDate: startDate,
        actualStartDate: null,
        endDate: endDate,
        actualEndDate: null,

        // Financial calculations
        contractDurationYears: calculationResults?.summary?.totalYears || 1,
        totalContractValue: calculationResults?.totalValue || 0,
        monthlyAmount: calculationResults?.monthlyAmount || 0,

        // Payment settings
        paymentDay: data.paymentDay || 1,
        paymentFrequency: calculatePaymentFrequency(data.products),
        firstInstallmentDate: startDate,
        paymentMethod: data.paymentMethod || 'تحويل بنكي',

        // Insurance and advance payment
        finalInsuranceRate: data.finalInsuranceRate || 0,
        finalInsuranceAmount: calculationResults?.insuranceAmount || 0,
        advancePaymentMonths: data.advancePaymentMonths || 0,
        advancePaymentAmount: calculationResults?.advancePaymentAmount || 0,

        // Contract management
        assetOwner: data.assetOwner || null,
        ownershipPercentage: data.ownershipPercentage || 100,
        responsibleDepartment: data.responsibleDepartment || null,
        region: data.region || null,
        financialGuarantorId: data.financialGuarantorId || null,

        // Payment settings
        irregularPaymentMonths: data.irregularPaymentMonths || 0,

        // Contract structure
        numberOfProducts: data.products?.length || 1,
        hasUnifiedActivationDate: data.hasUnifiedActivationDate !== false,
        checkStatus: data.checkStatus || 'لم يتقدم بالشيكات',

        // Penalties and fees
        lateFeeType: data.lateFeeType || 'نسبة مئوية',
        lateFeeValue: data.lateFeeValue || 0,
        gracePeriodDays: data.gracePeriodDays || 5,
        bouncedCheckFeeType: data.bouncedCheckFeeType || 'مبلغ ثابت',
        bouncedCheckFeeValue: data.bouncedCheckFeeValue || 0,
        additionalFees: data.additionalFees || [],

        // Notes
        notes: data.notes || null,
        importantNotes: data.importantNotes || null,

        // Products, Partners and status
        products: data.products || [],
        partners: data.partners || []
      };

      console.log('Form data received:', data);
      console.log('Calculation results:', calculationResults);
      console.log('Submitting contract:', contractData);

      // تحقق من الحقول المطلوبة
      const requiredFields = ['contractNumber', 'clientId', 'contractType', 'startDate', 'contractDurationYears', 'totalContractValue'];
      const missingFields = requiredFields.filter(field => !contractData[field]);
      if (missingFields.length > 0) {
        console.error('❌ Missing required fields:', missingFields);
        console.error('❌ Contract data:', contractData);
      } else {
        console.log('✅ All required fields present');
      }

      // Always create new contracts using unified API
      const apiUrl = '/api/contracts';
      const method = 'POST';

      console.log('🔥 API URL:', apiUrl);
      console.log('🔥 Method:', method);

      // Send to API
      const response = await fetch(apiUrl, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contractData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const savedContract = await response.json();
      console.log('Contract saved successfully:', savedContract);

      // Show success message for new contract
      showSaveSuccess("العقد");

      // إبطال الذاكرة المؤقتة لتحديث البيانات
      queryClient.invalidateQueries({ queryKey: ['/api/contracts'] });
      queryClient.invalidateQueries({ queryKey: ['/api/receivables'] });
      queryClient.invalidateQueries({ queryKey: ['/api/receivables/stats'] });
      console.log('🔄 Cache invalidated for contracts and receivables');

      // Navigate back to contracts list after successful save
      setTimeout(() => {
        setLocation('/contracts');
      }, 1500);

    } catch (error) {
      console.error('Error saving contract:', error);

      // Show error message for new contract
      showSaveError(error instanceof Error ? error.message : "حدث خطأ أثناء حفظ العقد");
    } finally {
      setIsNavigating(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900" dir="rtl">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Title */}
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  إنشاء عقد جديد
                </h1>
                <p className="text-sm text-gray-600">
                  أدخل جميع بيانات العقد مع الحسابات التلقائية
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setLocation('/contracts')}
                disabled={isNavigating}
              >
                <X className="h-4 w-4 ml-1" />
                إلغاء
              </Button>

              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handlePreview}
              >
                <Eye className="h-4 w-4 ml-1" />
                معاينة
              </Button>

              <Button
                type="button"
                size="sm"
                onClick={() => {
                  console.log('🔥 Save button clicked');
                  const formData = form.getValues();
                  onSubmit(formData);
                }}
                disabled={isNavigating}
                className="bg-green-600 hover:bg-green-700"
              >
                {isNavigating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-1"></div>
                    جاري الحفظ...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 ml-1" />
                    حفظ العقد
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="px-6 py-6">
        {/* Form for creating new contracts */}
        <form
          id="contract-form"
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-8"
        >
          {/* Step 1: معلومات العقد الأساسية */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-bold text-right mb-4">معلومات العقد الأساسية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-2" dir="rtl">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="contractNumber" className="text-right block mb-2">رقم العقد *</Label>
                  <Input
                    id="contractNumber"
                    {...form.register("contractNumber")}
                    placeholder="أدخل رقم العقد"
                    className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50"
                  />
                </div>

                <div>
                  <Label htmlFor="contractInternalId" className="text-right block mb-2">الرقم الداخلي</Label>
                  <Input
                    id="contractInternalId"
                    {...form.register("contractInternalId")}
                    placeholder="الرقم الداخلي للعقد"
                    className="text-right"
                  />
                </div>

                <div>
                  <Label htmlFor="contractType" className="text-right block mb-2">نوع العقد *</Label>
                  <Select
                    value={form.watch("contractType") || ""}
                    onValueChange={(value) => form.setValue("contractType", value)}
                  >
                    <SelectTrigger className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50">
                      <SelectValue placeholder="اختر نوع العقد" />
                    </SelectTrigger>
                    <SelectContent>
                      {contractTypes.length > 0 ? (
                        contractTypes.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))
                      ) : (
                        <>
                          <SelectItem value="إيجار">إيجار</SelectItem>
                          <SelectItem value="بيع">بيع</SelectItem>
                          <SelectItem value="شراكة">شراكة</SelectItem>
                          <SelectItem value="استثمار">استثمار</SelectItem>
                          <SelectItem value="خدمات">خدمات</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* حالة العقد */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div>
                  <Label htmlFor="contractStatus" className="text-right block mb-2">حالة العقد *</Label>
                  <Select
                    value={form.watch("contractStatus") || "نشط"}
                    onValueChange={(value) => {
                      form.setValue("contractStatus", value);

                      // إذا تم تغيير الحالة إلى "غير نشط"، قم بتحديث تواريخ التفعيل
                      if (value === "غير نشط") {
                        const contractDate = form.watch("contractDate");
                        if (contractDate) {
                          const products = form.watch("products") || [];
                          products.forEach((_, index) => {
                            form.setValue(`products.${index}.activationDate`, contractDate);
                          });
                        }
                      }
                    }}
                  >
                    <SelectTrigger className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50">
                      <SelectValue placeholder="اختر حالة العقد" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="نشط">نشط</SelectItem>
                      <SelectItem value="غير نشط">غير نشط</SelectItem>
                      <SelectItem value="معلق">معلق</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {form.watch("contractStatus") === "غير نشط" && (
                  <div className="md:col-span-2">
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <p className="text-sm text-yellow-800 text-right">
                        <strong>ملاحظة:</strong> العقود غير النشطة:
                        <br />• تواريخ التفعيل في المنتجات اختيارية (ستستخدم تاريخ البداية إذا لم تُحدد)
                        <br />• الاستحقاقات تبدأ من تاريخ البداية بدون إيرادات
                        <br />• تاريخ البداية إلزامي ومهم جداً
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* موضوع ووصف العقد */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-bold text-right mb-4">موضوع ووصف العقد</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-2" dir="rtl">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contractSubject" className="text-right block mb-2">موضوع العقد *</Label>
                  <Textarea
                    id="contractSubject"
                    {...form.register("contractSubject")}
                    placeholder="مثال: عقد إيجار مكاتب تجارية - برج الأعمال"
                    className="text-right min-h-[80px] border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50"
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="contractDescription" className="text-right block mb-2">وصف العقد</Label>
                  <Textarea
                    id="contractDescription"
                    {...form.register("contractDescription")}
                    placeholder="مثال: عقد إيجار مكاتب تجارية بالدور الثالث، يشمل خدمات الأمن والنظافة والصيانة"
                    className="text-right min-h-[80px]"
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* التواريخ الأساسية */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-bold text-right mb-4">التواريخ الأساسية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-2" dir="rtl">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contractDate" className="text-right block mb-2">
                    تاريخ البداية بالعقد / المزايدة / المفاوضة *
                    {form.watch("contractStatus") === "غير نشط" && (
                      <span className="text-red-600 text-sm"> (مهم جداً للعقود غير النشطة)</span>
                    )}
                  </Label>
                  <SimpleDateInput
                    id="contractDate"
                    value={form.watch("contractDate") || ""}
                    onChange={(value) => {
                      form.setValue("contractDate", value);

                      // إذا كان العقد غير نشط، قم بتحديث تواريخ التفعيل تلقائياً
                      if (form.watch("contractStatus") === "غير نشط") {
                        if (value) {
                          const products = form.watch("products") || [];
                          products.forEach((_, index) => {
                            if (!form.watch(`products.${index}.activationDate`)) {
                              form.setValue(`products.${index}.activationDate`, value);
                            }
                          });
                        }
                      }
                    }}
                    placeholder="مثال: 07/05/2025"
                    className={`text-right border-2 focus:border-yellow-500 ${
                      form.watch("contractStatus") === "غير نشط"
                        ? "border-red-400 bg-red-50"
                        : "border-yellow-400 bg-yellow-50"
                    }`}
                  />
                </div>

                <div>
                  <Label htmlFor="contractSigningDate" className="text-right block mb-2">تاريخ التوقيع على العقد *</Label>
                  <SimpleDateInput
                    id="contractSigningDate"
                    value={form.watch("contractSigningDate") || ""}
                    onChange={(value) => form.setValue("contractSigningDate", value)}
                    placeholder="مثال: 07/05/2025"
                    className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50"
                  />
                </div>


              </div>
            </CardContent>
          </Card>

          {/* العقود المرتبطة */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-bold text-right mb-4">العقود المرتبطة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-2" dir="rtl">
              <div className="flex items-center space-x-reverse space-x-2 mb-4">
                <Checkbox
                  id="hasRelatedContracts"
                  checked={(form.watch("parentContractId") || 0) > 0}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      form.setValue("parentContractId", 1);
                    } else {
                      form.setValue("parentContractId", 0);
                      setRelatedContractsText("");
                    }
                  }}
                />
                <Label htmlFor="hasRelatedContracts" className="text-sm font-medium text-right">
                  هل العقد مرتبط بعقد أو عقود أخرى؟
                </Label>
              </div>

              {(form.watch("parentContractId") || 0) > 0 && (
                <div className="p-4 bg-gray-50 rounded-lg border">
                  <Label htmlFor="relatedContractsText" className="text-right block mb-3 font-medium">
                    أرقام العقود المرتبطة
                  </Label>
                  <Textarea
                    id="relatedContractsText"
                    placeholder="أدخل أرقام العقود المرتبطة مفصولة بفاصلة أو على أسطر منفصلة&#10;مثال:&#10;CT-2024-001&#10;CT-2024-002&#10;CT-2024-003"
                    className="text-right min-h-[100px] resize-none"
                    rows={4}
                    value={relatedContractsText}
                    onChange={(e) => setRelatedContractsText(e.target.value)}
                  />
                  <p className="text-xs text-gray-500 mt-2 text-right">
                    يمكنك إدخال أرقام العقود المرتبطة مفصولة بفاصلة أو كل رقم في سطر منفصل
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Step 2: الجهة المالكة للأصل */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-bold text-right mb-4">الجهة المالكة للأصل</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-2" dir="rtl">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="assetOwner" className="text-right block mb-2">مالك الأصل *</Label>
                  <Select
                    value={form.watch("assetOwner") || ""}
                    onValueChange={(value) => form.setValue("assetOwner", value)}
                  >
                    <SelectTrigger className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50">
                      <SelectValue placeholder="اختر الجهة المالكة للأصل" />
                    </SelectTrigger>
                    <SelectContent>
                      {assetOwners.length > 0 ? (
                        assetOwners.map((owner) => (
                          <SelectItem key={owner.value} value={owner.value}>
                            {owner.label}
                          </SelectItem>
                        ))
                      ) : (
                        // Fallback options if no reference data is available
                        <>
                          <SelectItem value="company">الشركة</SelectItem>
                          <SelectItem value="partner1">الشريك الأول</SelectItem>
                          <SelectItem value="partner2">الشريك الثاني</SelectItem>
                          <SelectItem value="external">مالك خارجي</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="ownershipPercentage" className="text-right block mb-2">نسبة الملكية (%)</Label>
                  <Input
                    id="ownershipPercentage"
                    type="number"
                    min="0"
                    max="100"
                    {...form.register("ownershipPercentage", { valueAsNumber: true })}
                    placeholder="100"
                    className="text-right"
                  />
                </div>

                <div>
                  <Label htmlFor="responsibleDepartment" className="text-right block mb-2">الإدارة المسؤولة عن العقد *</Label>
                  <Select
                    value={form.watch("responsibleDepartment") || ""}
                    onValueChange={(value) => form.setValue("responsibleDepartment", value)}
                  >
                    <SelectTrigger className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50">
                      <SelectValue placeholder="اختر الإدارة المسؤولة" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.length > 0 ? (
                        departments.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))
                      ) : (
                        <>
                          <SelectItem value="إدارة العقود">إدارة العقود</SelectItem>
                          <SelectItem value="إدارة المبيعات">إدارة المبيعات</SelectItem>
                          <SelectItem value="إدارة التسويق">إدارة التسويق</SelectItem>
                          <SelectItem value="إدارة العمليات">إدارة العمليات</SelectItem>
                          <SelectItem value="إدارة الشؤون القانونية">إدارة الشؤون القانونية</SelectItem>
                          <SelectItem value="إدارة المالية">إدارة المالية</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="region" className="text-right block mb-2">المنطقة محل العقد *</Label>
                  <Select
                    value={form.watch("region") || ""}
                    onValueChange={(value) => form.setValue("region", value)}
                  >
                    <SelectTrigger className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50">
                      <SelectValue placeholder="اختر المنطقة" />
                    </SelectTrigger>
                    <SelectContent>
                      {regions.length > 0 ? (
                        regions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))
                      ) : (
                        <>
                          <SelectItem value="القاهرة الجديدة">القاهرة الجديدة</SelectItem>
                          <SelectItem value="مدينة نصر">مدينة نصر</SelectItem>
                          <SelectItem value="المعادي">المعادي</SelectItem>
                          <SelectItem value="الزمالك">الزمالك</SelectItem>
                          <SelectItem value="مصر الجديدة">مصر الجديدة</SelectItem>
                          <SelectItem value="الشيخ زايد">الشيخ زايد</SelectItem>
                          <SelectItem value="6 أكتوبر">6 أكتوبر</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* العميل والضامن المالي */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-bold text-right mb-4">العميل والضامن المالي</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-2" dir="rtl">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="clientId" className="text-right block mb-2">العميل *</Label>
                  <Select
                    value={form.watch("clientId")?.toString() || ""}
                    onValueChange={(value) => form.setValue("clientId", parseInt(value))}
                  >
                    <SelectTrigger className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50">
                      <SelectValue placeholder="اختر العميل" />
                    </SelectTrigger>
                    <SelectContent>
                      {clients.map((client) => (
                        <SelectItem key={client.id} value={client.id.toString()}>
                          {client.clientName} - {normalizeClientType(client.clientType)} - رقم: {client.clientId}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="financialGuarantorId" className="text-right block mb-2">الضامن المالي</Label>
                  <Select
                    value={form.watch("financialGuarantorId")?.toString() || ""}
                    onValueChange={(value) => form.setValue("financialGuarantorId", parseInt(value))}
                  >
                    <SelectTrigger className="text-right">
                      <SelectValue placeholder="اختر الضامن المالي" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">لا يوجد</SelectItem>
                      {clients.map((client) => (
                        <SelectItem key={client.id} value={client.id.toString()}>
                          {client.clientName} - رقم: {client.clientId}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* الشركاء والتحالفات */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-bold text-right mb-4">الشركاء والتحالفات</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-2" dir="rtl">
              <div className="flex items-center space-x-reverse space-x-2 mb-4">
                <Checkbox
                  id="hasPartners"
                  checked={(form.watch("partners") || []).length > 0}
                  onCheckedChange={(checked) => {
                    if (!checked) {
                      form.setValue("partners", []);
                    } else {
                      // إضافة شريك افتراضي عند التفعيل
                      form.setValue("partners", [{
                        partnerId: 0,
                        partnerType: "شريك",
                        partnershipPercentage: 0
                      }]);
                    }
                  }}
                />
                <Label htmlFor="hasPartners" className="text-sm font-medium text-right">
                  هل يوجد شركاء أو تحالفات في هذا العقد؟
                </Label>
              </div>

              {(form.watch("partners") || []).length > 0 && (
                <div className="space-y-4">
                  {form.watch("partners")?.map((partner, index) => (
                    <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-blue-50 rounded-lg border relative">
                      <div>
                        <Label className="text-right block mb-2 font-medium">
                          اختر الشريك
                        </Label>
                        <Select
                          value={partner.partnerId?.toString() || ""}
                          onValueChange={(value) => {
                            const currentPartners = form.getValues("partners") || [];
                            currentPartners[index].partnerId = parseInt(value);
                            form.setValue("partners", currentPartners);
                          }}
                        >
                          <SelectTrigger className="text-right">
                            <SelectValue placeholder="اختر من قائمة العملاء" />
                          </SelectTrigger>
                          <SelectContent>
                            {clients.map((client) => (
                              <SelectItem key={client.id} value={client.id.toString()}>
                                {client.clientName} - {normalizeClientType(client.clientType)} - رقم: {client.clientId}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className="text-right block mb-2 font-medium">
                          نوع الشراكة
                        </Label>
                        <Select
                          value={partner.partnerType || "شريك"}
                          onValueChange={(value) => {
                            const currentPartners = form.getValues("partners") || [];
                            currentPartners[index].partnerType = value as "شريك" | "تحالف";
                            form.setValue("partners", currentPartners);
                          }}
                        >
                          <SelectTrigger className="text-right">
                            <SelectValue placeholder="اختر نوع الشراكة" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="شريك">شريك</SelectItem>
                            <SelectItem value="تحالف">تحالف</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className="text-right block mb-2 font-medium">
                          نسبة الشراكة (%)
                        </Label>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          step="0.01"
                          value={partner.partnershipPercentage || 0}
                          onChange={(e) => {
                            const currentPartners = form.getValues("partners") || [];
                            currentPartners[index].partnershipPercentage = parseFloat(e.target.value) || 0;
                            form.setValue("partners", currentPartners);
                          }}
                          placeholder="0.00"
                          className="text-right"
                        />
                      </div>

                      <div className="flex items-end justify-start col-span-3">
                        {(form.watch("partners") || []).length > 1 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const currentPartners = form.getValues("partners") || [];
                              form.setValue("partners", currentPartners.filter((_, i) => i !== index));
                            }}
                            className="gap-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                            حذف
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}

                  <div className="flex justify-start">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const currentPartners = form.getValues("partners") || [];
                        form.setValue("partners", [...currentPartners, {
                          partnerId: 0,
                          partnerType: "شريك",
                          partnershipPercentage: 0
                        }]);
                      }}
                      className="gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      إضافة شريك آخر
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* مرفقات العقد */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-bold text-right mb-4">مرفقات العقد</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-2" dir="rtl">
              <div className="text-sm text-gray-600 mb-4">
                يمكنك رفع المستندات والملفات المتعلقة بالعقد (PDF, DOC, JPG, PNG)
              </div>

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <div className="space-y-2">
                  <div className="text-gray-500">
                    <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                      <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                  <div className="text-sm text-gray-600">
                    اسحب الملفات هنا أو انقر للاختيار
                  </div>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    className="hidden"
                    id="contract-attachments"
                    onChange={(e) => {
                      // Handle file upload
                      console.log('Files selected:', e.target.files);
                    }}
                  />
                  <label htmlFor="contract-attachments">
                    <Button type="button" variant="outline" className="gap-2" asChild>
                      <span>
                        <Upload className="h-4 w-4" />
                        اختيار الملفات
                      </span>
                    </Button>
                  </label>
                </div>
              </div>

              {/* قائمة المرفقات المرفوعة */}
              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-700">الملفات المرفوعة:</div>
                <div className="text-sm text-gray-500">لا توجد مرفقات حتى الآن</div>
              </div>
            </CardContent>
          </Card>

          {/* Step 3: المنتجات والمساحات */}
          <div className="flex justify-start mb-6" dir="rtl">
            <Button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                const currentProducts = form.getValues("products") || [];
                form.setValue("products", [
                  ...currentProducts,
                  {
                    productLabel: "",
                    area: 0,
                    meterPrice: 0,
                    activationDate: "",
                    endDate: "",
                    billingType: "شهري",
                    irregularBillingMonths: 0,
                    taxInfo: false,
                    taxRate: 0,
                    financialAccountingStartDate: "",
                    financialAccountingEndDate: "",
                    accountingDuration: 0,
                    hasAnnualIncrease: false,
                    increaseStartYear: 2,
                    increaseType: "نسبة مئوية",
                    increaseValue: 0,
                    // New billing fields
                    customPaymentType: "",
                    totalInstallments: 0,
                    monthsBetweenPayments: 0,
                    customIntervals: []
                  }
                ]);
              }}
              className="gap-2 bg-green-600 hover:bg-green-700 text-white shadow-md"
            >
              <Plus className="h-4 w-4" />
              إضافة منتج جديد
            </Button>
          </div>

          {form.watch("products")?.length === 1 && (
            <div className="text-center py-4 text-blue-600 bg-blue-50 rounded-lg mb-4">
              <p className="text-sm">
                📋 يحتوي العقد على منتج واحد إجباري. يمكنك إضافة منتجات إضافية باستخدام زر "إضافة منتج جديد"
              </p>
            </div>
          )}

          {form.watch("products")?.map((product, index) => (
            <Card key={index} className="border-r-4 border-r-primary bg-gradient-to-l from-blue-50/30 to-white">
              <CardHeader className="pb-3 bg-gradient-to-l from-blue-100/50 to-blue-50/30">
                <div className="flex items-center justify-between" dir="rtl">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {index + 1}
                    </div>
                    <CardTitle className="text-lg text-blue-800">
                      منتج {index + 1}
                    </CardTitle>
                  </div>
                  <div className="flex items-center gap-2">
                    {form.watch("products")?.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const currentProducts = form.getValues("products") || [];
                          form.setValue("products", currentProducts.filter((_, i) => i !== index));
                        }}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4 ml-1" />
                        حذف
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6" dir="rtl">
                {/* الصف الأول: البيانات الأساسية */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  {/* 1. اسم المنتج */}
                  <div>
                    <Label htmlFor={`products.${index}.productLabel`} className="text-right block mb-2">
                      اسم المنتج *
                    </Label>
                    <Input
                      {...form.register(`products.${index}.productLabel`)}
                      placeholder="مثال: تجاري، سكني، جراج"
                      className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50"
                    />
                  </div>

                  {/* 2. المساحة */}
                  <div>
                    <Label htmlFor={`products.${index}.area`} className="text-right block mb-2">
                      المساحة (متر مربع) *
                    </Label>
                    <Input
                      type="number"
                      step="0.01"
                      {...form.register(`products.${index}.area`, { valueAsNumber: true })}
                      placeholder="100.00"
                      className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50"
                    />
                  </div>

                  {/* 3. سعر المتر */}
                  <div>
                    <Label htmlFor={`products.${index}.meterPrice`} className="text-right block mb-2">
                      سعر المتر (ج.م) *
                    </Label>
                    <Input
                      type="number"
                      step="0.01"
                      {...form.register(`products.${index}.meterPrice`, { valueAsNumber: true })}
                      placeholder="1000.00"
                      className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50"
                    />
                  </div>
                </div>

                {/* الصف الثاني: الزيادة السنوية والضريبة ونوع الفوترة */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* 4. الزيادة السنوية */}
                  <div>
                    <Label className="text-right block mb-2">
                      الزيادة السنوية
                    </Label>
                    <div className="flex items-center space-x-reverse space-x-2 h-10 bg-gray-50 px-3 py-2 rounded-md border">
                      <Checkbox
                        id={`products.${index}.hasAnnualIncrease`}
                        checked={form.watch(`products.${index}.hasAnnualIncrease`) || false}
                        onCheckedChange={(checked) => form.setValue(`products.${index}.hasAnnualIncrease`, !!checked)}
                      />
                      <Label htmlFor={`products.${index}.hasAnnualIncrease`} className="text-sm font-medium text-gray-700 cursor-pointer">
                        يوجد زيادة سنوية
                      </Label>
                    </div>
                  </div>

                  {/* 5. الضريبة */}
                  <div>
                    <Label className="text-right block mb-2">
                      الضريبة
                    </Label>
                    <div className="flex items-center space-x-reverse space-x-2 h-10 bg-gray-50 px-3 py-2 rounded-md border">
                      <Checkbox
                        id={`products.${index}.taxInfo`}
                        checked={form.watch(`products.${index}.taxInfo`)}
                        onCheckedChange={(checked) => form.setValue(`products.${index}.taxInfo`, !!checked)}
                      />
                      <Label htmlFor={`products.${index}.taxInfo`} className="text-sm font-medium text-gray-700 cursor-pointer">
                        خاضع للضريبة
                      </Label>
                    </div>
                  </div>

                  {/* 6. نوع الفوترة */}
                  <div>
                    <Label htmlFor={`products.${index}.billingType`} className="text-right block mb-2">
                      نوع الفوترة *
                    </Label>
                    <Select
                      value={form.watch(`products.${index}.billingType`) || ""}
                      onValueChange={(value) => form.setValue(`products.${index}.billingType`, value)}
                    >
                      <SelectTrigger className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50">
                        <SelectValue placeholder="اختر نوع الفوترة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="شهري">شهري</SelectItem>
                        <SelectItem value="ربع سنوي">ربع سنوي</SelectItem>
                        <SelectItem value="نصف سنوي">نصف سنوي</SelectItem>
                        <SelectItem value="سنوي">سنوي</SelectItem>
                        <SelectItem value="غير منتظم">غير منتظم</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* تفاصيل الزيادة السنوية */}
                {form.watch(`products.${index}.hasAnnualIncrease`) && (
                  <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor={`products.${index}.increaseStartYear`} className="text-right block mb-2">
                          سنة بداية الزيادة *
                        </Label>
                        <Input
                          type="number"
                          min="1"
                          max="50"
                          {...form.register(`products.${index}.increaseStartYear`, { valueAsNumber: true })}
                          placeholder="2"
                          className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50"
                        />
                      </div>

                      <div>
                        <Label htmlFor={`products.${index}.increaseType`} className="text-right block mb-2">
                          نوع الزيادة *
                        </Label>
                        <Select
                          value={form.watch(`products.${index}.increaseType`) || ""}
                          onValueChange={(value) => form.setValue(`products.${index}.increaseType`, value)}
                        >
                          <SelectTrigger className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50">
                            <SelectValue placeholder="اختر نوع الزيادة" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="نسبة مئوية">نسبة مئوية</SelectItem>
                            <SelectItem value="مبلغ ثابت">مبلغ ثابت</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor={`products.${index}.increaseValue`} className="text-right block mb-2">
                          قيمة الزيادة *
                        </Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          {...form.register(`products.${index}.increaseValue`, { valueAsNumber: true })}
                          placeholder="5.00"
                          className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* تفاصيل الضريبة */}
                {form.watch(`products.${index}.taxInfo`) && (
                  <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor={`products.${index}.taxRate`} className="text-right block mb-2">
                          معدل الضريبة (%) *
                        </Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          {...form.register(`products.${index}.taxRate`, { valueAsNumber: true })}
                          placeholder="15.00"
                          className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* الصف الثالث: التواريخ والمدة */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* 7. تاريخ تفعيل المنتج */}
                  <div>
                    <Label htmlFor={`products.${index}.activationDate`} className="text-right block mb-2">
                      تاريخ تفعيل المنتج {form.watch("contractStatus") === "غير نشط" ? "(اختياري)" : "*"}
                    </Label>
                    <SimpleDateInput
                      value={form.watch(`products.${index}.activationDate`) || ""}
                      onChange={(value) => {
                        form.setValue(`products.${index}.activationDate`, value);
                        const durationYears = form.watch(`products.${index}.accountingDuration`);

                        if (value && durationYears > 0) {
                          // استخدام الدالة الموحدة لحساب تاريخ الانتهاء
                          const endDate = calculateEndDateFromDuration(value, durationYears);
                          form.setValue(`products.${index}.endDate`, endDate);
                        }
                      }}
                      placeholder={form.watch("contractStatus") === "غير نشط" ? "سيتم استخدام تاريخ البداية" : "مثال: 07/05/2025"}
                      className={`text-right border-2 focus:border-yellow-500 ${
                        form.watch("contractStatus") === "غير نشط"
                          ? "border-gray-300 bg-gray-50"
                          : "border-yellow-400 bg-yellow-50"
                      }`}
                    />
                  </div>

                  {/* 8. تاريخ انتهاء المنتج */}
                  <div>
                    <Label htmlFor={`products.${index}.endDate`} className="text-right block mb-2">
                      تاريخ انتهاء المنتج *
                    </Label>
                    <SimpleDateInput
                      value={form.watch(`products.${index}.endDate`) || ""}
                      onChange={(value) => {
                        form.setValue(`products.${index}.endDate`, value);
                        const activationDate = form.watch(`products.${index}.activationDate`);

                        if (activationDate && value) {
                          // استخدام الدالة الموحدة لحساب المدة
                          const duration = calculateDurationInYears(activationDate, value);
                          form.setValue(`products.${index}.accountingDuration`, duration);
                        }
                      }}
                      placeholder="مثال: 07/05/2027"
                      className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50"
                    />
                  </div>

                  {/* 9. المدة */}
                  <div>
                    <Label htmlFor={`products.${index}.accountingDuration`} className="text-right block mb-2">
                      مدة المنتج (بالسنوات) *
                    </Label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      {...form.register(`products.${index}.accountingDuration`, { valueAsNumber: true })}
                      placeholder="1.00"
                      className="text-right border-2 border-yellow-400 focus:border-yellow-500 bg-yellow-50"
                      onChange={(e) => {
                        const durationYears = parseFloat(e.target.value);
                        const activationDate = form.watch(`products.${index}.activationDate`);

                        if (activationDate && durationYears > 0) {
                          // استخدام الدالة الموحدة لحساب تاريخ الانتهاء
                          const endDate = calculateEndDateFromDuration(activationDate, durationYears);
                          form.setValue(`products.${index}.endDate`, endDate);
                        }
                      }}
                    />
                  </div>
                </div>

                {/* المدة التفصيلية */}
                {form.watch(`products.${index}.activationDate`) && form.watch(`products.${index}.endDate`) && (
                  <div className="mt-2 p-3 bg-blue-50 rounded-lg text-sm text-blue-700 text-center">
                    {(() => {
                      const startDateStr = form.watch(`products.${index}.activationDate`);
                      const endDateStr = form.watch(`products.${index}.endDate`);

                      if (startDateStr && endDateStr) {
                        // استخدام الدالة الموحدة لتنسيق المدة
                        return `المدة التفصيلية: ${formatDetailedDuration(startDateStr, endDateStr)}`;
                      }
                      return '';
                    })()}
                  </div>
                )}

                {/* إعدادات الفوترة غير المنتظمة - بدون تكرار الحقول الأساسية */}
                {(() => {
                  // التحقق من وجود أي منتج يستخدم فواتير غير منتظمة
                  const products = form.watch("products") || [];
                  const hasIrregularBilling = products.some(product => product.billingType === "غير منتظم");

                  if (!hasIrregularBilling) return null;

                  return (
                    <div className="border-t border-gray-200 pt-6">
                      <h4 className="text-lg font-semibold text-orange-800 mb-4 text-right">إعدادات الفوترة غير المنتظمة</h4>
                      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                        <p className="text-orange-700 text-sm mb-4">
                          تم اكتشاف منتجات تستخدم فوترة غير منتظمة. يرجى تكوين الإعدادات أدناه لكل منتج.
                        </p>

                        {/* عرض إعدادات كل منتج يستخدم فوترة غير منتظمة */}
                        {products.map((product, productIndex) =>
                          product.billingType === "غير منتظم" && (
                            <div key={productIndex} className="mb-6 p-4 bg-white rounded-lg border border-orange-300">
                              <h6 className="text-md font-semibold text-orange-800 mb-4 text-right">
                                {product.productLabel || `المنتج ${productIndex + 1}`}
                              </h6>

                              {/* نوع الإعداد */}
                              <div className="mb-4">
                                <Label className="text-right block mb-2">
                                  طريقة تحديد الفترات
                                </Label>
                                <Select
                                  value={form.watch(`products.${productIndex}.customPaymentType`) || ""}
                                  onValueChange={(value) => {
                                    form.setValue(`products.${productIndex}.customPaymentType`, value);
                                    // Reset other fields when changing type
                                    form.setValue(`products.${productIndex}.totalInstallments`, "");
                                    form.setValue(`products.${productIndex}.monthsBetweenPayments`, "");
                                    form.setValue(`products.${productIndex}.customIntervals`, []);
                                  }}
                                >
                                  <SelectTrigger className="text-right border-2 border-orange-400 focus:border-orange-500 bg-orange-50">
                                    <SelectValue placeholder="اختر طريقة تحديد الفترات" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="uniform">فترات منتظمة (عدد الدفعات + الفترة بينها)</SelectItem>
                                    <SelectItem value="custom">فترات مخصصة (تحديد كل فترة على حدة)</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>

                              {/* الفترات المنتظمة */}
                              {form.watch(`products.${productIndex}.customPaymentType`) === "uniform" && (
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div>
                                    <Label htmlFor={`products.${productIndex}.totalInstallments`} className="text-right block mb-2">
                                      إجمالي عدد الدفعات *
                                    </Label>
                                    <Input
                                      type="number"
                                      min="1"
                                      {...form.register(`products.${productIndex}.totalInstallments`, { valueAsNumber: true })}
                                      placeholder="مثال: 6"
                                      className="text-right border-2 border-orange-400 focus:border-orange-500 bg-orange-50"
                                    />
                                  </div>
                                  <div>
                                    <Label htmlFor={`products.${productIndex}.monthsBetweenPayments`} className="text-right block mb-2">
                                      الفترة بين الدفعات (بالأشهر) *
                                    </Label>
                                    <Input
                                      type="number"
                                      min="1"
                                      {...form.register(`products.${productIndex}.monthsBetweenPayments`, { valueAsNumber: true })}
                                      placeholder="مثال: 2"
                                      className="text-right border-2 border-orange-400 focus:border-orange-500 bg-orange-50"
                                    />
                                  </div>
                                </div>
                              )}

                              {/* الفترات المخصصة */}
                              {form.watch(`products.${productIndex}.customPaymentType`) === "custom" && (
                                <div>
                                  <Label className="text-right block mb-2">
                                    الفترات بين الدفعات (بالأشهر)
                                  </Label>
                                  <div className="space-y-2">
                                    {(form.watch(`products.${productIndex}.customIntervals`) || []).map((interval, intervalIndex) => (
                                      <div key={intervalIndex} className="flex items-center gap-2" dir="rtl">
                                        <span className="text-sm text-gray-600 min-w-[120px]">
                                          من الدفعة {intervalIndex + 1} إلى {intervalIndex + 2}:
                                        </span>
                                        <Input
                                          type="number"
                                          min="1"
                                          value={interval}
                                          onChange={(e) => {
                                            const intervals = [...(form.watch(`products.${productIndex}.customIntervals`) || [])];
                                            intervals[intervalIndex] = parseInt(e.target.value) || 1;
                                            form.setValue(`products.${productIndex}.customIntervals`, intervals);
                                          }}
                                          placeholder="عدد الأشهر"
                                          className="text-right border-2 border-orange-400 focus:border-orange-500 bg-orange-50 flex-1"
                                        />
                                        <span className="text-sm text-gray-600">شهر</span>
                                        <Button
                                          type="button"
                                          variant="outline"
                                          size="sm"
                                          onClick={() => {
                                            const intervals = [...(form.watch(`products.${productIndex}.customIntervals`) || [])];
                                            intervals.splice(intervalIndex, 1);
                                            form.setValue(`products.${productIndex}.customIntervals`, intervals);
                                          }}
                                          className="text-red-600 hover:text-red-700"
                                        >
                                          <Trash2 className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    ))}

                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      onClick={() => {
                                        const intervals = [...(form.watch(`products.${productIndex}.customIntervals`) || [])];
                                        intervals.push(1);
                                        form.setValue(`products.${productIndex}.customIntervals`, intervals);
                                      }}
                                      className="text-orange-600 hover:text-orange-700"
                                    >
                                      <Plus className="h-4 w-4 ml-1" />
                                      إضافة فترة جديدة
                                    </Button>
                                  </div>

                                  {(form.watch(`products.${productIndex}.customIntervals`) || []).length > 0 && (
                                    <div className="mt-3 p-3 bg-orange-100 rounded-lg text-sm text-orange-700">
                                      <strong>ملخص الدفعات:</strong>
                                      <br />
                                      إجمالي عدد الدفعات: {(form.watch(`products.${productIndex}.customIntervals`) || []).length + 1}
                                      <br />
                                      إجمالي المدة: {(form.watch(`products.${productIndex}.customIntervals`) || []).reduce((sum, interval) => sum + interval, 0)} شهر
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  );
                })()}


              </CardContent>
            </Card>
          ))}

          {/* Step 4: الالتزامات المالية */}
          {/* التأمين النهائي */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">التأمين النهائي</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="finalInsuranceRate">نسبة التأمين النهائي (%)</Label>
                  <Input
                    id="finalInsuranceRate"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    {...form.register("finalInsuranceRate", { valueAsNumber: true })}
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <Label htmlFor="finalInsuranceAmount">قيمة التأمين النهائي (محتسبة تلقائياً)</Label>
                  <Input
                    id="finalInsuranceAmount"
                    type="number"
                    step="0.01"
                    value={(calculationResults?.insuranceAmount || 0).toFixed(2)}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* الدفعة المقدمة */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">الدفعة المقدمة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="advancePaymentMonths">عدد شهور الدفعة المقدمة</Label>
                  <Input
                    id="advancePaymentMonths"
                    type="number"
                    min="0"
                    {...form.register("advancePaymentMonths", { valueAsNumber: true })}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="advancePaymentAmount">قيمة الدفعة المقدمة (محتسبة تلقائياً)</Label>
                  <Input
                    id="advancePaymentAmount"
                    type="number"
                    step="0.01"
                    value={(calculationResults?.advancePaymentAmount || 0).toFixed(2)}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>
            </CardContent>
          </Card>



          {/* الغرامات والرسوم الأخرى */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">الغرامات والرسوم الأخرى</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* غرامة التأخير */}
              <div>
                <h4 className="text-md font-medium mb-3">غرامة التأخير</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="lateFeeType">نوع غرامة التأخير</Label>
                    <Select
                      value={form.watch("lateFeeType") || ""}
                      onValueChange={(value) => form.setValue("lateFeeType", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر نوع الغرامة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="مبلغ ثابت">مبلغ ثابت</SelectItem>
                        <SelectItem value="نسبة مئوية">نسبة مئوية</SelectItem>
                        <SelectItem value="نسبة يومية">نسبة يومية</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="lateFeeValue">
                      قيمة غرامة التأخير {form.watch("lateFeeType") === 'مبلغ ثابت' ? '(ج.م)' : '(%)'}
                    </Label>
                    <Input
                      id="lateFeeValue"
                      type="number"
                      step="0.01"
                      {...form.register("lateFeeValue", { valueAsNumber: true })}
                      placeholder="0.00"
                    />
                  </div>
                  <div>
                    <Label htmlFor="gracePeriodDays">فترة سماح السداد (بالأيام)</Label>
                    <Input
                      id="gracePeriodDays"
                      type="number"
                      min="0"
                      {...form.register("gracePeriodDays", { valueAsNumber: true })}
                      placeholder="0"
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* غرامة ارتداد الشيكات */}
              <div>
                <h4 className="text-md font-medium mb-3">غرامة ارتداد الشيكات</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="bouncedCheckFeeType">نوع غرامة ارتداد الشيكات</Label>
                    <Select
                      value={form.watch("bouncedCheckFeeType") || ""}
                      onValueChange={(value) => form.setValue("bouncedCheckFeeType", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر نوع الغرامة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="مبلغ ثابت">مبلغ ثابت</SelectItem>
                        <SelectItem value="نسبة مئوية">نسبة مئوية</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="bouncedCheckFeeValue">
                      قيمة غرامة ارتداد الشيكات {form.watch("bouncedCheckFeeType") === 'مبلغ ثابت' ? '(ج.م)' : '(%)'}
                    </Label>
                    <Input
                      id="bouncedCheckFeeValue"
                      type="number"
                      step="0.01"
                      {...form.register("bouncedCheckFeeValue", { valueAsNumber: true })}
                      placeholder="0.00"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* الرسوم الإضافية */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">الرسوم الإضافية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-600">يمكنك إضافة رسوم إضافية مثل رسوم الصيانة، الرسوم الإدارية، أو أي رسوم أخرى</p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const currentFees = form.getValues("additionalFees") || [];
                    form.setValue("additionalFees", [
                      ...currentFees,
                      {
                        name: "",
                        amount: 0,
                        dueDate: ""
                      }
                    ]);
                  }}
                  className="text-blue-600 hover:text-blue-700"
                >
                  <Plus className="h-4 w-4 ml-1" />
                  إضافة رسوم
                </Button>
              </div>

              {form.watch("additionalFees")?.map((fee, index) => (
                <div key={index} className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex justify-between items-center mb-3">
                    <h5 className="font-medium text-gray-800">رسوم إضافية {index + 1}</h5>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentFees = form.getValues("additionalFees") || [];
                        const updatedFees = currentFees.filter((_, i) => i !== index);
                        form.setValue("additionalFees", updatedFees);
                      }}
                      className="text-red-600 hover:text-red-700 h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor={`additionalFees.${index}.name`}>نوع الرسوم</Label>
                      <Input
                        {...form.register(`additionalFees.${index}.name`)}
                        placeholder="مثل: رسوم صيانة، رسوم إدارية"
                      />
                    </div>
                    <div>
                      <Label htmlFor={`additionalFees.${index}.amount`}>قيمة الرسوم (ج.م)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        {...form.register(`additionalFees.${index}.amount`, { valueAsNumber: true })}
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <Label htmlFor={`additionalFees.${index}.dueDate`}>تاريخ الاستحقاق</Label>
                      <Input
                        type="date"
                        {...form.register(`additionalFees.${index}.dueDate`)}
                      />
                    </div>
                  </div>
                </div>
              ))}

              {(!form.watch("additionalFees") || form.watch("additionalFees")?.length === 0) && (
                <div className="text-center py-8 text-gray-500">
                  <p>لا توجد رسوم إضافية مضافة</p>
                  <p className="text-sm">اضغط على "إضافة رسوم" لإضافة رسوم جديدة</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* ملاحظات وبيانات إضافية */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">ملاحظات وبيانات إضافية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* ملاحظات مهمة */}
                <div>
                  <Label htmlFor="importantNotes">ملاحظات مهمة</Label>
                  <Textarea
                    id="importantNotes"
                    {...form.register("importantNotes")}
                    placeholder="أدخل أي ملاحظات مهمة حول العقد..."
                    className="min-h-[100px]"
                    rows={4}
                  />
                </div>

                {/* ملاحظات عامة */}
                <div>
                  <Label htmlFor="notes">ملاحظات عامة</Label>
                  <Textarea
                    id="notes"
                    {...form.register("notes")}
                    placeholder="أدخل أي ملاحظات عامة حول العقد..."
                    className="min-h-[100px]"
                    rows={4}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* نتائج الحسابات */}
          {calculationResults && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  الملخص المالي
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Main Financial Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200">
                    <div className="text-2xl font-bold text-blue-600 mb-1">
                      {calculationResults.totalValue?.toLocaleString()} ج.م
                    </div>
                    <div className="text-sm text-blue-800 font-medium">إجمالي قيمة العقد</div>
                  </div>

                  <div className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-lg border border-green-200">
                    <div className="text-2xl font-bold text-green-600 mb-1">
                      {calculationResults.insuranceAmount?.toLocaleString()} ج.م
                    </div>
                    <div className="text-sm text-green-800 font-medium">مبلغ التأمين النهائي</div>
                  </div>
                </div>

                {/* Detailed Statistics */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-lg font-bold text-purple-600">
                      {calculationResults.summary?.totalInstallments || 0}
                    </div>
                    <div className="text-sm text-gray-600">عدد الدفعات</div>
                  </div>

                  <div className="text-center">
                    <div className="text-lg font-bold">
                      {calculationResults.summary?.totalProducts || 0}
                    </div>
                    <div className="text-sm text-gray-600">عدد المنتجات</div>
                  </div>

                  <div className="text-center">
                    <div className="text-lg font-bold">
                      {calculationResults.summary?.totalArea?.toLocaleString() || 0} م²
                    </div>
                    <div className="text-sm text-gray-600">إجمالي المساحة</div>
                  </div>

                  <div className="text-center">
                    <div className="text-lg font-bold">
                      {calculationResults.summary?.totalYears || 0} سنة
                    </div>
                    <div className="text-sm text-gray-600">مدة العقد</div>
                  </div>
                </div>

                {/* Yearly Amounts Breakdown */}
                {calculationResults.yearlyAmounts && calculationResults.yearlyAmounts.length > 0 && (
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold mb-4 text-gray-800">تفصيل المبالغ السنوية التعاقدية</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {calculationResults.yearlyAmounts.map((yearData, index) => (
                        <div key={index} className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-4 rounded-lg border border-indigo-200">
                          <div className="text-center mb-3">
                            <div className="text-lg font-bold text-indigo-600 mb-2">
                              السنة التعاقدية {yearData.year}
                            </div>
                            <div className="text-xl font-bold text-indigo-800 mb-1">
                              {yearData.totalAmount?.toLocaleString()} ج.م
                            </div>
                            <div className="text-sm text-indigo-600">
                              متوسط شهري: {yearData.monthlyAmount?.toLocaleString()} ج.م
                            </div>
                          </div>

                          {/* Product details for this year */}
                          {yearData.products && yearData.products.length > 0 && (
                            <div className="border-t border-indigo-200 pt-3">
                              <div className="text-xs text-indigo-700 font-medium mb-2">تفاصيل الفوترة:</div>
                              {yearData.products.map((product, productIndex) => (
                                <div key={productIndex} className="text-xs text-indigo-600 mb-1">
                                  <div className="font-medium">{product.productLabel}</div>
                                  <div>
                                    {product.installmentAmount.toLocaleString()} ج.م / {product.billingType}
                                    ({product.installmentsPerYear} دفعة/سنة)
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Installments Schedule */}
                {calculationResults.yearlyAmounts && calculationResults.yearlyAmounts.length > 0 && (
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold mb-4 text-gray-800">جدول الأقساط</h4>
                    <div className="bg-white rounded-lg border overflow-hidden">
                      <table className="w-full">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">السنة</th>
                            <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">المنتج</th>
                            <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">نوع الفوترة</th>
                            <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">مبلغ القسط</th>
                            <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">عدد الأقساط/سنة</th>
                            <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">إجمالي السنة</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          {calculationResults.yearlyAmounts.map((yearData, yearIndex) => (
                            yearData.products?.map((product, productIndex) => (
                              <tr key={`${yearIndex}-${productIndex}`} className="hover:bg-gray-50">
                                <td className="px-4 py-3 text-sm font-medium text-gray-900">
                                  {yearData.year}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900">
                                  {product.productLabel}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-600">
                                  {product.billingType}
                                </td>
                                <td className="px-4 py-3 text-sm font-medium text-green-600">
                                  {product.installmentAmount.toLocaleString()} ج.م
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-600">
                                  {product.installmentsPerYear}
                                </td>
                                <td className="px-4 py-3 text-sm font-medium text-blue-600">
                                  {product.annualValue.toLocaleString()} ج.م
                                </td>
                              </tr>
                            )) || []
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* Calendar Year Revenues */}
                {calculationResults.calendarRevenues && calculationResults.calendarRevenues.length > 0 && (
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold mb-4 text-gray-800">الإيرادات السنوية الميلادية</h4>
                    <div className="bg-white rounded-lg border overflow-hidden">
                      <table className="w-full">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">السنة الميلادية</th>
                            <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">إجمالي الإيراد</th>
                            <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">التفاصيل</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          {calculationResults.calendarRevenues.map((revenue, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="px-4 py-3 text-sm font-medium text-gray-900">
                                {revenue.calendarYear}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-900">
                                {revenue.totalRevenue.toLocaleString()} ج.م
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-600">
                                {revenue.details.map((detail, detailIndex) => (
                                  <div key={detailIndex} className="mb-1">
                                    السنة التعاقدية {detail.contractYear}: من {detail.overlapStart} إلى {detail.overlapEnd}
                                    ({detail.overlapDays} يوم) = {detail.proportionalRevenue.toLocaleString()} ج.م
                                  </div>
                                ))}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* Calculation Breakdown */}
                {calculationResults.breakdown && calculationResults.breakdown.length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-medium mb-4 text-center">
                      تفصيل الحسابات
                    </h4>
                    <div className="space-y-2 text-sm">
                      {calculationResults.breakdown.map((item: any, index: number) => (
                        <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                          <span className="font-medium">{item.description}</span>
                          <div className="text-right">
                            <span className="font-bold text-green-600">
                              {item.amount?.toLocaleString()} ج.م
                            </span>
                            {item.percentage && (
                              <div className="text-xs text-gray-500">
                                ({item.percentage.toFixed(1)}%)
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Summary Totals */}
                <div className="mt-6 pt-4 border-t">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-xl font-bold text-blue-600">
                        {calculationResults.summary?.firstYearTotal?.toLocaleString()} ج.م
                      </div>
                      <div className="text-sm text-blue-800">السنة الأولى</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-xl font-bold text-green-600">
                        {calculationResults.summary?.lastYearTotal?.toLocaleString()} ج.م
                      </div>
                      <div className="text-sm text-green-800">السنة الأخيرة</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}



          {/* Contract Preview Modal */}
          <ContractPreviewModal
            isOpen={showPreview}
            onClose={() => setShowPreview(false)}
            contractData={previewData || form.getValues()}
            calculationResults={calculationResults}
          />
        </form>
      </main>

      {/* Footer with Action Buttons */}
      <div className="bg-white border-t shadow-lg">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Footer Title */}
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  إنشاء عقد جديد
                </h2>
                <p className="text-sm text-gray-600">
                  اختر الإجراء المطلوب
                </p>
              </div>
            </div>

            {/* Footer Action Buttons - Same as Header */}
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setLocation('/contracts')}
                disabled={isNavigating}
              >
                <X className="h-4 w-4 ml-1" />
                إلغاء
              </Button>

              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handlePreview}
              >
                <Eye className="h-4 w-4 ml-1" />
                معاينة
              </Button>

              <Button
                type="button"
                size="sm"
                onClick={() => {
                  console.log('🔥 Footer Save button clicked');
                  const formData = form.getValues();
                  onSubmit(formData);
                }}
                disabled={isNavigating}
                className="bg-green-600 hover:bg-green-700"
              >
                {isNavigating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-1"></div>
                    جاري الحفظ...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 ml-1" />
                    حفظ العقد
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
