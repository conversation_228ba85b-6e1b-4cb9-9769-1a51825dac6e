{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "vite --host --port 5173", "dev:fast": "vite --host --port 5173 --force", "dev:server": "node server.mjs", "dev:server:old": "node server.cjs", "dev:full": "concurrently \"node server.mjs\" \"vite --host --port 5173 --force\"", "start": "vite --host --port 5173", "start:server": "node --max-old-space-size=4096 server.mjs", "start:server:old": "node --max-old-space-size=4096 server.cjs", "start:full": "concurrently \"node --max-old-space-size=4096 server.mjs\" \"vite --host --port 5173\"", "start:full:esm": "concurrently \"node --max-old-space-size=4096 server.mjs\" \"vite --host --port 5173\"", "build": "vite build", "check": "npx tsc", "db:reset": "node -e \"const fs=require('fs'); const path='./contract-app.sqlite'; if(fs.existsSync(path)) {fs.unlinkSync(path); console.log('✅ تم مسح قاعدة البيانات');} else {console.log('ℹ️ لا توجد قاعدة بيانات للمسح');}\""}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-query": "^5.81.2", "@types/moment-hijri": "^2.1.4", "@types/react-datepicker": "^6.2.0", "better-sqlite3": "^12.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "compression": "^1.8.1", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^17.2.0", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.1", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "helmet": "^8.1.0", "immer": "^10.1.1", "input-otp": "^1.4.2", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "moment-hijri": "^3.0.0", "next-themes": "^0.4.6", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-datepicker": "^8.4.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.58.1", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.6.2", "recharts": "^2.15.2", "sqlite3": "^5.1.7", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "wouter": "^3.7.1", "ws": "^8.18.0", "zod": "^3.25.67", "zod-validation-error": "^3.4.0", "zustand": "^5.0.6"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.7", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.20", "concurrently": "^9.2.0", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.5", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5.6.3", "vite": "^5.4.19"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}