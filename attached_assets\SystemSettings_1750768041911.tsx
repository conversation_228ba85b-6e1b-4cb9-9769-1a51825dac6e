import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "../components/ui/form";
import { Input } from "../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select";
import { Textarea } from "../components/ui/textarea";
import { useToast } from "../hooks/use-toast";
import { apiRequest, api } from "../lib/queryClient";
import { type Settings } from "@shared/schema";
import * as z from "zod";

// Settings form schema
const settingsSchema = z.object({
  companyName: z.string().min(1, "اسم الشركة مطلوب"),
  programName: z.string().min(1, "اسم البرنامج مطلوب"),
  aboutProgram: z.string().optional(),
  companyLogo: z.string().optional(),
  language: z.enum(["ar", "en"]),
  dateFormat: z.string(),
  currencySymbol: z.string(),
  fontFamily: z.string(),
  workDays: z.string(),
  darkMode: z.boolean(),
});
import { cn } from "../lib/utils";
import {
  Settings as SettingsIcon,
  Building,
  Globe,
  Hash,
  Bell,
  Upload,
  X,
  Save,
  Edit,
  Plus,
  Trash2
} from "lucide-react";

// Reference Data Tab Component
const ReferenceDataTab = ({ lang }: { lang: "ar" | "en" }) => {
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [listName, setListName] = useState("");
  const [listDataText, setListDataText] = useState("");

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch reference lists
  const { data: referenceLists, isLoading } = useQuery({
    queryKey: ["/api/reference-lists"],
    queryFn: () => apiRequest("/api/reference-lists"),
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: async (data: { listName: string; listData: string[] }) => {
      return await api.post("/api/reference-lists", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-lists"] });
      toast({
        title: lang === "ar" ? "تم إنشاء القائمة بنجاح" : "List created successfully",
      });
      handleCancel();
    },
    onError: (error: Error) => {
      toast({
        title: lang === "ar" ? "خطأ في إنشاء القائمة" : "Error creating list",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: { listName: string; listData: string[] } }) => {
      return await api.put(`/api/reference-lists/${id}`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-lists"] });
      toast({
        title: lang === "ar" ? "تم تحديث القائمة بنجاح" : "List updated successfully",
      });
      handleCancel();
    },
    onError: (error: Error) => {
      toast({
        title: lang === "ar" ? "خطأ في تحديث القائمة" : "Error updating list",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      return await api.delete(`/api/reference-lists/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reference-lists"] });
      toast({
        title: lang === "ar" ? "تم حذف القائمة بنجاح" : "List deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: lang === "ar" ? "خطأ في حذف القائمة" : "Error deleting list",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Handle form submission
  const handleSubmit = () => {
    if (!listName.trim()) {
      toast({
        title: lang === "ar" ? "خطأ" : "Error",
        description: lang === "ar" ? "اسم القائمة مطلوب" : "List name is required",
        variant: "destructive",
      });
      return;
    }

    if (!listDataText.trim()) {
      toast({
        title: lang === "ar" ? "خطأ" : "Error",
        description: lang === "ar" ? "بيانات القائمة مطلوبة" : "List data is required",
        variant: "destructive",
      });
      return;
    }

    // Split data by "|" and filter empty values
    const listData = listDataText.split("|").map(item => item.trim()).filter(item => item !== "");

    if (listData.length === 0) {
      toast({
        title: lang === "ar" ? "خطأ" : "Error",
        description: lang === "ar" ? "يجب إدخال بيانات صحيحة للقائمة" : "Valid list data is required",
        variant: "destructive",
      });
      return;
    }

    const submitData = { listName: listName.trim(), listData };

    if (editingId) {
      updateMutation.mutate({ id: editingId, data: submitData });
    } else {
      createMutation.mutate(submitData);
    }
  };

  // Handle edit
  const handleEdit = (list: any) => {
    setEditingId(list.id);
    setIsAdding(true);
    setListName(list.listName);
    setListDataText(list.listData.join(" | "));
  };

  // Handle cancel
  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    setListName("");
    setListDataText("");
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{lang === "ar" ? "البيانات المرجعية" : "Reference Data"}</CardTitle>
          <Button onClick={() => setIsAdding(true)} disabled={isAdding}>
            <Plus className="h-4 w-4 mr-2" />
            {lang === "ar" ? "إضافة قائمة" : "Add List"}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add/Edit Form */}
        {isAdding && (
          <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
            <h3 className="text-lg font-medium mb-4">
              {editingId
                ? (lang === "ar" ? "تعديل القائمة" : "Edit List")
                : (lang === "ar" ? "إضافة قائمة جديدة" : "Add New List")
              }
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  {lang === "ar" ? "اسم القائمة *" : "List Name *"}
                </label>
                <Input
                  value={listName}
                  onChange={(e) => setListName(e.target.value)}
                  placeholder={lang === "ar" ? "أدخل اسم القائمة" : "Enter list name"}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  {lang === "ar" ? "بيانات القائمة *" : "List Data *"}
                </label>
                <textarea
                  value={listDataText}
                  onChange={(e) => setListDataText(e.target.value)}
                  placeholder={lang === "ar"
                    ? "أدخل البيانات مفصولة بـ | مثال: عنصر1 | عنصر2 | عنصر3"
                    : "Enter data separated by | example: item1 | item2 | item3"
                  }
                  className="w-full p-2 border rounded-md min-h-[100px] resize-vertical"
                  rows={4}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {lang === "ar"
                    ? "استخدم الرمز | للفصل بين العناصر"
                    : "Use | to separate items"
                  }
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={handleSubmit}
                  disabled={createMutation.isPending || updateMutation.isPending}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {editingId
                    ? (lang === "ar" ? "تحديث" : "Update")
                    : (lang === "ar" ? "حفظ" : "Save")
                  }
                </Button>
                <Button variant="outline" onClick={handleCancel}>
                  <X className="h-4 w-4 mr-2" />
                  {lang === "ar" ? "إلغاء" : "Cancel"}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Lists Display */}
        <div className="space-y-4">
          {referenceLists?.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {lang === "ar" ? "لا توجد قوائم مرجعية" : "No reference lists found"}
            </div>
          ) : (
            referenceLists?.map((list: any) => (
              <div key={list.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-lg font-medium">{list.listName}</h4>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(list)}
                      disabled={isAdding}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteMutation.mutate(list.id)}
                      disabled={deleteMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  {list.listData?.map((item: string, index: number) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm"
                    >
                      {item}
                    </span>
                  ))}
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const SystemSettings = () => {
  const [lang] = useState<"ar" | "en">((localStorage.getItem("lang") as "ar" | "en") || "ar");
  const [activeTab, setActiveTab] = useState("general");
  const [companyLogo, setCompanyLogo] = useState("");
  const [isGeneralEditing, setIsGeneralEditing] = useState(false);
  const [isCountryEditing, setIsCountryEditing] = useState(false);
  const [isNumberingEditing, setIsNumberingEditing] = useState(false);
  const [isNotificationEditing, setIsNotificationEditing] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Forms
  const generalForm = useForm<Partial<Settings>>({
    resolver: zodResolver(settingsSchema.partial()),
    defaultValues: {
      companyName: "",
      programName: "",
      companyRegNo: "",
      taxId: "",
      about: "",
    },
  });

  const countryForm = useForm<Partial<Settings>>({
    resolver: zodResolver(settingsSchema.partial()),
    defaultValues: {
      language: "ar",
      country: "مصر",
      currency: "EGP",
      currencySymbol: "ج.م",
      dateFormat: "DD/MM/YYYY",
      decimalPlaces: "2",
      workDays: [],
    },
  });

  const numberingForm = useForm<Partial<Settings>>({
    resolver: zodResolver(settingsSchema.partial()),
    defaultValues: {
      numberingFormat: "serial-year-unit",
    },
  });

  const notificationForm = useForm<Partial<Settings>>({
    resolver: zodResolver(settingsSchema.partial()),
    defaultValues: {
      notificationEmail: "",
    },
  });

  // Fetch settings
  const { data: settings, isLoading } = useQuery({
    queryKey: ["/api/settings"],
    queryFn: () => apiRequest("/api/settings"),
  });

  // Update forms when data loads
  useEffect(() => {
    if (settings) {
      generalForm.reset({
        companyName: settings.companyName || "",
        programName: settings.programName || "",
        companyRegNo: settings.companyRegNo || "",
        taxId: settings.taxId || "",
        about: settings.about || "",
      });

      countryForm.reset({
        language: settings.language || "ar",
        country: settings.country || "مصر",
        currency: settings.currency || "EGP",
        currencySymbol: settings.currencySymbol || "ج.م",
        dateFormat: settings.dateFormat || "DD/MM/YYYY",
        decimalPlaces: settings.decimalPlaces || "2",
        workDays: settings.workDays || [],
      });

      numberingForm.reset({
        numberingFormat: settings.numberingFormat || "serial-year-unit",
      });

      notificationForm.reset({
        notificationEmail: settings.notificationEmail || "",
      });

      setCompanyLogo(settings.companyLogo || "");

      // Update localStorage
      localStorage.setItem("companyName", settings.companyName || "");
      localStorage.setItem("programName", settings.programName || "");
      localStorage.setItem("about", settings.about || "");
      localStorage.setItem("companyLogo", settings.companyLogo || "");

      // Set editing states based on data existence
      const hasData = settings.companyName || settings.programName;
      setIsGeneralEditing(!hasData);
      setIsCountryEditing(!hasData);
      setIsNumberingEditing(!hasData);
      setIsNotificationEditing(!hasData);
    }
  }, [settings, generalForm, countryForm, numberingForm, notificationForm]);

  // Save mutations
  const saveSettingsMutation = useMutation({
    mutationFn: async (data: Partial<Settings>) => {
      return await apiRequest("/api/settings", {
        method: "POST",
        body: JSON.stringify({ ...data, companyLogo }),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/settings"] });
      
      // Update localStorage
      const formData = generalForm.getValues();
      localStorage.setItem("companyName", formData.companyName || "");
      localStorage.setItem("programName", formData.programName || "");
      localStorage.setItem("about", formData.about || "");
      localStorage.setItem("companyLogo", companyLogo || "");
      
      // Dispatch storage event
      window.dispatchEvent(new Event("storage"));
      
      toast({
        title: lang === "ar" ? "تم حفظ الإعدادات بنجاح" : "Settings saved successfully",
        description: lang === "ar" ? "تم تحديث جميع الإعدادات" : "All settings have been updated",
      });
    },
    onError: (error: Error) => {
      toast({
        title: lang === "ar" ? "خطأ في حفظ الإعدادات" : "Error saving settings",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Image compression
  const compressImage = (file: File, maxWidth: number = 800): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        let { width, height } = img;
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;
        ctx?.drawImage(img, 0, 0, width, height);
        
        const compressedDataUrl = canvas.toDataURL('image/png');
        resolve(compressedDataUrl);
      };

      img.src = URL.createObjectURL(file);
    });
  };

  // Handle logo upload
  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      if (!file.type.startsWith('image/')) {
        toast({
          title: lang === "ar" ? "خطأ في نوع الملف" : "Invalid file type",
          description: lang === "ar" ? "يرجى اختيار ملف صورة صحيح" : "Please select a valid image file",
          variant: "destructive",
        });
        return;
      }

      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: lang === "ar" ? "حجم الملف كبير" : "File too large",
          description: lang === "ar" ? "يرجى اختيار صورة أصغر من 5 ميجا" : "Please select an image smaller than 5MB",
          variant: "destructive",
        });
        return;
      }

      const compressedImage = await compressImage(file, 800);
      setCompanyLogo(compressedImage);
      
      toast({
        title: lang === "ar" ? "تم رفع اللوجو بنجاح" : "Logo uploaded successfully",
      });
    } catch (error) {
      toast({
        title: lang === "ar" ? "خطأ في معالجة الصورة" : "Error processing image",
        variant: "destructive",
      });
    }
  };

  // Handle form submissions
  const handleGeneralSave = (data: Partial<Settings>) => {
    saveSettingsMutation.mutate(data);
    setIsGeneralEditing(false);
  };

  const handleCountrySave = (data: Partial<Settings>) => {
    const generalData = generalForm.getValues();
    saveSettingsMutation.mutate({ ...generalData, ...data });
    setIsCountryEditing(false);
  };

  const handleNumberingSave = (data: Partial<Settings>) => {
    const generalData = generalForm.getValues();
    const countryData = countryForm.getValues();
    saveSettingsMutation.mutate({ ...generalData, ...countryData, ...data });
    setIsNumberingEditing(false);
  };

  const handleNotificationSave = (data: Partial<Settings>) => {
    const generalData = generalForm.getValues();
    const countryData = countryForm.getValues();
    const numberingData = numberingForm.getValues();
    saveSettingsMutation.mutate({ ...generalData, ...countryData, ...numberingData, ...data });
    setIsNotificationEditing(false);
  };

  // Data options
  const countries = [
    { label: "مصر", value: "مصر" },
    { label: "السعودية", value: "السعودية" },
    { label: "الإمارات", value: "الإمارات" },
    { label: "الكويت", value: "الكويت" },
    { label: "قطر", value: "قطر" },
    { label: "البحرين", value: "البحرين" },
    { label: "عمان", value: "عمان" },
  ];

  const currencies = [
    { label: "جنيه مصري (EGP)", value: "EGP", symbol: "ج.م" },
    { label: "ريال سعودي (SAR)", value: "SAR", symbol: "ر.س" },
    { label: "درهم إماراتي (AED)", value: "AED", symbol: "د.إ" },
    { label: "دينار كويتي (KWD)", value: "KWD", symbol: "د.ك" },
    { label: "ريال قطري (QAR)", value: "QAR", symbol: "ر.ق" },
    { label: "دينار بحريني (BHD)", value: "BHD", symbol: "د.ب" },
    { label: "ريال عماني (OMR)", value: "OMR", symbol: "ر.ع" },
  ];

  const dateFormats = [
    { label: "DD/MM/YYYY", value: "DD/MM/YYYY" },
    { label: "MM/DD/YYYY", value: "MM/DD/YYYY" },
    { label: "YYYY-MM-DD", value: "YYYY-MM-DD" },
    { label: "YYYY/MM/DD", value: "YYYY/MM/DD" },
  ];

  const languages = [
    { label: "العربية", value: "ar" },
    { label: "English", value: "en" },
  ];

  const workDaysOptions = [
    { label: lang === "ar" ? "الأحد" : "Sunday", value: "sunday" },
    { label: lang === "ar" ? "الاثنين" : "Monday", value: "monday" },
    { label: lang === "ar" ? "الثلاثاء" : "Tuesday", value: "tuesday" },
    { label: lang === "ar" ? "الأربعاء" : "Wednesday", value: "wednesday" },
    { label: lang === "ar" ? "الخميس" : "Thursday", value: "thursday" },
    { label: lang === "ar" ? "الجمعة" : "Friday", value: "friday" },
    { label: lang === "ar" ? "السبت" : "Saturday", value: "saturday" },
  ];

  const tabs = [
    {
      key: "general",
      label: lang === "ar" ? "تعريف الشركة" : "Company Settings",
      icon: Building,
    },
    {
      key: "country",
      label: lang === "ar" ? "البلد والعملة" : "Country & Currency",
      icon: Globe,
    },
    {
      key: "numbering",
      label: lang === "ar" ? "إعدادات الترقيم" : "Numbering Settings",
      icon: Hash,
    },
    {
      key: "notifications",
      label: lang === "ar" ? "إعدادات التنبيهات" : "Notification Settings",
      icon: Bell,
    },
    {
      key: "reference",
      label: lang === "ar" ? "البيانات المرجعية" : "Reference Data",
      icon: SettingsIcon,
    },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <div className="flex items-center space-x-reverse space-x-2">
              <SettingsIcon className="h-5 w-5 text-primary" />
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {lang === "ar" ? "إعدادات النظام" : "System Settings"}
              </h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-6">
          {/* Sidebar Navigation */}
          <div className="w-64 space-y-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <Button
                  key={tab.key}
                  variant={activeTab === tab.key ? "default" : "ghost"}
                  className={cn(
                    "w-full justify-start gap-3 h-12",
                    lang === "ar" ? "flex-row-reverse" : "flex-row",
                    activeTab === tab.key && "shadow-md"
                  )}
                  onClick={() => setActiveTab(tab.key)}
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </Button>
              );
            })}
          </div>

          {/* Content Area */}
          <div className="flex-1">
            {activeTab === "general" && (
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>{lang === "ar" ? "تعريف الشركة" : "Company Settings"}</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsGeneralEditing(!isGeneralEditing)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    {isGeneralEditing ? (lang === "ar" ? "إلغاء" : "Cancel") : (lang === "ar" ? "تعديل" : "Edit")}
                  </Button>
                </CardHeader>
                <CardContent>
                  <Form {...generalForm}>
                    <form onSubmit={generalForm.handleSubmit(handleGeneralSave)} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={generalForm.control}
                          name="companyName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{lang === "ar" ? "اسم الشركة *" : "Company Name *"}</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  disabled={!isGeneralEditing}
                                  placeholder={lang === "ar" ? "أدخل اسم الشركة" : "Enter company name"}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={generalForm.control}
                          name="programName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{lang === "ar" ? "اسم البرنامج *" : "Program Name *"}</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  disabled={!isGeneralEditing}
                                  placeholder={lang === "ar" ? "أدخل اسم البرنامج" : "Enter program name"}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={generalForm.control}
                          name="companyRegNo"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{lang === "ar" ? "رقم سجل الشركة" : "Company Registration Number"}</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  disabled={!isGeneralEditing}
                                  placeholder={lang === "ar" ? "أدخل رقم السجل" : "Enter registration number"}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={generalForm.control}
                          name="taxId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{lang === "ar" ? "رقم البطاقة الضريبية" : "Tax ID"}</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  disabled={!isGeneralEditing}
                                  placeholder={lang === "ar" ? "أدخل الرقم الضريبي" : "Enter tax ID"}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={generalForm.control}
                        name="about"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{lang === "ar" ? "نبذة عن الشركة" : "About Company"}</FormLabel>
                            <FormControl>
                              <Textarea
                                {...field}
                                disabled={!isGeneralEditing}
                                placeholder={lang === "ar" ? "أدخل نبذة عن الشركة" : "Enter company description"}
                                rows={3}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Company Logo */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {lang === "ar" ? "شعار الشركة" : "Company Logo"}
                        </label>
                        <div className="flex items-center gap-4">
                          {companyLogo && (
                            <div className="flex items-center gap-2">
                              <img
                                src={companyLogo}
                                alt="Company Logo"
                                className="w-16 h-16 object-contain border rounded"
                              />
                              {isGeneralEditing && (
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setCompanyLogo("")}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          )}
                          {isGeneralEditing && (
                            <div>
                              <input
                                type="file"
                                accept="image/*"
                                onChange={handleLogoUpload}
                                className="hidden"
                                id="logo-upload"
                              />
                              <label htmlFor="logo-upload">
                                <Button type="button" variant="outline" asChild>
                                  <span>
                                    <Upload className="h-4 w-4 mr-2" />
                                    {companyLogo
                                      ? (lang === "ar" ? "تغيير الشعار" : "Change Logo")
                                      : (lang === "ar" ? "رفع شعار" : "Upload Logo")
                                    }
                                  </span>
                                </Button>
                              </label>
                            </div>
                          )}
                        </div>
                      </div>

                      {isGeneralEditing && (
                        <div className="flex justify-end gap-4">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => setIsGeneralEditing(false)}
                          >
                            {lang === "ar" ? "إلغاء" : "Cancel"}
                          </Button>
                          <Button
                            type="submit"
                            disabled={saveSettingsMutation.isPending}
                          >
                            {saveSettingsMutation.isPending ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            ) : (
                              <Save className="h-4 w-4 mr-2" />
                            )}
                            {lang === "ar" ? "حفظ" : "Save"}
                          </Button>
                        </div>
                      )}
                    </form>
                  </Form>
                </CardContent>
              </Card>
            )}

            {activeTab === "country" && (
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>{lang === "ar" ? "البلد والعملة" : "Country & Currency"}</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsCountryEditing(!isCountryEditing)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    {isCountryEditing ? (lang === "ar" ? "إلغاء" : "Cancel") : (lang === "ar" ? "تعديل" : "Edit")}
                  </Button>
                </CardHeader>
                <CardContent>
                  <Form {...countryForm}>
                    <form onSubmit={countryForm.handleSubmit(handleCountrySave)} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={countryForm.control}
                          name="language"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{lang === "ar" ? "اللغة *" : "Language *"}</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={!isCountryEditing}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder={lang === "ar" ? "اختر اللغة" : "Select language"} />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {languages.map((language) => (
                                    <SelectItem key={language.value} value={language.value}>
                                      {language.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={countryForm.control}
                          name="dateFormat"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{lang === "ar" ? "تنسيق التاريخ *" : "Date Format *"}</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={!isCountryEditing}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder={lang === "ar" ? "اختر تنسيق التاريخ" : "Select date format"} />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {dateFormats.map((format) => (
                                    <SelectItem key={format.value} value={format.value}>
                                      {format.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={countryForm.control}
                          name="country"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{lang === "ar" ? "البلد *" : "Country *"}</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={!isCountryEditing}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder={lang === "ar" ? "اختر البلد" : "Select country"} />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {countries.map((country) => (
                                    <SelectItem key={country.value} value={country.value}>
                                      {country.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={countryForm.control}
                          name="currency"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{lang === "ar" ? "العملة *" : "Currency *"}</FormLabel>
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  // Auto-update currency symbol
                                  const selectedCurrency = currencies.find(c => c.value === value);
                                  if (selectedCurrency) {
                                    countryForm.setValue("currencySymbol", selectedCurrency.symbol);
                                  }
                                }}
                                defaultValue={field.value}
                                disabled={!isCountryEditing}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder={lang === "ar" ? "اختر العملة" : "Select currency"} />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {currencies.map((currency) => (
                                    <SelectItem key={currency.value} value={currency.value}>
                                      {currency.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={countryForm.control}
                          name="currencySymbol"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{lang === "ar" ? "رمز العملة *" : "Currency Symbol *"}</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  disabled={!isCountryEditing}
                                  placeholder={lang === "ar" ? "رمز العملة" : "Currency symbol"}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={countryForm.control}
                          name="decimalPlaces"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{lang === "ar" ? "عدد الخانات العشرية *" : "Decimal Places *"}</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={!isCountryEditing}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder={lang === "ar" ? "اختر عدد الخانات" : "Select decimal places"} />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="0">0</SelectItem>
                                  <SelectItem value="2">2</SelectItem>
                                  <SelectItem value="3">3</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Work Days */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {lang === "ar" ? "أيام العمل *" : "Work Days *"}
                        </label>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                          {workDaysOptions.map((day) => (
                            <label key={day.value} className="flex items-center space-x-2 space-x-reverse">
                              <input
                                type="checkbox"
                                disabled={!isCountryEditing}
                                checked={countryForm.watch("workDays")?.includes(day.value) || false}
                                onChange={(e) => {
                                  const currentDays = countryForm.getValues("workDays") || [];
                                  if (e.target.checked) {
                                    countryForm.setValue("workDays", [...currentDays, day.value]);
                                  } else {
                                    countryForm.setValue("workDays", currentDays.filter(d => d !== day.value));
                                  }
                                }}
                                className="rounded"
                              />
                              <span className="text-sm">{day.label}</span>
                            </label>
                          ))}
                        </div>
                      </div>

                      {isCountryEditing && (
                        <div className="flex justify-end gap-4">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => setIsCountryEditing(false)}
                          >
                            {lang === "ar" ? "إلغاء" : "Cancel"}
                          </Button>
                          <Button
                            type="submit"
                            disabled={saveSettingsMutation.isPending}
                          >
                            {saveSettingsMutation.isPending ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            ) : (
                              <Save className="h-4 w-4 mr-2" />
                            )}
                            {lang === "ar" ? "حفظ" : "Save"}
                          </Button>
                        </div>
                      )}
                    </form>
                  </Form>
                </CardContent>
              </Card>
            )}

            {activeTab === "numbering" && (
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>{lang === "ar" ? "إعدادات الترقيم" : "Numbering Settings"}</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsNumberingEditing(!isNumberingEditing)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    {isNumberingEditing ? (lang === "ar" ? "إلغاء" : "Cancel") : (lang === "ar" ? "تعديل" : "Edit")}
                  </Button>
                </CardHeader>
                <CardContent>
                  <Form {...numberingForm}>
                    <form onSubmit={numberingForm.handleSubmit(handleNumberingSave)} className="space-y-6">
                      <FormField
                        control={numberingForm.control}
                        name="numberingFormat"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{lang === "ar" ? "تنسيق الترقيم *" : "Numbering Format *"}</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value} disabled={!isNumberingEditing}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder={lang === "ar" ? "اختر تنسيق الترقيم" : "Select numbering format"} />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="serial-year-unit">
                                  {lang === "ar" ? "رقم تسلسلي - سنة - وحدة" : "Serial - Year - Unit"}
                                </SelectItem>
                                <SelectItem value="year-serial-unit">
                                  {lang === "ar" ? "سنة - رقم تسلسلي - وحدة" : "Year - Serial - Unit"}
                                </SelectItem>
                                <SelectItem value="unit-year-serial">
                                  {lang === "ar" ? "وحدة - سنة - رقم تسلسلي" : "Unit - Year - Serial"}
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {isNumberingEditing && (
                        <div className="flex justify-end gap-4">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => setIsNumberingEditing(false)}
                          >
                            {lang === "ar" ? "إلغاء" : "Cancel"}
                          </Button>
                          <Button
                            type="submit"
                            disabled={saveSettingsMutation.isPending}
                          >
                            {saveSettingsMutation.isPending ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            ) : (
                              <Save className="h-4 w-4 mr-2" />
                            )}
                            {lang === "ar" ? "حفظ" : "Save"}
                          </Button>
                        </div>
                      )}
                    </form>
                  </Form>
                </CardContent>
              </Card>
            )}

            {activeTab === "notifications" && (
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>{lang === "ar" ? "إعدادات التنبيهات" : "Notification Settings"}</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsNotificationEditing(!isNotificationEditing)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    {isNotificationEditing ? (lang === "ar" ? "إلغاء" : "Cancel") : (lang === "ar" ? "تعديل" : "Edit")}
                  </Button>
                </CardHeader>
                <CardContent>
                  <Form {...notificationForm}>
                    <form onSubmit={notificationForm.handleSubmit(handleNotificationSave)} className="space-y-6">
                      <FormField
                        control={notificationForm.control}
                        name="notificationEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{lang === "ar" ? "بريد التنبيهات" : "Notification Email"}</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                disabled={!isNotificationEditing}
                                type="email"
                                placeholder={lang === "ar" ? "أدخل بريد التنبيهات" : "Enter notification email"}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {isNotificationEditing && (
                        <div className="flex justify-end gap-4">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => setIsNotificationEditing(false)}
                          >
                            {lang === "ar" ? "إلغاء" : "Cancel"}
                          </Button>
                          <Button
                            type="submit"
                            disabled={saveSettingsMutation.isPending}
                          >
                            {saveSettingsMutation.isPending ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            ) : (
                              <Save className="h-4 w-4 mr-2" />
                            )}
                            {lang === "ar" ? "حفظ" : "Save"}
                          </Button>
                        </div>
                      )}
                    </form>
                  </Form>
                </CardContent>
              </Card>
            )}

            {activeTab === "reference" && (
              <ReferenceDataTab lang={lang} />
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default SystemSettings;
