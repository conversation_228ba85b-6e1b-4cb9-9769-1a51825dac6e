// ===== DATABASE INITIALIZATION =====
// Author: Augment Code
// Description: Database tables creation and initial data setup

import database from './db.mjs';

async function initializeDatabase() {
  try {
    const db = database.getDb();
    
    console.log('🔄 Initializing database tables...');

    // تشغيل جميع العمليات في transaction واحد
    await new Promise((resolve, reject) => {
      db.serialize(() => {
        // Settings table
        db.run(`
          CREATE TABLE IF NOT EXISTS Settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            companyName TEXT,
            programName TEXT,
            companyRegNo TEXT,
            taxId TEXT,
            about TEXT,
            companyLogo TEXT,
            companyAddress TEXT,
            companyPhone TEXT,
            companyEmail TEXT,
            companyWebsite TEXT,
            language TEXT DEFAULT 'ar',
            country TEXT,
            currency TEXT,
            currencySymbol TEXT,
            dateFormat TEXT,
            timeFormat TEXT DEFAULT '24',
            timeZone TEXT,
            fiscalYearStart TEXT,
            calendarType TEXT DEFAULT 'gregorian',
            theme TEXT DEFAULT 'system',
            fontSize TEXT DEFAULT 'medium',
            primaryColor TEXT DEFAULT '#3b82f6',
            fontFamily TEXT DEFAULT 'Cairo',
            decimalPlaces TEXT DEFAULT '2',
            numberSeparator TEXT DEFAULT ',',
            numberingFormat TEXT,
            contractNumberFormat TEXT,
            clientNumberFormat TEXT,
            paymentNumberFormat TEXT,
            invoiceNumberFormat TEXT,
            receiptNumberFormat TEXT,
            notificationEmail TEXT,
            notificationFrequency TEXT DEFAULT 'daily',
            enableEmailNotifications INTEGER DEFAULT 0,
            enableSMSNotifications INTEGER DEFAULT 0,
            enablePushNotifications INTEGER DEFAULT 0,
            defaultContractDuration TEXT,
            defaultPaymentTerms TEXT,
            defaultTaxRate TEXT,
            backupFrequency TEXT DEFAULT 'daily',
            enableMultiCurrency INTEGER DEFAULT 0,
            sessionTimeout TEXT,
            maxLoginAttempts TEXT,
            passwordPolicy TEXT,
            enableTwoFactor INTEGER DEFAULT 0,
            enableAuditLog INTEGER DEFAULT 1,
            cacheTimeout TEXT,
            maxFileSize TEXT,
            enableCaching INTEGER DEFAULT 1,
            enableCompression INTEGER DEFAULT 1,
            enableDebugMode INTEGER DEFAULT 0,
            regions TEXT,
            owners TEXT,
            governorates TEXT,
            workDays TEXT,
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `, (err) => {
          if (err) {
            console.error('❌ Error creating Settings table:', err);
            reject(err);
            return;
          }
          console.log('✅ Settings table created/verified');
        });

        // Reference Data table
        db.run(`
          CREATE TABLE IF NOT EXISTS ReferenceData (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            module TEXT NOT NULL,
            listName TEXT NOT NULL,
            itemValue TEXT NOT NULL,
            itemLabel TEXT NOT NULL,
            sortOrder INTEGER DEFAULT 0,
            isActive INTEGER DEFAULT 1,
            isDeleted INTEGER DEFAULT 0,
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `, (err) => {
          if (err) {
            console.error('❌ Error creating ReferenceData table:', err);
            reject(err);
            return;
          }
          console.log('✅ ReferenceData table created/verified');
        });

        // Clients table
        db.run(`
          CREATE TABLE IF NOT EXISTS Clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            clientId TEXT UNIQUE NOT NULL,
            clientType TEXT NOT NULL CHECK (clientType IN ('individual', 'company', 'أفراد', 'شركات')),
            clientName TEXT NOT NULL,
            nationalId TEXT,
            commercialRegister TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            city TEXT,
            country TEXT DEFAULT 'السعودية',
            isActive INTEGER DEFAULT 1,
            isDeleted INTEGER DEFAULT 0,
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `, (err) => {
          if (err) {
            console.error('❌ Error creating Clients table:', err);
            reject(err);
            return;
          }
          console.log('✅ Clients table created/verified');
        });

        // Contracts table
        db.run(`
          CREATE TABLE IF NOT EXISTS Contracts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            contractId TEXT UNIQUE NOT NULL,
            clientId TEXT NOT NULL,
            contractType TEXT NOT NULL,
            contractTitle TEXT NOT NULL,
            contractValue REAL NOT NULL,
            currency TEXT DEFAULT 'SAR',
            startDate DATE NOT NULL,
            endDate DATE,
            status TEXT DEFAULT 'active' CHECK (status IN ('draft', 'active', 'completed', 'cancelled')),
            paymentTerms TEXT,
            description TEXT,
            attachments TEXT,
            isActive INTEGER DEFAULT 1,
            isDeleted INTEGER DEFAULT 0,
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (clientId) REFERENCES Clients(clientId)
          )
        `, (err) => {
          if (err) {
            console.error('❌ Error creating Contracts table:', err);
            reject(err);
            return;
          }
          console.log('✅ Contracts table created/verified');
        });

        // Payments table
        db.run(`
          CREATE TABLE IF NOT EXISTS Payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            paymentId TEXT UNIQUE NOT NULL,
            contractId TEXT NOT NULL,
            clientId TEXT NOT NULL,
            paymentType TEXT NOT NULL CHECK (paymentType IN ('cash', 'bank_transfer', 'cheque', 'credit_card')),
            amount REAL NOT NULL,
            currency TEXT DEFAULT 'SAR',
            paymentDate DATE NOT NULL,
            dueDate DATE,
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
            reference TEXT,
            notes TEXT,
            isActive INTEGER DEFAULT 1,
            isDeleted INTEGER DEFAULT 0,
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (contractId) REFERENCES Contracts(contractId),
            FOREIGN KEY (clientId) REFERENCES Clients(clientId)
          )
        `, (err) => {
          if (err) {
            console.error('❌ Error creating Payments table:', err);
            reject(err);
            return;
          }
          console.log('✅ Payments table created/verified');
        });

        // Cheques table
        db.run(`
          CREATE TABLE IF NOT EXISTS Cheques (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            chequeId TEXT UNIQUE NOT NULL,
            contractId TEXT NOT NULL,
            clientId TEXT NOT NULL,
            chequeNumber TEXT NOT NULL,
            bankName TEXT NOT NULL,
            amount REAL NOT NULL,
            currency TEXT DEFAULT 'SAR',
            issueDate DATE NOT NULL,
            dueDate DATE NOT NULL,
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'deposited', 'cleared', 'bounced', 'cancelled')),
            notes TEXT,
            isActive INTEGER DEFAULT 1,
            isDeleted INTEGER DEFAULT 0,
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (contractId) REFERENCES Contracts(contractId),
            FOREIGN KEY (clientId) REFERENCES Clients(clientId)
          )
        `, (err) => {
          if (err) {
            console.error('❌ Error creating Cheques table:', err);
            reject(err);
            return;
          }
          console.log('✅ Cheques table created/verified');
        });

        // Receivables table
        db.run(`
          CREATE TABLE IF NOT EXISTS Receivables (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            receivableId TEXT UNIQUE NOT NULL,
            contractId TEXT NOT NULL,
            clientId TEXT NOT NULL,
            amount REAL NOT NULL,
            currency TEXT DEFAULT 'SAR',
            dueDate DATE NOT NULL,
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'partial', 'paid', 'overdue', 'written_off')),
            description TEXT,
            isActive INTEGER DEFAULT 1,
            isDeleted INTEGER DEFAULT 0,
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (contractId) REFERENCES Contracts(contractId),
            FOREIGN KEY (clientId) REFERENCES Clients(clientId)
          )
        `, (err) => {
          if (err) {
            console.error('❌ Error creating Receivables table:', err);
            reject(err);
            return;
          }
          console.log('✅ Receivables table created/verified');
          resolve();
        });
      });
    });

    console.log('✅ Database tables initialized successfully');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
}

export { initializeDatabase };
