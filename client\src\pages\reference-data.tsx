import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { useEnhancedToast } from '@/hooks/use-enhanced-toast';
import { useReferenceListsConfig, useReferenceData } from '@/hooks/use-reference-lists';
import { useLanguage } from '@/hooks/use-language';
import { cn } from '@/lib/utils';
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Database,
  Building,
  MapPin,
  Users,
  Briefcase,
  Tag,
  FileText,
  CheckCircle,
  Search,
} from 'lucide-react';

interface ReferenceDataItem {
  id: number;
  module: string;
  listName: string;
  itemValue: string;
  itemLabel: string;
  sortOrder: number;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

interface NewListForm {
  listName: string;
  displayName: string;
  description: string;
  linkedPages: string;
  isRequired: boolean;
}

interface NewItemForm {
  itemValue: string;
  itemLabel: string;
  sortOrder: number;
}

// Dynamic reference lists from configuration
const useReferenceLists = () => {
  const { data: configs = [] } = useReferenceListsConfig();

  return configs.map(config => ({
    key: config.listName,
    label: config.displayName,
    description: config.description,
    icon: getIconForList(config.listName),
    module: getModuleForList(config.listName),
    isRequired: config.isRequired
  }));
};

const getModuleForList = (listName: string) => {
  const moduleMap: Record<string, string> = {
    contractType: 'contracts',
    // Add other specific modules here if needed
  };
  return moduleMap[listName] || 'general';
};

const getIconForList = (listName: string) => {
  const iconMap: Record<string, any> = {
    banks: Building,
    governorates: MapPin,
    regions: MapPin,
    owners: Users,
    departments: Briefcase,
    contractType: FileText,
    categories: Tag,
    types: FileText,
    statuses: CheckCircle,
  };
  return iconMap[listName] || Database;
};

// Component لعرض عدد العناصر في القائمة
const ReferenceListItemCount = ({ listName }: { listName: string }) => {
  const module = getModuleForList(listName);
  const { data: items = [] } = useQuery({
    queryKey: ['reference-data', module, listName],
    queryFn: async () => {
      try {
        const response = await fetch(`/api/reference-data/${module}/${listName}`);
        if (!response.ok) return [];
        return response.json();
      } catch (error) {
        console.error('Error fetching items:', error);
        return [];
      }
    }
  });

  return <Badge variant="outline" className="text-xs">{items.length} عنصر</Badge>;
};

export default function ReferenceData() {
  const { toast } = useToast();
  const { showSaveSuccess, showSaveError, showUpdateSuccess, showUpdateError, showDeleteSuccess, showDeleteError } = useEnhancedToast();
  const queryClient = useQueryClient();
  const referenceLists = useReferenceLists();
  const { isRTL } = useLanguage();
  const [selectedList, setSelectedList] = useState<string | null>(null);
  const [showAddListDialog, setShowAddListDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<ReferenceDataItem | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Form states
  const [newListForm, setNewListForm] = useState<NewListForm>({
    listName: '',
    displayName: '',
    description: '',
    linkedPages: '',
    isRequired: false
  });

  const [newItemForm, setNewItemForm] = useState<NewItemForm>({
    itemValue: '',
    itemLabel: '',
    sortOrder: 999
  });

  // Get reference data for selected list
  const selectedModule = selectedList ? getModuleForList(selectedList) : 'general';
  const { data: referenceItems = [], isLoading: itemsLoading } = useReferenceData(
    selectedModule,
    selectedList || ''
  );

  // Filtered items based on search
  const filteredItems = referenceItems.filter((item: ReferenceDataItem) =>
    item.itemLabel.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.itemValue.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Create new reference list mutation
  const createListMutation = useMutation({
    mutationFn: async (data: NewListForm) => {
      const response = await fetch('/api/reference-lists-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'فشل في إنشاء القائمة');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reference-lists-config'] });
      setShowAddListDialog(false);
      setNewListForm({
        listName: '',
        displayName: '',
        description: '',
        linkedPages: '',
        isRequired: false
      });
      showSaveSuccess('القائمة المرجعية');
    },
    onError: (error: Error) => {
      showSaveError(error.message);
    }
  });

  // Add new item mutation
  const addItemMutation = useMutation({
    mutationFn: async (data: NewItemForm) => {
      const response = await fetch(`/api/reference-data/${selectedModule}/${selectedList}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'فشل في إضافة العنصر');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reference-data', selectedModule, selectedList] });
      setNewItemForm({
        itemValue: '',
        itemLabel: '',
        sortOrder: 999
      });
      showSaveSuccess('العنصر');
    },
    onError: (error: Error) => {
      showSaveError(error.message);
    }
  });

  // Update item mutation
  const updateItemMutation = useMutation({
    mutationFn: async (data: { id: number; itemValue: string; itemLabel: string; sortOrder: number; isActive: boolean }) => {
      const response = await fetch(`/api/reference-data/${data.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'فشل في تحديث العنصر');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reference-data', selectedModule, selectedList] });
      setEditingItem(null);
      showUpdateSuccess('العنصر');
    },
    onError: (error: Error) => {
      showUpdateError(error.message);
    }
  });

  // Delete item mutation
  const deleteItemMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/reference-data/${id}`, {
        method: 'DELETE'
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'فشل في حذف العنصر');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reference-data', selectedModule, selectedList] });
      showDeleteSuccess('العنصر');
    },
    onError: (error: Error) => {
      showDeleteError(error.message);
    }
  });

  // Handle form submissions
  const handleCreateList = () => {
    if (!newListForm.listName || !newListForm.displayName) {
      toast({
        title: 'خطأ في البيانات',
        description: 'يجب إدخال اسم القائمة والاسم المعروض',
        variant: 'destructive'
      });
      return;
    }
    createListMutation.mutate(newListForm);
  };

  const handleAddItem = () => {
    if (!newItemForm.itemValue || !newItemForm.itemLabel) {
      toast({
        title: 'خطأ في البيانات',
        description: 'يجب إدخال الرمز والتسمية',
        variant: 'destructive'
      });
      return;
    }
    addItemMutation.mutate(newItemForm);
  };

  const handleUpdateItem = (item: ReferenceDataItem) => {
    updateItemMutation.mutate({
      id: item.id,
      itemValue: item.itemValue,
      itemLabel: item.itemLabel,
      sortOrder: item.sortOrder,
      isActive: item.isActive
    });
  };

  const handleDeleteItem = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      deleteItemMutation.mutate(id);
    }
  };

  return (
    <div className={cn("space-y-6", isRTL ? "text-right" : "text-left")}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className={cn("flex items-center justify-between", isRTL ? "flex-row-reverse" : "flex-row")}>
            <div className={cn(isRTL ? "text-right" : "text-left")}>
              <CardTitle className="text-xl">البيانات المرجعية</CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                إدارة قوائم البنوك والمحافظات والمناطق والأقسام
              </p>
            </div>
            <Button className="gap-2" onClick={() => setShowAddListDialog(true)}>
              <Plus className="w-4 h-4" />
              إضافة قائمة جديدة
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Reference Lists Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
        {referenceLists.map((list) => {
          const Icon = list.icon;
          return (
            <Card key={list.key} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setSelectedList(list.key)}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Icon className="w-5 h-5 text-primary" />
                    <CardTitle className="text-base">{list.label}</CardTitle>
                  </div>
                  <div className="flex items-center gap-1">
                    {list.isRequired ? (
                      <Badge variant="destructive" className="text-xs">مطلوبة</Badge>
                    ) : (
                      <Badge variant="secondary" className="text-xs">اختيارية</Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground mb-3">{list.description}</p>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">العناصر:</span>
                  <ReferenceListItemCount listName={list.key} />
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Dialog لإضافة قائمة جديدة */}
      <Dialog open={showAddListDialog} onOpenChange={setShowAddListDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>إضافة قائمة مرجعية جديدة</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="listName">اسم القائمة (بالإنجليزية) *</Label>
              <Input
                id="listName"
                value={newListForm.listName}
                onChange={(e) => setNewListForm(prev => ({ ...prev, listName: e.target.value }))}
                placeholder="مثال: departments"
                className="text-left"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="displayName">الاسم المعروض *</Label>
              <Input
                id="displayName"
                value={newListForm.displayName}
                onChange={(e) => setNewListForm(prev => ({ ...prev, displayName: e.target.value }))}
                placeholder="مثال: الأقسام"
                className="text-right"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">الوصف</Label>
              <Input
                id="description"
                value={newListForm.description}
                onChange={(e) => setNewListForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="مثال: قائمة أقسام الشركة"
                className="text-right"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="linkedPages">الصفحات المرتبطة (مفصولة بفاصلة)</Label>
              <Input
                id="linkedPages"
                value={newListForm.linkedPages}
                onChange={(e) => setNewListForm(prev => ({ ...prev, linkedPages: e.target.value }))}
                placeholder="مثال: contracts, clients"
                className="text-left"
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isRequired"
                checked={newListForm.isRequired}
                onChange={(e) => setNewListForm(prev => ({ ...prev, isRequired: e.target.checked }))}
                className="rounded"
              />
              <Label htmlFor="isRequired">قائمة مطلوبة</Label>
            </div>
            <div className="flex gap-2 pt-4">
              <Button
                className="flex-1"
                onClick={handleCreateList}
                disabled={createListMutation.isPending}
              >
                {createListMutation.isPending ? 'جاري الإنشاء...' : 'إنشاء القائمة'}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowAddListDialog(false)}
                className="flex-1"
              >
                إلغاء
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Modal لإدارة القائمة المختارة */}
      {selectedList && (
        <Dialog open={true} onOpenChange={() => setSelectedList(null)}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                إدارة {referenceLists.find(list => list.key === selectedList)?.label}
              </DialogTitle>
            </DialogHeader>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 overflow-hidden">
              {/* Add New Item */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">إضافة عنصر جديد</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="itemValue">الرمز/القيمة *</Label>
                    <Input
                      id="itemValue"
                      value={newItemForm.itemValue}
                      onChange={(e) => setNewItemForm(prev => ({ ...prev, itemValue: e.target.value }))}
                      placeholder="مثال: NBE"
                      className="text-right"
                    />
                  </div>
                  <div>
                    <Label htmlFor="itemLabel">الاسم/التسمية *</Label>
                    <Input
                      id="itemLabel"
                      value={newItemForm.itemLabel}
                      onChange={(e) => setNewItemForm(prev => ({ ...prev, itemLabel: e.target.value }))}
                      placeholder="مثال: البنك الأهلي المصري"
                      className="text-right"
                    />
                  </div>
                  <div>
                    <Label htmlFor="sortOrder">ترتيب العرض</Label>
                    <Input
                      id="sortOrder"
                      type="number"
                      value={newItemForm.sortOrder}
                      onChange={(e) => setNewItemForm(prev => ({ ...prev, sortOrder: parseInt(e.target.value) || 999 }))}
                      placeholder="999"
                      className="text-right"
                    />
                  </div>
                  <Button
                    className="w-full"
                    onClick={handleAddItem}
                    disabled={addItemMutation.isPending}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {addItemMutation.isPending ? 'جاري الإضافة...' : 'إضافة'}
                  </Button>
                </CardContent>
              </Card>

              {/* Items List */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">قائمة العناصر</CardTitle>
                    <Badge variant="outline">{filteredItems.length} عنصر</Badge>
                  </div>
                  {/* Search */}
                  <div className="relative">
                    <Input
                      placeholder="البحث في القائمة..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="text-right pr-10"
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <Search className="w-4 h-4 text-muted-foreground" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="max-h-96 overflow-y-auto">
                  {itemsLoading ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">جاري التحميل...</p>
                    </div>
                  ) : filteredItems.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">
                        {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد عناصر في هذه القائمة'}
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {filteredItems.map((item: ReferenceDataItem) => (
                        <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                          {editingItem?.id === item.id ? (
                            <div className="flex-1 grid grid-cols-3 gap-2">
                              <Input
                                value={editingItem.itemValue}
                                onChange={(e) => setEditingItem(prev => prev ? { ...prev, itemValue: e.target.value } : null)}
                                className="text-right"
                                placeholder="الرمز"
                              />
                              <Input
                                value={editingItem.itemLabel}
                                onChange={(e) => setEditingItem(prev => prev ? { ...prev, itemLabel: e.target.value } : null)}
                                className="text-right"
                                placeholder="التسمية"
                              />
                              <Input
                                type="number"
                                value={editingItem.sortOrder}
                                onChange={(e) => setEditingItem(prev => prev ? { ...prev, sortOrder: parseInt(e.target.value) || 999 } : null)}
                                className="text-right"
                                placeholder="الترتيب"
                              />
                            </div>
                          ) : (
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <span className="font-medium">{item.itemLabel}</span>
                                <Badge variant="outline" className="text-xs">{item.itemValue}</Badge>
                              </div>
                              <div className="text-sm text-muted-foreground">
                                ترتيب: {item.sortOrder} | {item.isActive ? 'نشط' : 'غير نشط'}
                              </div>
                            </div>
                          )}
                          <div className="flex items-center gap-1">
                            {editingItem?.id === item.id ? (
                              <>
                                <Button
                                  size="sm"
                                  onClick={() => handleUpdateItem(editingItem)}
                                  disabled={updateItemMutation.isPending}
                                >
                                  <Save className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => setEditingItem(null)}
                                >
                                  <X className="w-3 h-3" />
                                </Button>
                              </>
                            ) : (
                              <>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => setEditingItem(item)}
                                >
                                  <Edit className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => handleDeleteItem(item.id)}
                                  disabled={deleteItemMutation.isPending}
                                >
                                  <Trash2 className="w-3 h-3" />
                                </Button>
                              </>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
