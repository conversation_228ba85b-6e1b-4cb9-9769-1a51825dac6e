// ===== FORMATTERS UTILITIES =====
// Author: Augment Code
// Description: Utility functions for formatting data

/**
 * Format currency amount
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: 'SAR')
 * @param {string} locale - Locale (default: 'ar-SA')
 * @returns {string} Formatted currency string
 */
const formatCurrency = (amount, currency = 'SAR', locale = 'ar-SA') => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '0.00';
  }

  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  } catch (error) {
    // Fallback formatting
    return `${amount.toFixed(2)} ${currency}`;
  }
};

/**
 * Format number with Arabic numerals
 * @param {number} number - Number to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted number string
 */
const formatNumberArabic = (number, options = {}) => {
  if (number === null || number === undefined || isNaN(number)) {
    return '0';
  }

  const {
    minimumFractionDigits = 0,
    maximumFractionDigits = 2,
    useGrouping = true
  } = options;

  try {
    return new Intl.NumberFormat('ar-SA', {
      minimumFractionDigits,
      maximumFractionDigits,
      useGrouping
    }).format(number);
  } catch (error) {
    return number.toString();
  }
};

/**
 * Format percentage
 * @param {number} value - Value to format as percentage
 * @param {number} total - Total value for percentage calculation
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted percentage string
 */
const formatPercentage = (value, total, decimals = 1) => {
  if (!value || !total || total === 0) return '0%';
  
  const percentage = (value / total) * 100;
  return `${percentage.toFixed(decimals)}%`;
};

/**
 * Format phone number
 * @param {string} phone - Phone number to format
 * @param {string} format - Format type ('international', 'national', 'local')
 * @returns {string} Formatted phone number
 */
const formatPhone = (phone, format = 'national') => {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  if (digits.length === 0) return '';
  
  // Saudi Arabia phone number formatting
  if (digits.startsWith('966')) {
    // International format
    const national = digits.substring(3);
    switch (format) {
      case 'international':
        return `+966 ${national.substring(0, 2)} ${national.substring(2, 5)} ${national.substring(5)}`;
      case 'national':
        return `0${national.substring(0, 2)} ${national.substring(2, 5)} ${national.substring(5)}`;
      case 'local':
        return national;
      default:
        return `0${national}`;
    }
  } else if (digits.startsWith('0') && digits.length === 10) {
    // National format starting with 0
    switch (format) {
      case 'international':
        return `+966 ${digits.substring(1, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}`;
      case 'national':
        return `${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}`;
      case 'local':
        return digits.substring(1);
      default:
        return digits;
    }
  } else if (digits.length === 9) {
    // Local format without country code or leading 0
    switch (format) {
      case 'international':
        return `+966 ${digits.substring(0, 2)} ${digits.substring(2, 5)} ${digits.substring(5)}`;
      case 'national':
        return `0${digits.substring(0, 2)} ${digits.substring(2, 5)} ${digits.substring(5)}`;
      case 'local':
        return digits;
      default:
        return `0${digits}`;
    }
  }
  
  // Return as-is if format not recognized
  return phone;
};

/**
 * Format file size
 * @param {number} bytes - File size in bytes
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted file size string
 */
const formatFileSize = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 بايت';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Format duration
 * @param {number} seconds - Duration in seconds
 * @returns {string} Formatted duration string
 */
const formatDuration = (seconds) => {
  if (!seconds || seconds < 0) return '0 ثانية';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  const parts = [];
  
  if (hours > 0) {
    parts.push(`${hours} ساعة`);
  }
  
  if (minutes > 0) {
    parts.push(`${minutes} دقيقة`);
  }
  
  if (remainingSeconds > 0 || parts.length === 0) {
    parts.push(`${remainingSeconds} ثانية`);
  }
  
  return parts.join(' و ');
};

/**
 * Truncate text with ellipsis
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length
 * @param {string} suffix - Suffix to add (default: '...')
 * @returns {string} Truncated text
 */
const truncateText = (text, maxLength, suffix = '...') => {
  if (!text || text.length <= maxLength) return text || '';
  
  return text.substring(0, maxLength - suffix.length) + suffix;
};

/**
 * Format ID number (mask sensitive parts)
 * @param {string} id - ID number to format
 * @param {boolean} mask - Whether to mask the ID
 * @returns {string} Formatted ID number
 */
const formatIdNumber = (id, mask = true) => {
  if (!id) return '';
  
  const cleanId = id.replace(/\D/g, '');
  
  if (cleanId.length === 10) {
    // Saudi national ID format
    if (mask) {
      return `${cleanId.substring(0, 1)}****${cleanId.substring(5)}`;
    } else {
      return `${cleanId.substring(0, 1)}-${cleanId.substring(1, 5)}-${cleanId.substring(5, 9)}-${cleanId.substring(9)}`;
    }
  }
  
  // Return masked version for other lengths
  if (mask && cleanId.length > 4) {
    return `${cleanId.substring(0, 2)}${'*'.repeat(cleanId.length - 4)}${cleanId.substring(cleanId.length - 2)}`;
  }
  
  return cleanId;
};

/**
 * Format contract number
 * @param {string} contractNumber - Contract number to format
 * @returns {string} Formatted contract number
 */
const formatContractNumber = (contractNumber) => {
  if (!contractNumber) return '';
  
  // Remove any existing formatting
  const clean = contractNumber.replace(/[^\w]/g, '');
  
  // Format as: XXXX-XXXX-XXXX
  if (clean.length >= 8) {
    return clean.replace(/(.{4})(.{4})(.*)/, '$1-$2-$3');
  } else if (clean.length >= 4) {
    return clean.replace(/(.{4})(.*)/, '$1-$2');
  }
  
  return clean;
};

module.exports = {
  formatCurrency,
  formatNumberArabic,
  formatPercentage,
  formatPhone,
  formatFileSize,
  formatDuration,
  truncateText,
  formatIdNumber,
  formatContractNumber
};
