import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { useDateFormat } from "@/hooks/use-date-format";
import { EnhancedContractCalculator } from "@/lib/enhanced-contract-calculator";
import { useCurrency } from "@/hooks/use-currency";
import { 
  Calendar, 
  DollarSign, 
  Edit, 
  Save, 
  X, 
  CheckCircle, 
  AlertTriangle, 
  <PERSON>,
  Calculator,
  Download,
  Print
} from "lucide-react";

interface FinancialCashFlowTableProps {
  contract: ContractData;
  installments: InstallmentData[];
  onInstallmentUpdate?: (installmentId: number, updatedInstallment: InstallmentData) => void;
  onSave?: (installments: InstallmentData[]) => void;
  editable?: boolean;
}

export default function FinancialCashFlowTable({
  contract,
  installments: initialInstallments,
  onInstallmentUpdate,
  onSave,
  editable = true
}: FinancialCashFlowTableProps) {
  const { toast } = useToast();
  const { formatDate } = useDateFormat();
  const [installments, setInstallments] = useState<InstallmentData[]>(initialInstallments);
  const [editingInstallment, setEditingInstallment] = useState<number | null>(null);
  const [editForm, setEditForm] = useState<Partial<InstallmentData>>({});
  const [selectedInstallments, setSelectedInstallments] = useState<number[]>([]);

  useEffect(() => {
    setInstallments(initialInstallments);
  }, [initialInstallments]);

  // Calculate summary statistics
  const summary = React.useMemo(() => {
    const totalAmount = installments.reduce((sum, inst) => sum + inst.installmentAmount, 0);
    const totalPaid = installments.reduce((sum, inst) => sum + inst.paidAmount, 0);
    const totalRemaining = installments.reduce((sum, inst) => sum + inst.remainingAmount, 0);
    const totalPenalties = installments.reduce((sum, inst) => sum + inst.penaltyAmount, 0);
    const paidCount = installments.filter(inst => inst.isPaid).length;
    const overdueCount = installments.filter(inst => 
      !inst.isPaid && new Date(inst.paymentDueDate) < new Date()
    ).length;

    return {
      totalAmount,
      totalPaid,
      totalRemaining,
      totalPenalties,
      paidCount,
      overdueCount,
      totalCount: installments.length,
      paymentProgress: totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0
    };
  }, [installments]);

  const handleEditStart = (installment: InstallmentData) => {
    setEditingInstallment(installment.installmentNumber);
    setEditForm({
      installmentAmount: installment.installmentAmount,
      baseAmount: installment.baseAmount,
      taxAmount: installment.taxAmount,
      paymentDueDate: installment.paymentDueDate,
      paidAmount: installment.paidAmount,
      penaltyAmount: installment.penaltyAmount,
      isPaid: installment.isPaid,
      notes: installment.notes
    });
  };

  const handleEditSave = (installmentNumber: number) => {
    const installmentIndex = installments.findIndex(inst => inst.installmentNumber === installmentNumber);
    if (installmentIndex === -1) return;

    const updatedInstallment = {
      ...installments[installmentIndex],
      ...editForm,
      remainingAmount: (editForm.installmentAmount || 0) - (editForm.paidAmount || 0)
    };

    const newInstallments = [...installments];
    newInstallments[installmentIndex] = updatedInstallment;
    setInstallments(newInstallments);

    if (onInstallmentUpdate) {
      onInstallmentUpdate(installmentNumber, updatedInstallment);
    }

    setEditingInstallment(null);
    setEditForm({});

    toast({
      title: "تم التحديث",
      description: "تم تحديث بيانات القسط بنجاح",
    });
  };

  const handleEditCancel = () => {
    setEditingInstallment(null);
    setEditForm({});
  };

  const handleSelectInstallment = (installmentNumber: number, checked: boolean) => {
    if (checked) {
      setSelectedInstallments([...selectedInstallments, installmentNumber]);
    } else {
      setSelectedInstallments(selectedInstallments.filter(id => id !== installmentNumber));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedInstallments(installments.map(inst => inst.installmentNumber));
    } else {
      setSelectedInstallments([]);
    }
  };

  const handleBulkPayment = () => {
    const updatedInstallments = installments.map(inst => {
      if (selectedInstallments.includes(inst.installmentNumber)) {
        return {
          ...inst,
          isPaid: true,
          paidAmount: inst.installmentAmount,
          remainingAmount: 0
        };
      }
      return inst;
    });

    setInstallments(updatedInstallments);
    setSelectedInstallments([]);

    toast({
      title: "تم تسجيل المدفوعات",
      description: `تم تسجيل ${selectedInstallments.length} دفعة بنجاح`,
    });
  };

  const handleSaveAll = () => {
    if (onSave) {
      onSave(installments);
    }
    toast({
      title: "تم الحفظ",
      description: "تم حفظ جدول التدفقات المالية بنجاح",
    });
  };

  const getInstallmentStatus = (installment: InstallmentData) => {
    if (installment.isPaid) {
      return { status: 'paid', label: 'مدفوع', color: 'bg-green-100 text-green-800' };
    }
    
    const dueDate = new Date(installment.paymentDueDate);
    const today = new Date();
    
    if (dueDate < today) {
      return { status: 'overdue', label: 'متأخر', color: 'bg-red-100 text-red-800' };
    }
    
    const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    if (daysUntilDue <= 7) {
      return { status: 'due-soon', label: 'مستحق قريباً', color: 'bg-yellow-100 text-yellow-800' };
    }
    
    return { status: 'pending', label: 'معلق', color: 'bg-gray-100 text-gray-800' };
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي القيمة</p>
                <p className="text-2xl font-bold text-blue-600">
                  {formatCurrency(summary.totalAmount)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المدفوع</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(summary.totalPaid)}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المتبقي</p>
                <p className="text-2xl font-bold text-orange-600">
                  {formatCurrency(summary.totalRemaining)}
                </p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">الغرامات</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatCurrency(summary.totalPenalties)}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">تقدم السداد</span>
            <span className="text-sm text-gray-600">
              {summary.paidCount} من {summary.totalCount} أقساط
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${summary.paymentProgress}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-600 mt-1">
            <span>{summary.paymentProgress.toFixed(1)}%</span>
            <span>{summary.overdueCount} متأخر</span>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      {editable && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {selectedInstallments.length > 0 && (
              <Button onClick={handleBulkPayment} className="gap-2">
                <CheckCircle className="h-4 w-4" />
                تسجيل دفع ({selectedInstallments.length})
              </Button>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" className="gap-2">
              <Download className="h-4 w-4" />
              تصدير
            </Button>
            <Button variant="outline" className="gap-2">
              <Print className="h-4 w-4" />
              طباعة
            </Button>
            <Button onClick={handleSaveAll} className="gap-2">
              <Save className="h-4 w-4" />
              حفظ التغييرات
            </Button>
          </div>
        </div>
      )}

      {/* Installments Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              جدول التدفقات المالية
            </CardTitle>
            {editable && (
              <div className="flex items-center space-x-reverse space-x-2">
                <Checkbox
                  id="select-all"
                  checked={selectedInstallments.length === installments.length}
                  onCheckedChange={handleSelectAll}
                />
                <Label htmlFor="select-all" className="text-sm">
                  تحديد الكل
                </Label>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  {editable && <TableHead className="w-12">تحديد</TableHead>}
                  <TableHead>رقم القسط</TableHead>
                  <TableHead>تاريخ الاستحقاق</TableHead>
                  <TableHead>تاريخ السداد المطلوب</TableHead>
                  <TableHead>المبلغ الأساسي</TableHead>
                  <TableHead>الضريبة</TableHead>
                  <TableHead>إجمالي القسط</TableHead>
                  <TableHead>المدفوع</TableHead>
                  <TableHead>المتبقي</TableHead>
                  <TableHead>الغرامة</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>السنة</TableHead>
                  {editable && <TableHead>الإجراءات</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {installments.map((installment) => {
                  const isEditing = editingInstallment === installment.installmentNumber;
                  const status = getInstallmentStatus(installment);
                  
                  return (
                    <TableRow key={installment.installmentNumber}>
                      {editable && (
                        <TableCell>
                          <Checkbox
                            checked={selectedInstallments.includes(installment.installmentNumber)}
                            onCheckedChange={(checked) => 
                              handleSelectInstallment(installment.installmentNumber, !!checked)
                            }
                          />
                        </TableCell>
                      )}
                      
                      <TableCell className="font-medium">
                        {installment.installmentNumber}
                      </TableCell>
                      
                      <TableCell>
                        {formatDate(installment.installmentDate)}
                      </TableCell>
                      
                      <TableCell>
                        {isEditing ? (
                          <Input
                            type="date"
                            value={editForm.paymentDueDate || installment.paymentDueDate}
                            onChange={(e) => setEditForm({...editForm, paymentDueDate: e.target.value})}
                            className="w-32"
                          />
                        ) : (
                          formatDate(installment.paymentDueDate)
                        )}
                      </TableCell>
                      
                      <TableCell>
                        {isEditing ? (
                          <Input
                            type="number"
                            step="0.01"
                            value={editForm.baseAmount || installment.baseAmount}
                            onChange={(e) => setEditForm({...editForm, baseAmount: parseFloat(e.target.value)})}
                            className="w-24"
                          />
                        ) : (
                          formatCurrency(installment.baseAmount)
                        )}
                      </TableCell>
                      
                      <TableCell>
                        {isEditing ? (
                          <Input
                            type="number"
                            step="0.01"
                            value={editForm.taxAmount || installment.taxAmount}
                            onChange={(e) => setEditForm({...editForm, taxAmount: parseFloat(e.target.value)})}
                            className="w-24"
                          />
                        ) : (
                          formatCurrency(installment.taxAmount)
                        )}
                      </TableCell>
                      
                      <TableCell className="font-semibold">
                        {isEditing ? (
                          <Input
                            type="number"
                            step="0.01"
                            value={editForm.installmentAmount || installment.installmentAmount}
                            onChange={(e) => setEditForm({...editForm, installmentAmount: parseFloat(e.target.value)})}
                            className="w-28"
                          />
                        ) : (
                          formatCurrency(installment.installmentAmount)
                        )}
                      </TableCell>
                      
                      <TableCell>
                        {isEditing ? (
                          <Input
                            type="number"
                            step="0.01"
                            value={editForm.paidAmount || installment.paidAmount}
                            onChange={(e) => setEditForm({...editForm, paidAmount: parseFloat(e.target.value)})}
                            className="w-24"
                          />
                        ) : (
                          formatCurrency(installment.paidAmount)
                        )}
                      </TableCell>
                      
                      <TableCell>
                        {formatCurrency(installment.remainingAmount)}
                      </TableCell>
                      
                      <TableCell>
                        {formatCurrency(installment.penaltyAmount)}
                      </TableCell>
                      
                      <TableCell>
                        <Badge className={status.color}>
                          {status.label}
                        </Badge>
                      </TableCell>
                      
                      <TableCell>
                        السنة {installment.yearOfContract}
                      </TableCell>
                      
                      {editable && (
                        <TableCell>
                          {isEditing ? (
                            <div className="flex items-center gap-1">
                              <Button
                                size="sm"
                                onClick={() => handleEditSave(installment.installmentNumber)}
                                className="h-8 w-8 p-0"
                              >
                                <Save className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={handleEditCancel}
                                className="h-8 w-8 p-0"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditStart(installment)}
                              className="h-8 w-8 p-0"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          )}
                        </TableCell>
                      )}
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
