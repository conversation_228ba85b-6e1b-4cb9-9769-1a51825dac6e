import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { But<PERSON> } from "../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Input } from "../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../components/ui/tabs";
import { Badge } from "../components/ui/badge";
import { Progress } from "../components/ui/progress";
import { apiRequest } from "../lib/queryClient";
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Calendar,
  FileText,
  Download,
  Filter,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  PieChart,
  Activity,
  Users,
  Building2
} from "lucide-react";
import labels from "../i18n";

interface FinancialSummary {
  totalContracts: number;
  activeContracts: number;
  totalContractValue: number;
  totalCollected: number;
  totalOutstanding: number;
  totalOverdue: number;
  collectionRate: number;
  averageContractValue: number;
}

interface MonthlyRevenue {
  month: string;
  revenue: number;
  collections: number;
  outstanding: number;
}

interface ContractTypeAnalysis {
  contractType: string;
  count: number;
  totalValue: number;
  averageValue: number;
  collectionRate: number;
}

interface ClientAnalysis {
  clientId: number;
  clientName: string;
  contractsCount: number;
  totalValue: number;
  paidAmount: number;
  outstandingAmount: number;
  paymentScore: number;
}

const FinancialReports = () => {
  const [lang] = useState<"ar" | "en">((localStorage.getItem("lang") as "ar" | "en") || "ar");
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");
  const [reportType, setReportType] = useState("summary");
  const t = labels[lang];

  // Fetch financial summary
  const { data: financialSummary, isLoading: summaryLoading } = useQuery({
    queryKey: ["/api/reports/financial-summary", dateFrom, dateTo],
    queryFn: () => apiRequest(`/api/reports/financial-summary?from=${dateFrom}&to=${dateTo}`),
  });

  // Fetch monthly revenue data
  const { data: monthlyRevenue = [], isLoading: revenueLoading } = useQuery({
    queryKey: ["/api/reports/monthly-revenue", dateFrom, dateTo],
    queryFn: () => apiRequest(`/api/reports/monthly-revenue?from=${dateFrom}&to=${dateTo}`),
  });

  // Fetch contract type analysis
  const { data: contractTypeAnalysis = [], isLoading: typeLoading } = useQuery({
    queryKey: ["/api/reports/contract-types", dateFrom, dateTo],
    queryFn: () => apiRequest(`/api/reports/contract-types?from=${dateFrom}&to=${dateTo}`),
  });

  // Fetch client analysis
  const { data: clientAnalysis = [], isLoading: clientLoading } = useQuery({
    queryKey: ["/api/reports/client-analysis", dateFrom, dateTo],
    queryFn: () => apiRequest(`/api/reports/client-analysis?from=${dateFrom}&to=${dateTo}`),
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${Math.round(value * 100) / 100}%`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadge = (score: number) => {
    if (score >= 90) return 'bg-green-100 text-green-800';
    if (score >= 70) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-reverse space-x-4">
              <BarChart3 className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {lang === "ar" ? "التقارير المالية" : "Financial Reports"}
              </h1>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                تصدير التقرير
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              فلاتر التقرير
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">من تاريخ</label>
                <Input
                  type="date"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">إلى تاريخ</label>
                <Input
                  type="date"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">نوع التقرير</label>
                <Select value={reportType} onValueChange={setReportType}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر نوع التقرير" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="summary">ملخص مالي</SelectItem>
                    <SelectItem value="detailed">تقرير مفصل</SelectItem>
                    <SelectItem value="collections">تقرير التحصيلات</SelectItem>
                    <SelectItem value="overdue">المتأخرات</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end">
                <Button className="w-full">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  تحديث التقرير
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Financial Summary Cards */}
        {financialSummary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي قيمة العقود</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {formatCurrency(financialSummary.totalContractValue)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {financialSummary.totalContracts} عقد
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">المبلغ المحصل</p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(financialSummary.totalCollected)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      معدل التحصيل: {formatPercentage(financialSummary.collectionRate)}
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">المبلغ المستحق</p>
                    <p className="text-2xl font-bold text-orange-600">
                      {formatCurrency(financialSummary.totalOutstanding)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      متوسط العقد: {formatCurrency(financialSummary.averageContractValue)}
                    </p>
                  </div>
                  <Target className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">المتأخرات</p>
                    <p className="text-2xl font-bold text-red-600">
                      {formatCurrency(financialSummary.totalOverdue)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      العقود النشطة: {financialSummary.activeContracts}
                    </p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Collection Rate Progress */}
        {financialSummary && (
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">معدل التحصيل الإجمالي</h3>
                <span className="text-2xl font-bold text-primary">
                  {formatPercentage(financialSummary.collectionRate)}
                </span>
              </div>
              <Progress value={financialSummary.collectionRate} className="h-4" />
              <div className="flex justify-between text-sm text-gray-600 mt-2">
                <span>المحصل: {formatCurrency(financialSummary.totalCollected)}</span>
                <span>المتبقي: {formatCurrency(financialSummary.totalOutstanding)}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Detailed Reports Tabs */}
        <Tabs defaultValue="revenue" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="revenue">الإيرادات الشهرية</TabsTrigger>
            <TabsTrigger value="contracts">تحليل العقود</TabsTrigger>
            <TabsTrigger value="clients">تحليل العملاء</TabsTrigger>
            <TabsTrigger value="performance">الأداء المالي</TabsTrigger>
          </TabsList>

          <TabsContent value="revenue">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  الإيرادات الشهرية
                </CardTitle>
              </CardHeader>
              <CardContent>
                {revenueLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  </div>
                ) : monthlyRevenue.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد بيانات إيرادات للفترة المحددة
                  </div>
                ) : (
                  <div className="space-y-4">
                    {monthlyRevenue.map((month: MonthlyRevenue, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <h4 className="font-medium">{month.month}</h4>
                          <p className="text-sm text-gray-600">
                            المحصل: {formatCurrency(month.collections)}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-green-600">
                            {formatCurrency(month.revenue)}
                          </p>
                          <p className="text-sm text-orange-600">
                            متبقي: {formatCurrency(month.outstanding)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contracts">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  تحليل العقود حسب النوع
                </CardTitle>
              </CardHeader>
              <CardContent>
                {typeLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  </div>
                ) : contractTypeAnalysis.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد بيانات عقود للفترة المحددة
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50 border-b">
                        <tr>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">نوع العقد</th>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">العدد</th>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">إجمالي القيمة</th>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">متوسط القيمة</th>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">معدل التحصيل</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {contractTypeAnalysis.map((type: ContractTypeAnalysis, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-4 py-3 text-sm font-medium">{type.contractType}</td>
                            <td className="px-4 py-3 text-sm">{type.count}</td>
                            <td className="px-4 py-3 text-sm">{formatCurrency(type.totalValue)}</td>
                            <td className="px-4 py-3 text-sm">{formatCurrency(type.averageValue)}</td>
                            <td className="px-4 py-3">
                              <Badge className={getScoreBadge(type.collectionRate)}>
                                {formatPercentage(type.collectionRate)}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="clients">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  تحليل أداء العملاء
                </CardTitle>
              </CardHeader>
              <CardContent>
                {clientLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  </div>
                ) : clientAnalysis.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد بيانات عملاء للفترة المحددة
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50 border-b">
                        <tr>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">اسم العميل</th>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">عدد العقود</th>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">إجمالي القيمة</th>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">المدفوع</th>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">المتبقي</th>
                          <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">نقاط الدفع</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {clientAnalysis.map((client: ClientAnalysis, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-4 py-3 text-sm font-medium">{client.clientName}</td>
                            <td className="px-4 py-3 text-sm">{client.contractsCount}</td>
                            <td className="px-4 py-3 text-sm">{formatCurrency(client.totalValue)}</td>
                            <td className="px-4 py-3 text-sm text-green-600">{formatCurrency(client.paidAmount)}</td>
                            <td className="px-4 py-3 text-sm text-orange-600">{formatCurrency(client.outstandingAmount)}</td>
                            <td className="px-4 py-3">
                              <Badge className={getScoreBadge(client.paymentScore)}>
                                {Math.round(client.paymentScore)}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  مؤشرات الأداء المالي
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium">مؤشرات التحصيل</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">معدل التحصيل الشهري</span>
                        <span className="font-medium">85%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">متوسط أيام التحصيل</span>
                        <span className="font-medium">12 يوم</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">نسبة المتأخرات</span>
                        <span className="font-medium text-red-600">8%</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h4 className="font-medium">مؤشرات العقود</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">معدل نمو العقود</span>
                        <span className="font-medium text-green-600">+15%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">متوسط قيمة العقد</span>
                        <span className="font-medium">{formatCurrency(125000)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">معدل تجديد العقود</span>
                        <span className="font-medium text-green-600">92%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default FinancialReports;
