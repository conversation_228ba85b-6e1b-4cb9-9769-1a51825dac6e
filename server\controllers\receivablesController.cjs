const receivablesService = require('../services/receivablesService.cjs');

exports.getAllReceivables = async (req, res) => {
  try {
    const receivables = await receivablesService.getAllReceivables();
    res.json(receivables);
  } catch (error) {
    res.status(500).json({ error: 'فشل في جلب الاستحقاقات', details: error.message });
  }
};

exports.getReceivableById = async (req, res) => {
  try {
    const receivable = await receivablesService.getReceivableById(req.params.id);
    if (!receivable) return res.status(404).json({ error: 'الاستحقاق غير موجود' });
    res.json(receivable);
  } catch (error) {
    res.status(500).json({ error: 'فشل في جلب الاستحقاق', details: error.message });
  }
};

exports.createReceivable = async (req, res) => {
  try {
    const newReceivable = await receivablesService.createReceivable(req.body);
    res.status(201).json(newReceivable);
  } catch (error) {
    res.status(400).json({ error: 'فشل في إنشاء الاستحقاق', details: error.message });
  }
};
