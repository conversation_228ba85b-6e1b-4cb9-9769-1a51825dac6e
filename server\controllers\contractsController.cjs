const contractsService = require('../services/contractsService.cjs');

exports.getAllContracts = async (req, res) => {
  try {
    const contracts = await contractsService.getAllContracts();
    res.json(contracts);
  } catch (error) {
    res.status(500).json({ error: 'فشل في جلب العقود', details: error.message });
  }
};

exports.getContractById = async (req, res) => {
  try {
    const contract = await contractsService.getContractById(req.params.id);
    if (!contract) return res.status(404).json({ error: 'العقد غير موجود' });
    res.json(contract);
  } catch (error) {
    res.status(500).json({ error: 'فشل في جلب العقد', details: error.message });
  }
};

exports.createContract = async (req, res) => {
  try {
    const newContract = await contractsService.createContract(req.body);
    res.status(201).json(newContract);
  } catch (error) {
    res.status(400).json({ error: 'فشل في إنشاء العقد', details: error.message });
  }
};
