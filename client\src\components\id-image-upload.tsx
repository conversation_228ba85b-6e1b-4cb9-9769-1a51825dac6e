import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useLanguage } from "@/hooks/use-language";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { compressImageIfNeeded, formatFileSize, getBase64Size } from "@/lib/image-utils";
import {
  Upload,
  X,
  Eye,
  Printer,
  Download,
  CreditCard,
  ZoomIn,
  Loader2
} from "lucide-react";

interface IdImageUploadProps {
  image: string | null;
  onImageChange: (image: string | null) => void;
  className?: string;
}

export function IdImageUpload({ image, onImageChange, className }: IdImageUploadProps) {
  const { isRTL } = useLanguage();
  const { toast } = useToast();
  const [dragOver, setDragOver] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [isCompressing, setIsCompressing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    if (!file.type.startsWith('image/')) {
      alert(isRTL ? "يرجى اختيار ملف صورة فقط" : "Please select an image file only");
      return;
    }

    // Check file size (max 10MB original file)
    if (file.size > 10 * 1024 * 1024) {
      alert(isRTL ? "حجم الصورة كبير جداً (الحد الأقصى 10 ميجا)" : "Image size is too large (max 10MB)");
      return;
    }

    try {
      setIsCompressing(true);

      // Compress image to max 1MB
      const compressedImage = await compressImageIfNeeded(file, 1);
      const compressedSize = getBase64Size(compressedImage);

      console.log(`Image compressed: ${formatFileSize(file.size)} → ${formatFileSize(compressedSize)}`);

      onImageChange(compressedImage);

      // إظهار رسالة توضيحية
      toast({
        title: isRTL ? "تم رفع الصورة" : "Image Uploaded",
        description: isRTL ? "تم رفع الصورة بنجاح. لا تنس حفظ بيانات العميل." : "Image uploaded successfully. Don't forget to save the client data.",
        duration: 3000,
      });
    } catch (error) {
      console.error('Image compression failed:', error);
      alert(isRTL ? "فشل في معالجة الصورة" : "Failed to process image");
    } finally {
      setIsCompressing(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const removeImage = () => {
    onImageChange(null);
  };

  const printImage = () => {
    if (!image) return;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>طباعة صورة البطاقة</title>
            <style>
              body {
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
                text-align: center;
              }
              .header {
                margin-bottom: 20px;
                border-bottom: 2px solid #333;
                padding-bottom: 10px;
              }
              .title {
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 10px;
              }
              .date {
                font-size: 14px;
                color: #666;
                margin-bottom: 20px;
              }
              img {
                max-width: 100%;
                max-height: 80vh;
                border: 2px solid #333;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
              }
              @media print {
                body { padding: 0; }
                .header { page-break-inside: avoid; }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <div class="title">صورة بطاقة الهوية</div>
              <div class="date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</div>
            </div>
            <img src="${image}" alt="صورة البطاقة" onload="window.print(); window.close();" />
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  const downloadImage = () => {
    if (!image) return;

    const link = document.createElement('a');
    link.href = image;
    link.download = `ID_Card_${new Date().getTime()}.jpg`;
    link.click();
  };

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className={cn(
            "flex items-center gap-2",
            isRTL ? "flex-row-reverse" : ""
          )}>
            <CreditCard className="h-5 w-5" />
            {isRTL ? "صورة بطاقة الهوية" : "ID Card Image"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!image ? (
            // Upload Area
            <div
              className={cn(
                "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
                isCompressing ? "cursor-not-allowed opacity-50" : "cursor-pointer",
                dragOver ? "border-primary bg-primary/5" : "border-gray-300 hover:border-gray-400"
              )}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                if (!isCompressing) {
                  fileInputRef.current?.click();
                }
              }}
            >
              {isCompressing ? (
                <>
                  <Loader2 className="h-12 w-12 mx-auto mb-4 text-primary animate-spin" />
                  <p className="text-sm text-gray-600 mb-2">
                    {isRTL ? "جاري ضغط الصورة..." : "Compressing image..."}
                  </p>
                </>
              ) : (
                <>
                  <CreditCard className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-sm text-gray-600 mb-2">
                    {isRTL
                      ? "اسحب صورة البطاقة هنا أو اضغط للاختيار"
                      : "Drag ID card image here or click to select"
                    }
                  </p>
                  <p className="text-xs text-gray-500">
                    {isRTL
                      ? "الأنواع المدعومة: JPG, PNG, GIF (سيتم ضغط الصورة تلقائياً)"
                      : "Supported: JPG, PNG, GIF (Image will be compressed automatically)"
                    }
                  </p>
                </>
              )}
            </div>
          ) : (
            // Image Preview
            <div className="space-y-3">
              <div className="relative group">
                <img
                  src={image}
                  alt={isRTL ? "صورة البطاقة" : "ID Card"}
                  className="w-full h-48 object-cover rounded-lg border-2 border-gray-200 cursor-pointer"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setShowModal(true);
                  }}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                  <ZoomIn className="h-8 w-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                </div>
              </div>
              
              <div className={cn(
                "flex gap-2",
                isRTL ? "flex-row-reverse" : ""
              )}>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setShowModal(true);
                  }}
                  className="flex-1 gap-2"
                >
                  <Eye className="h-4 w-4" />
                  {isRTL ? "عرض" : "View"}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    printImage();
                  }}
                  className="flex-1 gap-2"
                >
                  <Printer className="h-4 w-4" />
                  {isRTL ? "طباعة" : "Print"}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    downloadImage();
                  }}
                  className="flex-1 gap-2"
                >
                  <Download className="h-4 w-4" />
                  {isRTL ? "تحميل" : "Download"}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    removeImage();
                  }}
                  className="text-red-600 hover:text-red-700"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          <Input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
          />
        </CardContent>
      </Card>

      {/* Image Modal */}
      {showModal && image && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50">
          <div className="relative max-w-5xl max-h-[95vh] p-4 w-full">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setShowModal(false);
              }}
              className="absolute top-2 right-2 text-white hover:bg-white/20 z-10"
            >
              <X className="h-6 w-6" />
            </Button>
            
            <div className="bg-white rounded-lg overflow-hidden max-h-full">
              <div className="p-4 border-b">
                <h3 className="font-medium text-center">
                  {isRTL ? "صورة بطاقة الهوية" : "ID Card Image"}
                </h3>
              </div>
              
              <div className="p-4 max-h-[80vh] overflow-auto flex items-center justify-center">
                <img
                  src={image}
                  alt={isRTL ? "صورة البطاقة" : "ID Card"}
                  className="max-w-full max-h-full object-contain"
                />
              </div>
              
              <div className={cn(
                "p-4 border-t flex gap-2 justify-center",
                isRTL ? "flex-row-reverse" : ""
              )}>
                <Button
                  variant="outline"
                  onClick={printImage}
                  className="gap-2"
                >
                  <Printer className="h-4 w-4" />
                  {isRTL ? "طباعة" : "Print"}
                </Button>
                <Button
                  variant="outline"
                  onClick={downloadImage}
                  className="gap-2"
                >
                  <Download className="h-4 w-4" />
                  {isRTL ? "تحميل" : "Download"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
