import React, { useState, useEffect } from 'react';
import { useDateContext } from '@/contexts/date-context';
import { Input } from '@/components/ui/input';
import { Calendar } from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';
import moment from 'moment';

interface FormattedDateInputProps {
  value?: string;
  onChange?: (date: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  id?: string;
}

export const FormattedDateInput: React.FC<FormattedDateInputProps> = ({
  value,
  onChange,
  placeholder,
  className = '',
  disabled = false,
  required = false,
  id
}) => {
  const { formatDate, getDateFormat, parseDate, formatDateForInput } = useDateContext();
  const { language } = useLanguage();
  const [showNativePicker, setShowNativePicker] = useState(false);
  const [inputValue, setInputValue] = useState('');

  // Determine text direction based on language
  const isRTL = language === 'ar';
  const textDirection = isRTL ? 'rtl' : 'ltr';

  // Update input value when prop value changes
  useEffect(() => {
    setInputValue(value ? formatDate(value) : '');
  }, [value, formatDate]);

  // Get placeholder based on date format
  const getPlaceholder = (): string => {
    if (placeholder) return placeholder;
    const format = getDateFormat();
    return `مثال: ${format.replace(/YYYY/g, '2025').replace(/MM/g, '07').replace(/DD/g, '15')}`;
  };

  // Handle manual text input
  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Try to parse the date as user types
    if (newValue.length >= 8) { // Minimum length for a date
      const parsedDate = tryParseDate(newValue);
      if (parsedDate) {
        const isoString = formatDateForInput(parsedDate);
        onChange?.(isoString);
      }
    }
  };

  // Try to parse date from various formats
  const tryParseDate = (dateString: string): Date | null => {
    if (!dateString) return null;

    const currentFormat = getDateFormat();
    const formats = [
      currentFormat,
      'DD/MM/YYYY',
      'MM/DD/YYYY',
      'YYYY/MM/DD',
      'DD-MM-YYYY',
      'MM-DD-YYYY',
      'YYYY-MM-DD'
    ];

    for (const format of formats) {
      const momentDate = moment(dateString, format, true);
      if (momentDate.isValid()) {
        return momentDate.toDate();
      }
    }

    return null;
  };

  // Handle native date input change
  const handleNativeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isoDate = e.target.value; // This is always YYYY-MM-DD
    onChange?.(isoDate);
    setShowNativePicker(false);
  };

  // Handle calendar icon click
  const handleCalendarClick = () => {
    setShowNativePicker(true);
  };

  // Handle input blur (when user finishes typing)
  const handleBlur = () => {
    if (inputValue) {
      const parsedDate = tryParseDate(inputValue);
      if (parsedDate) {
        const isoString = formatDateForInput(parsedDate);
        const formattedDisplay = formatDate(isoString);
        setInputValue(formattedDisplay);
        onChange?.(isoString);
      } else {
        // Reset to original value if parsing failed
        setInputValue(value ? formatDate(value) : '');
      }
    }
  };

  return (
    <div className="relative">
      {/* Main input (allows typing and shows formatted date) */}
      <div className="relative">
        <Input
          id={id}
          type="text"
          value={inputValue}
          onChange={handleTextChange}
          onBlur={handleBlur}
          placeholder={getPlaceholder()}
          className={`${className} pr-10 ${isRTL ? 'text-right' : 'text-left'}`}
          style={{
            direction: textDirection,
            textAlign: isRTL ? 'right' : 'left',
            unicodeBidi: 'plaintext'
          }}
          disabled={disabled}
          required={required}
        />
        <Calendar
          className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
          onClick={handleCalendarClick}
        />
      </div>

      {/* Hidden native date picker */}
      {showNativePicker && (
        <div className="absolute inset-0 z-10">
          <Input
            type="date"
            value={value || ''}
            onChange={handleNativeChange}
            onBlur={() => setShowNativePicker(false)}
            className={`w-full h-full opacity-0 ${isRTL ? 'text-right' : 'text-left'}`}
            style={{
              direction: textDirection,
              textAlign: isRTL ? 'right' : 'left',
              unicodeBidi: 'plaintext'
            }}
            autoFocus
          />
        </div>
      )}

      {/* Debug info (remove in production) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="text-xs text-gray-400 mt-1">
          <div>القيمة: {value || 'فارغ'}</div>
          <div>المعروض: {inputValue || 'فارغ'}</div>
          <div>التنسيق: {getDateFormat()}</div>
        </div>
      )}
    </div>
  );
};

export default FormattedDateInput;
