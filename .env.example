# ===== CONTRACT MANAGEMENT SYSTEM - ENVIRONMENT VARIABLES EXAMPLE =====
# Author: Augment Code
# Description: Example environment variables file for Contract Management System
# 
# INSTRUCTIONS:
# 1. Copy this file to .env
# 2. Replace all placeholder values with your actual configuration
# 3. Never commit the .env file to version control

# ===== DATABASE CONFIGURATION =====
# SQLite Database Configuration
DB_TYPE=sqlite
DB_PATH=./contract-app.sqlite
DB_NAME=contract-app
DATABASE_URL=sqlite://./contract-app.sqlite

# Database Connection Pool Settings
DB_POOL_MIN=1
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=60000

# SQLite Optimization Settings
DB_JOURNAL_MODE=WAL
DB_SYNCHRONOUS=NORMAL
DB_CACHE_SIZE=1000
DB_TEMP_STORE=MEMORY
DB_BUSY_TIMEOUT=30000
DB_PAGE_SIZE=4096

# Database Security Settings
DB_FOREIGN_KEYS=ON
DB_QUERY_ONLY=OFF

# ===== SERVER CONFIGURATION =====
# Server Settings
PORT=3001
NODE_ENV=development
HOST=localhost

# CORS Settings
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization

# Request Limits
REQUEST_SIZE_LIMIT=10mb
REQUEST_TIMEOUT=30000

# ===== APPLICATION SETTINGS =====
# Application Information
APP_NAME=Contract Management System
APP_VERSION=3.0.0
APP_DESCRIPTION=Advanced Contract Management System with Connection Pool

# Default Company Settings (customize these for your organization)
DEFAULT_COMPANY_NAME=شركة إدارة العقود المصرية
DEFAULT_PROGRAM_NAME=نظام إدارة العقود المتقدم
DEFAULT_LANGUAGE=ar
DEFAULT_CURRENCY=جنيه مصري
DEFAULT_CURRENCY_SYMBOL=ج.م
DEFAULT_COUNTRY=مصر

# ===== SECURITY SETTINGS =====
# Session Configuration (CHANGE THESE IN PRODUCTION!)
SESSION_SECRET=change-this-to-a-random-secure-string-in-production
SESSION_TIMEOUT=1800000
SESSION_SECURE=false
SESSION_HTTP_ONLY=true

# JWT Configuration (CHANGE THESE IN PRODUCTION!)
JWT_SECRET=change-this-to-a-random-secure-jwt-secret-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Password Security
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8

# ===== LOGGING CONFIGURATION =====
# Logging Settings
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5
LOG_DATE_PATTERN=YYYY-MM-DD

# Database Logging
DB_LOG_QUERIES=true
DB_LOG_ERRORS=true
DB_LOG_PERFORMANCE=true

# ===== BACKUP CONFIGURATION =====
# Backup Settings
BACKUP_ENABLED=true
BACKUP_INTERVAL=daily
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=./backups
BACKUP_COMPRESSION=true

# ===== EMAIL CONFIGURATION =====
# Email Settings (configure if you need email notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-specific-password
EMAIL_FROM=<EMAIL>

# ===== FILE UPLOAD CONFIGURATION =====
# File Upload Settings
UPLOAD_MAX_SIZE=10mb
UPLOAD_ALLOWED_TYPES=pdf,doc,docx,jpg,jpeg,png,gif
UPLOAD_PATH=./uploads
UPLOAD_TEMP_PATH=./temp

# ===== CACHE CONFIGURATION =====
# Cache Settings
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_KEYS=1000
CACHE_CHECK_PERIOD=600

# ===== RATE LIMITING =====
# Rate Limiting Settings
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL=false

# ===== DEVELOPMENT SETTINGS =====
# Development Mode Settings
DEBUG_MODE=false
VERBOSE_LOGGING=true
ENABLE_CORS=true
ENABLE_COMPRESSION=true

# Hot Reload Settings
HOT_RELOAD=false
WATCH_FILES=true

# ===== PRODUCTION SETTINGS =====
# Production Optimizations
ENABLE_CLUSTERING=false
CLUSTER_WORKERS=auto
ENABLE_GZIP=true
ENABLE_ETAG=true

# Security Headers
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CSRF_PROTECTION=false

# ===== MONITORING & HEALTH CHECKS =====
# Health Check Settings
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Performance Monitoring
PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=1000
MEMORY_USAGE_ALERT_THRESHOLD=80

# ===== FEATURE FLAGS =====
# Feature Toggles
ENABLE_NEW_CONTRACT_TABLE=true
ENABLE_ADVANCED_REPORTING=true
ENABLE_AUDIT_LOGGING=true
ENABLE_REAL_TIME_NOTIFICATIONS=false

# Experimental Features
ENABLE_EXPERIMENTAL_FEATURES=false
ENABLE_BETA_UI=false

# ===== INTEGRATION SETTINGS =====
# External API Settings
EXTERNAL_API_TIMEOUT=30000
EXTERNAL_API_RETRY_ATTEMPTS=3
EXTERNAL_API_RETRY_DELAY=1000

# Webhook Settings
WEBHOOK_ENABLED=false
WEBHOOK_SECRET=change-this-webhook-secret-in-production
WEBHOOK_TIMEOUT=10000

# ===== NOTES FOR DEVELOPERS =====
# 
# IMPORTANT SECURITY NOTES:
# 1. Always change SESSION_SECRET and JWT_SECRET in production
# 2. Use strong, randomly generated secrets (at least 32 characters)
# 3. Never commit the actual .env file to version control
# 4. Use different secrets for different environments (dev, staging, prod)
# 5. Regularly rotate secrets in production
#
# DATABASE NOTES:
# 1. The system currently uses SQLite for simplicity
# 2. For production, consider using PostgreSQL or MySQL
# 3. Adjust DB_CACHE_SIZE based on available memory
# 4. Monitor DB_BUSY_TIMEOUT if you experience lock issues
#
# PERFORMANCE NOTES:
# 1. Increase DB_CACHE_SIZE for better performance (more memory usage)
# 2. Adjust REQUEST_TIMEOUT based on your application needs
# 3. Enable clustering in production for better performance
# 4. Monitor SLOW_QUERY_THRESHOLD and optimize slow queries
