const paymentsService = require('../services/paymentsService.cjs');

exports.getAllPayments = async (req, res) => {
  try {
    const payments = await paymentsService.getAllPayments();
    res.json(payments);
  } catch (error) {
    res.status(500).json({ error: 'فشل في جلب المدفوعات', details: error.message });
  }
};

exports.getPaymentById = async (req, res) => {
  try {
    const payment = await paymentsService.getPaymentById(req.params.id);
    if (!payment) return res.status(404).json({ error: 'المدفوعات غير موجودة' });
    res.json(payment);
  } catch (error) {
    res.status(500).json({ error: 'فشل في جلب المدفوعات', details: error.message });
  }
};

exports.createPayment = async (req, res) => {
  try {
    const newPayment = await paymentsService.createPayment(req.body);
    res.status(201).json(newPayment);
  } catch (error) {
    res.status(400).json({ error: 'فشل في إنشاء المدفوعات', details: error.message });
  }
};
