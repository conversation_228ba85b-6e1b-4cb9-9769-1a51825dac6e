// ===== REPORTS STORE =====
// Author: Augment Code
// Description: Centralized state management for reports using Zustand

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import apiMiddleware from '../lib/apiMiddleware';

// Initial state
const initialState = {
  // Reports data
  reports: [],
  dashboardStats: null,
  financialReports: [],
  contractReports: [],
  clientReports: [],
  paymentReports: [],
  selectedReport: null,
  
  // Search and filters
  searchQuery: '',
  reportTypeFilter: '',
  dateRangeFilter: { start: null, end: null },
  clientFilter: '',
  contractFilter: '',
  
  // Loading states
  isLoading: false,
  isGenerating: false,
  isExporting: false,
  isDashboardLoading: false,
  
  // Error states
  error: null,
  generateError: null,
  exportError: null,
  dashboardError: null,
  
  // Cache and metadata
  lastFetched: null,
  dashboardLastFetched: null,
  
  // UI state
  showReportDetails: false,
  
  // Report configurations
  reportConfigs: [],
  configsLoading: false,
  configsError: null
};

// Create the reports store
export const useReportsStore = create()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Actions
        actions: {
          // Fetch dashboard statistics
          async fetchDashboardStats(forceRefresh = false) {
            const state = get();
            
            if (state.isDashboardLoading || (!forceRefresh && state.dashboardLastFetched && 
                Date.now() - new Date(state.dashboardLastFetched).getTime() < 60000)) {
              return state.dashboardStats;
            }

            set((state) => {
              state.isDashboardLoading = true;
              state.dashboardError = null;
            });

            try {
              const stats = await apiMiddleware.get('/api/reports/dashboard', {
                loadingMessage: 'جاري تحميل إحصائيات لوحة التحكم...',
                showErrorNotification: true
              });

              set((state) => {
                state.dashboardStats = stats;
                state.isDashboardLoading = false;
                state.dashboardLastFetched = new Date().toISOString();
                state.dashboardError = null;
              });

              return stats;
            } catch (error) {
              console.error('Failed to fetch dashboard stats:', error);
              
              set((state) => {
                state.isDashboardLoading = false;
                state.dashboardError = error.message || 'فشل في تحميل إحصائيات لوحة التحكم';
              });

              throw error;
            }
          },

          // Fetch reports by type
          async fetchReportsByType(reportType, filters = {}) {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const queryParams = new URLSearchParams(filters).toString();
              const url = `/api/reports/${reportType}${queryParams ? `?${queryParams}` : ''}`;
              
              const reports = await apiMiddleware.get(url, {
                loadingMessage: `جاري تحميل تقارير ${reportType}...`,
                showErrorNotification: true
              });

              set((state) => {
                switch (reportType) {
                  case 'financial':
                    state.financialReports = reports || [];
                    break;
                  case 'contracts':
                    state.contractReports = reports || [];
                    break;
                  case 'clients':
                    state.clientReports = reports || [];
                    break;
                  case 'payments':
                    state.paymentReports = reports || [];
                    break;
                  default:
                    state.reports = reports || [];
                }
                state.isLoading = false;
                state.lastFetched = new Date().toISOString();
                state.error = null;
              });

              return reports;
            } catch (error) {
              console.error(`Failed to fetch ${reportType} reports:`, error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || `فشل في تحميل تقارير ${reportType}`;
              });

              throw error;
            }
          },

          // Generate custom report
          async generateReport(reportConfig) {
            set((state) => {
              state.isGenerating = true;
              state.generateError = null;
            });

            try {
              const report = await apiMiddleware.post('/api/reports/generate', reportConfig, {
                loadingMessage: 'جاري إنشاء التقرير...',
                successMessage: 'تم إنشاء التقرير بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.reports.unshift(report);
                state.isGenerating = false;
                state.generateError = null;
              });

              return { success: true, data: report };
            } catch (error) {
              console.error('Failed to generate report:', error);
              
              set((state) => {
                state.isGenerating = false;
                state.generateError = error.message || 'فشل في إنشاء التقرير';
              });

              return { success: false, error: error.message };
            }
          },

          // Export report
          async exportReport(reportId, format = 'pdf') {
            set((state) => {
              state.isExporting = true;
              state.exportError = null;
            });

            try {
              const exportData = await apiMiddleware.get(`/api/reports/${reportId}/export/${format}`, {
                loadingMessage: `جاري تصدير التقرير كـ ${format.toUpperCase()}...`,
                successMessage: 'تم تصدير التقرير بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.isExporting = false;
                state.exportError = null;
              });

              return { success: true, data: exportData };
            } catch (error) {
              console.error('Failed to export report:', error);
              
              set((state) => {
                state.isExporting = false;
                state.exportError = error.message || 'فشل في تصدير التقرير';
              });

              return { success: false, error: error.message };
            }
          },

          // Fetch financial summary
          async fetchFinancialSummary(dateRange) {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const summary = await apiMiddleware.get('/api/reports/financial-summary', {
                params: dateRange,
                loadingMessage: 'جاري تحميل الملخص المالي...',
                showErrorNotification: true
              });

              return { success: true, data: summary };
            } catch (error) {
              console.error('Failed to fetch financial summary:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحميل الملخص المالي';
              });

              return { success: false, error: error.message };
            }
          },

          // Fetch contracts analysis
          async fetchContractsAnalysis(filters = {}) {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const analysis = await apiMiddleware.get('/api/reports/contracts-analysis', {
                params: filters,
                loadingMessage: 'جاري تحليل العقود...',
                showErrorNotification: true
              });

              return { success: true, data: analysis };
            } catch (error) {
              console.error('Failed to fetch contracts analysis:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحليل العقود';
              });

              return { success: false, error: error.message };
            }
          },

          // Fetch clients performance
          async fetchClientsPerformance(filters = {}) {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const performance = await apiMiddleware.get('/api/reports/clients-performance', {
                params: filters,
                loadingMessage: 'جاري تحليل أداء العملاء...',
                showErrorNotification: true
              });

              return { success: true, data: performance };
            } catch (error) {
              console.error('Failed to fetch clients performance:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحليل أداء العملاء';
              });

              return { success: false, error: error.message };
            }
          },

          // Fetch payments analysis
          async fetchPaymentsAnalysis(dateRange) {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const analysis = await apiMiddleware.get('/api/reports/payments-analysis', {
                params: dateRange,
                loadingMessage: 'جاري تحليل المدفوعات...',
                showErrorNotification: true
              });

              return { success: true, data: analysis };
            } catch (error) {
              console.error('Failed to fetch payments analysis:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحليل المدفوعات';
              });

              return { success: false, error: error.message };
            }
          },

          // Fetch report configurations
          async fetchReportConfigs() {
            set((state) => {
              state.configsLoading = true;
              state.configsError = null;
            });

            try {
              const configs = await apiMiddleware.get('/api/reports/configurations', {
                showErrorNotification: false
              });

              set((state) => {
                state.reportConfigs = configs || [];
                state.configsLoading = false;
                state.configsError = null;
              });

              return configs;
            } catch (error) {
              console.error('Failed to fetch report configs:', error);
              
              set((state) => {
                state.reportConfigs = [];
                state.configsLoading = false;
                state.configsError = error.message;
              });

              return [];
            }
          },

          // Save report configuration
          async saveReportConfig(config) {
            try {
              const savedConfig = await apiMiddleware.post('/api/reports/configurations', config, {
                loadingMessage: 'جاري حفظ إعدادات التقرير...',
                successMessage: 'تم حفظ إعدادات التقرير بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.reportConfigs.unshift(savedConfig);
              });

              return { success: true, data: savedConfig };
            } catch (error) {
              console.error('Failed to save report config:', error);
              return { success: false, error: error.message };
            }
          },

          // Set selected report
          setSelectedReport(report) {
            set((state) => {
              state.selectedReport = report;
              state.showReportDetails = !!report;
            });
          },

          // Set filters
          setSearchQuery(query) {
            set((state) => {
              state.searchQuery = query;
            });
          },

          setReportTypeFilter(type) {
            set((state) => {
              state.reportTypeFilter = type;
            });
          },

          setDateRangeFilter(dateRange) {
            set((state) => {
              state.dateRangeFilter = dateRange;
            });
          },

          setClientFilter(clientId) {
            set((state) => {
              state.clientFilter = clientId;
            });
          },

          setContractFilter(contractId) {
            set((state) => {
              state.contractFilter = contractId;
            });
          },

          // Clear errors
          clearErrors() {
            set((state) => {
              state.error = null;
              state.generateError = null;
              state.exportError = null;
              state.dashboardError = null;
              state.configsError = null;
            });
          },

          // Get filtered reports
          getFilteredReports() {
            const state = get();
            const { 
              reports, 
              searchQuery, 
              reportTypeFilter, 
              dateRangeFilter,
              clientFilter,
              contractFilter 
            } = state;

            return reports.filter(report => {
              // Search filter
              const matchesSearch = !searchQuery ||
                report.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                report.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                report.reportType?.toLowerCase().includes(searchQuery.toLowerCase());

              // Report type filter
              const matchesType = !reportTypeFilter || reportTypeFilter === "all" ||
                report.reportType === reportTypeFilter;

              // Date range filter
              const matchesDateRange = !dateRangeFilter.start || !dateRangeFilter.end ||
                (new Date(report.createdAt) >= new Date(dateRangeFilter.start) &&
                 new Date(report.createdAt) <= new Date(dateRangeFilter.end));

              // Client filter
              const matchesClient = !clientFilter || clientFilter === "all" ||
                report.clientId === clientFilter;

              // Contract filter
              const matchesContract = !contractFilter || contractFilter === "all" ||
                report.contractId === contractFilter;

              return matchesSearch && matchesType && matchesDateRange && matchesClient && matchesContract;
            });
          },

          // Get report by ID
          getReportById(reportId) {
            const state = get();
            return state.reports.find(report => report.id === reportId);
          },

          // Refresh dashboard
          async refreshDashboard() {
            return get().actions.fetchDashboardStats(true);
          },

          // Refresh data
          async refresh() {
            const actions = get().actions;
            await Promise.allSettled([
              actions.fetchDashboardStats(true),
              actions.fetchReportConfigs()
            ]);
          }
        }
      })),
      {
        name: 'reports-store',
        partialize: (state) => ({
          reports: state.reports,
          dashboardStats: state.dashboardStats,
          lastFetched: state.lastFetched,
          dashboardLastFetched: state.dashboardLastFetched,
          searchQuery: state.searchQuery,
          reportTypeFilter: state.reportTypeFilter,
          dateRangeFilter: state.dateRangeFilter,
          reportConfigs: state.reportConfigs
        })
      }
    ),
    {
      name: 'reports-store'
    }
  )
);

// Selectors
export const useReports = () => useReportsStore((state) => state.reports);
export const useDashboardStats = () => useReportsStore((state) => state.dashboardStats);
export const useFinancialReports = () => useReportsStore((state) => state.financialReports);
export const useContractReports = () => useReportsStore((state) => state.contractReports);
export const useClientReports = () => useReportsStore((state) => state.clientReports);
export const usePaymentReports = () => useReportsStore((state) => state.paymentReports);
export const useReportsLoading = () => useReportsStore((state) => state.isLoading);
export const useReportsError = () => useReportsStore((state) => state.error);
export const useSelectedReport = () => useReportsStore((state) => state.selectedReport);
export const useReportConfigs = () => useReportsStore((state) => state.reportConfigs);
export const useReportsActions = () => useReportsStore((state) => state.actions);

export const useFilteredReports = () => useReportsStore((state) => 
  state.actions.getFilteredReports()
);

export const useReportsStatus = () => useReportsStore((state) => ({
  isLoading: state.isLoading,
  isGenerating: state.isGenerating,
  isExporting: state.isExporting,
  isDashboardLoading: state.isDashboardLoading,
  configsLoading: state.configsLoading,
  error: state.error,
  generateError: state.generateError,
  exportError: state.exportError,
  dashboardError: state.dashboardError,
  configsError: state.configsError,
  lastFetched: state.lastFetched,
  dashboardLastFetched: state.dashboardLastFetched
}));

export default useReportsStore;
