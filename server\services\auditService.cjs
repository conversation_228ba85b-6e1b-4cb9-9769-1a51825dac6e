// ===== AUDIT SERVICE =====
// Author: Augment Code
// Description: Service for audit trail and system monitoring

const { formatDateArabic } = require('../utils/index.cjs');

class AuditService {
  constructor(db) {
    this.db = db;
  }

  /**
   * Log user action for audit trail
   * @param {Object} params - Audit parameters
   * @param {string} params.tableName - Table name
   * @param {number} params.recordId - Record ID
   * @param {string} params.action - Action type (INSERT, UPDATE, DELETE)
   * @param {Object} params.oldValues - Old values (for UPDATE/DELETE)
   * @param {Object} params.newValues - New values (for INSERT/UPDATE)
   * @param {number} params.userId - User ID
   * @param {string} params.userIP - User IP address
   * @param {string} params.userAgent - User agent string
   * @param {string} params.sessionId - Session ID
   * @returns {Promise<number>} Audit log ID
   */
  async logAction(params) {
    const {
      tableName,
      recordId,
      action,
      oldValues = null,
      newValues = null,
      userId = null,
      userIP = null,
      userAgent = null,
      sessionId = null
    } = params;

    // Calculate changed fields for UPDATE actions
    let changedFields = null;
    if (action === 'UPDATE' && oldValues && newValues) {
      changedFields = this.getChangedFields(oldValues, newValues);
    }

    return new Promise((resolve, reject) => {
      this.db.run(`
        INSERT INTO AuditLog (
          tableName, recordId, action, oldValues, newValues, 
          changedFields, userId, userIP, userAgent, sessionId
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        tableName,
        recordId,
        action,
        oldValues ? JSON.stringify(oldValues) : null,
        newValues ? JSON.stringify(newValues) : null,
        changedFields ? JSON.stringify(changedFields) : null,
        userId,
        userIP,
        userAgent,
        sessionId
      ], function(err) {
        if (err) reject(err);
        else resolve(this.lastID);
      });
    });
  }

  /**
   * Get audit trail for a specific record
   * @param {string} tableName - Table name
   * @param {number} recordId - Record ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Audit trail entries
   */
  async getRecordAuditTrail(tableName, recordId, options = {}) {
    const { limit = 50, offset = 0, action = null } = options;
    
    let query = `
      SELECT * FROM AuditLog 
      WHERE tableName = ? AND recordId = ?
    `;
    const params = [tableName, recordId];

    if (action) {
      query += ` AND action = ?`;
      params.push(action);
    }

    query += ` ORDER BY timestamp DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    return new Promise((resolve, reject) => {
      this.db.all(query, params, (err, rows) => {
        if (err) reject(err);
        else resolve(this.formatAuditEntries(rows));
      });
    });
  }

  /**
   * Get audit trail for a user
   * @param {number} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} User audit trail
   */
  async getUserAuditTrail(userId, options = {}) {
    const { 
      limit = 100, 
      offset = 0, 
      tableName = null, 
      action = null,
      startDate = null,
      endDate = null
    } = options;
    
    let query = `SELECT * FROM AuditLog WHERE userId = ?`;
    const params = [userId];

    if (tableName) {
      query += ` AND tableName = ?`;
      params.push(tableName);
    }

    if (action) {
      query += ` AND action = ?`;
      params.push(action);
    }

    if (startDate) {
      query += ` AND timestamp >= ?`;
      params.push(startDate);
    }

    if (endDate) {
      query += ` AND timestamp <= ?`;
      params.push(endDate);
    }

    query += ` ORDER BY timestamp DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    return new Promise((resolve, reject) => {
      this.db.all(query, params, (err, rows) => {
        if (err) reject(err);
        else resolve(this.formatAuditEntries(rows));
      });
    });
  }

  /**
   * Get system audit statistics
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Audit statistics
   */
  async getAuditStatistics(options = {}) {
    const { 
      startDate = null, 
      endDate = null,
      tableName = null 
    } = options;

    let whereClause = '';
    const params = [];

    if (startDate || endDate || tableName) {
      const conditions = [];
      
      if (startDate) {
        conditions.push('timestamp >= ?');
        params.push(startDate);
      }
      
      if (endDate) {
        conditions.push('timestamp <= ?');
        params.push(endDate);
      }
      
      if (tableName) {
        conditions.push('tableName = ?');
        params.push(tableName);
      }
      
      whereClause = 'WHERE ' + conditions.join(' AND ');
    }

    const queries = {
      totalActions: `SELECT COUNT(*) as count FROM AuditLog ${whereClause}`,
      actionsByType: `
        SELECT action, COUNT(*) as count 
        FROM AuditLog ${whereClause}
        GROUP BY action
      `,
      actionsByTable: `
        SELECT tableName, COUNT(*) as count 
        FROM AuditLog ${whereClause}
        GROUP BY tableName
      `,
      actionsByUser: `
        SELECT userId, COUNT(*) as count 
        FROM AuditLog ${whereClause}
        GROUP BY userId
        ORDER BY count DESC
        LIMIT 10
      `,
      recentActions: `
        SELECT * FROM AuditLog ${whereClause}
        ORDER BY timestamp DESC
        LIMIT 20
      `
    };

    const results = {};

    for (const [key, query] of Object.entries(queries)) {
      results[key] = await new Promise((resolve, reject) => {
        if (key === 'totalActions') {
          this.db.get(query, params, (err, row) => {
            if (err) reject(err);
            else resolve(row.count);
          });
        } else {
          this.db.all(query, params, (err, rows) => {
            if (err) reject(err);
            else resolve(key === 'recentActions' ? this.formatAuditEntries(rows) : rows);
          });
        }
      });
    }

    return results;
  }

  /**
   * Clean up old audit logs
   * @param {number} retentionDays - Number of days to keep logs
   * @returns {Promise<number>} Number of deleted records
   */
  async cleanupOldLogs(retentionDays = 365) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    return new Promise((resolve, reject) => {
      this.db.run(`
        DELETE FROM AuditLog 
        WHERE timestamp < ?
      `, [cutoffDate.toISOString()], function(err) {
        if (err) reject(err);
        else resolve(this.changes);
      });
    });
  }

  /**
   * Export audit trail to CSV
   * @param {Object} filters - Export filters
   * @returns {Promise<string>} CSV content
   */
  async exportAuditTrail(filters = {}) {
    const {
      startDate = null,
      endDate = null,
      tableName = null,
      userId = null,
      action = null
    } = filters;

    let query = `SELECT * FROM AuditLog WHERE 1=1`;
    const params = [];

    if (startDate) {
      query += ` AND timestamp >= ?`;
      params.push(startDate);
    }

    if (endDate) {
      query += ` AND timestamp <= ?`;
      params.push(endDate);
    }

    if (tableName) {
      query += ` AND tableName = ?`;
      params.push(tableName);
    }

    if (userId) {
      query += ` AND userId = ?`;
      params.push(userId);
    }

    if (action) {
      query += ` AND action = ?`;
      params.push(action);
    }

    query += ` ORDER BY timestamp DESC`;

    const rows = await new Promise((resolve, reject) => {
      this.db.all(query, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    // Convert to CSV
    const headers = [
      'التاريخ والوقت',
      'الجدول',
      'رقم السجل',
      'الإجراء',
      'المستخدم',
      'عنوان IP',
      'الحقول المتغيرة'
    ];

    let csv = headers.join(',') + '\n';

    for (const row of rows) {
      const csvRow = [
        formatDateArabic(row.timestamp),
        row.tableName,
        row.recordId,
        this.getActionLabel(row.action),
        row.userId || 'غير محدد',
        row.userIP || 'غير محدد',
        row.changedFields ? JSON.parse(row.changedFields).join(', ') : 'غير محدد'
      ].map(field => `"${field}"`).join(',');

      csv += csvRow + '\n';
    }

    return csv;
  }

  // Helper methods
  getChangedFields(oldValues, newValues) {
    const changed = [];
    
    for (const key in newValues) {
      if (oldValues[key] !== newValues[key]) {
        changed.push(key);
      }
    }
    
    return changed;
  }

  formatAuditEntries(entries) {
    return entries.map(entry => ({
      ...entry,
      oldValues: entry.oldValues ? JSON.parse(entry.oldValues) : null,
      newValues: entry.newValues ? JSON.parse(entry.newValues) : null,
      changedFields: entry.changedFields ? JSON.parse(entry.changedFields) : null,
      formattedTimestamp: formatDateArabic(entry.timestamp),
      actionLabel: this.getActionLabel(entry.action)
    }));
  }

  getActionLabel(action) {
    const labels = {
      'INSERT': 'إضافة',
      'UPDATE': 'تعديل',
      'DELETE': 'حذف'
    };
    return labels[action] || action;
  }
}

module.exports = AuditService;
