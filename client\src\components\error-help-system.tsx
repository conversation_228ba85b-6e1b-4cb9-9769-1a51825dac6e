import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, Di<PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  HelpCircle, 
  BookOpen, 
  AlertTriangle, 
  CheckCircle, 
  ExternalLink,
  Search,
  Settings,
  Plus,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface HelpContent {
  title: string;
  description: string;
  steps?: string[];
  tips?: string[];
  relatedActions?: Array<{
    label: string;
    action: string;
    icon?: React.ComponentType<any>;
  }>;
}

interface ErrorHelpSystemProps {
  errorCode?: string;
  operation?: string;
  className?: string;
}

const helpContent: Record<string, HelpContent> = {
  // Client-related errors
  'CLIENT_ID_EXISTS': {
    title: 'رقم العميل مستخدم من قبل',
    description: 'هذا الرقم مسجل لعميل آخر في النظام',
    steps: [
      'تحقق من رقم العميل المدخل',
      'ابحث عن العميل الموجود باستخدام الرقم',
      'إذا كان نفس العميل، استخدم بياناته الموجودة',
      'إذا كان عميل مختلف، اختر رقم عميل جديد'
    ],
    tips: [
      'يمكن استخدام رقم الهوية الوطنية كرقم عميل',
      'تأكد من عدم وجود أرقام مكررة',
      'استخدم نظام ترقيم متسلسل للعملاء الجدد'
    ],
    relatedActions: [
      { label: 'البحث عن العميل', action: 'search_client', icon: Search },
      { label: 'إنشاء رقم جديد', action: 'generate_id', icon: RefreshCw }
    ]
  },
  
  'CLIENT_PHONE_INVALID': {
    title: 'رقم الهاتف غير صحيح',
    description: 'تنسيق رقم الهاتف المدخل غير مقبول',
    steps: [
      'تأكد من أن الرقم يحتوي على أرقام فقط',
      'أضف رمز الدولة إذا لزم الأمر (+966 للسعودية)',
      'تحقق من طول الرقم (10 أرقام للأرقام المحلية)',
      'احذف أي مسافات أو رموز إضافية'
    ],
    tips: [
      'مثال صحيح: 0501234567',
      'مثال صحيح: +966501234567',
      'تجنب استخدام المسافات أو الشرطات',
      'تأكد من بداية الرقم بـ 05 للجوال'
    ]
  },
  
  'CLIENT_EMAIL_INVALID': {
    title: 'البريد الإلكتروني غير صحيح',
    description: 'تنسيق البريد الإلكتروني المدخل غير صحيح',
    steps: [
      'تأكد من وجود رمز @ في البريد',
      'تحقق من وجود نطاق صحيح بعد @',
      'تأكد من عدم وجود مسافات',
      'استخدم أحرف إنجليزية فقط'
    ],
    tips: [
      'مثال صحيح: <EMAIL>',
      'مثال صحيح: <EMAIL>',
      'تجنب استخدام الأحرف العربية',
      'تأكد من صحة النطاق (.com, .org, .sa)'
    ]
  },
  
  // Contract-related errors
  'CONTRACT_NO_PRODUCTS': {
    title: 'العقد لا يحتوي على منتجات',
    description: 'يجب إضافة منتج واحد على الأقل للعقد',
    steps: [
      'انقر على زر "إضافة منتج"',
      'املأ بيانات المنتج (الاسم، المساحة، السعر)',
      'اختر نوع الفوترة المناسب',
      'احفظ المنتج قبل حفظ العقد'
    ],
    tips: [
      'يمكن إضافة عدة منتجات للعقد الواحد',
      'تأكد من صحة المساحة وسعر المتر',
      'اختر نوع الفوترة حسب اتفاقية العقد'
    ],
    relatedActions: [
      { label: 'إضافة منتج', action: 'add_product', icon: Plus }
    ]
  },
  
  'REFERENCE_DATA_MISSING': {
    title: 'بيانات مرجعية مطلوبة',
    description: 'يجب إعداد البيانات المرجعية قبل المتابعة',
    steps: [
      'اذهب إلى صفحة الإعدادات',
      'انقر على "البيانات المرجعية"',
      'أضف البيانات المطلوبة (المناطق، البنوك، إلخ)',
      'احفظ البيانات وعد للصفحة السابقة'
    ],
    tips: [
      'البيانات المرجعية تستخدم في عدة أماكن',
      'تأكد من إضافة جميع البيانات المطلوبة',
      'يمكن تعديل البيانات المرجعية لاحقاً'
    ],
    relatedActions: [
      { label: 'إعداد البيانات المرجعية', action: 'setup_reference_data', icon: Settings }
    ]
  },
  
  // Database errors
  'DATABASE_BUSY': {
    title: 'قاعدة البيانات مشغولة',
    description: 'النظام مشغول حالياً بعملية أخرى',
    steps: [
      'انتظر بضع ثوان',
      'أعد المحاولة',
      'إذا استمر الخطأ، أعد تشغيل البرنامج'
    ],
    tips: [
      'هذا خطأ مؤقت عادة',
      'تجنب النقر المتكرر على الأزرار',
      'انتظر انتهاء العمليات الجارية'
    ],
    relatedActions: [
      { label: 'إعادة المحاولة', action: 'retry_now', icon: RefreshCw }
    ]
  },
  
  // Network errors
  'CONNECTION_FAILED': {
    title: 'فشل الاتصال بالخادم',
    description: 'تعذر الاتصال بالخادم أو قاعدة البيانات',
    steps: [
      'تحقق من اتصال الإنترنت',
      'تأكد من تشغيل الخادم',
      'أعد تشغيل البرنامج إذا لزم الأمر',
      'اتصل بالدعم الفني إذا استمر الخطأ'
    ],
    tips: [
      'تحقق من إعدادات الشبكة',
      'تأكد من عدم حجب البرنامج بواسطة الجدار الناري',
      'جرب إعادة تشغيل الراوتر'
    ]
  }
};

const operationHelp: Record<string, HelpContent> = {
  'create_client': {
    title: 'إنشاء عميل جديد',
    description: 'دليل إنشاء عميل جديد في النظام',
    steps: [
      'أدخل رقم العميل (رقم الهوية أو رقم مخصص)',
      'اختر نوع العميل (أفراد أو شركات)',
      'أدخل الاسم الكامل للعميل',
      'أضف معلومات الاتصال (هاتف، عنوان، بريد إلكتروني)',
      'احفظ بيانات العميل'
    ],
    tips: [
      'رقم العميل يجب أن يكون فريد',
      'تأكد من صحة معلومات الاتصال',
      'يمكن تعديل بيانات العميل لاحقاً'
    ]
  },
  
  'create_contract': {
    title: 'إنشاء عقد جديد',
    description: 'دليل إنشاء عقد جديد في النظام',
    steps: [
      'اختر العميل من القائمة أو أضف عميل جديد',
      'حدد تاريخ بداية العقد',
      'أضف منتجات العقد مع تفاصيلها',
      'حدد منطقة العقد والقسم المسؤول',
      'راجع البيانات واحفظ العقد'
    ],
    tips: [
      'تأكد من إعداد البيانات المرجعية أولاً',
      'يمكن إضافة عدة منتجات للعقد الواحد',
      'راجع الحسابات المالية قبل الحفظ'
    ]
  }
};

export function ErrorHelpSystem({ errorCode, operation, className }: ErrorHelpSystemProps) {
  const [open, setOpen] = useState(false);
  
  const getHelpContent = () => {
    if (errorCode && helpContent[errorCode]) {
      return helpContent[errorCode];
    }
    if (operation && operationHelp[operation]) {
      return operationHelp[operation];
    }
    return null;
  };

  const content = getHelpContent();
  
  if (!content) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className={cn("gap-2", className)}>
          <HelpCircle className="w-4 h-4" />
          مساعدة
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-right">
            <BookOpen className="w-5 h-5" />
            {content.title}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {content.description}
          </div>
          
          <Tabs defaultValue="steps" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="steps">خطوات الحل</TabsTrigger>
              <TabsTrigger value="tips">نصائح</TabsTrigger>
              <TabsTrigger value="actions">إجراءات</TabsTrigger>
            </TabsList>
            
            <TabsContent value="steps" className="space-y-4">
              {content.steps && (
                <div className="space-y-3">
                  <h4 className="font-medium flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    خطوات الحل:
                  </h4>
                  <ol className="space-y-2 mr-6">
                    {content.steps.map((step, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <Badge variant="outline" className="mt-0.5 min-w-[24px] h-6 flex items-center justify-center">
                          {index + 1}
                        </Badge>
                        <span className="text-sm">{step}</span>
                      </li>
                    ))}
                  </ol>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="tips" className="space-y-4">
              {content.tips && (
                <div className="space-y-3">
                  <h4 className="font-medium flex items-center gap-2">
                    <AlertTriangle className="w-4 h-4 text-yellow-600" />
                    نصائح مهمة:
                  </h4>
                  <ul className="space-y-2 mr-6">
                    {content.tips.map((tip, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <span className="text-yellow-600 mt-1">💡</span>
                        <span className="text-sm">{tip}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="actions" className="space-y-4">
              {content.relatedActions && (
                <div className="space-y-3">
                  <h4 className="font-medium">إجراءات مقترحة:</h4>
                  <div className="grid gap-2">
                    {content.relatedActions.map((action, index) => {
                      const IconComponent = action.icon || ExternalLink;
                      return (
                        <Button
                          key={index}
                          variant="outline"
                          className="justify-start gap-2"
                          onClick={() => {
                            // Handle action
                            console.log('Action:', action.action);
                            setOpen(false);
                          }}
                        >
                          <IconComponent className="w-4 h-4" />
                          {action.label}
                        </Button>
                      );
                    })}
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default ErrorHelpSystem;
