const dashboardService = require('../services/dashboardService.cjs');

class DashboardController {
  // جلب ملخص شامل للداشبورد
  async getDashboardSummary(req, res) {
    try {
      const summary = await dashboardService.getDashboardSummary();
      res.json({
        success: true,
        data: summary
      });
    } catch (error) {
      console.error('Error getting dashboard summary:', error);
      res.status(500).json({
        success: false,
        error: 'فشل في جلب ملخص الداشبورد',
        details: [error.message]
      });
    }
  }

  // إحصائيات سريعة
  async getQuickStats(req, res) {
    try {
      const stats = await dashboardService.getQuickStats();
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Error getting quick stats:', error);
      res.status(500).json({
        success: false,
        error: 'فشل في جلب الإحصائيات',
        details: [error.message]
      });
    }
  }
}

module.exports = new DashboardController();
