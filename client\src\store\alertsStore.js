// ===== ALERTS STORE =====
// Author: Augment Code
// Description: Centralized state management for alerts and notifications using Zustand

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import apiMiddleware from '../lib/apiMiddleware';

// Initial state
const initialState = {
  // Alerts data
  alerts: [],
  unreadAlerts: [],
  readAlerts: [],
  systemAlerts: [],
  userAlerts: [],
  selectedAlert: null,
  
  // Search and filters
  searchQuery: '',
  typeFilter: '',
  priorityFilter: '',
  statusFilter: '',
  dateRangeFilter: { start: null, end: null },
  
  // Loading states
  isLoading: false,
  isAdding: false,
  isUpdating: false,
  isDeleting: false,
  isMarkingRead: false,
  
  // Error states
  error: null,
  addError: null,
  updateError: null,
  deleteError: null,
  
  // Cache and metadata
  lastFetched: null,
  totalCount: 0,
  unreadCount: 0,
  
  // UI state
  showAlertDetails: false,
  
  // Alert settings
  alertSettings: null,
  settingsLoading: false,
  settingsError: null
};

// Create the alerts store
export const useAlertsStore = create()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Actions
        actions: {
          // Fetch all alerts
          async fetchAlerts(forceRefresh = false) {
            const state = get();
            
            if (state.isLoading || (!forceRefresh && state.lastFetched && 
                Date.now() - new Date(state.lastFetched).getTime() < 30000)) {
              return state.alerts;
            }

            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const alerts = await apiMiddleware.get('/api/alerts', {
                loadingMessage: 'جاري تحميل التنبيهات...',
                showErrorNotification: true
              });

              set((state) => {
                state.alerts = alerts || [];
                state.totalCount = alerts?.length || 0;
                state.unreadCount = alerts?.filter(alert => !alert.isRead).length || 0;
                state.isLoading = false;
                state.lastFetched = new Date().toISOString();
                state.error = null;
              });

              return alerts;
            } catch (error) {
              console.error('Failed to fetch alerts:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحميل التنبيهات';
              });

              throw error;
            }
          },

          // Fetch alerts by status
          async fetchAlertsByStatus(status) {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const alerts = await apiMiddleware.get(`/api/alerts/status/${status}`, {
                loadingMessage: `جاري تحميل التنبيهات ${status}...`,
                showErrorNotification: true
              });

              set((state) => {
                switch (status) {
                  case 'unread':
                    state.unreadAlerts = alerts || [];
                    break;
                  case 'read':
                    state.readAlerts = alerts || [];
                    break;
                  case 'system':
                    state.systemAlerts = alerts || [];
                    break;
                  case 'user':
                    state.userAlerts = alerts || [];
                    break;
                }
                state.isLoading = false;
                state.error = null;
              });

              return alerts;
            } catch (error) {
              console.error(`Failed to fetch ${status} alerts:`, error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || `فشل في تحميل التنبيهات ${status}`;
              });

              throw error;
            }
          },

          // Search alerts
          async searchAlerts(query) {
            set((state) => {
              state.searchQuery = query;
              state.isLoading = true;
              state.error = null;
            });

            try {
              const url = query 
                ? `/api/alerts/search?q=${encodeURIComponent(query)}`
                : '/api/alerts';

              const alerts = await apiMiddleware.get(url, {
                loadingMessage: query ? 'جاري البحث...' : 'جاري تحميل التنبيهات...',
                showErrorNotification: true
              });

              set((state) => {
                state.alerts = alerts || [];
                state.totalCount = alerts?.length || 0;
                state.unreadCount = alerts?.filter(alert => !alert.isRead).length || 0;
                state.isLoading = false;
                state.error = null;
              });

              return alerts;
            } catch (error) {
              console.error('Failed to search alerts:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في البحث عن التنبيهات';
              });

              throw error;
            }
          },

          // Add new alert
          async addAlert(alertData) {
            set((state) => {
              state.isAdding = true;
              state.addError = null;
            });

            try {
              const newAlert = await apiMiddleware.post('/api/alerts', alertData, {
                loadingMessage: 'جاري إضافة التنبيه...',
                successMessage: 'تم إضافة التنبيه بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.alerts.unshift(newAlert);
                state.totalCount += 1;
                
                if (!newAlert.isRead) {
                  state.unreadCount += 1;
                  state.unreadAlerts.unshift(newAlert);
                } else {
                  state.readAlerts.unshift(newAlert);
                }
                
                if (newAlert.alertType === 'system') {
                  state.systemAlerts.unshift(newAlert);
                } else {
                  state.userAlerts.unshift(newAlert);
                }
                
                state.isAdding = false;
                state.addError = null;
              });

              return { success: true, data: newAlert };
            } catch (error) {
              console.error('Failed to add alert:', error);
              
              set((state) => {
                state.isAdding = false;
                state.addError = error.message || 'فشل في إضافة التنبيه';
              });

              return { success: false, error: error.message };
            }
          },

          // Update alert
          async updateAlert(alertId, updates) {
            set((state) => {
              state.isUpdating = true;
              state.updateError = null;
            });

            try {
              const updatedAlert = await apiMiddleware.put(`/api/alerts/${alertId}`, updates, {
                loadingMessage: 'جاري تحديث التنبيه...',
                successMessage: 'تم تحديث التنبيه بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                // Update in main alerts array
                const index = state.alerts.findIndex(alert => alert.id === alertId);
                if (index !== -1) {
                  const oldAlert = state.alerts[index];
                  state.alerts[index] = updatedAlert;
                  
                  // Update unread count if read status changed
                  if (oldAlert.isRead !== updatedAlert.isRead) {
                    if (updatedAlert.isRead) {
                      state.unreadCount -= 1;
                    } else {
                      state.unreadCount += 1;
                    }
                  }
                }
                
                // Update in status-specific arrays
                const updateInArray = (array) => {
                  const idx = array.findIndex(alert => alert.id === alertId);
                  if (idx !== -1) {
                    array[idx] = updatedAlert;
                  }
                };
                
                updateInArray(state.unreadAlerts);
                updateInArray(state.readAlerts);
                updateInArray(state.systemAlerts);
                updateInArray(state.userAlerts);
                
                if (state.selectedAlert?.id === alertId) {
                  state.selectedAlert = updatedAlert;
                }
                
                state.isUpdating = false;
                state.updateError = null;
              });

              return { success: true, data: updatedAlert };
            } catch (error) {
              console.error('Failed to update alert:', error);
              
              set((state) => {
                state.isUpdating = false;
                state.updateError = error.message || 'فشل في تحديث التنبيه';
              });

              return { success: false, error: error.message };
            }
          },

          // Mark alert as read
          async markAlertAsRead(alertId) {
            set((state) => {
              state.isMarkingRead = true;
            });

            try {
              const updatedAlert = await apiMiddleware.put(`/api/alerts/${alertId}/read`, {}, {
                showSuccessNotification: false,
                showErrorNotification: true
              });

              set((state) => {
                // Update in main alerts array
                const index = state.alerts.findIndex(alert => alert.id === alertId);
                if (index !== -1) {
                  const oldAlert = state.alerts[index];
                  state.alerts[index] = updatedAlert;
                  
                  if (!oldAlert.isRead && updatedAlert.isRead) {
                    state.unreadCount -= 1;
                  }
                }
                
                // Move from unread to read arrays
                state.unreadAlerts = state.unreadAlerts.filter(alert => alert.id !== alertId);
                const existsInRead = state.readAlerts.find(alert => alert.id === alertId);
                if (!existsInRead) {
                  state.readAlerts.unshift(updatedAlert);
                }
                
                if (state.selectedAlert?.id === alertId) {
                  state.selectedAlert = updatedAlert;
                }
                
                state.isMarkingRead = false;
              });

              return { success: true, data: updatedAlert };
            } catch (error) {
              console.error('Failed to mark alert as read:', error);
              
              set((state) => {
                state.isMarkingRead = false;
              });

              return { success: false, error: error.message };
            }
          },

          // Mark all alerts as read
          async markAllAlertsAsRead() {
            set((state) => {
              state.isMarkingRead = true;
            });

            try {
              await apiMiddleware.put('/api/alerts/mark-all-read', {}, {
                loadingMessage: 'جاري تحديث جميع التنبيهات...',
                successMessage: 'تم تحديث جميع التنبيهات كمقروءة',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                // Mark all alerts as read
                state.alerts = state.alerts.map(alert => ({ ...alert, isRead: true }));
                state.unreadAlerts = [];
                state.readAlerts = [...state.alerts];
                state.unreadCount = 0;
                state.isMarkingRead = false;
              });

              return { success: true };
            } catch (error) {
              console.error('Failed to mark all alerts as read:', error);
              
              set((state) => {
                state.isMarkingRead = false;
              });

              return { success: false, error: error.message };
            }
          },

          // Delete alert
          async deleteAlert(alertId) {
            set((state) => {
              state.isDeleting = true;
              state.deleteError = null;
            });

            try {
              await apiMiddleware.delete(`/api/alerts/${alertId}`, {
                loadingMessage: 'جاري حذف التنبيه...',
                successMessage: 'تم حذف التنبيه بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                // Find alert before deletion to update counters
                const alertToDelete = state.alerts.find(alert => alert.id === alertId);
                
                // Remove from all arrays
                state.alerts = state.alerts.filter(alert => alert.id !== alertId);
                state.unreadAlerts = state.unreadAlerts.filter(alert => alert.id !== alertId);
                state.readAlerts = state.readAlerts.filter(alert => alert.id !== alertId);
                state.systemAlerts = state.systemAlerts.filter(alert => alert.id !== alertId);
                state.userAlerts = state.userAlerts.filter(alert => alert.id !== alertId);
                
                state.totalCount -= 1;
                
                if (alertToDelete && !alertToDelete.isRead) {
                  state.unreadCount -= 1;
                }
                
                if (state.selectedAlert?.id === alertId) {
                  state.selectedAlert = null;
                  state.showAlertDetails = false;
                }
                
                state.isDeleting = false;
                state.deleteError = null;
              });

              return { success: true };
            } catch (error) {
              console.error('Failed to delete alert:', error);
              
              set((state) => {
                state.isDeleting = false;
                state.deleteError = error.message || 'فشل في حذف التنبيه';
              });

              return { success: false, error: error.message };
            }
          },

          // Fetch alert settings
          async fetchAlertSettings() {
            set((state) => {
              state.settingsLoading = true;
              state.settingsError = null;
            });

            try {
              const settings = await apiMiddleware.get('/api/alerts/settings', {
                showErrorNotification: false
              });

              set((state) => {
                state.alertSettings = settings;
                state.settingsLoading = false;
                state.settingsError = null;
              });

              return settings;
            } catch (error) {
              console.error('Failed to fetch alert settings:', error);
              
              set((state) => {
                state.alertSettings = null;
                state.settingsLoading = false;
                state.settingsError = error.message;
              });

              return null;
            }
          },

          // Update alert settings
          async updateAlertSettings(settings) {
            set((state) => {
              state.settingsLoading = true;
              state.settingsError = null;
            });

            try {
              const updatedSettings = await apiMiddleware.put('/api/alerts/settings', settings, {
                loadingMessage: 'جاري تحديث إعدادات التنبيهات...',
                successMessage: 'تم تحديث إعدادات التنبيهات بنجاح',
                showSuccessNotification: true,
                showErrorNotification: true
              });

              set((state) => {
                state.alertSettings = updatedSettings;
                state.settingsLoading = false;
                state.settingsError = null;
              });

              return { success: true, data: updatedSettings };
            } catch (error) {
              console.error('Failed to update alert settings:', error);
              
              set((state) => {
                state.settingsLoading = false;
                state.settingsError = error.message || 'فشل في تحديث إعدادات التنبيهات';
              });

              return { success: false, error: error.message };
            }
          },

          // Set selected alert
          setSelectedAlert(alert) {
            set((state) => {
              state.selectedAlert = alert;
              state.showAlertDetails = !!alert;
            });

            // Mark as read when selected
            if (alert && !alert.isRead) {
              get().actions.markAlertAsRead(alert.id);
            }
          },

          // Set filters
          setSearchQuery(query) {
            set((state) => {
              state.searchQuery = query;
            });
          },

          setTypeFilter(type) {
            set((state) => {
              state.typeFilter = type;
            });
          },

          setPriorityFilter(priority) {
            set((state) => {
              state.priorityFilter = priority;
            });
          },

          setStatusFilter(status) {
            set((state) => {
              state.statusFilter = status;
            });
          },

          setDateRangeFilter(dateRange) {
            set((state) => {
              state.dateRangeFilter = dateRange;
            });
          },

          // Clear errors
          clearErrors() {
            set((state) => {
              state.error = null;
              state.addError = null;
              state.updateError = null;
              state.deleteError = null;
              state.settingsError = null;
            });
          },

          // Get filtered alerts
          getFilteredAlerts() {
            const state = get();
            const { 
              alerts, 
              searchQuery, 
              typeFilter, 
              priorityFilter, 
              statusFilter,
              dateRangeFilter 
            } = state;

            return alerts.filter(alert => {
              // Search filter
              const matchesSearch = !searchQuery ||
                alert.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                alert.message?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                alert.alertType?.toLowerCase().includes(searchQuery.toLowerCase());

              // Type filter
              const matchesType = !typeFilter || typeFilter === "all" ||
                alert.alertType === typeFilter;

              // Priority filter
              const matchesPriority = !priorityFilter || priorityFilter === "all" ||
                alert.priority === priorityFilter;

              // Status filter
              const matchesStatus = !statusFilter || statusFilter === "all" ||
                (statusFilter === 'read' && alert.isRead) ||
                (statusFilter === 'unread' && !alert.isRead);

              // Date range filter
              const matchesDateRange = !dateRangeFilter.start || !dateRangeFilter.end ||
                (new Date(alert.createdAt) >= new Date(dateRangeFilter.start) &&
                 new Date(alert.createdAt) <= new Date(dateRangeFilter.end));

              return matchesSearch && matchesType && matchesPriority && matchesStatus && matchesDateRange;
            });
          },

          // Get alert by ID
          getAlertById(alertId) {
            const state = get();
            return state.alerts.find(alert => alert.id === alertId);
          },

          // Refresh data
          async refresh() {
            return get().actions.fetchAlerts(true);
          }
        }
      })),
      {
        name: 'alerts-store',
        partialize: (state) => ({
          alerts: state.alerts,
          lastFetched: state.lastFetched,
          searchQuery: state.searchQuery,
          typeFilter: state.typeFilter,
          priorityFilter: state.priorityFilter,
          statusFilter: state.statusFilter,
          alertSettings: state.alertSettings
        })
      }
    ),
    {
      name: 'alerts-store'
    }
  )
);

// Selectors
export const useAlerts = () => useAlertsStore((state) => state.alerts);
export const useUnreadAlerts = () => useAlertsStore((state) => state.unreadAlerts);
export const useReadAlerts = () => useAlertsStore((state) => state.readAlerts);
export const useSystemAlerts = () => useAlertsStore((state) => state.systemAlerts);
export const useUserAlerts = () => useAlertsStore((state) => state.userAlerts);
export const useAlertsLoading = () => useAlertsStore((state) => state.isLoading);
export const useAlertsError = () => useAlertsStore((state) => state.error);
export const useSelectedAlert = () => useAlertsStore((state) => state.selectedAlert);
export const useUnreadCount = () => useAlertsStore((state) => state.unreadCount);
export const useAlertSettings = () => useAlertsStore((state) => state.alertSettings);
export const useAlertsActions = () => useAlertsStore((state) => state.actions);

export const useFilteredAlerts = () => useAlertsStore((state) => 
  state.actions.getFilteredAlerts()
);

export const useAlertsStatus = () => useAlertsStore((state) => ({
  isLoading: state.isLoading,
  isAdding: state.isAdding,
  isUpdating: state.isUpdating,
  isDeleting: state.isDeleting,
  isMarkingRead: state.isMarkingRead,
  settingsLoading: state.settingsLoading,
  error: state.error,
  addError: state.addError,
  updateError: state.updateError,
  deleteError: state.deleteError,
  settingsError: state.settingsError,
  totalCount: state.totalCount,
  unreadCount: state.unreadCount,
  lastFetched: state.lastFetched
}));

export default useAlertsStore;
