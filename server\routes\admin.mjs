// ===== ADMIN ROUTES =====
// Author: Augment Code
// Description: Administrative routes for system management

import express from 'express';
import { createRequire } from 'module';

const require = createRequire(import.meta.url);
const router = express.Router();

// Import middleware and services using require for CommonJS compatibility
const { requireAuth, requireRole, asyncHandler } = require('../middleware/index.cjs');
const BackupService = require('../services/backupService.cjs');
const AuditService = require('../services/auditService.cjs');
import MigrationRunner from '../migrations/migrationRunner.mjs';

// Initialize services (will be properly initialized with db in main server)
let backupService, auditService, migrationRunner;

const initializeServices = (db) => {
  backupService = new BackupService(db);
  auditService = new AuditService(db);
  migrationRunner = new MigrationRunner(db);
};

// Middleware for admin routes (commented out for now to avoid auth issues during testing)
// router.use(requireAuth);
// router.use(requireRole('admin'));

// ===== BACKUP MANAGEMENT =====

// Create full backup
router.post('/backup/create', async (req, res) => {
  try {
    const { description } = req.body;
    const userId = req.user?.id || 'system';

    const backup = await backupService.createFullBackup(description, userId);
    
    res.json({
      success: true,
      message: 'تم إنشاء النسخة الاحتياطية بنجاح',
      backup
    });
  } catch (error) {
    console.error('Backup creation failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في إنشاء النسخة الاحتياطية',
      error: error.message
    });
  }
});

// List all backups
router.get('/backup/list', async (req, res) => {
  try {
    const backups = await backupService.listBackups();
    
    res.json({
      success: true,
      backups
    });
  } catch (error) {
    console.error('Failed to list backups:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في جلب قائمة النسخ الاحتياطية',
      error: error.message
    });
  }
});

// Restore from backup
router.post('/backup/restore/:backupId', async (req, res) => {
  try {
    const { backupId } = req.params;
    const userId = req.user?.id || 'system';

    await backupService.restoreFromBackup(backupId, userId);
    
    res.json({
      success: true,
      message: 'تم استعادة النسخة الاحتياطية بنجاح'
    });
  } catch (error) {
    console.error('Backup restoration failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في استعادة النسخة الاحتياطية',
      error: error.message
    });
  }
});

// Delete backup
router.delete('/backup/:backupId', async (req, res) => {
  try {
    const { backupId } = req.params;
    const userId = req.user?.id || 'system';

    await backupService.deleteBackup(backupId, userId);
    
    res.json({
      success: true,
      message: 'تم حذف النسخة الاحتياطية بنجاح'
    });
  } catch (error) {
    console.error('Backup deletion failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في حذف النسخة الاحتياطية',
      error: error.message
    });
  }
});

// ===== AUDIT TRAIL =====

// Get audit logs
router.get('/audit/logs', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 50, 
      action, 
      userId, 
      startDate, 
      endDate 
    } = req.query;

    const logs = await auditService.getAuditLogs({
      page: parseInt(page),
      limit: parseInt(limit),
      action,
      userId,
      startDate,
      endDate
    });
    
    res.json({
      success: true,
      logs
    });
  } catch (error) {
    console.error('Failed to get audit logs:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في جلب سجلات المراجعة',
      error: error.message
    });
  }
});

// Get audit statistics
router.get('/audit/stats', async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    const stats = await auditService.getAuditStatistics(period);
    
    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Failed to get audit stats:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في جلب إحصائيات المراجعة',
      error: error.message
    });
  }
});

// ===== MIGRATION MANAGEMENT =====

// Get migration status
router.get('/migrations/status', async (req, res) => {
  try {
    const history = await migrationRunner.getMigrationHistory();
    const pending = await migrationRunner.getPendingMigrations();
    
    res.json({
      success: true,
      history,
      pending
    });
  } catch (error) {
    console.error('Failed to get migration status:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في جلب حالة الترحيلات',
      error: error.message
    });
  }
});

// Run pending migrations
router.post('/migrations/run', async (req, res) => {
  try {
    await migrationRunner.runMigrations();
    
    res.json({
      success: true,
      message: 'تم تشغيل الترحيلات بنجاح'
    });
  } catch (error) {
    console.error('Migration run failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تشغيل الترحيلات',
      error: error.message
    });
  }
});

// ===== SYSTEM HEALTH =====

// Get system health status
router.get('/health', async (req, res) => {
  try {
    const health = {
      database: 'connected',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '2.0.0'
    };
    
    res.json({
      success: true,
      health
    });
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في فحص حالة النظام',
      error: error.message
    });
  }
});

export { router, initializeServices };
