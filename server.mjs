// ===== CONTRACT MANAGEMENT SYSTEM SERVER =====
// Author: Augment Code
// Version: 2.0.0 - Clean Architecture (ESM)
// Description: Modern Express.js server with MVC architecture

console.log('🚀 Starting Contract Management Server v2.0 (ESM)...');

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create require function for CommonJS modules
import { createRequire } from 'module';
const require = createRequire(import.meta.url);

// Import database and initialization
import database from './server/models/db.mjs';
import { initializeDatabase } from './server/models/initDatabase.mjs';

// Import CommonJS database for services compatibility
const databaseCjs = require('./server/models/db.cjs');

// Import new services and middleware
import { BackupService, AuditService } from './server/services/index.mjs';
import MigrationRunner from './server/migrations/migrationRunner.mjs';
import { router as adminRouter, initializeServices } from './server/routes/admin.mjs';

// Create Express app
const app = express();

// ===== MIDDLEWARE SETUP =====
app.use(cors({
  origin: process.env.NODE_ENV === 'production' ? false : true,
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ limit: '10mb', extended: true }));

// Security middleware
app.use(helmet({
  contentSecurityPolicy: false, // Disable for development
  crossOriginEmbedderPolicy: false
}));

app.use(compression());

// Request logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`📝 ${timestamp} - ${req.method} ${req.path}`);
  next();
});

// ===== ROUTES SETUP =====
// Import routes (using require for CommonJS routes)
const dashboardRoutes = require('./server/routes/dashboard.cjs');
const settingsRoutes = require('./server/routes/settings.cjs');
const clientsRoutes = require('./server/routes/clients.cjs');
const contractsRoutes = require('./server/routes/contracts.cjs');
const paymentsRoutes = require('./server/routes/payments.cjs');
const receivablesRoutes = require('./server/routes/receivables.cjs');
const chequesRoutes = require('./server/routes/cheques.cjs');
const referenceDataRoutes = require('./server/routes/reference-data.cjs');

// Mount API routes
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/clients', clientsRoutes);
app.use('/api/contracts', contractsRoutes);
app.use('/api/payments', paymentsRoutes);
app.use('/api/receivables', receivablesRoutes);
app.use('/api/cheques', chequesRoutes);
app.use('/api/reference-data', referenceDataRoutes);

// Add admin routes for new features
app.use('/api/admin', adminRouter);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    database: database.isConnected ? 'Connected' : 'Disconnected'
  });
});

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, 'client/dist')));

  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'client/dist/index.html'));
  });
}

// ===== ERROR HANDLING =====
// 404 handler for API routes
app.use('/api/*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'المسار غير موجود',
    path: req.path
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('🚨 Global error handler:', err);

  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV !== 'production';

  res.status(err.status || 500).json({
    success: false,
    error: 'خطأ داخلي في الخادم',
    details: isDevelopment ? [err.message] : ['حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى'],
    ...(isDevelopment && { stack: err.stack })
  });
});

// ===== SERVER STARTUP =====
async function startServer() {
  try {
    // Connect to database (ESM)
    await database.connect();
    console.log('✅ Database connected successfully');

    // Connect to CommonJS database for services
    await databaseCjs.connect();
    console.log('✅ CommonJS Database connected successfully');

    // Initialize database tables
    await initializeDatabase();
    console.log('✅ Database initialized successfully');

    // Initialize new services
    console.log('🔧 Initializing services...');
    initializeServices(database);

    // Run database migrations
    console.log('🔄 Running database migrations...');
    const migrationRunner = new MigrationRunner(database);
    await migrationRunner.runMigrations();
    console.log('✅ Database migrations completed');

    // Start server
    const PORT = process.env.PORT || 3001;
    const server = app.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 Health check: http://localhost:${PORT}/api/health`);
      console.log(`🛡️  Admin API: http://localhost:${PORT}/api/admin`);
      console.log(`📱 Frontend URL: http://localhost:5173`);
      console.log('🎯 Ready to accept connections!');
    });

    // Graceful shutdown
    process.on('SIGTERM', async () => {
      console.log('🔄 SIGTERM received, shutting down gracefully...');
      server.close(async () => {
        await database.close();
        console.log('✅ Server closed successfully');
        process.exit(0);
      });
    });

    process.on('SIGINT', async () => {
      console.log('🔄 SIGINT received, shutting down gracefully...');
      server.close(async () => {
        await database.close();
        console.log('✅ Server closed successfully');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
startServer();
