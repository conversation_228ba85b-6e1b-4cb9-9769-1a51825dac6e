import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { Eye, EyeOff, AlertTriangle, Moon, Sun, Loader2 } from "lucide-react";
import { useLanguage } from "@/hooks/use-language";
import { useTheme } from "@/components/theme-provider";
import { useSettings } from "@/contexts/settings-context";
import labels from "@/lib/i18n";

const DEMO_USER = "admin";
const DEMO_PASS = "1234";

const loginSchema = z.object({
  username: z.string().min(1, "اسم المستخدم مطلوب"),
  password: z.string().min(1, "كلمة المرور مطلوبة"),
  remember: z.boolean().optional(),
});

function getCurrentTime(lang: "ar" | "en") {
  const now = new Date();
  let hours = now.getHours();
  const minutes = now.getMinutes();
  const isAM = hours < 12;
  let period = "";
  if (lang === "ar") {
    period = isAM ? "صباحاً" : "مساءً";
  } else {
    period = isAM ? "AM" : "PM";
  }
  hours = hours % 12;
  if (hours === 0) hours = 12;
  const minStr = minutes.toString().padStart(2, "0");
  return `${hours}:${minStr} ${period}`;
}

const getGreeting = (lang: "ar" | "en") => {
  const hour = new Date().getHours();
  if (lang === "ar") {
    if (hour < 12) return "صباح الخير!";
    if (hour < 18) return "مساء الخير!";
    return "مساء الخير!";
  } else {
    if (hour < 12) return "Good morning!";
    if (hour < 18) return "Good afternoon!";
    return "Good evening!";
  }
};

const getDateString = (lang: "ar" | "en") => {
  const now = new Date();
  if (lang === "ar") {
    return now.toLocaleDateString("ar-EG", { day: "2-digit", month: "2-digit", year: "numeric" });
  } else {
    const y = now.getFullYear();
    const m = String(now.getMonth() + 1).padStart(2, "0");
    const d = String(now.getDate()).padStart(2, "0");
    return `${y}/${m}/${d}`;
  }
};

const Login = () => {
  const { settings, formatDate } = useSettings();
  const { language, setLanguage } = useLanguage();
  const { theme, setTheme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState(getCurrentTime(language));
  const [wrongTries, setWrongTries] = useState(Number(localStorage.getItem("wrongTries") || 0));
  const [showTriesAlert, setShowTriesAlert] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Use settings for company info
  const companyName = settings.companyName || "شركة إدارة العقود";
  const programName = settings.programName || "نظام إدارة العقود";
  const logoUrl = settings.companyLogo || "";
  const about = settings.about || "";

  const t = labels[language];
  const dir = language === "ar" ? "rtl" : "ltr";

  const form = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
      remember: localStorage.getItem("rememberMe") === "true",
    },
  });



  // Update direction
  useEffect(() => {
    document.body.dir = dir;
  }, [dir]);

  // Set time once without interval
  useEffect(() => {
    setCurrentTime(getCurrentTime(language));
  }, [language]);

  // Handle form submission
  const onSubmit = (values: z.infer<typeof loginSchema>) => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      if (values.username === DEMO_USER && values.password === DEMO_PASS) {
        if (values.remember) {
          localStorage.setItem("rememberMe", "true");
        } else {
          localStorage.removeItem("rememberMe");
        }
        localStorage.setItem("loggedIn", "true");
        localStorage.setItem("wrongTries", "0");
        setWrongTries(0);
        setShowTriesAlert(false);
        setLocation("/");
      } else {
        const tries = wrongTries + 1;
        setWrongTries(tries);
        localStorage.setItem("wrongTries", String(tries));
        if (tries >= 3) setShowTriesAlert(true);
        toast({
          title: language === "ar" ? "خطأ في تسجيل الدخول" : "Login Error",
          description: language === "ar" ? "اسم المستخدم أو كلمة المرور غير صحيحة" : "Wrong username or password",
          variant: "destructive",
        });
      }
    }, 1200);
  };

  return (
    <div 
      dir={dir} 
      className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300"
    >
      <div className="flex min-h-screen">
        {/* Company Section */}
        <div className={cn(
          "flex-1 flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 p-8",
          language === "ar" ? "order-2" : "order-1"
        )}>
          <div className="text-center max-w-md">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-8 leading-tight">
              {companyName}
            </h1>
            
            {logoUrl && (
              <img
                src={logoUrl}
                alt="Company Logo"
                className="w-64 h-64 object-contain mx-auto mb-6 animate-fade-in"
              />
            )}
            
            {about && (
              <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed whitespace-pre-line">
                {about}
              </p>
            )}
          </div>
        </div>

        {/* Login Section */}
        <div className={cn(
          "flex-1 flex items-center justify-center p-8",
          language === "ar" ? "order-1" : "order-2"
        )}>
          <Card className="w-full max-w-md shadow-xl">
            <CardContent className="p-8">
              {/* Header Controls */}
              <div className="flex justify-between items-center mb-6">
                <Select value={language} onValueChange={setLanguage}>
                  <SelectTrigger className="w-24">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ar">العربية</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                  </SelectContent>
                </Select>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                  className="gap-2"
                >
                  {theme === "dark" ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                  {language === "ar" ? "الوضع الليلي" : "Dark Mode"}
                </Button>
              </div>

              {/* Title */}
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  {programName}
                </h2>
                <p className="text-blue-600 dark:text-blue-400 text-lg font-medium mb-1">
                  {getGreeting(language)}
                </p>
                <p className="text-gray-500 dark:text-gray-400">
                  {language === "ar" ? "تسجيل الدخول للبرنامج" : "Login to the system"}
                </p>
              </div>

              {/* Alert for wrong tries */}
              {showTriesAlert && (
                <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        {language === "ar"
                          ? `لقد أدخلت بيانات خاطئة ${wrongTries} مرات!`
                          : `You have entered wrong credentials ${wrongTries} times!`
                        }
                      </p>
                      <p className="text-sm text-yellow-600 dark:text-yellow-300">
                        {language === "ar"
                          ? "تأكد من اسم المستخدم وكلمة المرور أو تواصل مع الدعم."
                          : "Check your username and password or contact support."
                        }
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Login Form */}
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{language === "ar" ? "اسم المستخدم" : "Username"}</FormLabel>
                        <FormControl>
                          <Input 
                            {...field} 
                            placeholder={language === "ar" ? "اسم المستخدم" : "Username"}
                            autoComplete="username"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{language === "ar" ? "كلمة المرور" : "Password"}</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input 
                              {...field} 
                              type={showPassword ? "text" : "password"}
                              placeholder={language === "ar" ? "كلمة المرور" : "Password"}
                              autoComplete="current-password"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? (
                                <EyeOff className="h-4 w-4 text-gray-400" />
                              ) : (
                                <Eye className="h-4 w-4 text-gray-400" />
                              )}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex items-center justify-between">
                    <FormField
                      control={form.control}
                      name="remember"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            />
                          </FormControl>
                          <FormLabel className="text-sm font-normal">
                            {language === "ar" ? "تذكرني" : "Remember me"}
                          </FormLabel>
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {language === "ar" ? "جاري تسجيل الدخول..." : "Logging in..."}
                      </>
                    ) : (
                      language === "ar" ? "تسجيل الدخول" : "Login"
                    )}
                  </Button>
                </form>
              </Form>

              {/* Demo Credentials */}
              <div className="mt-6 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <p className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                  {language === "ar" ? "بيانات التجربة:" : "Demo Credentials:"}
                </p>
                <p className="text-sm text-blue-600 dark:text-blue-300">
                  {language === "ar" ? "المستخدم: admin | كلمة المرور: 1234" : "Username: admin | Password: 1234"}
                </p>
              </div>

              {/* Time and Date */}
              <div className="mt-4 text-center text-sm text-gray-500 dark:text-gray-400">
                <p>{currentTime}</p>
                <p>{getDateString(language)}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Login;