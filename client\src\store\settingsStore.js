// ===== SETTINGS STORE =====
// Author: Augment Code
// Description: Centralized state management for application settings using Zustand

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// API service for settings
const settingsAPI = {
  async getSettings() {
    const response = await fetch('/api/settings');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  },

  async updateSettings(settings) {
    const response = await fetch('/api/settings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(settings),
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }
};

// Initial state
const initialState = {
  // Settings data
  settings: {
    companyName: 'شركة إدارة العقود المصرية',
    programName: 'نظام إدارة العقود المتقدم',
    language: 'ar',
    currency: 'جنيه مصري',
    currencySymbol: 'ج.م',
    country: 'مصر',
    dateFormat: 'yyyy/mm/dd',
    workDays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس'],
    regions: [],
    owners: [],
    governorates: []
  },

  // Loading states
  isLoading: false,
  isSaving: false,

  // Error states
  error: null,
  saveError: null,

  // Status flags
  isInitialized: false,
  hasUnsavedChanges: false,
  lastSaved: null,
  lastFetched: null
};

// Create the settings store
export const useSettingsStore = create()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Actions
        actions: {
          // Fetch settings from API
          async fetchSettings() {
            const state = get();
            if (state.isLoading) return; // Prevent multiple simultaneous requests

            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const settings = await settingsAPI.getSettings();
              
              set((state) => {
                state.settings = { ...state.settings, ...settings };
                state.isLoading = false;
                state.isInitialized = true;
                state.lastFetched = new Date().toISOString();
                state.error = null;
              });

              return settings;
            } catch (error) {
              console.error('Failed to fetch settings:', error);
              
              set((state) => {
                state.isLoading = false;
                state.error = error.message || 'فشل في تحميل الإعدادات';
              });

              throw error;
            }
          },

          // Update settings locally (without saving)
          updateSettings(updates) {
            set((state) => {
              state.settings = { ...state.settings, ...updates };
              state.hasUnsavedChanges = true;
            });
          },

          // Update a specific setting
          updateSetting(key, value) {
            set((state) => {
              state.settings[key] = value;
              state.hasUnsavedChanges = true;
            });
          },

          // Save settings to API
          async saveSettings(settingsToSave = null) {
            const state = get();
            if (state.isSaving) return; // Prevent multiple simultaneous saves

            const settings = settingsToSave || state.settings;

            set((state) => {
              state.isSaving = true;
              state.saveError = null;
            });

            try {
              const result = await settingsAPI.updateSettings(settings);
              
              set((state) => {
                state.settings = { ...state.settings, ...result.savedData };
                state.isSaving = false;
                state.hasUnsavedChanges = false;
                state.lastSaved = new Date().toISOString();
                state.saveError = null;
              });

              return result;
            } catch (error) {
              console.error('Failed to save settings:', error);
              
              set((state) => {
                state.isSaving = false;
                state.saveError = error.message || 'فشل في حفظ الإعدادات';
              });

              throw error;
            }
          },

          // Reset settings to initial state
          resetSettings() {
            set((state) => {
              state.settings = { ...initialState.settings };
              state.hasUnsavedChanges = true;
            });
          },

          // Discard unsaved changes
          discardChanges() {
            const state = get();
            if (state.lastFetched) {
              // Re-fetch from server to get latest data
              return state.actions.fetchSettings();
            } else {
              // Reset to initial state
              set((state) => {
                state.settings = { ...initialState.settings };
                state.hasUnsavedChanges = false;
              });
            }
          },

          // Clear errors
          clearError() {
            set((state) => {
              state.error = null;
            });
          },

          clearSaveError() {
            set((state) => {
              state.saveError = null;
            });
          },

          // Initialize store (fetch settings if not already initialized)
          async initialize() {
            const state = get();
            if (!state.isInitialized && !state.isLoading) {
              return state.actions.fetchSettings();
            }
          },

          // Get specific setting
          getSetting(key, defaultValue = null) {
            const state = get();
            return state.settings[key] ?? defaultValue;
          },

          // Check if settings are valid
          validateSettings(settings = null) {
            const settingsToValidate = settings || get().settings;
            const errors = [];

            if (!settingsToValidate.companyName?.trim()) {
              errors.push('اسم الشركة مطلوب');
            }

            if (!settingsToValidate.programName?.trim()) {
              errors.push('اسم البرنامج مطلوب');
            }

            if (!settingsToValidate.currency?.trim()) {
              errors.push('العملة مطلوبة');
            }

            return {
              isValid: errors.length === 0,
              errors
            };
          },

          // Export settings
          exportSettings() {
            const state = get();
            return {
              ...state.settings,
              exportedAt: new Date().toISOString(),
              version: '1.0'
            };
          },

          // Import settings
          importSettings(importedSettings) {
            if (!importedSettings || typeof importedSettings !== 'object') {
              throw new Error('بيانات الاستيراد غير صحيحة');
            }

            const validation = get().actions.validateSettings(importedSettings);
            if (!validation.isValid) {
              throw new Error(`بيانات الاستيراد غير صحيحة: ${validation.errors.join(', ')}`);
            }

            set((state) => {
              state.settings = { ...state.settings, ...importedSettings };
              state.hasUnsavedChanges = true;
            });
          }
        }
      })),
      {
        name: 'settings-store',
        partialize: (state) => ({
          settings: state.settings,
          lastSaved: state.lastSaved,
          lastFetched: state.lastFetched
        })
      }
    ),
    {
      name: 'settings-store'
    }
  )
);

// Selectors for easy access to specific parts of the state
export const useSettings = () => useSettingsStore((state) => state.settings);
export const useSettingsLoading = () => useSettingsStore((state) => state.isLoading);
export const useSettingsSaving = () => useSettingsStore((state) => state.isSaving);
export const useSettingsError = () => useSettingsStore((state) => state.error);
export const useSettingsSaveError = () => useSettingsStore((state) => state.saveError);
export const useSettingsActions = () => useSettingsStore((state) => state.actions);
export const useHasUnsavedChanges = () => useSettingsStore((state) => state.hasUnsavedChanges);

// Computed selectors
export const useIsSettingsReady = () => useSettingsStore((state) => 
  state.isInitialized && !state.isLoading && !state.error
);

export const useSettingsStatus = () => useSettingsStore((state) => ({
  isLoading: state.isLoading,
  isSaving: state.isSaving,
  error: state.error,
  saveError: state.saveError,
  hasUnsavedChanges: state.hasUnsavedChanges,
  isInitialized: state.isInitialized,
  lastSaved: state.lastSaved,
  lastFetched: state.lastFetched
}));

export default useSettingsStore;
