import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useRoute, Link, useLocation } from "wouter";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { type InsertClient } from "@shared/schema";
import { clientSchema, type ClientFormData } from "@/lib/validation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useEnhancedToast } from "@/hooks/use-enhanced-toast";
import { useLanguage } from "@/hooks/use-language";
import { cn } from "@/lib/utils";
import { getBase64Size, formatFileSize } from "@/lib/image-utils";
import { ArrowRight, Save, Eye, Loader2, Building2, AlertTriangle } from "lucide-react";
import { ClientCard } from "@/components/client-card";
import { IdImageUpload } from "@/components/id-image-upload";
import { DocumentUpload, type DocumentFile } from "@/components/document-upload";
import { useReferenceDropdownOptions } from "@/hooks/use-reference-lists";
import labels from "@/lib/i18n";

export default function ClientsEdit() {
  const { language, isRTL } = useLanguage();
  const [showPreview, setShowPreview] = useState(false);
  const [idImage, setIdImage] = useState<string | null>(null);
  const [documents, setDocuments] = useState<DocumentFile[]>([]);

  // استخدام القوائم المرجعية للعملاء
  const { options: clientCategories } = useReferenceDropdownOptions('clients', 'clientFinancial_Category');
  const { options: clientSources } = useReferenceDropdownOptions('clients', 'clientSource');
  const { options: legalStatuses } = useReferenceDropdownOptions('clients', 'clientLegal_Status');
  const [match, params] = useRoute("/clients/edit/:id");
  const [, setLocation] = useLocation();
  const queryClient = useQueryClient();
  const { showUpdateSuccess, showUpdateError } = useEnhancedToast();
  const t = labels[language];

  const clientId = params?.id;

  // Fetch client data
  const { data: client, isLoading: isLoadingClient, error } = useQuery({
    queryKey: ["/api/clients/by-client-id", clientId],
    queryFn: async () => {
      console.log('🔥 Fetching client data for clientId:', clientId);
      const response = await apiRequest('GET', `/api/clients/by-client-id/${clientId}`);
      const data = await response.json();
      console.log('🔥 Raw client data from API:', data);
      return data;
    },
    enabled: !!clientId,
  });

  // Fetch contracts for preview
  const { data: contracts = [] } = useQuery({
    queryKey: ["/api/contracts", clientId],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', `/api/contracts?clientId=${clientId}`);
        return response.json();
      } catch (error) {
        console.warn('Failed to fetch contracts:', error);
        return [];
      }
    },
    enabled: !!clientId,
  });

  const form = useForm<ClientFormData>({
    resolver: zodResolver(clientSchema),
    defaultValues: {
      clientId: "",
      clientType: "individual",
      clientName: "",
      clientAddress: "",
      clientPhoneWhatsapp: "",
      clientPhone2: "",
      clientPhone3: "",
      clientEmail: "",
      clientNotes: "",
      clientFinancial_Category: "",
      clientLegal_Rep: "",
      clientReg_Number: "",
      clientTaxReg_Number: "",
      clientLegal_Status: "",
      clientRemarks: "",
      clientID_Image: "",
      clientDocuments: "",
      isActive: true,
    },
  });

  // Update form when client data is loaded
  useEffect(() => {
    if (client) {
      console.log('🔥 Client data received from server:', client);
      console.log('🔥 Client type from server:', client.clientType);

      // Fix legacy data: convert Arabic to English values
      const normalizedClientType = client.clientType === 'فرد' || client.clientType === 'أفراد' ? 'individual' :
                                   client.clientType === 'شركة' || client.clientType === 'شركات' ? 'company' :
                                   client.clientType || 'individual';

      console.log('🔧 Normalized client type:', normalizedClientType);

      form.reset({
        clientId: client.clientId || "",
        clientType: normalizedClientType,
        clientName: client.clientName || "",
        clientAddress: client.clientAddress || "",
        clientPhoneWhatsapp: client.clientPhoneWhatsapp || "",
        clientPhone2: client.clientPhone2 || "",
        clientPhone3: client.clientPhone3 || "",
        clientEmail: client.clientEmail || "",
        clientNotes: client.clientNotes || "",
        clientFinancial_Category: client.clientFinancial_Category || "",
        clientLegal_Rep: client.clientLegal_Rep || "",
        clientReg_Number: client.clientReg_Number || "",
        clientTaxReg_Number: client.clientTaxReg_Number || "",
        clientLegal_Status: client.clientLegal_Status || "",
        clientRemarks: client.clientRemarks || "",
        clientID_Image: client.clientID_Image || "",
        clientDocuments: client.clientDocuments || "",
        isActive: client.isActive ?? true,
      });

      // Set image and documents
      setIdImage(client.clientID_Image || null);
      try {
        const parsedDocs = client.clientDocuments ? JSON.parse(client.clientDocuments) : [];
        setDocuments(Array.isArray(parsedDocs) ? parsedDocs : []);
      } catch (e) {
        console.warn('Failed to parse client documents:', e);
        setDocuments([]);
      }
    }
  }, [client, form]);

  const updateClientMutation = useMutation({
    mutationFn: async (data: ClientFormData) => {
      console.log('Sending update data:', data); // Debug log
      const response = await apiRequest('PUT', `/api/clients/by-client-id/${clientId}`, data);
      return response.json();
    },
    onSuccess: () => {
      showUpdateSuccess("بيانات العميل");
      // Invalidate all client queries (including search queries)
      queryClient.invalidateQueries({
        queryKey: ["/api/clients"],
        exact: false // This will invalidate all queries that start with ['/api/clients']
      });

      // العودة لصفحة العملاء بعد الحفظ
      setTimeout(() => {
        setLocation("/clients");
      }, 1000);
    },
    onError: (error: any) => {
      console.error('Update error:', error);

      let errorMessage = error.message;

      // معالجة رسائل الأخطاء المختلفة
      if (error.message.includes('رقم العميل مستخدم من قبل')) {
        errorMessage = isRTL
          ? "رقم العميل المدخل مستخدم من قبل. يرجى اختيار رقم عميل مختلف."
          : "The entered client ID is already in use. Please choose a different client ID.";
      } else if (error.message.includes('Failed to fetch')) {
        errorMessage = isRTL
          ? "تعذر الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى."
          : "Could not connect to server. Please check your internet connection and try again.";
      } else if (error.message.includes('بيانات العميل غير صحيحة')) {
        errorMessage = isRTL
          ? "يرجى التحقق من صحة البيانات المدخلة"
          : "Please check the entered data";
      }

      showUpdateError(errorMessage);
    },
  });

  const onSubmit = (data: InsertClient) => {
    // Prepare submit data exactly like the old project
    const submitData = {
      clientId: data.clientId,
      clientType: data.clientType,
      clientName: data.clientName,
      clientAddress: data.clientAddress,
      clientPhoneWhatsapp: data.clientPhoneWhatsapp,
      clientPhone2: data.clientPhone2,
      clientPhone3: data.clientPhone3,
      clientEmail: data.clientEmail,
      clientNotes: data.clientNotes,
      clientFinancial_Category: data.clientFinancial_Category,
      clientLegal_Rep: data.clientLegal_Rep,
      clientReg_Number: data.clientReg_Number,
      clientTaxReg_Number: data.clientTaxReg_Number,
      clientLegal_Status: data.clientLegal_Status,
      clientRemarks: data.clientRemarks,
      clientID_Image: idImage,
      clientDocuments: JSON.stringify(documents),
      isActive: data.isActive ?? true,
    };

    console.log('Submitting client data:', submitData); // Debug log
    updateClientMutation.mutate(submitData);
  };

  const handlePrint = () => {
    window.print();
  };

  // Calculate total data size
  const getTotalDataSize = () => {
    let totalSize = 0;

    if (idImage) {
      totalSize += getBase64Size(idImage);
    }

    documents.forEach(doc => {
      totalSize += doc.size;
    });

    return totalSize;
  };

  const totalSize = getTotalDataSize();
  const isDataSizeLarge = totalSize > 10 * 1024 * 1024; // 10MB warning

  if (!match) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">{isRTL ? "صفحة غير موجودة" : "Page not found"}</p>
          <Link href="/clients">
            <Button>{isRTL ? "العودة للعملاء" : "Back to Clients"}</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (isLoadingClient) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="mt-4 text-gray-600">{isRTL ? "جاري تحميل بيانات العميل..." : "Loading client data..."}</p>
      </div>
    );
  }

  if (error || !client) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">{isRTL ? "العميل غير موجود" : "Client not found"}</p>
          <Link href="/clients">
            <Button>{isRTL ? "العودة" : "Go Back"}</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (showPreview) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className={cn(
                "flex items-center space-x-4",
                isRTL ? "space-x-reverse" : ""
              )}>
                <Button variant="ghost" size="sm" onClick={() => setShowPreview(false)}>
                  <ArrowRight className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                  {isRTL ? "العودة للتعديل" : "Back to Edit"}
                </Button>
                <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  {isRTL ? "معاينة كارت العميل" : "Client Card Preview"}
                </h1>
              </div>
            </div>
          </div>
        </header>

        <main className="py-8">
          <ClientCard
            client={{
              ...form.getValues(),
              clientID_Image: idImage,
              clientDocuments: JSON.stringify(documents)
            } as any}
            contracts={contracts}
            showPrintButton={true}
            showEditButton={false}
            onPrint={handlePrint}
          />
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className={cn(
              "flex items-center space-x-4",
              isRTL ? "space-x-reverse" : ""
            )}>
              <Link href="/clients">
                <Button variant="ghost" size="sm">
                  <ArrowRight className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                  {isRTL ? "العودة للعملاء" : "Back to Clients"}
                </Button>
              </Link>
              <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {isRTL ? "تعديل بيانات العميل" : "Edit Client"}
              </h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>{isRTL ? "البيانات الأساسية" : "Basic Information"}</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="clientId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t.clientId} *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={isRTL ? "أدخل رقم العميل" : "Enter client ID"}
                          className="border-2 border-yellow-400 focus:border-yellow-500"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t.clientType} *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger className="border-2 border-yellow-400 focus:border-yellow-500">
                            <SelectValue placeholder={isRTL ? "اختر نوع العميل" : "Select client type"} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="individual">{t.clientTypes.individuals}</SelectItem>
                          <SelectItem value="company">{t.clientTypes.companies}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientName"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>{t.clientName} *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={isRTL ? "أدخل اسم العميل" : "Enter client name"}
                          className="border-2 border-yellow-400 focus:border-yellow-500"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientAddress"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>{t.address}</FormLabel>
                      <FormControl>
                        <Textarea placeholder={isRTL ? "أدخل عنوان العميل" : "Enter client address"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientPhoneWhatsapp"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "رقم الهاتف/واتساب" : "Phone/WhatsApp"} *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={isRTL ? "أدخل رقم الهاتف" : "Enter phone number"}
                          className="border-2 border-yellow-400 focus:border-yellow-500"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientPhone2"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "رقم هاتف إضافي" : "Additional Phone"}</FormLabel>
                      <FormControl>
                        <Input placeholder={isRTL ? "رقم هاتف إضافي" : "Additional phone number"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientPhone3"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "رقم هاتف ثالث" : "Third Phone"}</FormLabel>
                      <FormControl>
                        <Input placeholder={isRTL ? "رقم هاتف ثالث" : "Third phone number"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t.email}</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder={isRTL ? "أدخل البريد الإلكتروني" : "Enter email address"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientNotes"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>{t.notes}</FormLabel>
                      <FormControl>
                        <Textarea placeholder={isRTL ? "أدخل أي ملاحظات" : "Enter any notes"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Financial & Legal Information */}
            <Card>
              <CardHeader>
                <CardTitle className={cn(
                  "flex items-center space-x-2",
                  isRTL ? "space-x-reverse" : ""
                )}>
                  <Building2 className="h-5 w-5 text-primary" />
                  <span>{isRTL ? "المعلومات المالية والقانونية" : "Financial & Legal Information"}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="clientFinancial_Category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t.financialCategory}</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value || ""}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={isRTL ? "مثال: فئة أ، فئة ب" : "e.g., Category A, Category B"} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ممتاز">ممتاز</SelectItem>
                          <SelectItem value="جيد">جيد</SelectItem>
                          <SelectItem value="متوسط">متوسط</SelectItem>
                          <SelectItem value="ضعيف">ضعيف</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientLegal_Rep"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "الممثل القانوني" : "Legal Representative"}</FormLabel>
                      <FormControl>
                        <Input placeholder={isRTL ? "اسم الممثل القانوني" : "Legal representative name"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientReg_Number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "رقم التسجيل" : "Registration Number"}</FormLabel>
                      <FormControl>
                        <Input placeholder={isRTL ? "رقم التسجيل التجاري" : "Commercial registration number"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientTaxReg_Number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "رقم التسجيل الضريبي" : "Tax Registration Number"}</FormLabel>
                      <FormControl>
                        <Input placeholder={isRTL ? "رقم التسجيل الضريبي" : "Tax registration number"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientLegal_Status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "الحالة القانونية" : "Legal Status"}</FormLabel>
                      <FormControl>
                        <Input placeholder={isRTL ? "مثال: مؤسسة فردية، شركة محدودة" : "e.g., Sole Proprietorship, LLC"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientRemarks"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>{isRTL ? "ملاحظات إضافية" : "Additional Remarks"}</FormLabel>
                      <FormControl>
                        <Textarea placeholder={isRTL ? "أي ملاحظات إضافية" : "Any additional remarks"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* ID Card Image */}
            <IdImageUpload
              image={idImage}
              onImageChange={setIdImage}
            />

            {/* Additional Documents */}
            <DocumentUpload
              documents={documents}
              onDocumentsChange={setDocuments}
              label={isRTL ? "المستندات الإضافية" : "Additional Documents"}
              maxFiles={10}
            />

            {/* Data Size Indicator */}
            {totalSize > 0 && (
              <div className={cn(
                "flex items-center gap-2 p-3 rounded-lg",
                isDataSizeLarge ? "bg-yellow-50 border border-yellow-200" : "bg-gray-50 border border-gray-200"
              )}>
                {isDataSizeLarge && <AlertTriangle className="h-4 w-4 text-yellow-600" />}
                <span className="text-sm">
                  {isRTL ? "حجم البيانات الإجمالي:" : "Total data size:"} {formatFileSize(totalSize)}
                </span>
                {isDataSizeLarge && (
                  <span className="text-xs text-yellow-600">
                    {isRTL ? "(حجم كبير - قد يستغرق وقتاً أطول للحفظ)" : "(Large size - may take longer to save)"}
                  </span>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className={cn(
              "flex gap-4 justify-end",
              isRTL ? "flex-row-reverse" : ""
            )}>
              <Button
                type="button"
                variant="outline"
                onClick={() => setLocation("/clients")}
                className="gap-2"
              >
                <ArrowRight className="h-4 w-4" />
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={() => setShowPreview(true)}
                className="gap-2"
              >
                <Eye className="h-4 w-4" />
                {isRTL ? "معاينة كارت العميل" : "Preview Client Card"}
              </Button>

              <Button
                type="submit"
                disabled={updateClientMutation.isPending}
                className="gap-2"
              >
                {updateClientMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                {updateClientMutation.isPending ? t.loading : (isRTL ? "حفظ التعديلات" : "Save Changes")}
              </Button>
            </div>
          </form>
        </Form>
      </main>
    </div>
  );
}
