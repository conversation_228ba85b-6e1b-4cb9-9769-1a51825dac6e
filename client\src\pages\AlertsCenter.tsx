import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useDateFormat } from "@/hooks/use-date-format";
import { useCurrency } from "@/hooks/use-currency";
import {
  Bell,
  AlertTriangle,
  Clock,
  DollarSign,
  Calendar,
  CheckCircle,
  X,
  Filter,
  Search,
  Eye,
  Archive,
  Trash2,
  Settings,
  TrendingUp,
  Users,
  FileText,
  Target
} from "lucide-react";

interface Alert {
  id: number;
  alertType: string;
  alertTitle: string;
  alertMessage: string;
  alertPriority: 'منخفض' | 'متوسط' | 'عالي' | 'حرج';
  contractId?: number;
  contractNumber?: string;
  clientId?: number;
  clientName?: string;
  alertDate: string;
  isRead: boolean;
  isArchived: boolean;
  actionRequired: boolean;
  actionUrl?: string;
  createdAt: string;
}

interface AlertStats {
  totalAlerts: number;
  unreadAlerts: number;
  highPriorityAlerts: number;
  actionRequiredAlerts: number;
  overduePayments: number;
  contractExpirations: number;
  systemAlerts: number;
}

const AlertsCenter = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [alertStats, setAlertStats] = useState<AlertStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { formatDate } = useDateFormat();

  // Mock data for demonstration
  useEffect(() => {
    const mockAlerts: Alert[] = [
      {
        id: 1,
        alertType: "payment_overdue",
        alertTitle: "دفعة متأخرة",
        alertMessage: "العقد رقم CT-001 لديه دفعة متأخرة بقيمة 15,000 جنيه",
        alertPriority: "عالي",
        contractId: 1,
        contractNumber: "CT-001",
        clientId: 1,
        clientName: "الكبير قوي",
        alertDate: "2024-01-15",
        isRead: false,
        isArchived: false,
        actionRequired: true,
        actionUrl: "/contracts/1",
        createdAt: "2024-01-15T10:00:00Z"
      },
      {
        id: 2,
        alertType: "contract_expiring",
        alertTitle: "عقد قارب على الانتهاء",
        alertMessage: "العقد رقم CT-002 سينتهي خلال 30 يوم",
        alertPriority: "متوسط",
        contractId: 2,
        contractNumber: "CT-002",
        clientId: 2,
        clientName: "فاطمة أحمد",
        alertDate: "2024-01-20",
        isRead: true,
        isArchived: false,
        actionRequired: true,
        actionUrl: "/contracts/2",
        createdAt: "2024-01-20T14:30:00Z"
      },
      {
        id: 3,
        alertType: "system",
        alertTitle: "تحديث النظام",
        alertMessage: "تم تحديث النظام بنجاح إلى الإصدار 2.1.0",
        alertPriority: "منخفض",
        alertDate: "2024-01-22",
        isRead: false,
        isArchived: false,
        actionRequired: false,
        createdAt: "2024-01-22T09:15:00Z"
      }
    ];

    const mockStats: AlertStats = {
      totalAlerts: 15,
      unreadAlerts: 8,
      highPriorityAlerts: 3,
      actionRequiredAlerts: 5,
      overduePayments: 2,
      contractExpirations: 4,
      systemAlerts: 1
    };

    setTimeout(() => {
      setAlerts(mockAlerts);
      setAlertStats(mockStats);
      setIsLoading(false);
    }, 1000);
  }, []);

  const markAsRead = (alertId: number) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, isRead: true } : alert
    ));
    toast({
      title: "تم وضع علامة كمقروء",
      description: "تم تحديث حالة التنبيه",
      variant: "success"
    });
  };

  const archiveAlert = (alertId: number) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, isArchived: true } : alert
    ));
    toast({
      title: "تم الأرشفة",
      description: "تم أرشفة التنبيه بنجاح",
      variant: "success"
    });
  };

  const deleteAlert = (alertId: number) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
    toast({
      title: "تم الحذف",
      description: "تم حذف التنبيه بنجاح",
      variant: "success"
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'حرج': return 'bg-red-100 text-red-800 border-red-200';
      case 'عالي': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'متوسط': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'منخفض': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'payment_overdue': return <DollarSign className="h-4 w-4" />;
      case 'contract_expiring': return <Calendar className="h-4 w-4" />;
      case 'system': return <Settings className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const filteredAlerts = alerts.filter(alert => {
    const matchesTab = activeTab === "all" || 
                      (activeTab === "unread" && !alert.isRead) ||
                      (activeTab === "action" && alert.actionRequired) ||
                      (activeTab === "archived" && alert.isArchived);
    
    const matchesSearch = alert.alertTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         alert.alertMessage.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (alert.clientName && alert.clientName.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesPriority = priorityFilter === "all" || alert.alertPriority === priorityFilter;
    const matchesType = typeFilter === "all" || alert.alertType === typeFilter;
    
    return matchesTab && matchesSearch && matchesPriority && matchesType;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">جاري تحميل التنبيهات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Bell className="h-6 w-6" />
            مركز التنبيهات
          </h1>
          <p className="text-gray-600">إدارة ومتابعة جميع التنبيهات والإشعارات</p>
        </div>
        <Button variant="outline" size="sm">
          <Settings className="h-4 w-4 mr-2" />
          إعدادات التنبيهات
        </Button>
      </div>

      {/* Statistics Cards */}
      {alertStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Bell className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">إجمالي التنبيهات</p>
                  <p className="text-lg font-semibold">{alertStats.totalAlerts}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">غير مقروءة</p>
                  <p className="text-lg font-semibold">{alertStats.unreadAlerts}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <div>
                  <p className="text-sm text-gray-600">أولوية عالية</p>
                  <p className="text-lg font-semibold">{alertStats.highPriorityAlerts}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">تتطلب إجراء</p>
                  <p className="text-lg font-semibold">{alertStats.actionRequiredAlerts}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-red-600" />
                <div>
                  <p className="text-sm text-gray-600">دفعات متأخرة</p>
                  <p className="text-lg font-semibold">{alertStats.overduePayments}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-yellow-600" />
                <div>
                  <p className="text-sm text-gray-600">عقود منتهية</p>
                  <p className="text-lg font-semibold">{alertStats.contractExpirations}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Settings className="h-4 w-4 text-gray-600" />
                <div>
                  <p className="text-sm text-gray-600">تنبيهات النظام</p>
                  <p className="text-lg font-semibold">{alertStats.systemAlerts}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="البحث في التنبيهات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="الأولوية" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأولويات</SelectItem>
                <SelectItem value="حرج">حرج</SelectItem>
                <SelectItem value="عالي">عالي</SelectItem>
                <SelectItem value="متوسط">متوسط</SelectItem>
                <SelectItem value="منخفض">منخفض</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="النوع" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأنواع</SelectItem>
                <SelectItem value="payment_overdue">دفعات متأخرة</SelectItem>
                <SelectItem value="contract_expiring">عقود منتهية</SelectItem>
                <SelectItem value="system">تنبيهات النظام</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Alerts Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">جميع التنبيهات</TabsTrigger>
          <TabsTrigger value="unread">غير مقروءة</TabsTrigger>
          <TabsTrigger value="action">تتطلب إجراء</TabsTrigger>
          <TabsTrigger value="archived">مؤرشفة</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {filteredAlerts.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد تنبيهات</h3>
                <p className="text-gray-600">لا توجد تنبيهات تطابق المعايير المحددة</p>
              </CardContent>
            </Card>
          ) : (
            filteredAlerts.map((alert) => (
              <Card key={alert.id} className={`${!alert.isRead ? 'border-l-4 border-l-blue-500' : ''}`}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <div className="mt-1">
                        {getAlertIcon(alert.alertType)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className={`font-medium ${!alert.isRead ? 'font-semibold' : ''}`}>
                            {alert.alertTitle}
                          </h3>
                          <Badge className={getPriorityColor(alert.alertPriority)}>
                            {alert.alertPriority}
                          </Badge>
                          {alert.actionRequired && (
                            <Badge variant="outline" className="text-purple-600 border-purple-200">
                              يتطلب إجراء
                            </Badge>
                          )}
                        </div>
                        <p className="text-gray-600 mb-2">{alert.alertMessage}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(alert.alertDate)}
                          </span>
                          {alert.clientName && (
                            <span className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              {alert.clientName}
                            </span>
                          )}
                          {alert.contractNumber && (
                            <span className="flex items-center gap-1">
                              <FileText className="h-3 w-3" />
                              {alert.contractNumber}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {!alert.isRead && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => markAsRead(alert.id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                      {alert.actionRequired && alert.actionUrl && (
                        <Button variant="outline" size="sm">
                          اتخاذ إجراء
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => archiveAlert(alert.id)}
                      >
                        <Archive className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteAlert(alert.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AlertsCenter;
