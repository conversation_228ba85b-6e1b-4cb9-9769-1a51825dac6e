// ===== AUTHENTICATION MIDDLEWARE =====
// Author: Augment Code
// Description: Middleware for user authentication and session management

const { promisify } = require('util');

/**
 * Authentication middleware
 * Checks if user is authenticated via session
 */
const requireAuth = (req, res, next) => {
  // For now, we'll skip authentication in development
  // This can be enhanced later with proper user authentication
  if (process.env.NODE_ENV === 'development') {
    // Mock user for development
    req.user = {
      id: 1,
      username: 'admin',
      role: 'admin',
      permissions: ['read', 'write', 'delete', 'admin']
    };
    return next();
  }

  // Check session for authentication
  if (req.session && req.session.user) {
    req.user = req.session.user;
    return next();
  }

  // Return unauthorized if not authenticated
  return res.status(401).json({
    success: false,
    error: 'غير مصرح بالوصول',
    code: 'UNAUTHORIZED'
  });
};

/**
 * Optional authentication middleware
 * Adds user info if authenticated, but doesn't require it
 */
const optionalAuth = (req, res, next) => {
  if (req.session && req.session.user) {
    req.user = req.session.user;
  }
  next();
};

/**
 * Role-based authorization middleware
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'غير مصرح بالوصول',
        code: 'UNAUTHORIZED'
      });
    }

    const userRoles = Array.isArray(req.user.role) ? req.user.role : [req.user.role];
    const requiredRoles = Array.isArray(roles) ? roles : [roles];
    
    const hasRole = requiredRoles.some(role => userRoles.includes(role));
    
    if (!hasRole) {
      return res.status(403).json({
        success: false,
        error: 'ليس لديك صلاحية للوصول لهذا المورد',
        code: 'FORBIDDEN'
      });
    }

    next();
  };
};

/**
 * Permission-based authorization middleware
 */
const requirePermission = (permissions) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'غير مصرح بالوصول',
        code: 'UNAUTHORIZED'
      });
    }

    const userPermissions = req.user.permissions || [];
    const requiredPermissions = Array.isArray(permissions) ? permissions : [permissions];
    
    const hasPermission = requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: 'ليس لديك صلاحية لتنفيذ هذا الإجراء',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    next();
  };
};

module.exports = {
  requireAuth,
  optionalAuth,
  requireRole,
  requirePermission
};
