import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  AlertTriangle, 
  XCircle, 
  AlertCircle, 
  Info,
  X,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface FieldError {
  field: string;
  message: string;
  guidance?: string;
  severity?: 'error' | 'warning' | 'info';
}

interface FormErrorDisplayProps {
  errors: FieldError[];
  onDismiss?: () => void;
  onFieldFocus?: (fieldName: string) => void;
  className?: string;
  title?: string;
  showFieldNames?: boolean;
  compact?: boolean;
}

const severityConfig = {
  error: {
    icon: XCircle,
    bgColor: 'bg-red-50 dark:bg-red-950/20',
    borderColor: 'border-red-200 dark:border-red-800',
    iconColor: 'text-red-600 dark:text-red-400',
    textColor: 'text-red-900 dark:text-red-100',
    badgeVariant: 'destructive' as const
  },
  warning: {
    icon: AlertTriangle,
    bgColor: 'bg-yellow-50 dark:bg-yellow-950/20',
    borderColor: 'border-yellow-200 dark:border-yellow-800',
    iconColor: 'text-yellow-600 dark:text-yellow-400',
    textColor: 'text-yellow-900 dark:text-yellow-100',
    badgeVariant: 'warning' as const
  },
  info: {
    icon: Info,
    bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
    iconColor: 'text-blue-600 dark:text-blue-400',
    textColor: 'text-blue-900 dark:text-blue-100',
    badgeVariant: 'secondary' as const
  }
};

const fieldNameTranslations = {
  // Client fields
  'clientId': 'رقم العميل',
  'clientName': 'اسم العميل',
  'clientType': 'نوع العميل',
  'clientAddress': 'عنوان العميل',
  'clientPhone': 'رقم الهاتف',
  'clientPhoneWhatsapp': 'رقم الواتساب',
  'clientEmail': 'البريد الإلكتروني',
  
  // Contract fields
  'contractNumber': 'رقم العقد',
  'contractStartDate': 'تاريخ بداية العقد',
  'contractEndDate': 'تاريخ نهاية العقد',
  'contractDuration': 'مدة العقد',
  'contractRegion': 'منطقة العقد',
  'contractDepartment': 'القسم',
  'contractClient': 'عميل العقد',
  
  // Product fields
  'productName': 'اسم المنتج',
  'productLabel': 'تسمية المنتج',
  'area': 'المساحة',
  'meterPrice': 'سعر المتر',
  'billingType': 'نوع الفوترة',
  
  // Payment fields
  'paymentAmount': 'مبلغ الدفع',
  'paymentDate': 'تاريخ الدفع',
  'paymentMethod': 'طريقة الدفع',
  'receiptNumber': 'رقم الإيصال',
  
  // Settings fields
  'companyName': 'اسم الشركة',
  'programName': 'اسم البرنامج',
  'currency': 'العملة'
};

export function FormErrorDisplay({
  errors,
  onDismiss,
  onFieldFocus,
  className,
  title = 'يرجى تصحيح الأخطاء التالية:',
  showFieldNames = true,
  compact = false
}: FormErrorDisplayProps) {
  if (!errors || errors.length === 0) {
    return null;
  }

  const getFieldDisplayName = (fieldName: string) => {
    return fieldNameTranslations[fieldName] || fieldName;
  };

  const groupedErrors = errors.reduce((acc, error) => {
    const severity = error.severity || 'error';
    if (!acc[severity]) {
      acc[severity] = [];
    }
    acc[severity].push(error);
    return acc;
  }, {} as Record<string, FieldError[]>);

  const handleFieldClick = (fieldName: string) => {
    if (onFieldFocus) {
      onFieldFocus(fieldName);
    }
  };

  if (compact) {
    return (
      <div className={cn(
        "space-y-2",
        className
      )}>
        {Object.entries(groupedErrors).map(([severity, severityErrors]) => {
          const config = severityConfig[severity as keyof typeof severityConfig];
          const IconComponent = config.icon;
          
          return (
            <div
              key={severity}
              className={cn(
                "flex items-start gap-3 p-3 rounded-lg border",
                config.bgColor,
                config.borderColor
              )}
            >
              <IconComponent className={cn("w-4 h-4 mt-0.5 flex-shrink-0", config.iconColor)} />
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className={cn("text-sm font-medium", config.textColor)}>
                    {severityErrors.length} خطأ
                  </span>
                  <Badge variant={config.badgeVariant} className="text-xs">
                    {severity === 'error' ? 'خطأ' : severity === 'warning' ? 'تحذير' : 'معلومات'}
                  </Badge>
                </div>
                <div className="space-y-1">
                  {severityErrors.map((error, index) => (
                    <div key={index} className="text-xs">
                      {showFieldNames && (
                        <button
                          onClick={() => handleFieldClick(error.field)}
                          className={cn(
                            "font-medium hover:underline cursor-pointer",
                            config.textColor
                          )}
                        >
                          {getFieldDisplayName(error.field)}:
                        </button>
                      )}
                      <span className={cn("mr-1", config.textColor)}>
                        {error.message}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
              {onDismiss && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onDismiss}
                  className="h-6 w-6 p-0"
                >
                  <X className="w-3 h-3" />
                </Button>
              )}
            </div>
          );
        })}
      </div>
    );
  }

  return (
    <Alert className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <AlertCircle className="w-5 h-5 text-red-600" />
          <h4 className="font-semibold text-red-900 dark:text-red-100">
            {title}
          </h4>
          <Badge variant="destructive" className="text-xs">
            {errors.length} خطأ
          </Badge>
        </div>
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="h-8 w-8 p-0"
          >
            <X className="w-4 h-4" />
          </Button>
        )}
      </div>

      <AlertDescription className="space-y-3">
        {Object.entries(groupedErrors).map(([severity, severityErrors]) => {
          const config = severityConfig[severity as keyof typeof severityConfig];
          const IconComponent = config.icon;
          
          return (
            <div key={severity} className="space-y-2">
              {severity !== 'error' && (
                <div className="flex items-center gap-2">
                  <IconComponent className={cn("w-4 h-4", config.iconColor)} />
                  <span className={cn("text-sm font-medium", config.textColor)}>
                    {severity === 'warning' ? 'تحذيرات:' : 'معلومات:'}
                  </span>
                </div>
              )}
              
              <div className="space-y-2 mr-6">
                {severityErrors.map((error, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex items-start gap-2">
                      <span className="text-sm">•</span>
                      <div className="flex-1">
                        {showFieldNames && (
                          <button
                            onClick={() => handleFieldClick(error.field)}
                            className="font-medium text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
                          >
                            {getFieldDisplayName(error.field)}:
                          </button>
                        )}
                        <span className="text-sm text-gray-700 dark:text-gray-300 mr-1">
                          {error.message}
                        </span>
                      </div>
                    </div>
                    
                    {error.guidance && (
                      <div className="mr-4 text-xs text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-2 rounded">
                        💡 {error.guidance}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </AlertDescription>
    </Alert>
  );
}

// Hook for managing form errors
export function useFormErrors() {
  const [errors, setErrors] = React.useState<FieldError[]>([]);

  const addError = (field: string, message: string, guidance?: string, severity: 'error' | 'warning' | 'info' = 'error') => {
    setErrors(prev => [
      ...prev.filter(e => e.field !== field),
      { field, message, guidance, severity }
    ]);
  };

  const removeError = (field: string) => {
    setErrors(prev => prev.filter(e => e.field !== field));
  };

  const clearErrors = () => {
    setErrors([]);
  };

  const hasErrors = errors.length > 0;
  const hasErrorsForField = (field: string) => errors.some(e => e.field === field);

  return {
    errors,
    addError,
    removeError,
    clearErrors,
    hasErrors,
    hasErrorsForField,
    setErrors
  };
}

export default FormErrorDisplay;
