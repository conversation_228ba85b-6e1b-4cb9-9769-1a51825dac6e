import { z } from "zod";
import { pgTable, text, integer, timestamp, boolean, decimal, serial } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

// Drizzle table definitions
export const clients = pgTable("clients", {
  id: serial("id").primaryKey(),
  clientId: text("client_id").notNull().unique(),
  clientType: text("client_type").notNull(),
  clientName: text("client_name").notNull(),
  clientAddress: text("client_address"),
  clientPhoneWhatsapp: text("client_phone_whatsapp").notNull(),
  clientPhone2: text("client_phone_2"),
  clientPhone3: text("client_phone_3"),
  clientEmail: text("client_email"),
  clientNotes: text("client_notes"),
  clientFinancialGuarantee: text("client_financial_guarantee"),
  clientID_Image: text("client_id_image"),
  clientFinancial_Category: text("client_financial_category"),
  clientLegal_Rep: text("client_legal_rep"),
  clientPartner: text("client_partner"),
  clientReg_Number: text("client_reg_number"),
  clientTaxReg_Number: text("client_tax_reg_number"),
  clientLegal_Status: text("client_legal_status"),
  clientRemarks: text("client_remarks"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const contracts = pgTable("contracts", {
  id: serial("id").primaryKey(),
  contractNumber: text("contract_number").notNull().unique(),
  contractInternalId: text("contract_internal_id"),
  contractDescription: text("contract_description"),
  contractSubject: text("contract_subject"),
  clientId: integer("client_id").notNull().references(() => clients.id),
  clientName: text("client_name").notNull(),
  clientType: text("client_type").notNull(),
  contractType: text("contract_type").notNull(),
  contractStatus: text("contract_status").notNull(),
  contractDate: timestamp("contract_date").notNull(),
  startDate: timestamp("start_date").notNull(),
  endDate: timestamp("end_date").notNull(),
  contractDurationYears: integer("contract_duration_years").notNull(),
  totalContractValue: decimal("total_contract_value", { precision: 12, scale: 2 }).notNull(),
  monthlyAmount: decimal("monthly_amount", { precision: 12, scale: 2 }).notNull(),
  paymentFrequency: text("payment_frequency").notNull(),
  numberOfProducts: integer("number_of_products").notNull(),
  hasUnifiedActivationDate: boolean("has_unified_activation_date").notNull(),
  consecutiveMissedPayments: integer("consecutive_missed_payments").default(0).notNull(),
  totalMissedPayments: integer("total_missed_payments").default(0).notNull(),
  autoTerminationSuggested: boolean("auto_termination_suggested").default(false).notNull(),
  systemFlags: text("system_flags"),
  outstandingAmount: decimal("outstanding_amount", { precision: 12, scale: 2 }),
  paidAmount: decimal("paid_amount", { precision: 12, scale: 2 }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const payments = pgTable("payments", {
  id: serial("id").primaryKey(),
  contractId: integer("contract_id").notNull().references(() => contracts.id),
  contractNumber: text("contract_number").notNull(),
  clientId: integer("client_id").notNull().references(() => clients.id),
  clientName: text("client_name").notNull(),
  paymentType: text("payment_type").notNull(),
  installmentNumber: integer("installment_number").notNull(),
  dueDate: timestamp("due_date").notNull(),
  amount: decimal("amount", { precision: 12, scale: 2 }).notNull(),
  paidAmount: decimal("paid_amount", { precision: 12, scale: 2 }).default('0').notNull(),
  remainingAmount: decimal("remaining_amount", { precision: 12, scale: 2 }).notNull(),
  paymentStatus: text("payment_status").notNull(),
  paymentDate: timestamp("payment_date"),
  paymentMethod: text("payment_method"),
  receiptNumber: text("receipt_number"),
  notes: text("notes"),
  overdueBy: integer("overdue_by"),
  lateFee: decimal("late_fee", { precision: 12, scale: 2 }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const referenceData = pgTable("reference_data", {
  id: serial("id").primaryKey(),
  category: text("category").notNull(),
  name: text("name").notNull(),
  nameEn: text("name_en"),
  description: text("description"),
  isActive: boolean("is_active").default(true).notNull(),
  sortOrder: integer("sort_order").default(0).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const settings = pgTable("settings", {
  id: serial("id").primaryKey(),
  companyName: text("company_name"),
  programName: text("program_name"),
  companyRegNo: text("company_reg_no"),
  taxId: text("tax_id"),
  about: text("about"),
  companyLogo: text("company_logo"),
  language: text("language").default("ar").notNull(),
  country: text("country"),
  currency: text("currency").default("EGP").notNull(),
  currencySymbol: text("currency_symbol").default("ج.م").notNull(),
  dateFormat: text("date_format").default("DD/MM/YYYY").notNull(),
  decimalPlaces: text("decimal_places").default("2").notNull(),
  workDays: text("work_days").array(),
  notificationEmail: text("notification_email"),
  numberingFormat: text("numbering_format").default("serial-year-unit").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Zod schemas for validation and types
export const clientSchema = createSelectSchema(clients);
export const insertClientSchema = createInsertSchema(clients).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const contractSchema = createSelectSchema(contracts);
export const insertContractSchema = createInsertSchema(contracts).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const paymentSchema = createSelectSchema(payments);
export const insertPaymentSchema = createInsertSchema(payments).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const referenceDataSchema = createSelectSchema(referenceData);
export const insertReferenceDataSchema = createInsertSchema(referenceData).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const settingsSchema = createSelectSchema(settings);
export const insertSettingsSchema = createInsertSchema(settings).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// TypeScript types
export type Client = typeof clients.$inferSelect;
export type InsertClient = typeof clients.$inferInsert;

export type Contract = typeof contracts.$inferSelect;
export type InsertContract = typeof contracts.$inferInsert;

export type Payment = typeof payments.$inferSelect;
export type InsertPayment = typeof payments.$inferInsert;

export type ReferenceData = typeof referenceData.$inferSelect;
export type InsertReferenceData = typeof referenceData.$inferInsert;

export type Settings = typeof settings.$inferSelect;
export type InsertSettings = typeof settings.$inferInsert;

// Dashboard stats type
export const dashboardStatsSchema = z.object({
  totalClients: z.number(),
  totalContracts: z.number(),
  activeContracts: z.number(),
  outstandingAmount: z.number(),
  overduePayments: z.number(),
  monthlyRevenue: z.number(),
  totalRevenue: z.number(),
});

export type DashboardStats = z.infer<typeof dashboardStatsSchema>;

// Additional schemas for bank payments and treasury operations
export const insertBankPaymentSchema = z.object({
  contractId: z.number(),
  contractNumber: z.string(),
  clientId: z.number(),
  clientName: z.string(),
  amount: z.number(),
  paymentDate: z.string(),
  bankName: z.string().optional(),
  accountNumber: z.string().optional(),
  referenceNumber: z.string().optional(),
  notes: z.string().optional(),
});

export const insertCashReceiptSchema = z.object({
  contractId: z.number(),
  contractNumber: z.string(),
  clientId: z.number(),
  clientName: z.string(),
  amount: z.number(),
  receiptDate: z.string(),
  receiptNumber: z.string(),
  receivedBy: z.string().optional(),
  notes: z.string().optional(),
});

export const insertChequeReceiptSchema = z.object({
  contractId: z.number(),
  contractNumber: z.string(),
  clientId: z.number(),
  clientName: z.string(),
  amount: z.number(),
  chequeNumber: z.string(),
  chequeDate: z.string(),
  bankName: z.string(),
  dueDate: z.string(),
  status: z.string().default('pending'),
  notes: z.string().optional(),
});

export type InsertBankPayment = z.infer<typeof insertBankPaymentSchema>;
export type InsertCashReceipt = z.infer<typeof insertCashReceiptSchema>;
export type InsertChequeReceipt = z.infer<typeof insertChequeReceiptSchema>;

// Additional types for bank payments and treasury operations
export type BankPayment = InsertBankPayment & {
  id: number;
  createdAt: string;
  updatedAt: string;
};

export type CashReceipt = InsertCashReceipt & {
  id: number;
  createdAt: string;
  updatedAt: string;
};

export type ChequeReceipt = InsertChequeReceipt & {
  id: number;
  createdAt: string;
  updatedAt: string;
};