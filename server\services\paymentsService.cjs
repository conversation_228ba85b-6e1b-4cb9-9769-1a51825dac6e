const database = require('../models/db.cjs');

class PaymentsService {
  // جلب جميع المدفوعات
  getAllPayments() {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      const sql = `
        SELECT
          p.*,
          c.contractNumber,
          c.contractSubject,
          cl.clientName
        FROM CashReceipts p
        LEFT JOIN Contracts c ON p.contractId = c.id
        LEFT JOIN Clients cl ON c.clientId = cl.id
        WHERE p.isActive = 1
        ORDER BY p.paymentDate DESC
      `;

      db.all(sql, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  }

  // جلب مدفوعة بواسطة ID
  getPaymentById(id) {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      const sql = `
        SELECT
          p.*,
          c.contractNumber,
          c.contractSubject,
          cl.clientName
        FROM CashReceipts p
        LEFT JOIN Contracts c ON p.contractId = c.id
        LEFT JOIN Clients cl ON c.clientId = cl.id
        WHERE p.id = ? AND p.isActive = 1
      `;

      db.get(sql, [id], (err, row) => {
        if (err) return reject(err);
        resolve(row);
      });
    });
  }

  // إنشاء مدفوعة جديدة
  createPayment(paymentData) {
    return new Promise((resolve, reject) => {
      const db = database.getConnection();
      const sql = `
        INSERT INTO CashReceipts (
          receiptNumber, paymentDate, amount, paymentType, contractId,
          receivableId, contractNumber, contractSubject, clientName,
          receivableDescription, paymentStatus, nonContractType, notes, isActive
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
      `;

      const params = [
        paymentData.receiptNumber,
        paymentData.paymentDate,
        paymentData.amount,
        paymentData.paymentType,
        paymentData.contractId,
        paymentData.receivableId,
        paymentData.contractNumber,
        paymentData.contractSubject,
        paymentData.clientName,
        paymentData.receivableDescription,
        paymentData.paymentStatus,
        paymentData.nonContractType,
        paymentData.notes
      ];

      db.run(sql, params, function(err) {
        if (err) return reject(err);
        resolve({ id: this.lastID, ...paymentData });
      });
    });
  }
}

module.exports = new PaymentsService();
