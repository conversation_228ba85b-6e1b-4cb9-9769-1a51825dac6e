const express = require('express');
const router = express.Router();
const settingsController = require('../controllers/settingsController.cjs');

// Routes for settings
router.get('/', settingsController.getSettings);        // GET /api/settings
router.post('/', settingsController.saveSettings);      // POST /api/settings
router.post('/reset', settingsController.resetSettings); // POST /api/settings/reset

module.exports = router;
