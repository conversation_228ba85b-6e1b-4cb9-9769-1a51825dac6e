// ===== RECEIVABLES ROUTES =====
// Author: Augment Code
// Description: Routes for managing receivables and installments

const express = require('express');
const router = express.Router();

// Import validation utilities
const {
  sanitizeString,
  sanitizeNumber
} = require('../validation-utils.cjs');

// Receivables routes will be added here
// This is a placeholder file that will be populated with actual routes

module.exports = router;
