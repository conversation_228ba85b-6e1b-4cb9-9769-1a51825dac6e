/**
 * Enhanced Contract Calculator
 * Based on the reference design with improved calculations
 */

export interface ContractProduct {
  productLabel: string;
  area: number;
  meterPrice: number;
  activationDate: string;
  endDate: string;
  billingType: string;
  irregularBillingMonths?: number;
  taxInfo: boolean;
  taxRate: number;
  financialAccountingStartDate: string;
  financialAccountingEndDate: string;
  accountingDuration: number;
  hasAnnualIncrease: boolean;
  increaseStartYear: number;
  increaseType: string;
  increaseValue: number;
}

export interface ContractCalculationData {
  products: ContractProduct[];
  finalInsuranceRate: number;
  advancePaymentMonths: number;
  gracePeriodDays: number;
}

export interface CalculationResults {
  totalValue: number;
  baseValue?: number;
  monthlyAmount: number;
  yearlyAmount: number;
  insuranceAmount: number;
  advancePaymentAmount: number;
  totalWithTax: number;
  breakdown: CalculationBreakdown[];
  installments: InstallmentDetails[];
  summary: CalculationSummary;
  yearlyAmounts?: YearlyAmount[];
  calendarRevenues?: CalendarRevenue[];
}

export interface YearlyAmount {
  year: number;
  totalAmount: number;
  monthlyAmount: number;
  products?: ProductYearDetail[];
}

export interface ProductYearDetail {
  productLabel: string;
  annualValue: number;
  installmentAmount: number;
  installmentsPerYear: number;
  billingType: string;
}

export interface CalendarRevenue {
  calendarYear: number;
  totalRevenue: number;
  details: RevenueDetail[];
}

export interface RevenueDetail {
  contractYear: number;
  overlapStart: string;
  overlapEnd: string;
  overlapDays: number;
  contractYearDays: number;
  contractYearTotal: number;
  proportionalRevenue: number;
}

export interface CalculationBreakdown {
  description: string;
  amount: number;
  percentage?: number;
  year?: number;
}

export interface InstallmentDetails {
  number: number;
  amount: number;
  dueDate: Date;
  periodStart: Date;
  periodEnd: Date;
  year: number;
  baseAmount: number;
  increaseAmount: number;
  taxAmount: number;
  totalAmount: number;
}

export interface CalculationSummary {
  totalProducts: number;
  totalArea: number;
  averagePricePerMeter: number;
  totalYears: number;
  totalInstallments: number;
  firstYearTotal: number;
  lastYearTotal: number;
}

export class EnhancedContractCalculator {
  /**
   * Calculate comprehensive contract values
   */
  static calculateContract(data: ContractCalculationData): CalculationResults {
    console.log('🔢 Starting calculation with data:', data);
    console.log('🔢 Number of products:', data.products?.length || 0);
    console.log('🔢 Insurance rate:', data.finalInsuranceRate);
    console.log('🔢 Advance payment months:', data.advancePaymentMonths);

    // Extract contract duration from products (max duration)
    const contractDurationYears = this.calculateContractDuration(data.products);
    console.log('🔢 Contract duration:', contractDurationYears, 'years');

    // Calculate yearly amounts with annual increases
    const yearlyAmounts = this.calculateYearlyAmounts(data.products, contractDurationYears);
    console.log('🔢 Yearly amounts:', yearlyAmounts);

    // Calculate total value including all increases
    const totalValueWithIncreases = yearlyAmounts.reduce((sum, year) => sum + year.totalAmount, 0);
    console.log('🔢 Total value with increases:', totalValueWithIncreases);

    // Calculate insurance amount based on contract duration
    const insuranceAmount = this.calculateInsuranceAmount(yearlyAmounts, data.finalInsuranceRate, contractDurationYears);
    console.log('🔢 Insurance amount:', insuranceAmount);

    const totalWithTax = totalValueWithIncreases + insuranceAmount;

    // Calculate average monthly amount (for display purposes)
    const totalMonths = contractDurationYears * 12;
    const monthlyAmount = totalValueWithIncreases / totalMonths;

    // Calculate advance payment based on consecutive months from contract start
    const advancePaymentAmount = this.calculateAdvancePayment(yearlyAmounts, data.advancePaymentMonths || 0);

    // Generate installments based on product billing types and dates
    const installments = this.generateInstallmentsFromProducts(data.products);

    // Calculate calendar year revenues
    const calendarRevenues = this.calculateCalendarYearRevenues(data.products, contractDurationYears);

    // Generate summary
    const summary = this.generateSummary(
      data.products,
      contractDurationYears,
      installments
    );

    // Generate breakdown
    const breakdown = this.generateBreakdown(
      { totalValue: totalValueWithIncreases, totalArea: 0, totalTax: 0, productBreakdown: [], averagePricePerMeter: 0 },
      insuranceAmount,
      data
    );

    const results = {
      totalValue: totalValueWithIncreases,
      baseValue: yearlyAmounts[0]?.totalAmount || 0,
      monthlyAmount,
      yearlyAmount: totalValueWithIncreases / contractDurationYears,
      insuranceAmount,
      advancePaymentAmount,
      totalWithTax,
      breakdown,
      installments,
      summary,
      yearlyAmounts,
      calendarRevenues
    };

    console.log('🔢 Final results:', results);
    return results;
  }

  /**
   * Calculate contract duration from products (max duration)
   */
  private static calculateContractDuration(products: ContractProduct[]): number {
    let maxDuration = 0;

    products.forEach(product => {
      let duration = 0;

      // Use accountingDuration if available (this is the most accurate)
      if (product.accountingDuration && product.accountingDuration > 0) {
        duration = product.accountingDuration;
      }
      // Otherwise calculate from dates (in whole years)
      else if (product.activationDate && product.endDate) {
        const startDate = new Date(product.activationDate);
        const endDate = new Date(product.endDate);

        // Calculate difference in years more accurately
        const yearDiff = endDate.getFullYear() - startDate.getFullYear();
        const monthDiff = endDate.getMonth() - startDate.getMonth();
        const dayDiff = endDate.getDate() - startDate.getDate();

        // حساب المدة بالسنوات الكاملة
        // إذا كان الشهر واليوم في تاريخ الانتهاء أكبر من أو يساوي تاريخ البداية
        if (monthDiff > 0 || (monthDiff === 0 && dayDiff >= 0)) {
          duration = yearDiff;
        } else {
          // إذا لم نصل بعد لتاريخ الذكرى السنوية، نطرح سنة
          duration = yearDiff - 1;
        }

        // للعقود التي تنتهي في نفس التاريخ أو بعد سنة كاملة بالضبط
        // نتأكد من أن المدة لا تقل عن سنة واحدة
        if (duration <= 0) {
          duration = 1;
        }
      }

      if (duration > maxDuration) {
        maxDuration = duration;
      }
    });

    return Math.max(maxDuration, 1); // Minimum 1 year
  }

  /**
   * Calculate insurance amount based on contract duration rules
   */
  private static calculateInsuranceAmount(yearlyAmounts: any[], insuranceRate: number, contractDurationYears: number): number {
    if (!insuranceRate || insuranceRate <= 0) return 0;

    let insuranceBase = 0;

    console.log('🔢 Insurance calculation for', contractDurationYears, 'years contract');
    console.log('🔢 Insurance rate:', insuranceRate, '%');

    if (contractDurationYears <= 3) {
      // For contracts 3 years or less: insurance on total contract value (OLD SYSTEM LOGIC)
      insuranceBase = yearlyAmounts.reduce((sum, year) => sum + year.totalAmount, 0);
      console.log('🔢 Contract ≤ 3 years: Insurance base = total contract value =', insuranceBase);
    } else {
      // For contracts > 3 years: insurance on (first 3 years + last year - third year) (OLD SYSTEM LOGIC)
      const firstThreeYears = yearlyAmounts.slice(0, 3).reduce((sum, year) => sum + year.totalAmount, 0);
      const lastYear = yearlyAmounts[yearlyAmounts.length - 1]?.totalAmount || 0;
      const thirdYear = yearlyAmounts[2]?.totalAmount || 0;
      insuranceBase = firstThreeYears + lastYear - thirdYear;

      console.log('🔢 Contract > 3 years (OLD SYSTEM LOGIC):');
      console.log('🔢 First 3 years total:', firstThreeYears);
      console.log('🔢 Third year amount:', thirdYear);
      console.log('🔢 Last year amount:', lastYear);
      console.log('🔢 Insurance base: firstThreeYears + lastYear - thirdYear =', firstThreeYears, '+', lastYear, '-', thirdYear, '=', insuranceBase);
    }

    // Round insurance base like old system
    insuranceBase = Math.round(insuranceBase * 100) / 100;

    // Insurance rate is already a percentage (10 means 10%), so divide by 100
    const insuranceAmount = Math.round((insuranceBase * insuranceRate / 100) * 100) / 100;
    console.log('🔢 Final insurance amount:', insuranceBase, '×', insuranceRate, '% =', insuranceAmount);

    return insuranceAmount;
  }

  /**
   * Calculate advance payment based on consecutive months from contract start
   */
  private static calculateAdvancePayment(yearlyAmounts: any[], advancePaymentMonths: number): number {
    if (!advancePaymentMonths || advancePaymentMonths <= 0) return 0;

    let totalAdvancePayment = 0;
    let remainingMonths = advancePaymentMonths;
    let yearIndex = 0;

    console.log('🔢 Calculating advance payment for', advancePaymentMonths, 'months');

    while (remainingMonths > 0 && yearIndex < yearlyAmounts.length) {
      const yearData = yearlyAmounts[yearIndex];
      const yearlyAmount = yearData.totalAmount;
      const monthlyAmount = yearlyAmount / 12;

      // How many months to take from this year
      const monthsFromThisYear = Math.min(remainingMonths, 12);
      const amountFromThisYear = monthlyAmount * monthsFromThisYear;

      totalAdvancePayment += amountFromThisYear;
      remainingMonths -= monthsFromThisYear;

      console.log(`🔢 Year ${yearData.year}: Taking ${monthsFromThisYear} months × ${monthlyAmount.toFixed(2)} = ${amountFromThisYear.toFixed(2)}`);
      console.log(`🔢 Remaining months: ${remainingMonths}`);

      yearIndex++;
    }

    totalAdvancePayment = Math.round(totalAdvancePayment * 100) / 100;
    console.log('🔢 Total advance payment:', totalAdvancePayment);

    return totalAdvancePayment;
  }

  /**
   * Calculate yearly amounts with annual increases and billing frequency
   */
  private static calculateYearlyAmounts(products: ContractProduct[], contractDurationYears: number) {
    const yearlyAmounts = [];

    console.log('🔢 Calculating yearly amounts for', contractDurationYears, 'years');
    console.log('🔢 Products:', products);

    for (let year = 0; year < contractDurationYears; year++) {
      let yearTotal = 0;
      const yearDetails = [];

      products.forEach(product => {
        // Base annual value for this product (area × price per meter)
        const baseAmount = product.area * product.meterPrice;
        let currentYearValue = baseAmount;

        console.log(`🔢 Year ${year + 1}, Product ${product.productLabel}: Base = ${product.area} × ${product.meterPrice} = ${baseAmount}`);

        // Apply annual increase using the OLD SYSTEM LOGIC
        if (product.hasAnnualIncrease && (year + 1) >= product.increaseStartYear) {
          if (product.increaseType === 'نسبة مئوية') {
            if (year === 0) {
              // First year - no increase
              currentYearValue = baseAmount;
            } else {
              // Use compound percentage increase like old system
              const increaseRate = product.increaseValue / 100;
              currentYearValue = baseAmount * Math.pow(1 + increaseRate, year);
              console.log(`🔢 Compound increase: ${baseAmount} × (1 + ${increaseRate})^${year} = ${currentYearValue}`);
            }
          } else if (product.increaseType === 'مبلغ ثابت') {
            // Fixed percentage increase like old system
            const increaseRate = product.increaseValue / 100;
            currentYearValue = baseAmount + (baseAmount * increaseRate * year);
            console.log(`🔢 Fixed increase: ${baseAmount} + (${baseAmount} × ${increaseRate} × ${year}) = ${currentYearValue}`);
          }
        }

        // Round to 2 decimal places like old system
        currentYearValue = Math.round(currentYearValue * 100) / 100;

        // Add tax if applicable
        if (product.taxInfo && product.taxRate > 0) {
          const taxAmount = (currentYearValue * product.taxRate) / 100;
          currentYearValue += taxAmount;
          currentYearValue = Math.round(currentYearValue * 100) / 100;
          console.log(`🔢 Tax added: ${taxAmount}, new total: ${currentYearValue}`);
        }

        // Calculate installment amount based on billing frequency
        let installmentAmount = 0;
        let installmentsPerYear = 0;

        switch (product.billingType) {
          case 'شهري':
            installmentAmount = currentYearValue / 12;
            installmentsPerYear = 12;
            break;
          case 'ربع سنوي':
            installmentAmount = currentYearValue / 4;
            installmentsPerYear = 4;
            break;
          case 'نصف سنوي':
            installmentAmount = currentYearValue / 2;
            installmentsPerYear = 2;
            break;
          case 'سنوي':
            installmentAmount = currentYearValue;
            installmentsPerYear = 1;
            break;
          default:
            installmentAmount = currentYearValue / 12;
            installmentsPerYear = 12;
        }

        // Round installment amount
        installmentAmount = Math.round(installmentAmount * 100) / 100;

        yearTotal += currentYearValue;

        yearDetails.push({
          productLabel: product.productLabel,
          annualValue: currentYearValue,
          installmentAmount: installmentAmount,
          installmentsPerYear: installmentsPerYear,
          billingType: product.billingType
        });
      });

      // Round year total
      yearTotal = Math.round(yearTotal * 100) / 100;
      console.log(`🔢 Year ${year + 1} total: ${yearTotal}`);

      yearlyAmounts.push({
        year: year + 1,
        totalAmount: yearTotal,
        monthlyAmount: Math.round((yearTotal / 12) * 100) / 100,
        products: yearDetails
      });
    }

    console.log('🔢 Final yearly amounts:', yearlyAmounts);
    return yearlyAmounts;
  }

  /**
   * Calculate calendar year revenues based on contract years
   */
  private static calculateCalendarYearRevenues(products: ContractProduct[], contractDurationYears: number) {
    if (!products.length) return [];

    // Get contract start date from first product
    const contractStartDate = new Date(products[0].activationDate);
    const contractEndDate = new Date(contractStartDate);
    contractEndDate.setFullYear(contractEndDate.getFullYear() + contractDurationYears);
    // طرح يوم واحد للحصول على آخر يوم في فترة العقد
    contractEndDate.setDate(contractEndDate.getDate() - 1);

    // Get yearly amounts with increases
    const yearlyAmounts = this.calculateYearlyAmounts(products, contractDurationYears);

    const calendarRevenues = [];

    // Find all calendar years that overlap with the contract
    const startYear = contractStartDate.getFullYear();
    const endYear = contractEndDate.getFullYear();

    for (let calendarYear = startYear; calendarYear <= endYear; calendarYear++) {
      let totalRevenue = 0;
      const yearDetails = [];

      // Calculate revenue for each contract year that overlaps with this calendar year
      for (let contractYear = 1; contractYear <= contractDurationYears; contractYear++) {
        const contractYearStart = new Date(contractStartDate);
        contractYearStart.setFullYear(contractYearStart.getFullYear() + (contractYear - 1));

        const contractYearEnd = new Date(contractYearStart);
        contractYearEnd.setFullYear(contractYearEnd.getFullYear() + 1);
        contractYearEnd.setDate(contractYearEnd.getDate() - 1); // End of contract year

        // Calendar year boundaries
        const calendarYearStart = new Date(calendarYear, 0, 1);
        const calendarYearEnd = new Date(calendarYear, 11, 31);

        // Find overlap period
        const overlapStart = new Date(Math.max(contractYearStart.getTime(), calendarYearStart.getTime()));
        const overlapEnd = new Date(Math.min(contractYearEnd.getTime(), calendarYearEnd.getTime()));

        if (overlapStart <= overlapEnd) {
          // Calculate days in overlap
          const overlapDays = Math.ceil((overlapEnd.getTime() - overlapStart.getTime()) / (1000 * 60 * 60 * 24)) + 1;
          const contractYearDays = Math.ceil((contractYearEnd.getTime() - contractYearStart.getTime()) / (1000 * 60 * 60 * 24)) + 1;

          // Calculate proportional revenue
          const contractYearTotal = yearlyAmounts[contractYear - 1]?.totalAmount || 0;
          const proportionalRevenue = (contractYearTotal * overlapDays) / contractYearDays;

          totalRevenue += proportionalRevenue;

          yearDetails.push({
            contractYear: contractYear,
            overlapStart: overlapStart.toISOString().split('T')[0],
            overlapEnd: overlapEnd.toISOString().split('T')[0],
            overlapDays: overlapDays,
            contractYearDays: contractYearDays,
            contractYearTotal: contractYearTotal,
            proportionalRevenue: proportionalRevenue
          });
        }
      }

      if (totalRevenue > 0) {
        calendarRevenues.push({
          calendarYear: calendarYear,
          totalRevenue: totalRevenue,
          details: yearDetails
        });
      }
    }

    return calendarRevenues;
  }

  /**
   * Generate installments from products based on their billing types and dates
   */
  private static generateInstallmentsFromProducts(products: ContractProduct[]): InstallmentDetails[] {
    const installmentMap = new Map<string, {
      amount: number;
      products: ContractProduct[];
      dueDate: Date;
      year: number;
    }>();

    products.forEach(product => {
      const startDate = new Date(product.activationDate);
      const endDate = new Date(product.endDate);

      // Calculate product total value
      const productValue = product.area * product.meterPrice;
      const taxAmount = product.taxInfo ? (productValue * product.taxRate) / 100 : 0;
      const totalProductValue = productValue + taxAmount;

      // Determine billing frequency
      let installmentsPerYear = 12;
      let monthsInterval = 1;

      switch (product.billingType) {
        case 'ربع سنوي':
          installmentsPerYear = 4;
          monthsInterval = 3;
          break;
        case 'نصف سنوي':
          installmentsPerYear = 2;
          monthsInterval = 6;
          break;
        case 'سنوي':
          installmentsPerYear = 1;
          monthsInterval = 12;
          break;
        default: // شهري
          installmentsPerYear = 12;
          monthsInterval = 1;
      }

      // Calculate total number of installments for this product
      const durationYears = product.accountingDuration ||
        ((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 365.25));
      const totalInstallments = Math.ceil(durationYears * installmentsPerYear);
      const installmentAmount = totalProductValue / totalInstallments;

      // Generate installments
      const currentDate = new Date(startDate);
      let installmentCount = 0;

      while (installmentCount < totalInstallments && currentDate <= endDate) {
        const dateKey = currentDate.toISOString().split('T')[0];
        const year = Math.floor(installmentCount / installmentsPerYear) + 1;

        if (installmentMap.has(dateKey)) {
          // Combine with existing installment on same date
          const existing = installmentMap.get(dateKey)!;
          existing.amount += installmentAmount;
          existing.products.push(product);
        } else {
          // Create new installment
          installmentMap.set(dateKey, {
            amount: installmentAmount,
            products: [product],
            dueDate: new Date(currentDate),
            year: year
          });
        }

        // Move to next installment date
        currentDate.setMonth(currentDate.getMonth() + monthsInterval);
        installmentCount++;
      }
    });

    // Convert to sorted array
    return Array.from(installmentMap.entries())
      .sort(([dateA], [dateB]) => dateA.localeCompare(dateB))
      .map(([dateKey, installmentData], index) => ({
        number: index + 1,
        amount: Math.round(installmentData.amount * 100) / 100, // Round to 2 decimals
        dueDate: installmentData.dueDate,
        periodStart: new Date(installmentData.dueDate),
        periodEnd: new Date(installmentData.dueDate.getTime() + (30 * 24 * 60 * 60 * 1000)), // +30 days
        year: installmentData.year,
        baseAmount: Math.round(installmentData.amount * 100) / 100,
        increaseAmount: 0,
        taxAmount: 0,
        totalAmount: Math.round(installmentData.amount * 100) / 100
      }));
  }

  /**
   * Calculate base values from products
   */
  private static calculateBaseValues(products: ContractProduct[]) {
    let totalValue = 0;
    let totalArea = 0;
    let totalTax = 0;

    const productBreakdown = products.map(product => {
      const productValue = product.area * product.meterPrice;
      const taxAmount = product.taxInfo ? (productValue * product.taxRate) / 100 : 0;
      const totalProductValue = productValue + taxAmount;

      totalValue += productValue;
      totalArea += product.area;
      totalTax += taxAmount;

      return {
        product: product.productLabel,
        area: product.area,
        pricePerMeter: product.meterPrice,
        baseValue: productValue,
        taxAmount,
        totalValue: totalProductValue
      };
    });

    return {
      totalValue,
      totalArea,
      totalTax,
      productBreakdown,
      averagePricePerMeter: totalArea > 0 ? totalValue / totalArea : 0
    };
  }



  /**
   * Generate calculation breakdown
   */
  private static generateBreakdown(
    baseCalculation: any,
    insuranceAmount: number,
    data: ContractCalculationData
  ): CalculationBreakdown[] {
    const breakdown: CalculationBreakdown[] = [];

    // Base amount
    breakdown.push({
      description: 'القيمة الأساسية للعقد',
      amount: baseCalculation.totalValue
    });

    // Product breakdown
    baseCalculation.productBreakdown.forEach((product: any) => {
      breakdown.push({
        description: `${product.product} - ${product.area} متر مربع`,
        amount: product.baseValue
      });

      if (product.taxAmount > 0) {
        breakdown.push({
          description: `ضريبة ${product.product}`,
          amount: product.taxAmount,
          percentage: product.taxRate
        });
      }
    });

    // Insurance
    if (insuranceAmount > 0) {
      breakdown.push({
        description: 'التأمين النهائي',
        amount: insuranceAmount,
        percentage: data.finalInsuranceRate
      });
    }

    return breakdown;
  }

  /**
   * Generate calculation summary
   */
  private static generateSummary(
    products: ContractProduct[],
    durationYears: number,
    installments: InstallmentDetails[]
  ): CalculationSummary {
    const totalArea = products.reduce((sum, p) => sum + p.area, 0);
    const totalValue = products.reduce((sum, p) => sum + (p.area * p.meterPrice), 0);

    // Calculate first and last year totals from installments
    const firstYearInstallments = installments.filter(i => i.year === 1);
    const lastYear = Math.max(...installments.map(i => i.year));
    const lastYearInstallments = installments.filter(i => i.year === lastYear);

    const firstYearTotal = firstYearInstallments.reduce((sum, i) => sum + i.amount, 0);
    const lastYearTotal = lastYearInstallments.reduce((sum, i) => sum + i.amount, 0);

    return {
      totalProducts: products.length,
      totalArea,
      averagePricePerMeter: totalArea > 0 ? totalValue / totalArea : 0,
      totalYears: durationYears,
      totalInstallments: installments.length,
      firstYearTotal,
      lastYearTotal
    };
  }
}
