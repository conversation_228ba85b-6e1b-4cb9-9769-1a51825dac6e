import React from 'react';
import { useDateContext } from '@/contexts/date-context';
import { Input } from '@/components/ui/input';
import { useLanguage } from '@/hooks/use-language';

interface SimpleDatePickerProps {
  value?: string;
  onChange?: (date: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  id?: string;
}

export const SimpleDatePicker: React.FC<SimpleDatePickerProps> = ({
  value,
  onChange,
  placeholder,
  className = '',
  disabled = false,
  required = false,
  id
}) => {
  const { formatDate, getDateFormat, parseDate, formatDateForInput } = useDateContext();
  const { language } = useLanguage();

  // Determine text direction based on language
  const isRTL = language === 'ar';
  const textDirection = isRTL ? 'rtl' : 'ltr';

  // Convert value to display format
  const displayValue = value ? formatDate(value) : '';
  
  // Get placeholder based on date format
  const getPlaceholder = (): string => {
    if (placeholder) return placeholder;
    const format = getDateFormat();
    return `مثال: ${format.replace(/YYYY/g, '2024').replace(/MM/g, '01').replace(/DD/g, '15')}`;
  };

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    // If it's a date input, the value will be in YYYY-MM-DD format
    if (e.target.type === 'date') {
      onChange?.(inputValue);
      return;
    }
    
    // For text input, try to parse the date
    const parsedDate = parseDate(inputValue);
    if (parsedDate) {
      const isoString = formatDateForInput(parsedDate);
      onChange?.(isoString);
    } else {
      onChange?.(inputValue); // Keep the raw value for now
    }
  };

  return (
    <div className="space-y-2">
      {/* HTML5 Date Input (hidden but functional) */}
      <Input
        id={id}
        type="date"
        value={value || ''}
        onChange={handleChange}
        className={`${className} ${isRTL ? 'text-right' : 'text-left'}`}
        style={{
          direction: textDirection,
          textAlign: isRTL ? 'right' : 'left',
          unicodeBidi: 'plaintext'
        }}
        disabled={disabled}
        required={required}
      />
      
      {/* Display formatted date */}
      <div className="text-sm text-gray-600">
        <strong>التنسيق المعروض:</strong> {displayValue || 'لم يتم اختيار تاريخ'}
      </div>
      
      {/* Debug info */}
      <div className="text-xs text-gray-400">
        <div><strong>القيمة المحفوظة:</strong> {value || 'فارغ'}</div>
        <div><strong>التنسيق المطلوب:</strong> {getDateFormat()}</div>
      </div>
    </div>
  );
};

export default SimpleDatePicker;
