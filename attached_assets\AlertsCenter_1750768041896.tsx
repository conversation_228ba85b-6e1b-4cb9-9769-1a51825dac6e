import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "../components/ui/tabs";
import { Input } from "../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select";
import { useToast } from "../hooks/use-toast";
import { apiRequest } from "../lib/queryClient";
import {
  Bell,
  AlertTriangle,
  Clock,
  DollarSign,
  Calendar,
  CheckCircle,
  X,
  Filter,
  Search,
  Eye,
  Archive,
  Trash2,
  Settings,
  TrendingUp,
  Users,
  FileText,
  Target
} from "lucide-react";
import labels from "../i18n";

interface Alert {
  id: number;
  alertType: string;
  alertTitle: string;
  alertMessage: string;
  alertPriority: 'منخفض' | 'متوسط' | 'عالي' | 'حرج';
  contractId?: number;
  contractNumber?: string;
  clientId?: number;
  clientName?: string;
  alertDate: string;
  isRead: boolean;
  isArchived: boolean;
  actionRequired: boolean;
  actionUrl?: string;
  createdAt: string;
}

interface AlertStats {
  totalAlerts: number;
  unreadAlerts: number;
  highPriorityAlerts: number;
  actionRequiredAlerts: number;
  overduePayments: number;
  contractExpirations: number;
  systemAlerts: number;
}

const AlertsCenter = () => {
  const [lang] = useState<"ar" | "en">((localStorage.getItem("lang") as "ar" | "en") || "ar");
  const [activeTab, setActiveTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const t = labels[lang];

  // Fetch alerts
  const { data: alerts = [], isLoading: alertsLoading } = useQuery({
    queryKey: ["/api/alerts", activeTab, searchTerm, priorityFilter, typeFilter],
    queryFn: () => apiRequest(`/api/alerts?tab=${activeTab}&search=${searchTerm}&priority=${priorityFilter}&type=${typeFilter}`),
  });

  // Fetch alert statistics
  const { data: alertStats, isLoading: statsLoading } = useQuery({
    queryKey: ["/api/alerts/stats"],
    queryFn: () => apiRequest("/api/alerts/stats"),
  });

  // Mark alert as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: (alertId: number) => apiRequest(`/api/alerts/${alertId}/read`, {
      method: 'PUT',
    }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/alerts"] });
      queryClient.invalidateQueries({ queryKey: ["/api/alerts/stats"] });
    },
  });

  // Archive alert mutation
  const archiveAlertMutation = useMutation({
    mutationFn: (alertId: number) => apiRequest(`/api/alerts/${alertId}/archive`, {
      method: 'PUT',
    }),
    onSuccess: () => {
      toast({
        title: "تم الأرشفة",
        description: "تم أرشفة التنبيه بنجاح",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/alerts"] });
      queryClient.invalidateQueries({ queryKey: ["/api/alerts/stats"] });
    },
  });

  // Delete alert mutation
  const deleteAlertMutation = useMutation({
    mutationFn: (alertId: number) => apiRequest(`/api/alerts/${alertId}`, {
      method: 'DELETE',
    }),
    onSuccess: () => {
      toast({
        title: "تم الحذف",
        description: "تم حذف التنبيه بنجاح",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/alerts"] });
      queryClient.invalidateQueries({ queryKey: ["/api/alerts/stats"] });
    },
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'حرج': return 'bg-red-100 text-red-800 border-red-200';
      case 'عالي': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'متوسط': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'منخفض': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case 'payment_overdue': return <DollarSign className="h-4 w-4" />;
      case 'contract_expiring': return <Calendar className="h-4 w-4" />;
      case 'system_alert': return <Settings className="h-4 w-4" />;
      case 'performance_alert': return <TrendingUp className="h-4 w-4" />;
      case 'client_alert': return <Users className="h-4 w-4" />;
      case 'contract_alert': return <FileText className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getAlertTypeLabel = (alertType: string) => {
    const types: Record<string, string> = {
      'payment_overdue': 'دفعة متأخرة',
      'contract_expiring': 'انتهاء عقد',
      'system_alert': 'تنبيه نظام',
      'performance_alert': 'تنبيه أداء',
      'client_alert': 'تنبيه عميل',
      'contract_alert': 'تنبيه عقد',
    };
    return types[alertType] || alertType;
  };

  const filteredAlerts = alerts.filter((alert: Alert) => {
    const matchesTab = activeTab === 'all' || 
      (activeTab === 'unread' && !alert.isRead) ||
      (activeTab === 'action' && alert.actionRequired) ||
      (activeTab === 'archived' && alert.isArchived);
    
    const matchesSearch = searchTerm === '' || 
      alert.alertTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.alertMessage.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.clientName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.contractNumber?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesPriority = priorityFilter === 'all' || alert.alertPriority === priorityFilter;
    const matchesType = typeFilter === 'all' || alert.alertType === typeFilter;
    
    return matchesTab && matchesSearch && matchesPriority && matchesType;
  });

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-reverse space-x-4">
              <Bell className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {lang === "ar" ? "مركز التنبيهات" : "Alerts Center"}
              </h1>
              {alertStats && alertStats.unreadAlerts > 0 && (
                <Badge className="bg-red-500 text-white">
                  {alertStats.unreadAlerts} غير مقروء
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                إعدادات التنبيهات
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Alert Statistics */}
        {alertStats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي التنبيهات</p>
                    <p className="text-2xl font-bold text-blue-600">{alertStats.totalAlerts}</p>
                  </div>
                  <Bell className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">غير مقروءة</p>
                    <p className="text-2xl font-bold text-red-600">{alertStats.unreadAlerts}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">أولوية عالية</p>
                    <p className="text-2xl font-bold text-orange-600">{alertStats.highPriorityAlerts}</p>
                  </div>
                  <Target className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">تتطلب إجراء</p>
                    <p className="text-2xl font-bold text-purple-600">{alertStats.actionRequiredAlerts}</p>
                  </div>
                  <Clock className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              فلاتر التنبيهات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">البحث</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="البحث في التنبيهات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block">الأولوية</label>
                <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الأولويات" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأولويات</SelectItem>
                    <SelectItem value="حرج">حرج</SelectItem>
                    <SelectItem value="عالي">عالي</SelectItem>
                    <SelectItem value="متوسط">متوسط</SelectItem>
                    <SelectItem value="منخفض">منخفض</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block">النوع</label>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الأنواع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأنواع</SelectItem>
                    <SelectItem value="payment_overdue">دفعة متأخرة</SelectItem>
                    <SelectItem value="contract_expiring">انتهاء عقد</SelectItem>
                    <SelectItem value="system_alert">تنبيه نظام</SelectItem>
                    <SelectItem value="performance_alert">تنبيه أداء</SelectItem>
                    <SelectItem value="client_alert">تنبيه عميل</SelectItem>
                    <SelectItem value="contract_alert">تنبيه عقد</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-end">
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => {
                    setSearchTerm("");
                    setPriorityFilter("all");
                    setTypeFilter("all");
                  }}
                >
                  مسح الفلاتر
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Alerts Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">جميع التنبيهات</TabsTrigger>
            <TabsTrigger value="unread">غير مقروءة</TabsTrigger>
            <TabsTrigger value="action">تتطلب إجراء</TabsTrigger>
            <TabsTrigger value="archived">مؤرشفة</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-4">
            {alertsLoading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            ) : filteredAlerts.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد تنبيهات</h3>
                  <p className="text-gray-600">
                    {searchTerm || priorityFilter !== "all" || typeFilter !== "all"
                      ? "لا توجد تنبيهات تطابق معايير البحث"
                      : "لا توجد تنبيهات في هذه الفئة"}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredAlerts.map((alert: Alert) => (
                  <Card 
                    key={alert.id} 
                    className={`hover:shadow-md transition-shadow ${
                      !alert.isRead ? 'border-l-4 border-l-blue-500 bg-blue-50/30' : ''
                    }`}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="flex items-center gap-2">
                              {getAlertIcon(alert.alertType)}
                              <Badge variant="outline" className="text-xs">
                                {getAlertTypeLabel(alert.alertType)}
                              </Badge>
                            </div>
                            <Badge className={getPriorityColor(alert.alertPriority)}>
                              {alert.alertPriority}
                            </Badge>
                            {alert.actionRequired && (
                              <Badge className="bg-purple-100 text-purple-800">
                                يتطلب إجراء
                              </Badge>
                            )}
                            {!alert.isRead && (
                              <Badge className="bg-blue-100 text-blue-800">
                                جديد
                              </Badge>
                            )}
                          </div>
                          
                          <h3 className="font-semibold text-lg mb-2">{alert.alertTitle}</h3>
                          <p className="text-gray-600 mb-3">{alert.alertMessage}</p>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span>{formatDate(alert.alertDate)}</span>
                            {alert.contractNumber && (
                              <span>العقد: {alert.contractNumber}</span>
                            )}
                            {alert.clientName && (
                              <span>العميل: {alert.clientName}</span>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          {!alert.isRead && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => markAsReadMutation.mutate(alert.id)}
                              disabled={markAsReadMutation.isPending}
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {alert.actionUrl && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.location.href = alert.actionUrl!}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {!alert.isArchived && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => archiveAlertMutation.mutate(alert.id)}
                              disabled={archiveAlertMutation.isPending}
                            >
                              <Archive className="h-4 w-4" />
                            </Button>
                          )}
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deleteAlertMutation.mutate(alert.id)}
                            disabled={deleteAlertMutation.isPending}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default AlertsCenter;
