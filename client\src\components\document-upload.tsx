import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useLanguage } from "@/hooks/use-language";
import { cn } from "@/lib/utils";
import { compressImageIfNeeded, formatFileSize, getBase64Size, isImageFile } from "@/lib/image-utils";
import {
  Upload,
  X,
  Eye,
  Printer,
  Download,
  FileText,
  Image as ImageIcon,
  File,
  Loader2
} from "lucide-react";

export interface DocumentFile {
  id: string;
  name: string;
  type: string;
  data: string; // base64 data
  size: number;
  uploadDate: string;
}

interface DocumentUploadProps {
  documents: DocumentFile[];
  onDocumentsChange: (documents: DocumentFile[]) => void;
  maxFiles?: number;
  acceptedTypes?: string;
  label?: string;
  className?: string;
}

export function DocumentUpload({
  documents,
  onDocumentsChange,
  maxFiles = 10,
  acceptedTypes = "image/*,.pdf,.doc,.docx,.txt",
  label,
  className
}: DocumentUploadProps) {
  const { isRTL } = useLanguage();
  const [dragOver, setDragOver] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<DocumentFile | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (files: FileList | null) => {
    if (!files) return;

    const remainingSlots = maxFiles - documents.length;
    const filesToProcess = Math.min(files.length, remainingSlots);

    if (filesToProcess === 0) {
      alert(isRTL ? `الحد الأقصى ${maxFiles} ملفات` : `Maximum ${maxFiles} files allowed`);
      return;
    }

    setIsProcessing(true);
    const newDocuments: DocumentFile[] = [];

    try {
      for (let i = 0; i < filesToProcess; i++) {
        const file = files[i];

        // Check file size (max 20MB original)
        if (file.size > 20 * 1024 * 1024) {
          alert(isRTL ? `الملف ${file.name} كبير جداً (الحد الأقصى 20 ميجا)` : `File ${file.name} is too large (max 20MB)`);
          continue;
        }

        try {
          let processedData: string;
          let finalSize: number;

          if (isImageFile(file)) {
            // Compress images to max 2MB
            processedData = await compressImageIfNeeded(file, 2);
            finalSize = getBase64Size(processedData);
          } else {
            // For non-image files, just convert to base64
            processedData = await new Promise<string>((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = () => resolve(reader.result as string);
              reader.onerror = () => reject(new Error('Failed to read file'));
              reader.readAsDataURL(file);
            });
            finalSize = file.size;
          }

          const newDoc: DocumentFile = {
            id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: file.name,
            type: file.type,
            data: processedData,
            size: finalSize,
            uploadDate: new Date().toISOString()
          };

          newDocuments.push(newDoc);

          if (isImageFile(file)) {
            console.log(`Image compressed: ${formatFileSize(file.size)} → ${formatFileSize(finalSize)}`);
          }

        } catch (error) {
          console.error(`Failed to process file ${file.name}:`, error);
          alert(isRTL ? `فشل في معالجة الملف ${file.name}` : `Failed to process file ${file.name}`);
        }
      }

      if (newDocuments.length > 0) {
        onDocumentsChange([...documents, ...newDocuments]);
      }

    } finally {
      setIsProcessing(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const removeDocument = (id: string) => {
    onDocumentsChange(documents.filter(doc => doc.id !== id));
  };

  const viewDocument = (doc: DocumentFile) => {
    setSelectedDocument(doc);
    setShowModal(true);
  };

  const printDocument = (doc: DocumentFile) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const isImage = doc.type.startsWith('image/');
      
      printWindow.document.write(`
        <html>
          <head>
            <title>طباعة المستند - ${doc.name}</title>
            <style>
              body {
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
                text-align: center;
              }
              .header {
                margin-bottom: 20px;
                border-bottom: 2px solid #333;
                padding-bottom: 10px;
              }
              .document-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
              }
              .document-info {
                font-size: 14px;
                color: #666;
                margin-bottom: 20px;
              }
              img {
                max-width: 100%;
                max-height: 80vh;
                border: 1px solid #ddd;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              }
              iframe {
                width: 100%;
                height: 80vh;
                border: 1px solid #ddd;
              }
              @media print {
                body { padding: 0; }
                .header { page-break-inside: avoid; }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <div class="document-title">${doc.name}</div>
              <div class="document-info">تاريخ الرفع: ${new Date(doc.uploadDate).toLocaleDateString('ar-EG')}</div>
            </div>
            ${isImage 
              ? `<img src="${doc.data}" alt="${doc.name}" onload="window.print(); window.close();" />`
              : `<iframe src="${doc.data}" onload="window.print(); window.close();"></iframe>`
            }
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  const downloadDocument = (doc: DocumentFile) => {
    const link = document.createElement('a');
    link.href = doc.data;
    link.download = doc.name;
    link.click();
  };



  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <ImageIcon className="h-5 w-5" />;
    if (type.includes('pdf')) return <FileText className="h-5 w-5" />;
    return <File className="h-5 w-5" />;
  };

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className={cn(
            "flex items-center gap-2",
            isRTL ? "flex-row-reverse" : ""
          )}>
            <Upload className="h-5 w-5" />
            {label || (isRTL ? "تحميل المستندات" : "Upload Documents")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Upload Area */}
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
              isProcessing ? "cursor-not-allowed opacity-50" : "cursor-pointer",
              dragOver ? "border-primary bg-primary/5" : "border-gray-300 hover:border-gray-400",
              documents.length >= maxFiles && "opacity-50 cursor-not-allowed"
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => !isProcessing && documents.length < maxFiles && fileInputRef.current?.click()}
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-8 w-8 mx-auto mb-2 text-primary animate-spin" />
                <p className="text-sm text-gray-600 mb-2">
                  {isRTL ? "جاري معالجة الملفات..." : "Processing files..."}
                </p>
              </>
            ) : (
              <>
                <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-600 mb-2">
                  {isRTL
                    ? "اسحب الملفات هنا أو اضغط للاختيار"
                    : "Drag files here or click to select"
                  }
                </p>
                <p className="text-xs text-gray-500">
                  {isRTL
                    ? `الحد الأقصى: ${maxFiles} ملفات`
                    : `Maximum: ${maxFiles} files`
                  }
                </p>
                <p className="text-xs text-gray-500">
                  {isRTL
                    ? "الأنواع المدعومة: صور، PDF، Word، نصوص (سيتم ضغط الصور تلقائياً)"
                    : "Supported: Images, PDF, Word, Text (Images will be compressed automatically)"
                  }
                </p>
              </>
            )}
          </div>

          <Input
            ref={fileInputRef}
            type="file"
            multiple
            accept={acceptedTypes}
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
          />

          {/* Documents List */}
          {documents.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                {isRTL ? "المستندات المرفوعة:" : "Uploaded Documents:"}
              </Label>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {documents.map((doc) => (
                  <div
                    key={doc.id}
                    className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50"
                  >
                    <div className="flex-shrink-0 text-gray-500">
                      {getFileIcon(doc.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{doc.name}</p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(doc.size)} • {new Date(doc.uploadDate).toLocaleDateString(isRTL ? 'ar-EG' : 'en-US')}
                      </p>
                    </div>
                    <div className={cn(
                      "flex gap-1",
                      isRTL ? "flex-row-reverse" : ""
                    )}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => viewDocument(doc)}
                        className="h-8 w-8 p-0"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => printDocument(doc)}
                        className="h-8 w-8 p-0"
                      >
                        <Printer className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => downloadDocument(doc)}
                        className="h-8 w-8 p-0"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDocument(doc.id)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Document Modal */}
      {showModal && selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="relative max-w-4xl max-h-[90vh] p-4 w-full">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowModal(false)}
              className="absolute top-2 right-2 text-white hover:bg-white/20 z-10"
            >
              <X className="h-6 w-6" />
            </Button>
            
            <div className="bg-white rounded-lg overflow-hidden max-h-full">
              <div className="p-4 border-b">
                <h3 className="font-medium">{selectedDocument.name}</h3>
                <p className="text-sm text-gray-500">
                  {formatFileSize(selectedDocument.size)} • {new Date(selectedDocument.uploadDate).toLocaleDateString(isRTL ? 'ar-EG' : 'en-US')}
                </p>
              </div>
              
              <div className="p-4 max-h-[70vh] overflow-auto">
                {selectedDocument.type.startsWith('image/') ? (
                  <img
                    src={selectedDocument.data}
                    alt={selectedDocument.name}
                    className="max-w-full h-auto"
                  />
                ) : (
                  <iframe
                    src={selectedDocument.data}
                    className="w-full h-96 border-0"
                    title={selectedDocument.name}
                  />
                )}
              </div>
              
              <div className={cn(
                "p-4 border-t flex gap-2",
                isRTL ? "flex-row-reverse" : ""
              )}>
                <Button
                  variant="outline"
                  onClick={() => printDocument(selectedDocument)}
                  className="gap-2"
                >
                  <Printer className="h-4 w-4" />
                  {isRTL ? "طباعة" : "Print"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => downloadDocument(selectedDocument)}
                  className="gap-2"
                >
                  <Download className="h-4 w-4" />
                  {isRTL ? "تحميل" : "Download"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
