// ===== MIGRATION RUNNER =====
// Author: Augment Code
// Description: Database migration runner and manager

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createRequire } from 'module';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const require = createRequire(import.meta.url);

class MigrationRunner {
  constructor(db) {
    this.db = db;
    this.migrationsPath = __dirname;
  }

  async init() {
    // Create migrations tracking table
    await this.db.run(`
      CREATE TABLE IF NOT EXISTS MigrationHistory (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        migrationName TEXT NOT NULL UNIQUE,
        appliedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        rollbackAt DATETIME,
        status TEXT DEFAULT 'APPLIED' CHECK(status IN ('APPLIED', 'ROLLED_BACK', 'FAILED'))
      )
    `);
  }

  async runMigrations() {
    console.log('Starting database migrations...');

    try {
      // Initialize migration table first
      await this.init();
      // Get all migration files
      const migrationFiles = fs.readdirSync(this.migrationsPath)
        .filter(file => file.endsWith('.cjs') && !file.includes('migrationRunner'))
        .sort();

      console.log(`Found ${migrationFiles.length} migration files`);

      for (const file of migrationFiles) {
        await this.runMigration(file);
      }

      console.log('✅ All migrations completed successfully');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  async runMigration(filename) {
    const migrationName = path.basename(filename, '.cjs');
    
    // Check if migration already applied
    const existing = await this.db.get(
      'SELECT * FROM MigrationHistory WHERE migrationName = ? AND status = "APPLIED"',
      [migrationName]
    );

    if (existing) {
      console.log(`⏭️  Migration ${migrationName} already applied, skipping`);
      return;
    }

    console.log(`🔄 Running migration: ${migrationName}`);

    try {
      // Load and execute migration
      const migrationPath = path.join(this.migrationsPath, filename);
      const migration = require(migrationPath);
      
      if (typeof migration.up === 'function') {
        migration.up(this.db);
        
        // Record successful migration
        await this.db.run(
          'INSERT INTO MigrationHistory (migrationName, status) VALUES (?, "APPLIED")',
          [migrationName]
        );
        
        console.log(`✅ Migration ${migrationName} completed successfully`);
      } else {
        throw new Error(`Migration ${migrationName} does not export an 'up' function`);
      }
    } catch (error) {
      // Record failed migration
      await this.db.run(
        'INSERT INTO MigrationHistory (migrationName, status) VALUES (?, "FAILED")',
        [migrationName]
      );
      
      console.error(`❌ Migration ${migrationName} failed:`, error);
      throw error;
    }
  }

  async rollbackMigration(migrationName) {
    console.log(`🔄 Rolling back migration: ${migrationName}`);

    try {
      // Check if migration was applied
      const existing = await this.db.get(
        'SELECT * FROM MigrationHistory WHERE migrationName = ? AND status = "APPLIED"',
        [migrationName]
      );

      if (!existing) {
        console.log(`⏭️  Migration ${migrationName} not found or not applied`);
        return;
      }

      // Load and execute rollback
      const migrationPath = path.join(this.migrationsPath, `${migrationName}.cjs`);
      const migration = require(migrationPath);
      
      if (typeof migration.down === 'function') {
        migration.down(this.db);
        
        // Record rollback
        await this.db.run(
          'UPDATE MigrationHistory SET status = "ROLLED_BACK", rollbackAt = CURRENT_TIMESTAMP WHERE migrationName = ?',
          [migrationName]
        );
        
        console.log(`✅ Migration ${migrationName} rolled back successfully`);
      } else {
        throw new Error(`Migration ${migrationName} does not export a 'down' function`);
      }
    } catch (error) {
      console.error(`❌ Rollback ${migrationName} failed:`, error);
      throw error;
    }
  }

  async getMigrationHistory() {
    return await this.db.all(
      'SELECT * FROM MigrationHistory ORDER BY appliedAt DESC'
    );
  }

  async getAppliedMigrations() {
    return await this.db.all(
      'SELECT migrationName FROM MigrationHistory WHERE status = "APPLIED" ORDER BY appliedAt'
    );
  }

  async getPendingMigrations() {
    const migrationFiles = fs.readdirSync(this.migrationsPath)
      .filter(file => file.endsWith('.cjs') && !file.includes('migrationRunner'))
      .map(file => path.basename(file, '.cjs'))
      .sort();

    const appliedMigrations = await this.getAppliedMigrations();
    const appliedNames = appliedMigrations.map(m => m.migrationName);

    return migrationFiles.filter(name => !appliedNames.includes(name));
  }

  async resetMigrations() {
    console.log('🔄 Resetting all migrations...');
    
    try {
      await this.db.run('DELETE FROM MigrationHistory');
      console.log('✅ Migration history cleared');
    } catch (error) {
      console.error('❌ Failed to reset migrations:', error);
      throw error;
    }
  }
}

export default MigrationRunner;
