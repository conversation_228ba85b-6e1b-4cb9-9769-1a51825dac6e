import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  AlertTriangle, 
  Database, 
  ExternalLink, 
  CheckCircle,
  XCircle,
  Settings
} from 'lucide-react';
import { useReferenceListsWithAlerts, type ReferenceListStatus } from '@/hooks/use-reference-lists';

interface ReferenceListsAlertProps {
  pageName: string;
  showDetailedStatus?: boolean;
  className?: string;
}

export function ReferenceListsAlert({ 
  pageName, 
  showDetailedStatus = false,
  className = ""
}: ReferenceListsAlertProps) {
  const { status, hasIssues, missingRequired, summary } = useReferenceListsWithAlerts(pageName);

  if (!status) return null;

  const handleOpenReferenceData = () => {
    window.open('/reference-data', '_blank');
  };

  // Simple alert for missing required lists
  if (hasIssues && !showDetailedStatus) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>قوائم مرجعية مطلوبة</AlertTitle>
        <AlertDescription className="flex items-center justify-between">
          <span>
            يجب إعداد {missingRequired.length} قائمة مرجعية قبل المتابعة
          </span>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleOpenReferenceData}
            className="ml-2"
          >
            <Settings className="w-4 h-4 mr-1" />
            إعداد الآن
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  // Detailed status view
  if (showDetailedStatus) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            حالة البيانات المرجعية
            {hasIssues && (
              <Badge variant="destructive">
                {missingRequired.length} مفقود
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Summary */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{summary?.total || 0}</div>
              <div className="text-sm text-muted-foreground">إجمالي القوائم</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{summary?.configured || 0}</div>
              <div className="text-sm text-muted-foreground">معدة</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{summary?.required || 0}</div>
              <div className="text-sm text-muted-foreground">مطلوبة</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{summary?.missingRequired || 0}</div>
              <div className="text-sm text-muted-foreground">مفقودة</div>
            </div>
          </div>

          {/* Lists Status */}
          <div className="space-y-2">
            {status.map((list: ReferenceListStatus) => (
              <div key={list.listName} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {list.isConfigured ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-600" />
                  )}
                  <div>
                    <div className="font-medium">{list.displayName}</div>
                    <div className="text-sm text-muted-foreground">{list.description}</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {list.isRequired && (
                    <Badge variant="outline">مطلوبة</Badge>
                  )}
                  <Badge variant={list.isConfigured ? 'default' : 'destructive'}>
                    {list.itemCount} عنصر
                  </Badge>
                </div>
              </div>
            ))}
          </div>

          {/* Action Button */}
          <div className="flex justify-center pt-4">
            <Button onClick={handleOpenReferenceData} className="flex items-center gap-2">
              <ExternalLink className="w-4 h-4" />
              فتح البيانات المرجعية
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return null;
}

// Compact version for use in forms
export function ReferenceListsCompactAlert({ pageName, className = "" }: ReferenceListsAlertProps) {
  const { hasIssues, missingRequired } = useReferenceListsWithAlerts(pageName, false);

  if (!hasIssues) return null;

  return (
    <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-3 ${className}`}>
      <div className="flex items-center gap-2">
        <AlertTriangle className="w-4 h-4 text-yellow-600" />
        <span className="text-sm font-medium text-yellow-800">
          تحتاج إعداد {missingRequired.length} قائمة مرجعية
        </span>
        <Button 
          variant="link" 
          size="sm" 
          onClick={() => window.open('/reference-data', '_blank')}
          className="text-yellow-700 hover:text-yellow-900 p-0 h-auto"
        >
          إعداد الآن
        </Button>
      </div>
    </div>
  );
}

// Hook for checking if page can proceed
export function useCanProceed(pageName: string) {
  const { hasIssues, missingRequired } = useReferenceListsWithAlerts(pageName, false);
  
  return {
    canProceed: !hasIssues,
    blockedBy: missingRequired,
    message: hasIssues 
      ? `يجب إعداد ${missingRequired.length} قائمة مرجعية أولاً`
      : 'يمكن المتابعة'
  };
}
